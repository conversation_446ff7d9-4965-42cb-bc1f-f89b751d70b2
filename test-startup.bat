@echo off
echo 正在测试设计成果下载服务启动...
echo.

REM 设置Java路径（如果需要）
REM set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_XXX

REM 编译Java文件
echo 编译Java源文件...
javac -cp "target\classes;src\main\resources" -d target\classes src\main\java\com\supermap\*.java

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 运行应用
echo 启动应用...
java -cp "target\classes;src\main\resources" -Dspring.profiles.active=debug com.supermap.AchivementsDownloadApplication

pause
