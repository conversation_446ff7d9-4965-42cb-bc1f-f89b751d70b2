package com.supermap.common.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public  class BaseDto
        implements Serializable {

    @ApiModelProperty(value = "当前页码")
    private int current;

    @ApiModelProperty(value = "返回页数量")
    private int size;

    @ApiModelProperty(value = "开始日期")
    private String startDate;

    @ApiModelProperty(value = "结束日期")
    private String endDate;

    @ApiModelProperty(value = "是否分页1不分页0分页")
    private Integer isPage = 0;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "搜索关键字")
    private String searchName;



}
