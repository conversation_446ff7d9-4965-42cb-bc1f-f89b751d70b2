package com.supermap;

import com.supermap.config.VersionInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.oas.annotations.EnableOpenApi;

@SpringBootApplication
@ServletComponentScan   //启动器启动时，扫描本目录以及子目录带有的webservlet注解的
@EnableOpenApi// 启动swagger ui注解
@Slf4j
@EnableScheduling
@EnableDiscoveryClient
@EnableAsync
public class AchivementsDownloadApplication {

    //private static Logger logger3 = LoggerFactory.getLogger(GmApiApplication.class);

    /**
     * main
     * @params [args]
     * @return void
     * <AUTHOR>
     * @date 2019/10/8 11:11
     */
    public static void main(String[] args) {
        log.info("设计成果下载开始启动...");
        SpringApplication springApplication = new SpringApplication(AchivementsDownloadApplication.class);
        springApplication.addListeners(new ApplicationStartup());

        try {
            ConfigurableApplicationContext context = springApplication.run(args);
            log.info("Spring应用上下文启动完成");

            // 获取并输出版本信息
            try {
                VersionInfo versionInfo = context.getBean(VersionInfo.class);
                log.info("设计成果下载服务 版本: {}, 构建时间: {}", versionInfo.getVersion(), versionInfo.getBuildTime());
            } catch (Exception e) {
                log.warn("获取版本信息失败: {}", e.getMessage());
                log.info("设计成果下载服务 版本: 未知, 构建时间: 未知");
            }

            log.info("设计成果下载服务启动成功!");
        } catch (Exception e) {
            log.error("设计成果下载服务启动失败: {}", e.getMessage(), e);
            throw e;
        }
    }

}
