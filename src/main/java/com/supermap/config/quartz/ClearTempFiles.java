package com.supermap.config.quartz;

import com.supermap.tools.file.PathUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.util.Date;

@Component
/**
 * 定时清除过期临时文件的方法
 */
public class ClearTempFiles {

    @Scheduled(cron = "0 0 0 * * ?") // 每天的零点执行
    public void checkForDeleteTempFiles() {
        String strPackagePath = PathUtils.getPackagePath();
        File folderPackage = new File(strPackagePath);
        if(!folderPackage.exists())
            return;
        File[] files = folderPackage.listFiles();
        for(int i=0;i< files.length;i++){
            File file = files[i];
            if(file.isDirectory()/*||file.getName().indexOf(".zip")==file.getName().length()-4*/){
                Long lLastModified = file.lastModified();
                Long lNow = (new Date()).getTime();
                long diffInSeconds = (lNow-lLastModified)/1000;
                int daysDiff = (int)(diffInSeconds / (60*60*24));
                if(daysDiff>=1){
                    try {
                        deleteDirectory(file.toPath());
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    private void deleteDirectory(Path path) throws IOException {
        if (Files.isDirectory(path, LinkOption.NOFOLLOW_LINKS)) {
            try (DirectoryStream<Path> entries = Files.newDirectoryStream(path)) {
                for (Path entry : entries) {
                    deleteDirectory(entry);
                }
            }
        }

        Files.delete(path);
    }
}
