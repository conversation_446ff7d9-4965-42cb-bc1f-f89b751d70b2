//package com.supermap.config.quartz;
//
//import com.supermap.base.entity.quartz.TaskDefine;
//import com.supermap.pipedesign.pipechina.demo.controller.quartz.SchedulerQuartzJob;
//import lombok.extern.slf4j.Slf4j;
//import org.quartz.JobKey;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
///**
// * ScheduleJobInitListener
// *
// * <AUTHOR>
// * @date 21/7/12 16:39
// */
//
//@Slf4j
//@Component
//public class ScheduleJobInitListener implements CommandLineRunner {
//
//    @Autowired
//    private QuartzConfigration quartzConfigration;
//
//    //假如 这个定时任务的 名字叫做HelloWorld, 组名GroupOne
//    private final JobKey jobKey = JobKey.jobKey("Token", "GroupOne");
//
//    @Override
//    public void run(String... arg0) {
//        try {
//            //创建一个定时任务
//            TaskDefine task = new TaskDefine(jobKey,
//                    "这是一个测试的 任务",       //定时任务 的描述
////                    "0 0/1 * * * ?",           //定时任务 的cron表达式 每个小时执行一次
//                    "0 0 0/1 * * ?",           //定时任务 的cron表达式 每个小时执行一次
//                    null,
//                    SchedulerQuartzJob.class //定时任务 的具体执行逻辑
//            );
//            quartzConfigration.scheduleJob(task);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }
//
//}