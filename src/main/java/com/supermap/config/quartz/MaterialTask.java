package com.supermap.config.quartz;

import com.supermap.pipedesign.pipechina.metadata.service.ITypeRegisterTemService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 材料库定时任务
 */
@Component
public class MaterialTask {
    //@Autowired
    //private TypeRegisterTemImpl typeRegisterTemImpl;
//    @Resource(name = "TypeRegisterTemImpl")
//    private ITypeRegisterTemService typeRegisterTemImpl;
//
//    @Scheduled(cron="0 10 0 * * ?")
//    //@Scheduled(fixedRate = 5000)
//   public void testTask(){
//        LocalDate localDate = LocalDate.now().minusDays(1);
//        typeRegisterTemImpl.selInfoFrom(1, localDate.toString());
//    }
}