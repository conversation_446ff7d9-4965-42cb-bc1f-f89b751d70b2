package com.supermap.config.filter;

import cn.hutool.http.HttpUtil;
import com.supermap.tools.http.RequestUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.List;
import java.util.Map;

/**
 * HttpServletRequestWrapperMerge
 *
 * <AUTHOR>
 * @date 22/2/24 16:17
 */

public class HttpServletRequestWrapperMerge extends HttpServletRequestWrapper {
    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @throws IllegalArgumentException if the request is null
     */
    public HttpServletRequestWrapperMerge(HttpServletRequest request) {
        super(request);
    }

    private Map<String, List<String>> bodyMap = null;

    /**
     * 当调用 Content-Type x-www-form-urlencoded
     * @param name
     * @return
     */
    @Override
    public String getHeader(String name) {
        // 先从原本的Request中获取头，如果为空且名字为token，则从参数中查找并返回
        String superHeader = super.getHeader(name);
        // content-type = application/x-www-form-urlencoded
        if("content-type".equals(name.toLowerCase())){
            return "application/x-www-form-urlencoded";
        }
        return superHeader;
    }

    @Override
    public String[] getParameterValues(String name) {
        if (bodyMap == null) {
            String postBody = RequestUtils.getPostBody((HttpServletRequest) this.getRequest());
            bodyMap = HttpUtil.decodeParams(postBody,"UTF-8");
        }
        //
        for (Map.Entry<String, List<String>> ent:bodyMap.entrySet()) {
            if (name.equals(ent.getKey())) {
                return ent.getValue().toArray(new String[ent.getValue().size()]);
            }
        }
        return super.getParameterValues(name);
    }
}