package com.supermap.config.filter;

import cn.hutool.json.JSONUtil;
import com.supermap.config.db.DbVariable;
import com.supermap.tools.http.RequestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.FilterConfig;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * ControllerFilter
 *
 * <AUTHOR>
 * @date 21/8/13 15:40
 */

public class ControllerFilter implements Filter {


    private static Logger logger = LoggerFactory.getLogger(ControllerFilter.class);

    /**
     * 过滤器初始化
     */
    @Override
    public void init(FilterConfig cfg) throws ServletException {
    }

    /**
     * 执行过滤操作
     */
    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) res;
        response.setContentType("text/html;charset=utf-8");
        //
        //判断是否druid访问
        long begintime = System.currentTimeMillis();
        //logger.info("[doFilter] - [begintime] - " + begintime);
        String swagger = "/swagger-resources/configuration/security";
        if (request.getRequestURI().indexOf(swagger) > -1) {
            ServletOutputStream out = response.getOutputStream();
            out.write("{}".getBytes());
            out.flush();
            return;
        }
        //获取请求
//        logger.info("[postBody]" + RequestUtils.getPostBody(request));
//        logger.info("[url]" + request.getRequestURI());
        String projectId = request.getHeader("projectId");
        if (StringUtils.isNotBlank(projectId)) {
            DbVariable.getInstance().setProjectId(projectId);
        }
        HttpServletRequestWrapper requestWrapper = new HttpServletRequestWrapperMergeBak(request);
        chain.doFilter(requestWrapper, response);

        long endtime = System.currentTimeMillis();
        //logger.info("[doFilter] - [endtime] - " + endtime);

        long costTime = (endtime - begintime);
        //logger.info("[此次请求耗时] - {}毫秒 - [{}]", costTime, request.getRequestURL());
    }


    /**
     * 过滤器释放资源
     */
    @Override
    public void destroy() {
    }

}