package com.supermap.config.filter;


import cn.hutool.http.HttpUtil;
import com.supermap.tools.gson.GsonUtil;
import com.supermap.tools.http.RequestUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * HttpServletRequestWrapperMerge
 *
 * <AUTHOR>
 * @date 21/9/9 18:34
 */


public class HttpServletRequestWrapperMergeBak extends HttpServletRequestWrapper {

    /**
     * 请求体
     */
    private String newBody;

    private Map<String, List<String>> bodyMap = null;

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @throws IllegalArgumentException if the request is null
     */
    public HttpServletRequestWrapperMergeBak(HttpServletRequest request) {
        super(request);
        String contentType = request.getHeader("content-type");
        if (contentType!=null && !"".equals(contentType) && contentType.indexOf("multipart/form-data;") > -1) {
        } else {
            newBody = RequestUtils.getPostBody((HttpServletRequest) this.getRequest());
            //
            if (bodyMap == null) {
                //
                if (newBody.length() > 0) {
                    if (newBody.indexOf("{") == 0 || newBody.indexOf("[") == 0) {
                        bodyMap = GsonUtil.GsonToMapString(newBody);
                    } else {
                        bodyMap = HttpUtil.decodeParams(newBody, "UTF-8");
                    }
                }
            }
        }

    }

    /**
     * 获取请求体
     *
     * @return 请求体
     */
    public String getBody() {
        return newBody;
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        // 创建字节数组输入流
        final ByteArrayInputStream bais = new ByteArrayInputStream(newBody.getBytes(StandardCharsets.UTF_8));

        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public int read() throws IOException {
                return bais.read();
            }
        };
    }


    /**
     * 当调用 Content-Type x-www-form-urlencoded
     *
     * @param name
     * @return
     */
    @Override
    public String getHeader(String name) {
        // 先从原本的Request中获取头，如果为空且名字为token，则从参数中查找并返回
        String superHeader = super.getHeader(name);
        if (superHeader==null){
            return null;
        }
        // content-type = application/x-www-form-urlencoded
        if ("content-type".equals(name.toLowerCase()) && (superHeader.indexOf("multipart/form-data;") == -1)) {
            return "application/x-www-form-urlencoded";
        }
        return superHeader;
    }

    @Override
    public String[] getParameterValues(String name) {
        //
        if (bodyMap != null) {
            for (Map.Entry<String, List<String>> ent : bodyMap.entrySet()) {
                if (name.equals(ent.getKey())) {
                    return ent.getValue().toArray(new String[ent.getValue().size()]);
                }
            }
        }
        return super.getParameterValues(name);
    }

    /*@Override
    public String getParameter(String name) {
        //
        if (bodyMap != null) {
            for (Map.Entry<String, List<String>> ent : bodyMap.entrySet()) {
                if (name.equals(ent.getKey())) {
                    return ent.getValue().toString();
                }
            }
        }
        return super.getParameter(name);
    }*/

}