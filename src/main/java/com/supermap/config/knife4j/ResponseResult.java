package com.supermap.config.knife4j;

import com.supermap.base.entity.Result;
import com.supermap.base.entity.ResultEnum;
import com.supermap.config.exception.BusinessException;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * ResponseResult
 * 使用 @ControllerAdvice & ResponseBodyAdvice
 * 拦截Controller方法默认返回参数，
 * 统一处理返回值/响应体
 *
 * <AUTHOR>
 * @date 2021/3/6 18:33
 */
// @ControllerAdvice：作用：对所有控制器中，被@RequestMapping注解标注的方法，进行增强（也可以直接使用@RestControllerAdvice）
// 控制器类增强：可以对Controller中所有使用@RequestMapping注解的方法增强
@RestControllerAdvice(basePackages = {
        "com.supermap.pipedesign.pipechina.inspectroutes.controllers",
        "com.supermap.pipedesign.pipechina.sqlite.controller",
        "com.supermap.pipedesign.pipechina.common.controller",
        "com.supermap.pipedesign.pipechina.engineering.controllers",
        "com.supermap.pipedesign.pipechina.entitiesachivment.controllers",
        "com.supermap.pipedesign.pipechina.metadata.controllers",
        "com.supermap.pipedesign.pipechina.rules.controllers",
        "com.supermap.pipedesign.pipechina.drawingtemplate.controllers",
        "com.supermap.pipedesign.pipechina.file.controllers"
        })
public class ResponseResult<T> implements ResponseBodyAdvice<Object> {

    /**
     * 被拦截的响应，立即执行该方法。
     * body ：是请求控制器方法接口后，响应的内容。（其他参数不用了解）
     */
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
                                  ServerHttpResponse response) {
        // String类型不能直接包装
        if (returnType.getGenericParameterType().equals(String.class)) {
            return GsonUtil.GsonString(Result.success(body));
        }
        // 判断返回值结果是否是一个异常对象类型
        else if (body instanceof BusinessException) {
            // 如果是异常类型，传入异常状态码（枚举类型）和异常数据。
            return Result.error(ResultEnum.ERROR, body);
        }
        return Result.success(body);
    }

    /**
     * 这个方法的返回值，决定是否启动结果响应拦截，当返回为true是，表示拦截
     */
    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

}