package com.supermap.config.db;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * CustomIdGenerator
 *
 * <AUTHOR>
 * @date 2020/7/7 15:18
 */
@Component
public class CustomIdGenerator implements IdentifierGenerator {


    @Override
    public Number nextId(Object entity) {
        return null;
    }

    @Override
    public String nextUUID(Object entity) {
        return UUID.randomUUID().toString();//返回36位uuid ，覆盖默认返回32位
    }
}