package com.supermap.config.db;

/**
 * DBTypeEnum
 *
 * <AUTHOR>
 * @date 2021/3/3 15:24
 */
public enum DBTypeEnum {

    // 工程库
    engineering("engineering"),
    // 元数据库
    metadata("metadata"),

    // 模型-模板库
    drawingtemplate("drawingtemplate"),
    //规则库
    rules("rules"),

    //成果库
    entitiesachivment("entitiesachivment"),

    //管知行轨迹库
    inspectroutes("inspectroutes");

    private String value;

    DBTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
