package com.supermap.config.db;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.supermap.tools.spring.SpringBootBeanUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.jdbc.DataSourceBuilder;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * DBContextHolder
 *
 * <AUTHOR>
 * @date 2021/3/3 11:54
 */
public class DBContextHolder {
    private static final Logger log = LoggerFactory.getLogger(DBContextHolder.class);

//    private static final ThreadLocal contextHolder = new ThreadLocal<>();
//    private static final ThreadLocal<String> dbKeys = ThreadLocal.withInitial(() -> null);
    private static final ThreadLocal<String> dbKeys = new ThreadLocal<>();
    private static final Map<Object, Object> dataSources = new HashMap<>();

    public static DataSource buildDataSource(String calssName, String userName, String password, String url, String datasourceName) {
        //"************************************************************************************************************"
        /*DataSourceBuilder<?> builder = DataSourceBuilder.create();
        builder.driverClassName(calssName);
        builder.username(userName);
        builder.password(password);
        builder.url(url);
        return builder.build();*/

        try {

            /*Map map = new HashMap<>();
            map.put(DruidDataSourceFactory.PROP_DRIVERCLASSNAME, calssName);
            map.put(DruidDataSourceFactory.PROP_URL, url);
            map.put(DruidDataSourceFactory.PROP_USERNAME, userName);
            map.put(DruidDataSourceFactory.PROP_PASSWORD, password);

            // 初始化时建立物理连接的个数
            map.put(DruidDataSourceFactory.PROP_INITIALSIZE, "3");
            // 最小连接池数量
            map.put(DruidDataSourceFactory.PROP_MINIDLE, "1");
            // 最大连接池数量
            map.put(DruidDataSourceFactory.PROP_MAXACTIVE, "5");
            // 获取连接时最大等待时间，单位毫秒
            map.put(DruidDataSourceFactory.PROP_MAXWAIT, "60000");
            // 检测连接的间隔时间，单位毫秒
            map.put(DruidDataSourceFactory.PROP_TIMEBETWEENEVICTIONRUNSMILLIS, "60000");
            // wall:防御sql注入  stat:监控统计
            map.put(DruidDataSourceFactory.PROP_FILTERS, "wall,stat");

            map.put(DruidDataSourceFactory.PROP_NAME, datasourceName);*/

            DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
            dataSource.setUrl(url);
            dataSource.setDriverClassName(calssName);
            dataSource.setUsername(userName);
            dataSource.setPassword(password);

            dataSource.setName(datasourceName);
            dataSource.setMaxWait(60000);
            dataSource.setInitialSize(3);
            dataSource.setMinIdle(1);
            dataSource.setMaxActive(3);
            //dataSource.setRemoveAbandonedTimeout();

            return dataSource;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @作者 eomer
     * @描述 添加数据源
     * @日期 2023/04/18 16:29
     * 参数 name 项目ID
     * 参数 calssName
     * 参数 userName
     * 参数 password
     * 参数 url
     * @返回值 void
     **/
    public static void addDataSource(String name, String calssName, String userName, String password, String url) {
        if (dataSources.get(name) == null){
            addDataSource(name,buildDataSource(calssName, userName, password, url,name));
        }

    }

    /**
     * 动态添加一个数据源
     *
     * @param name       数据源的key
     * @param dataSource 数据源对象
     */
    public static void addDataSource(String name, DataSource dataSource) {

        if (dataSource == null) {
            return;
        }
        DynamicDataSource dynamicDataSource = SpringBootBeanUtil.getBean(DynamicDataSource.class);
        dataSources.put(name, dataSource);
        dynamicDataSource.setTargetDataSources(dataSources);
        dynamicDataSource.afterPropertiesSet();

        log.info("添加了数据源:{}", name);
    }

    public static void removeDataSource(String key){
        dataSources.remove(key);
    }
    /**
     * @描述 获取数据源列表
     * @日期 2023/04/18 16:49
     * @作者 eomer
     **/
    public static Map<Object, Object> getDataSources(){
        return dataSources;
    }
    /**
     * @描述 根据key 获取数据源
     * @日期 2023/04/18 16:55
     * @作者 eomer
     **/
    public static Object getDataSource(String key){
        return dataSources.get(key);
    }

    /**
     * 切换数据源
     */
    public static void switchDb(String dbKey) {
        dbKeys.set(dbKey);
    }

    /**
     * 重置数据源
     */
    public static void resetDb() {
        dbKeys.remove();
    }

    /**
     * 获取当前数据源
     */
    public static String currentDb() {
        return dbKeys.get();
    }

}
