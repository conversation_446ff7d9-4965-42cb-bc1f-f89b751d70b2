package com.supermap.base.entity;


import lombok.Data;

/**
 * LoginToken
 * 登录信息
 * <AUTHOR>
 * @date 2020/5/11 10:47
 */
@Data
public class LoginToken {

    private String id;

    private String sessionId;
    /**
     * 用户id
     */
    private String colUserId;
    /**
     * 租户id
     */
    private String colTenantId;
    /**
     * 是否租户
     */
    private boolean colIsTenant;

    /**
     * 所属公司id
     */
    private String colOrgTid;
    /**
     * 所属公司
     */
    private String colCompanyName;

    /**
     * 部门id
     */
    private String colDepartId;

    /**
     * 部门名称
     */
    private String colDepartName;

    /**
     * 岗位id
     */
    private String colPostId;

    /**
     * 岗位名称
     */
    private String colPostName;

    /**
     * 账号名称
     */
    private String colAccountName;
    /**
     * 昵称
     */
    private String colUserName;
    /**
     * 登录类型（是否超级用户：平台超级管理员 admin、 租户超级管理员 tenant、 员工 employee）
     */
    private String loginType;
    /**
     * 客户端类型（PC 、 APP）
     */
    private String clientType;
    /**
     * 登录时间
     */
    private long issuedAt;
    /**
     * 截止时间
     */
    private long expiresIn;
    /**
     * 登录ip
     */
    private String clientIp;
    /**
     * 公钥
     */
    private String publicKey;
    /**
     * 私钥
     */
    private String privateKey;

}
