package com.supermap.base.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 响应数据实体包装类
 *
 * @Date 2020/4/26 18:10
 * @Created by eomer
 */
@Data
@ApiModel(value = "Result", description = "响应数据实体包装类")
public class Result {


    /**
     * code : 0      0 成功 ， 非0 错误代码， 501服务器错误
     * message : ""  消息提示
     * data : null   响应数据
     */
    @ApiModelProperty(value = "响应编码")
    private int code;
    @ApiModelProperty(value = "消息提示")
    private String msg;
    @ApiModelProperty(value = "响应数据")
    private Object data;

    /**
     * 请求成功
     *
     * @param data 响应数据
     * @return com.eomer.entity.base.Response
     * @Date 2020/4/30 11:11
     * @auther eomer
     */
    public static Result success(Object data) {

        Result result = new Result();
        result.setCode(ResultEnum.OK.getCode());
        result.setMsg(ResultEnum.OK.getMsg());
        result.setData(data);
        return result;
    }

    public static Result success() {
        return success("");
    }

    public static Result success(String msg) {
        Result result = new Result();
        result.setCode(ResultEnum.OK.getCode());
        result.setMsg(msg);
        return result;
    }


    /**
     * 请求异常
     * @param eEnum
     * @param error_msgg
     * @return com.eomer.entity.base.Response
     * @Date 2020/4/30 11:10
     * @auther eomer
     */
    public static Result error(ResultEnum eEnum, String error_msgg) {

        Result result = new Result();
        result.setCode(eEnum.getCode());
        result.setMsg(eEnum.getMsg()+" "+error_msgg);
        result.setData(null);
        return result;
    }

    public static Result error(ResultEnum eEnum, Object data) {

        Result result = new Result();
        result.setCode(eEnum.getCode());
        result.setMsg(eEnum.getMsg());
        result.setData(data);
        return result;
    }

    public static Result error(ResultEnum eEnum) {
        return error(eEnum,"");
    }


}
