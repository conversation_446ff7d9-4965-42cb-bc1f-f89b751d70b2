package com.supermap.base.entity;

import lombok.Data;

import javax.servlet.http.HttpServletRequest;

/**
 * Request
 *
 * <AUTHOR>
 * @date 2020/12/10 9:31
 */
@Data
public class Request {

    /**
     * 名称  类型 是否必须 描述
     * method	String	是	API接口名称。
     * client_type String 客户端类型
     * sign	String	是	API输入参数签名结果，签名算法介绍请点击这里。
     * session	String	否	用户登录授权成功后，TOP颁发给应用的授权信息，详细介绍请点击这里。
     * 当此API的标签上注明：“需要授权”，则此参数必传；“不需要授权”，则此参数不需要传；“可选授权”，则此参数为可选。
     * timestamp	String	是	时间戳，格式为yyyy-MM-dd HH:mm:ss，时区为GMT+8，例如：2015-01-01 12:00:00。
     * v	String	是	API 版本，默认：1.0.0。
     * partner_id	String	否	合作伙伴身份标识。 ISV使用
     * sign_method	String	是	签名的摘要算法，默认为： md5。
     * format	String	否	响应格式。默认为json格式，可选值：xml，json。
     * simplify	Boolean	否	是否采用精简JSON返回格式，仅当format=json时有效，默认值为：false。
     * data Object 参数
     * sign = MD5(method+client_type+v+session+timestamp)
     */
    private String method;
    private String client_type;
    private String sign_method;
    private String sign;
    private String session;
    private String timestamp;
    private String version;
    private Object data;

    private String format;
    private boolean simplify;
    private String partner_id;
    private String body;


    private String trimParams(String params){
        return params==null?"":params.trim();
    }

    public Request(HttpServletRequest request, String body, Object data) {

        String method = request.getHeader("method");
        String sign_method = request.getHeader("sign_method");
        String sign = request.getHeader("sign");
        String session = request.getHeader("session");
        String timestamp = request.getHeader("timestamp");
        String client_type = request.getHeader("client_type");
        String version = request.getHeader("version");
        //
        this.method = trimParams(method);
        this.client_type = trimParams(client_type);
        this.sign_method = trimParams(sign_method);
        this.sign = trimParams(sign);
        //添加登录判断，解决前端session不为空问题
        if("SYS_USER_LOGIN".equals(method)){
            this.session = "";
        }else {
            this.session = trimParams(session);
        }

        this.timestamp = trimParams(timestamp);
        this.version = trimParams(version);
        this.body = trimParams(body);
        this.data = data;
    }

    public Request(String method, String client_type, String sign_method, String sign, String session, String timestamp, String version, Object data) {
        this.method = method;
        this.client_type = client_type;
        this.sign_method = sign_method;
        this.sign = sign;
        this.session = session;
        this.timestamp = timestamp;
        this.version = version;
        this.data = data;
    }

    public Request(String method, String client_type, String sign_method, String sign, String session, String timestamp, String version, Object data, String format, boolean simplify, String partner_id) {
        this.method = method;
        this.client_type = client_type;
        this.sign_method = sign_method;
        this.sign = sign;
        this.session = session;
        this.timestamp = timestamp;
        this.version = version;
        this.data = data;
        this.format = format;
        this.simplify = simplify;
        this.partner_id = partner_id;
    }

    public String getRequestContent(){
        return method + client_type + sign_method + timestamp + version + session + body;
    }


}
