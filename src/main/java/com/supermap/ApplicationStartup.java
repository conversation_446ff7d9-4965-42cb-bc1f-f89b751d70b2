package com.supermap;

import com.supermap.tools.base.RedisCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * 该类用来进行系统启动后的初始化工作
 */
@Slf4j
@Configuration
@EnableScheduling
public class ApplicationStartup implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private RedisCommonUtil _redisCommonUtil;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        log.info("ApplicationStartup: 接收到ContextRefreshedEvent事件");
    }

    /**
     * 初始化Redis的变量值
     */
    //@Scheduled(fixedRate = 1000)      ----这行代码开启后将会启用定时器功能
    public void initRedisValues(){
        try {
            log.info("ApplicationStartup: 开始初始化Redis变量值");
            //初始化并行执行的进程/线程数为0
            _redisCommonUtil.setKeyAndTime("parallelprocesscount", "0", 1, TimeUnit.HOURS);
            log.info("ApplicationStartup: Redis变量值初始化完成");
        } catch (Exception e) {
            log.error("ApplicationStartup: Redis变量值初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 在项目启动时进行的环境初始化操作
     * (只在系统启动时执行一次)
     */
    @PostConstruct
    public void prepareEnvironments() {
        log.info("ApplicationStartup: 开始执行环境初始化操作");
        try {
            initRedisValues();
            log.info("ApplicationStartup: 环境初始化操作完成");
        } catch (Exception e) {
            log.error("ApplicationStartup: 环境初始化操作失败: {}", e.getMessage(), e);
        }
    }
}
