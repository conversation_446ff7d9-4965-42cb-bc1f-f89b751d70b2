package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.base.entity.TableEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 施工阶段表(定义预可行性研究，可行性研究，初步设计，施工图设计，竣工图设计等阶段,0,1,2,3,4,5) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface RulesCommonMapper extends BaseMapper<TableEntity> {

    @Select(" ${selectSql} ")
    List<Map> getList(@Param("selectSql") String selectSql);

}
