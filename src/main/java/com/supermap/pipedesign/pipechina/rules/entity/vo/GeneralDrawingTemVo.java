package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawingTem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 通用图 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
public class GeneralDrawingTemVo implements Serializable {

    private List<GeneralTreeTemVo> treeTemVoList;

    private List<GeneralDrawingTem>  generalDrawingTemList;

}
