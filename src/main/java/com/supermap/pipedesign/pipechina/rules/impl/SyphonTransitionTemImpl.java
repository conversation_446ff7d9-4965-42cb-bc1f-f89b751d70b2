package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.rules.dao.SyphonTransitionTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.SyphonTransitionTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.SyphonTransitionTemVo;
import com.supermap.pipedesign.pipechina.rules.service.ISyphonTransitionTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 弯管 过渡段规则模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Service("SyphonTransitionTemImpl")
public class SyphonTransitionTemImpl extends ServiceImpl<SyphonTransitionTemMapper, SyphonTransitionTem> implements ISyphonTransitionTemService {

    @Autowired
    private SyphonTransitionTemMapper syphonTransitionTemMapper;

    /**
    * 添加弯管 过渡段规则模板表信息
    * @param vo
    * @return int
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public int insert(SyphonTransitionTemVo vo) {
        int result = 0;
        List<SyphonTransitionTem> list = vo.getList();
        for (SyphonTransitionTem tem : list){
            SyphonTransitionTem syphonTransitionTem = syphonTransitionTemMapper.selectById(tem.getPkid());
            if (syphonTransitionTem != null) {
                result = syphonTransitionTemMapper.updateById(tem);
                continue;
            }
            result = syphonTransitionTemMapper.insert(tem);
        }
        return result;
    }


    /**
    * 全部查询
    * @param syphonTransitionTem
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.SyphonTransitionTem>
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public List<SyphonTransitionTem> list(SyphonTransitionTem syphonTransitionTem) {
        QueryWrapper<SyphonTransitionTem> queryWrapper = new QueryWrapper<>();
        return syphonTransitionTemMapper.selectList(queryWrapper);
    }


}
