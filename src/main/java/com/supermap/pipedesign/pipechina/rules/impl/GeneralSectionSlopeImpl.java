package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralSectionSlope;
import com.supermap.pipedesign.pipechina.rules.dao.GeneralSectionSlopeMapper;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralSectionSlopeTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.GeneralSectionSlopeProVo;
import com.supermap.pipedesign.pipechina.rules.service.IGeneralSectionSlopeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 一般段放坡规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Service("GeneralSectionSlopeImpl")
public class GeneralSectionSlopeImpl extends ServiceImpl<GeneralSectionSlopeMapper, GeneralSectionSlope> implements IGeneralSectionSlopeService {

    @Autowired
    private GeneralSectionSlopeMapper generalSectionSlopeMapper;

    /**
    * 添加一般段放坡规则信息
    *
    * @param generalSectionSlope
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int insert(GeneralSectionSlope generalSectionSlope) {

        //generalSectionSlope.setUserId(JavaUtils.getUUID36());
        //generalSectionSlope.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return generalSectionSlopeMapper.insert(generalSectionSlope);
    }

    /**
    * 删除一般段放坡规则信息
    *
    * @param generalSectionSlopeId
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int delete(String generalSectionSlopeId) {
        return generalSectionSlopeMapper.deleteById(generalSectionSlopeId);
    }

    /**
    * 更新一般段放坡规则信息
    *
    * @param generalSectionSlope
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int update(@Valid GeneralSectionSlopeProVo generalSectionSlope) {
        List<GeneralSectionSlope> excavationThroughTemList = generalSectionSlope.getGeneralSectionSlopeTemList();
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("type",excavationThroughTemList.get(0).getType());
        queryWrapper.eq("project_id",excavationThroughTemList.get(0).getProjectId());
        generalSectionSlopeMapper.delete(queryWrapper);
        for (GeneralSectionSlope excavationThroughTems : excavationThroughTemList) {
            generalSectionSlopeMapper.insert(excavationThroughTems);
        }
        return 1;
    }

    /**
    * 全部查询
    *
    * @param type
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.GeneralSectionSlope>
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public List<GeneralSectionSlope> list(Integer type, String projectId) {
        QueryWrapper<GeneralSectionSlope> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type",type);
        queryWrapper.eq("project_id",projectId);
        return generalSectionSlopeMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<GeneralSectionSlope> generalSectionSlopeIPage = new Page<>();
        generalSectionSlopeIPage.setCurrent(current);
        generalSectionSlopeIPage.setSize(size);

        QueryWrapper<GeneralSectionSlope> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return generalSectionSlopeMapper.selectPage(generalSectionSlopeIPage, queryWrapper);
    }


}
