package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.supermap.common.entity.DictText;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class SpaceRoutVo
        implements Serializable {

    private String pkid;

    @ApiModelProperty(value = "对应“路由碰撞检查规则大类注册表”中的规则类型")
    private String ruleParent;

    @ApiModelProperty(value = "法律法规的名称，例如：《建设项目使用林地审核审批管理办法》")
    private String ruleName;

    @ApiModelProperty(value = "规则内容，例如：各类建设项目不得使用Ⅰ级保护林地。")
    private String ruleContent;

    @ApiModelProperty(value = "例如：通用、输油管道、输气管道")

    private List<DictionaryVo> ruleSuitDict;

    @DictText(keyColumn = "pkid", tableName = "wbs_dictionary", target = "ruleSuitName", textColumn = "dictionary_name", commaSeparate = true)
    private String ruleSuit;

    private String ruleSuitName;

    @ApiModelProperty(value = "法律法规的实施时间")
    private String implementationTime;

    @ApiModelProperty(value = "指规用此规则检测到异常之后的提示类型，如：错误提示、告警提示、一般提示")
    @DictText(keyColumn = "pkid", tableName = "wbs_dictionary", target = "promptTypeName", textColumn = "dictionary_name")
    private String promptType;

    private String promptTypeName;

    @ApiModelProperty(value = "启用状态")
    private Boolean useStatus;

    @ApiModelProperty(value = "更新用户id")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    @ApiModelProperty(value = "更新时间")
    private String projectId;

    private List<DatasetBean> dataset;

    public List<DatasetBean> getDataset() {
        return dataset;
    }

    public void setDataset(List<DatasetBean> dataset) {
        this.dataset = dataset;
    }

    @ApiModelProperty(value ="是否添加实体(0不添加1添加到高后果2添加到敏感区)")
    private Integer isAddEntitys;


}
