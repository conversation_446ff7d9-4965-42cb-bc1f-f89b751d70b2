package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LinesAngle对象", description="")
public class LinesAngleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "线数据集名称")
    private String datesetname;

    @ApiModelProperty(value = "线数据集名称")
    private String datesetid;

    @ApiModelProperty(value = "最小角度")
    private Integer anglemin;

    @ApiModelProperty(value = "最大角度")
    private Integer anglemax;

    @ApiModelProperty(value = "关联项目id")
    private String projectid;

    @ApiModelProperty(value = "元数据库中规则和算法关联表的PKID")
    private String rulealgoritid;

    @ApiModelProperty(value = "设置位置")
    private String position;

    @ApiModelProperty(value = "规则类型")
    private String rulealgorittype;



}
