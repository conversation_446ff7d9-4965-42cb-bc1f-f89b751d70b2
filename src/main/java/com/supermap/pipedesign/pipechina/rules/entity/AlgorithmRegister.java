package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * $算法注册表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_algorithm_register")
@ApiModel(value="AlgorithmRegister对象", description="$算法注册表")
public class AlgorithmRegister implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "算法名称")
    @TableField("algorithm_name")
    private String algorithmname;

    @ApiModelProperty(value = "各种算法的算法类型，如管线与管线相交算法、沿管线中心线创建等")
    @TableField("algorithm_type")
    private String algorithmtype;

    @ApiModelProperty(value = "是否启用 0 启用 1 禁用")
    @TableField("enabled")
    private Boolean enabled;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "分组 auto_reate自动创建，route_collision路由碰撞，engineering_unit工程量统计")
    @TableField("algorithm_group")
    private String algorithmgroup;

    @ApiModelProperty(value = "点线面 123")
    @TableField("show_types")
    private String showtypes;


}
