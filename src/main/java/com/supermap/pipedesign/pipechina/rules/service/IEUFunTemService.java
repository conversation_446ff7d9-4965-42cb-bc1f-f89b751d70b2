package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.EUFunTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 算法模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Repository
public interface IEUFunTemService extends IService<EUFunTem> {

 /**
 * 添加算法模板信息
 *
 * @param eUFunTem
 * @return int
 * @Date 2023-03-09
 * @auther eomer
 */
 int insert(EUFunTem eUFunTem);

 /**
 * 删除算法模板信息
 *
 * @param eUFunTemId
 * @return int
 * @Date 2023-03-09
 * @auther eomer
 */
 int delete(String eUFunTemId);

 /**
 * 更新算法模板信息
 *
 * @param eUFunTem
 * @return int
 * @Date 2023-03-09
 * @auther eomer
 */
 int update(EUFunTem eUFunTem);

 /**
 * 全部查询
 *
 * @param eUFunTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.EUFunTem>
 * @Date 2023-03-09
 * @auther eomer
 */
 List<EUFunTem> list(EUFunTem eUFunTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-09
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
