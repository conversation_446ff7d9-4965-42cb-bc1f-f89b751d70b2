package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EntitytypeMergeVo
        implements Serializable {

    private String pkid;

    @ApiModelProperty(value = "规则名称")
    private String rulename;

    @ApiModelProperty(value = "间距")
    private String spacing;

    private String updateuser;

    private String updatetime;

    private String mergeEntitypes;

    private String projectId;

    @ApiModelProperty(value = "合并后最大剩余数")
    private Integer largeremain;


    private List<EntityTypesIds> entityTypesIds;


    @Data
    public class EntityTypesIds{

        @ApiModelProperty(value = "实体id")
        private String entityTypesId;

        @ApiModelProperty(value = "实体名称")
        private String entityTypesName;

        @ApiModelProperty(value = "实体别名")
        private String entityTypesAlias;

        @ApiModelProperty(value = "优先级")
        private Integer priority;

    }

}
