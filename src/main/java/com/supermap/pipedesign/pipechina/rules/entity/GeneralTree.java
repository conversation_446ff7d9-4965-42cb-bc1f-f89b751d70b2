package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 通用图目录树 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_general_tree")
@ApiModel(value="GeneralTree对象", description="通用图目录树")
public class GeneralTree implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "父id")
    @TableField("pid")
    private String pid;

    @ApiModelProperty(value = "目录名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;


}
