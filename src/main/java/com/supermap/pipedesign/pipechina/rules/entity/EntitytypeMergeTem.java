package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 实体合并 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_entitytype_merge_tem")
@ApiModel(value="EntitytypeMergeTem对象", description="实体合并")
public class EntitytypeMergeTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @TableField("rule_name")
    private String rulename;

    @TableField("spacing")
    private Double spacing;

    @TableField("update_user")
    private String updateuser;

    @TableField("update_time")
    private Timestamp updatetime;

    @ApiModelProperty(value = "合并后最大剩余数")
    @TableField("largere_main")
    private Integer largeremain;


}
