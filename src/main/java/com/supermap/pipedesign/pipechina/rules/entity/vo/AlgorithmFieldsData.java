package com.supermap.pipedesign.pipechina.rules.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AlgorithmFieldsData {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "适用图层")
    private String code;

    @ApiModelProperty(value = "缓冲半径")
    private String dataType;

    @ApiModelProperty(value = "算法类型")
    private String inputType;

    @ApiModelProperty(value = "描述")
    private String description;

    public List<AlgorithmFieldsData> children;

}

