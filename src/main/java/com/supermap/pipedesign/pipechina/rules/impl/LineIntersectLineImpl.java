package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.LineIntersectLine;
import com.supermap.pipedesign.pipechina.rules.dao.LineIntersectLineMapper;
import com.supermap.pipedesign.pipechina.rules.service.ILineIntersectLineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("LineIntersectLineImpl")
public class LineIntersectLineImpl extends ServiceImpl<LineIntersectLineMapper, LineIntersectLine> implements ILineIntersectLineService {

    @Autowired
    private LineIntersectLineMapper lineIntersectLineMapper;

    /**
    * 添加信息
    *
    * @param lineIntersectLine
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(LineIntersectLine lineIntersectLine) {

        //lineIntersectLine.setUserId(JavaUtils.getUUID36());
        //lineIntersectLine.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return lineIntersectLineMapper.insert(lineIntersectLine);
    }

    /**
    * 删除信息
    *
    * @param lineIntersectLineId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String lineIntersectLineId) {
        return lineIntersectLineMapper.deleteById(lineIntersectLineId);
    }

    /**
    * 更新信息
    *
    * @param lineIntersectLine
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(LineIntersectLine lineIntersectLine) {
        return lineIntersectLineMapper.updateById(lineIntersectLine);
    }

    /**
    * 全部查询
    *
    * @param lineIntersectLine
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.LineIntersectLine>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<LineIntersectLine> list(LineIntersectLine lineIntersectLine) {

        QueryWrapper<LineIntersectLine> queryWrapper = new QueryWrapper<>();

        return lineIntersectLineMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<LineIntersectLine> lineIntersectLineIPage = new Page<>();
        lineIntersectLineIPage.setCurrent(current);
        lineIntersectLineIPage.setSize(size);

        QueryWrapper<LineIntersectLine> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return lineIntersectLineMapper.selectPage(lineIntersectLineIPage, queryWrapper);
    }


}
