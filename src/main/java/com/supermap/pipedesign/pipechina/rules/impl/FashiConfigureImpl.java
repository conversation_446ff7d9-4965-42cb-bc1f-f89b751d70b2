package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.dao.FashiConfigureTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.FashiConfigure;
import com.supermap.pipedesign.pipechina.rules.dao.FashiConfigureMapper;
import com.supermap.pipedesign.pipechina.rules.entity.FashiConfigureTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.FashiConfigureProjectVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.FashiConfigureVo;
import com.supermap.pipedesign.pipechina.rules.service.IFashiConfigureService;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Service("FashiConfigureImpl")
public class FashiConfigureImpl extends ServiceImpl<FashiConfigureMapper, FashiConfigure> implements IFashiConfigureService {

    @Autowired
    private FashiConfigureMapper fashiConfigureMapper;

    @Autowired
    private FashiConfigureTemMapper fashiConfigureTemMapper;

    /**
    * 添加信息
    *
    * @param fashiConfigure
    * @return int
    * @Date 2023-02-07
    * @auther eomer
    */
    @Override
    public int insert(FashiConfigure fashiConfigure) {

        //fashiConfigure.setUserId(JavaUtils.getUUID36());
        //fashiConfigure.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return fashiConfigureMapper.insert(fashiConfigure);
    }

    /**
    * 删除信息
    *
    * @param fashiConfigureId
    * @return int
    * @Date 2023-02-07
    * @auther eomer
    */
    @Override
    public int delete(String fashiConfigureId) {
        return fashiConfigureMapper.deleteById(fashiConfigureId);
    }

    /**
    * 更新信息
    *
    * @param fashiConfigure
    * @return int
    * @Date 2023-02-07
    * @auther eomer
    */
    @Override
    public int update(FashiConfigure fashiConfigure) {
        return fashiConfigureMapper.updateById(fashiConfigure);
    }

    /**
    * 全部查询
    *
    * @param fashiConfigure
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.FashiConfigure>
    * @Date 2023-02-07
    * @auther eomer
    */
    @Override
    public List<FashiConfigureTem> list(FashiConfigureTem fashiConfigure) {
        QueryWrapper<FashiConfigureTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type_val",fashiConfigure.getTypeval());
        queryWrapper.orderByAsc("levelval");
        return fashiConfigureTemMapper.selectList(queryWrapper);
    }



    /**
     * 全部查询
     *
     * @param fashiConfigure
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.FashiConfigure>
     * @Date 2023-02-07
     * @auther eomer
     */
    @Override
    public List<FashiConfigure> listPro(FashiConfigure fashiConfigure) {
        QueryWrapper<FashiConfigure> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type_val",fashiConfigure.getTypeVal());
        queryWrapper.eq("project_id",fashiConfigure.getProjectId());
        queryWrapper.orderByAsc("levelval");
        return fashiConfigureMapper.selectList(queryWrapper);
    }



    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-07
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<FashiConfigure> fashiConfigureIPage = new Page<>();
        fashiConfigureIPage.setCurrent(current);
        fashiConfigureIPage.setSize(size);

        QueryWrapper<FashiConfigure> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }
        queryWrapper.orderByAsc("levelval");
        return fashiConfigureMapper.selectPage(fashiConfigureIPage, queryWrapper);
    }

    /**
     * 阀室设置规则更新
     * @param fashiConfigureVo
     * @return
     */
    @Override
    public int updatefashi(FashiConfigureVo fashiConfigureVo) {
        int result = 0 ;
        List<FashiConfigure> fashiConfigureList = fashiConfigureVo.getFashiConfigureList();
        for (int i = 0; i < fashiConfigureList.size(); i++) {
            FashiConfigure fashiConfigure = fashiConfigureList.get(i);
            FashiConfigureTem tem = GsonUtil.ObjectToEntity(fashiConfigure,FashiConfigureTem.class);
            result = fashiConfigureTemMapper.updateById(tem);
        }
        return result;
    }

    /**
     * 阀室设置规则更新(项目)
     * @param fashiConfigureProjectVo
     * @return
     */
    @Override
    public int updatefashiProject(FashiConfigureProjectVo fashiConfigureProjectVo,String projectId) {
        int result = 0 ;
        List<FashiConfigureTem> fashiConfigureProList = fashiConfigureProjectVo.getFashiConfigureList();
        for (int i = 0; i < fashiConfigureProList.size(); i++) {
            FashiConfigureTem tem = fashiConfigureProList.get(i);
            FashiConfigure fashi = GsonUtil.ObjectToEntity(tem, FashiConfigure.class);
            fashi.setProjectId(projectId);
            QueryWrapper<FashiConfigure> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("pkid",fashi.getPkid());
            queryWrapper.eq("project_id",fashi.getProjectId());
            result = fashiConfigureMapper.update(fashi,queryWrapper);
        }
        return result;
    }

}
