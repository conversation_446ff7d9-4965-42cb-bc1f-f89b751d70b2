package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.rules.dao.LongitudinalLineTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.LongitudinalLineTem;
import com.supermap.pipedesign.pipechina.rules.service.ILongitudinalLineTemService;
import com.supermap.tools.base.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;


/**
 * <p>
 * 纵管线生成规则-模型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Service("LongitudinalLineTemImpl")
public class LongitudinalLineTemImpl extends ServiceImpl<LongitudinalLineTemMapper, LongitudinalLineTem> implements ILongitudinalLineTemService {

    @Autowired
    private LongitudinalLineTemMapper longitudinalLineTemMapper;

    /**
     * 查询纵管线
     * <AUTHOR>
     * @Description
     * @Date 2023/3/16 10:49
     **/
    @Override
    public LongitudinalLineTem getInfo() {
        QueryWrapper<LongitudinalLineTem> queryWrapper = new QueryWrapper<>();

        return longitudinalLineTemMapper.selectOne(queryWrapper);
    }

    /**
     * 更新纵管线
     * <AUTHOR>
     * @Description
     * @Date 2023/3/16 10:52
     **/
    @Override
    public int updateLongitudinalLineTem(LongitudinalLineTem longitudinalLineTem) {
        if (JavaUtils.isEmtryOrNull(longitudinalLineTem.getPkid())){
            longitudinalLineTem.setPkid(UUID.randomUUID().toString());
            return longitudinalLineTemMapper.insert(longitudinalLineTem);
        }
        return longitudinalLineTemMapper.updateById(longitudinalLineTem);
    }
}
