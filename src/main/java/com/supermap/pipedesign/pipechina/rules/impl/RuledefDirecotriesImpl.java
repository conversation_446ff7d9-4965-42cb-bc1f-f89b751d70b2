package com.supermap.pipedesign.pipechina.rules.impl;

import com.supermap.pipedesign.pipechina.rules.dao.RuledefDirecotriesTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.RuledefDirecotries;
import com.supermap.pipedesign.pipechina.rules.dao.RuledefDirecotriesMapper;
import com.supermap.pipedesign.pipechina.rules.entity.RuledefDirecotriesTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.RuledefDirecotriesVo;
import com.supermap.pipedesign.pipechina.rules.service.IRuledefDirecotriesService;
import com.supermap.tools.base.PinYinUtils;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.UUID;


/**
 * <p>
 * 规则目录表(定义规则上下级目录) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
@Service("RuledefDirecotriesImpl")
public class RuledefDirecotriesImpl extends ServiceImpl<RuledefDirecotriesMapper, RuledefDirecotries> implements IRuledefDirecotriesService {

    @Autowired
    private RuledefDirecotriesMapper ruledefDirecotriesMapper;

    @Autowired
    private RuledefDirecotriesTemMapper ruledefDirecotriesTemMapper;


    @Override
    public List<RuledefDirecotriesVo> selRulesTreeTemList(String pkid){
        return  ruledefDirecotriesTemMapper.selRulesTreeList(pkid);
    }

    @Override
    public List<RuledefDirecotriesVo> selRulesTreeList(String projectid){
        String pkid = "a24f7b04-5087-4e87-bc50-74be114da076";
        return  ruledefDirecotriesMapper.selRulesTreeList(pkid,projectid);
    }

    @Override
    public int updateRulesTree( RuledefDirecotriesVo ruledefDirecotriesVo){
        RuledefDirecotriesTem ruledefDirecotries
                = GsonUtil.ObjectToEntity(ruledefDirecotriesVo,RuledefDirecotriesTem.class);
        ruledefDirecotries.setPkid(UUID.randomUUID().toString());
        RuledefDirecotries theLastRule
                = ruledefDirecotriesTemMapper.selTheLastRule( ruledefDirecotriesVo.getPid() );
        int sortOf = 1 ;
        if( theLastRule != null){
            sortOf = theLastRule.getSort()+1;
        }
        String nameToPinYin= PinYinUtils.CnToPinYin(ruledefDirecotriesVo.getDirectoryname());
        ruledefDirecotries.setDirectoryalias( nameToPinYin );
        ruledefDirecotries.setSort( sortOf );
        return ruledefDirecotriesTemMapper.insert( ruledefDirecotries );
    }

    @Override
    public int updateProRulesTree( RuledefDirecotriesVo ruledefDirecotriesVo){
        RuledefDirecotries ruledefDirecotries
                = GsonUtil.ObjectToEntity(ruledefDirecotriesVo,RuledefDirecotries.class);
        ruledefDirecotries.setPkid(UUID.randomUUID().toString());
        RuledefDirecotries theLastRule = ruledefDirecotriesMapper.selTheLastRule( ruledefDirecotriesVo.getPid() );
        int sortOf = 1 ;
        if( theLastRule != null){
            sortOf = theLastRule.getSort()+1;
        }
        String nameToPinYin= PinYinUtils.CnToPinYin(ruledefDirecotriesVo.getDirectoryname());
        ruledefDirecotries.setDirectoryalias( nameToPinYin );
        ruledefDirecotries.setSort( sortOf );
        return ruledefDirecotriesMapper.insert( ruledefDirecotries );
    }


    @Override
    public int updateRulesName( RuledefDirecotriesVo ruledefDirecotriesVo){
        RuledefDirecotriesTem ruledefDirecotries = ruledefDirecotriesTemMapper.selectById( ruledefDirecotriesVo.getPkid() );
        ruledefDirecotries.setDirectoryname( ruledefDirecotriesVo.getDirectoryname() );
        return  ruledefDirecotriesTemMapper.updateById( ruledefDirecotries );
    }


    @Override
    public int updateProRulesName( RuledefDirecotriesVo ruledefDirecotriesVo){
        RuledefDirecotries ruledefDirecotries = ruledefDirecotriesMapper.selectById( ruledefDirecotriesVo.getPkid() );
        ruledefDirecotries.setDirectoryname( ruledefDirecotriesVo.getDirectoryname() );
        return  ruledefDirecotriesMapper.updateById( ruledefDirecotries );
    }

}
