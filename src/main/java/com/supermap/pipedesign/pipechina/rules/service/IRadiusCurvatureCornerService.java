package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.RadiusCurvatureCorner;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 曲率半径与转角界限规则-项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Repository
public interface IRadiusCurvatureCornerService extends IService<RadiusCurvatureCorner> {

 /**
  * 查询曲率半径与转角界限规则
  * <AUTHOR>
  * @Description
  * @Date 2023/3/16 11:34
  **/
 List<RadiusCurvatureCorner> selInfoRadius(String projectId);

 /**
  * 更新曲率半径与转角界限规则
  * <AUTHOR>
  * @Description
  * @Date 2023/3/16 11:56
  **/
 int updateRadius(Map<String,List<RadiusCurvatureCorner>> map);

 /**
  * 删除
  * <AUTHOR>
  * @Description
  * @Date 2023/3/16 18:39
  **/
 int deletRadius(String radiusId,String projectId);


 }
