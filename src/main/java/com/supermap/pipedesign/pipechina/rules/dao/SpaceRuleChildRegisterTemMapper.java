package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.supermap.pipedesign.pipechina.rules.entity.vo.SpaceRoutVo;
import com.supermap.pipedesign.pipechina.rules.entity.SpaceRuleChildRegisterTem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Mapper
public interface SpaceRuleChildRegisterTemMapper extends BaseMapper<SpaceRuleChildRegisterTem> {

    @Select("select pkid,rule_name as ruleName,implementation_time as implementationTime,update_user as updateUser,update_time as updateTime,rule_suit as ruleSuit " +
            ",prompt_type as promptType,rule_content as ruleContent,use_status as useStatus,is_add_entitys as isAddEntitys  from qi_space_rule_child_register_tem ${ew.customSqlSegment}" )
    IPage<SpaceRoutVo> selSpaceRoutList(IPage<SpaceRoutVo> page, @Param(Constants.WRAPPER) Wrapper<SpaceRoutVo> queryWrapper);

        @Select("select pkid,rule_parent as ruleParent, rule_name as ruleName,rule_content as ruleContent, rule_suit as ruleSuit,  " +
                "implementation_time as implementationTime, prompt_type as promptType, use_status as useStatus, " +
                "update_user as updateUser, update_time as updateTime " +
            ",prompt_type as promptType from qi_space_rule_child_register_tem where rule_parent=#{ruleParent}" )
    List<SpaceRoutVo> selSpaceRoutLeftList(String ruleParent);

}
