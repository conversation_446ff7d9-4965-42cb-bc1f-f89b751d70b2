package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.NoTrafficAlgorithm;
import com.supermap.pipedesign.pipechina.rules.dao.NoTrafficAlgorithmMapper;
import com.supermap.pipedesign.pipechina.rules.service.INoTrafficAlgorithmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 空间规则算法表，定义了算法关联的图层和运算规则与范围 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@Service("NoTrafficAlgorithmImpl")
public class NoTrafficAlgorithmImpl extends ServiceImpl<NoTrafficAlgorithmMapper, NoTrafficAlgorithm> implements INoTrafficAlgorithmService {

    @Autowired
    private NoTrafficAlgorithmMapper noTrafficAlgorithmMapper;

    /**
    * 添加空间规则算法表，定义了算法关联的图层和运算规则与范围信息
    *
    * @param noTrafficAlgorithm
    * @return int
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public int insert(NoTrafficAlgorithm noTrafficAlgorithm) {

        //noTrafficAlgorithm.setUserId(JavaUtils.getUUID36());
        //noTrafficAlgorithm.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return noTrafficAlgorithmMapper.insert(noTrafficAlgorithm);
    }

    /**
    * 删除空间规则算法表，定义了算法关联的图层和运算规则与范围信息
    *
    * @param noTrafficAlgorithmId
    * @return int
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public int delete(String noTrafficAlgorithmId) {
        return noTrafficAlgorithmMapper.deleteById(noTrafficAlgorithmId);
    }

    /**
    * 更新空间规则算法表，定义了算法关联的图层和运算规则与范围信息
    *
    * @param noTrafficAlgorithm
    * @return int
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public int update(NoTrafficAlgorithm noTrafficAlgorithm) {
        return noTrafficAlgorithmMapper.updateById(noTrafficAlgorithm);
    }

    /**
    * 全部查询
    *
    * @param noTrafficAlgorithm
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.NoTrafficAlgorithm>
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public List<NoTrafficAlgorithm> list(NoTrafficAlgorithm noTrafficAlgorithm) {

        QueryWrapper<NoTrafficAlgorithm> queryWrapper = new QueryWrapper<>();

        return noTrafficAlgorithmMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<NoTrafficAlgorithm> noTrafficAlgorithmIPage = new Page<>();
        noTrafficAlgorithmIPage.setCurrent(current);
        noTrafficAlgorithmIPage.setSize(size);

        QueryWrapper<NoTrafficAlgorithm> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return noTrafficAlgorithmMapper.selectPage(noTrafficAlgorithmIPage, queryWrapper);
    }


}
