package com.supermap.pipedesign.pipechina.rules.entity.dto;

import com.supermap.common.entity.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QuantitiesDto extends BaseDto {

    @ApiModelProperty(value = "专业id")
    private String subjectId;

    @ApiModelProperty(value = "阶段（预可研0、可研1、初设2、施工3、竣工4）")
    private String stageCode;

    @ApiModelProperty(value = "规则名称")
    private String rulesName;

    @ApiModelProperty(value = "实体名称")
    private String entityName;

    @ApiModelProperty(value = "项目id")
    private String projectId;
}
