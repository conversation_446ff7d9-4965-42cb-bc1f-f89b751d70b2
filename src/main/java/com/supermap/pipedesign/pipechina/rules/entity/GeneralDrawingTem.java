package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 通用图 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_general_drawing_tem")
@ApiModel(value="GeneralDrawingTem对象", description="通用图")
public class GeneralDrawingTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "通用图名称")
    @TableField("gd_name")
    private String gdName;

    @ApiModelProperty(value = "通用图描述")
    @TableField("gd_describe")
    private String gdDescribe;

    @ApiModelProperty(value = "更新人")
    @TableField("update_user")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Timestamp updateTime;

    @ApiModelProperty(value = "目录id")
    @TableField("pid")
    private String pid;

    @ApiModelProperty(value = "路径")
    @TableField("path_url")
    private String pathUrl;

    @ApiModelProperty(value = "文件名")
    @TableField("file_name")
    private String fileName;


}
