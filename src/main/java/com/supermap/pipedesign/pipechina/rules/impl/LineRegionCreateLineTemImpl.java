package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.LineRegionCreateLineTem;
import com.supermap.pipedesign.pipechina.rules.dao.LineRegionCreateLineTemMapper;
import com.supermap.pipedesign.pipechina.rules.service.ILineRegionCreateLineTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 基于中线与面图层交叉线创建线实体 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Service("LineRegionCreateLineTemImpl")
public class LineRegionCreateLineTemImpl extends ServiceImpl<LineRegionCreateLineTemMapper, LineRegionCreateLineTem> implements ILineRegionCreateLineTemService {

    @Autowired
    private LineRegionCreateLineTemMapper lineRegionCreateLineTemMapper;

    /**
    * 添加基于中线与面图层交叉线创建线实体信息
    *
    * @param lineRegionCreateLineTem
    * @return int
    * @Date 2023-02-22
    * @auther eomer
    */
    @Override
    public int insert(LineRegionCreateLineTem lineRegionCreateLineTem) {

        //lineRegionCreateLineTem.setUserId(JavaUtils.getUUID36());
        //lineRegionCreateLineTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return lineRegionCreateLineTemMapper.insert(lineRegionCreateLineTem);
    }

    /**
    * 删除基于中线与面图层交叉线创建线实体信息
    *
    * @param lineRegionCreateLineTemId
    * @return int
    * @Date 2023-02-22
    * @auther eomer
    */
    @Override
    public int delete(String lineRegionCreateLineTemId) {
        return lineRegionCreateLineTemMapper.deleteById(lineRegionCreateLineTemId);
    }

    /**
    * 更新基于中线与面图层交叉线创建线实体信息
    *
    * @param lineRegionCreateLineTem
    * @return int
    * @Date 2023-02-22
    * @auther eomer
    */
    @Override
    public int update(LineRegionCreateLineTem lineRegionCreateLineTem) {
        return lineRegionCreateLineTemMapper.updateById(lineRegionCreateLineTem);
    }

    /**
    * 全部查询
    *
    * @param lineRegionCreateLineTem
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.LineRegionCreateLineTem>
    * @Date 2023-02-22
    * @auther eomer
    */
    @Override
    public List<LineRegionCreateLineTem> list(LineRegionCreateLineTem lineRegionCreateLineTem) {

        QueryWrapper<LineRegionCreateLineTem> queryWrapper = new QueryWrapper<>();

        return lineRegionCreateLineTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-22
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<LineRegionCreateLineTem> lineRegionCreateLineTemIPage = new Page<>();
        lineRegionCreateLineTemIPage.setCurrent(current);
        lineRegionCreateLineTemIPage.setSize(size);

        QueryWrapper<LineRegionCreateLineTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return lineRegionCreateLineTemMapper.selectPage(lineRegionCreateLineTemIPage, queryWrapper);
    }


}
