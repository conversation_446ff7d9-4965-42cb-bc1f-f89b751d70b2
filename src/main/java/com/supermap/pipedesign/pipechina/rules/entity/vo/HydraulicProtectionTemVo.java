package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 水工保护总量表-模型 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="HydraulicProtectionTem对象", description="水工保护总量表-模型")
public class HydraulicProtectionTemVo {

    List<Map<String, Object>> hydraulicProtectionTemVo;

}
