package com.supermap.pipedesign.pipechina.rules.dao;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.supermap.pipedesign.pipechina.rules.entity.SpaceRuleChildRegister;
import com.supermap.pipedesign.pipechina.rules.entity.vo.SpaceRoutVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 空间规则算法表，定义了算法关联的图层和运算规则与范围 Mapper 接口
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@Mapper
public interface SpaceRuleChildRegisterMapper extends BaseMapper<SpaceRuleChildRegister> {



    @Select("select pkid,rule_name as ruleName,implementation_time as implementationTime,update_user as updateUser,update_time as updateTime,rule_suit as ruleSuit " +
            ",prompt_type as promptType,rule_content as ruleContent,use_status as useStatus, is_add_entitys as isAddEntitys from qi_space_rule_child_register ${ew.customSqlSegment}" )
    IPage<SpaceRoutVo> selSpaceRoutList(IPage<SpaceRoutVo> page, @Param(Constants.WRAPPER) Wrapper<SpaceRoutVo> queryWrapper);


    @Select(" select pkid,rule_name from qi_space_rule_child_register where rule_parent=#{ruleParent} and project_id = #{projectId}")
    List<SpaceRoutVo> selByRuleParentId(@Param(value = "ruleParent") String ruleParent, @Param(value = "projectId") String projectId);

    @Select("select pkid,rule_parent as ruleParent, rule_name as ruleName,rule_content as ruleContent, rule_suit as ruleSuit,  " +
            "implementation_time as implementationTime, prompt_type as promptType, use_status as useStatus, " +
            "update_user as updateUser, update_time as updateTime " +
            ",prompt_type as promptType from qi_space_rule_child_register_tem where rule_parent=#{ruleParent}" )
    List<SpaceRoutVo> selSpaceRoutLeftList(String ruleParent);

}
