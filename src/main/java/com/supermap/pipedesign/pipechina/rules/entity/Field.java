package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 质检字段表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_field")
@ApiModel(value="Field对象", description="质检字段表")
public class Field implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "字段名称")
    @TableField("field_name")
    private String fieldName;

    @ApiModelProperty(value = "字段别名")
    @TableField("field_alias")
    private String fieldAlias;

    @ApiModelProperty(value = "是否必须(是否必须存在)")
    @TableField("is_required")
    private BigDecimal isRequired;

    @ApiModelProperty(value = "取值范围(内容之间用英文逗号分隔，如果内容不为空，必须从其中选择一个当作值内容)")
    @TableField("field_range")
    private String fieldRange;

    @ApiModelProperty(value = "长度")
    @TableField("field_length")
    private BigDecimal fieldLength;

    @ApiModelProperty(value = "类型")
    @TableField("field_type")
    private String fieldType;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "质检规则id")
    @TableField("rule_id")
    private String ruleId;

    @ApiModelProperty(value = "小数点前位数")
    @TableField("figures_before")
    private Integer figuresBefore;

    @ApiModelProperty(value = "小数点后位数")
    @TableField("figures_after")
    private Integer figuresAfter;

    @ApiModelProperty(value = "排序(desc,acs)")
    @TableField("orders")
    private String orders;

    @ApiModelProperty(value = "精度")
    @TableField("precision")
    private Integer precision;

    @ApiModelProperty(value = "是否可为负数")
    @TableField("is_negative")
    private Boolean isNegative;

    @ApiModelProperty(value = "最小值")
    @TableField("min_values")
    private String minValues;

    @ApiModelProperty(value = "最大值")
    @TableField("max_values")
    private String maxValues;

    @ApiModelProperty(value = "数据格式(例如：XX°.XX′XX″	内容：°,′,″	说明：字段内容必须包含这三个字符，并且顺序必须是从前往后)")
    @TableField("data_format")
    private String dataFormat;

    @ApiModelProperty(value = "是否检查桩号")
    @TableField("is_check_station")
    private Boolean isCheckStation;

    @ApiModelProperty(value = "是否为定位字段")
    @TableField("is_location_field")
    private Boolean isLocationField;


}
