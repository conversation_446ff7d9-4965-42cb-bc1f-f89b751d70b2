package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 空间规则算法表，定义了算法关联的图层和运算规则与范围 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_space_rule_child_register_tem")
@ApiModel(value="SpaceRuleChildRegisterTem对象", description="空间规则算法表，定义了算法关联的图层和运算规则与范围")
public class SpaceRuleChildRegisterTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "对应“路由碰撞检查规则大类注册表”中的规则类型")
    @TableField("rule_parent")
    private String ruleParent;

    @ApiModelProperty(value = "法律法规的名称，例如：《建设项目使用林地审核审批管理办法》")
    @TableField("rule_name")
    private String ruleName;

    @ApiModelProperty(value = "规则内容，例如：各类建设项目不得使用Ⅰ级保护林地。")
    @TableField("rule_content")
    private String ruleContent;

    @ApiModelProperty(value = "例如：通用、输油管道、输气管道")
    @TableField("rule_suit")
    private String ruleSuit;

    @ApiModelProperty(value = "法律法规的实施时间")
    @TableField("implementation_time")
    private String implementationTime;

    @ApiModelProperty(value = "指规用此规则检测到异常之后的提示类型，如：错误提示、告警提示、一般提示")
    @TableField("prompt_type")
    private String promptType;

    @TableField("use_status")
    private Boolean useStatus;

    @TableField("update_user")
    private String updateUser;

    @TableField("update_user_id")
    private String updateUserId;

    @TableField("update_time")
    private Timestamp updateTime;

    @ApiModelProperty(value ="是否添加实体(0不添加1添加到高后果2添加到敏感区)")
    @TableField("is_add_entitys")
    private Integer isAddEntitys;


}
