package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.rules.entity.ElbowBendLineSectionTem;
import com.supermap.pipedesign.pipechina.rules.entity.HydraulicProtectionTem;
import com.supermap.pipedesign.pipechina.rules.dao.HydraulicProtectionTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.vo.HydraulicProtectionTemVo;
import com.supermap.pipedesign.pipechina.rules.service.IHydraulicProtectionTemService;
import com.supermap.tools.gson.GsonUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 水工保护总量表-模型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Service("HydraulicProtectionTemImpl")
@RequiredArgsConstructor
public class HydraulicProtectionTemImpl extends ServiceImpl<HydraulicProtectionTemMapper, HydraulicProtectionTem> implements IHydraulicProtectionTemService {

    private final HydraulicProtectionTemMapper hydraulicProtectionTemMapper;

    /**
     * 添加水工保护总量表-模型信息
     *
     * @param hydraulicProtectionTem
     * @return int
     * @Date 2023-03-21
     * @auther eomer
     */
    @Override
    public int insert(HydraulicProtectionTem hydraulicProtectionTem) {

        // hydraulicProtectionTem.setUserId(JavaUtils.getUUID36());
        // hydraulicProtectionTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return hydraulicProtectionTemMapper.insert(hydraulicProtectionTem);
    }

    /**
     * 删除水工保护总量表-模型信息
     *
     * @param hydraulicProtectionTemId
     * @return int
     * @Date 2023-03-21
     * @auther eomer
     */
    @Override
    public int delete(String hydraulicProtectionTemId) {
        return hydraulicProtectionTemMapper.deleteById(hydraulicProtectionTemId);
    }

    /**
     * 更新水工保护总量表-模型信息
     *
     * @param hydraulicProtectionTem
     * @return int
     * @Date 2023-03-21
     * @auther eomer
     */
    @Override
    public int update(@Valid HydraulicProtectionTemVo hydraulicProtectionTem) {

        hydraulicProtectionTemMapper.delete(new QueryWrapper<>());

        List<Map<String, Object>> hydraulicProtectionTemVos = hydraulicProtectionTem.getHydraulicProtectionTemVo();

        for (Map<String, Object> map : hydraulicProtectionTemVos) {
            List<Map> list = GsonUtil.ObjectToList(map.get("list1"), Map.class);
            List<Map> list2 = GsonUtil.ObjectToList(map.get("list2"), Map.class);
            List<Map> list3 = GsonUtil.ObjectToList(map.get("list3"), Map.class);
            extracted(list);
            extracted(list2);
            extracted(list3);
        }


        return 1;
    }

    private void extracted(List<Map> list) {
        for (Map<String, Object> mapTem : list) {
            int row = (int) Math.round((Double) mapTem.get("row"));
            Integer filedIndex = (int) Math.round((Double) mapTem.get("filed_index"));
            mapTem.remove("row");
            mapTem.remove("filed_index");
            for (Map.Entry<String, Object> key : mapTem.entrySet()) {
                HydraulicProtectionTem tem = new HydraulicProtectionTem();
                tem.setBendCode(key.getKey());
                tem.setFieldValue(key.getValue().toString());
                tem.setRow(row);
                tem.setFiledIndex(filedIndex);
                hydraulicProtectionTemMapper.insert(tem);

            }
        }
    }

    /**
     * 全部查询
     *
     * @param hydraulicProtectionTem
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.HydraulicProtectionTem>
     * @Date 2023-03-21
     * @auther eomer
     */
    @Override
    public List<List<Map<String, Object>>> list(HydraulicProtectionTem hydraulicProtectionTem) {
        List<Integer> list = new ArrayList();

        List<List<Map<String, Object>>> objects1 = new ArrayList<>();
        list.add(1);
        list.add(2);
        list.add(3);
        for (Integer o : list) {
            List<HydraulicProtectionTem> elbowBendLineSectionTems = getHydraulicProtectionTems(o);
            List<List<HydraulicProtectionTem>> elbowBendLineSectionTemList = new ArrayList<>();
            // elbowBendLineSectionTemList.add(new ArrayList<>());
            // 行号分组循环
            int count = 0;
            int row = 0;
            Map<Integer, Integer> map = new HashMap<>();
            for (HydraulicProtectionTem lineSectionTem : elbowBendLineSectionTems) {
                if (lineSectionTem.getRow() == 1) {
                    row++;
                }
            }
            for (HydraulicProtectionTem bendLineSectionTem : elbowBendLineSectionTems) {


                Integer bendLineSection = map.get(bendLineSectionTem.getRow());
                if (bendLineSection == null) {
                    map.put(bendLineSectionTem.getRow(), count);
                    ArrayList<HydraulicProtectionTem> objects = new ArrayList<>();
                    objects.add(bendLineSectionTem);
                    elbowBendLineSectionTemList.add(objects);
                } else {
                    List<HydraulicProtectionTem> objects = elbowBendLineSectionTemList.get(count);
                    objects.add(bendLineSectionTem);

                }
                if (elbowBendLineSectionTemList.get(count).size() == row) {
                    count++;
                    // elbowBendLineSectionTemList.add(new ArrayList<>());
                }
            }
            List<Map<String, Object>> mapList = new ArrayList<>();
            for (List<HydraulicProtectionTem> sectionTemList : elbowBendLineSectionTemList) {
                Map<String, Object> data = new HashMap<>();

                for (HydraulicProtectionTem bendLineSectionTem : sectionTemList) {
                    data.put(bendLineSectionTem.getBendCode(), bendLineSectionTem.getFieldValue());

                }
                mapList.add(data);

            }
            objects1.add(mapList);
        }

        return objects1;
    }

    private List<HydraulicProtectionTem> getHydraulicProtectionTems(Integer o) {
        QueryWrapper<HydraulicProtectionTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("filed_index", o);
        queryWrapper.orderByAsc("row");
        List<HydraulicProtectionTem> elbowBendLineSectionTems = hydraulicProtectionTemMapper.selectList(queryWrapper);
        return elbowBendLineSectionTems;
    }


    private final DictionaryMapper dictionaryMapper;

    @Override
    public List titleList() {
        QueryWrapper<Dictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("sort");
        queryWrapper.eq("pid", "8f571f24-c6db-4613-9903-d038384c6e71");
        //水工保护总量表，材料形式表头
        List<Dictionary> dictionaries = dictionaryMapper.selectList(queryWrapper);
        //防护措施表头
        List<Dictionary> dictionariess = dictionaryMapper.selectList(queryWrapper);
        QueryWrapper<Dictionary> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.orderByAsc("sort");
        queryWrapper1.eq("pid", "60056301004001001");
        List<Dictionary> dictionariesss = dictionaryMapper.selectList(queryWrapper1);
        Dictionary dictionary = new Dictionary();
        dictionary.setDictionaryname("管径(mm)");
        dictionary.setDictionarycode("guanjing");

        Dictionary dictionary1 = new Dictionary();
        dictionary1.setDictionaryname("材料形式");
        dictionary1.setDictionarycode("cailiaoxingshi");
        dictionaries.add(0, dictionary);
        dictionariess.add(0, dictionary1);
        List list = new ArrayList();
        list.add(dictionaries);
        list.add(dictionariess);
        list.add(dictionariesss);
        return list;
    }

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @param startDate
     * @param endDate
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * @Date 2023-03-21
     * @auther eomer
     */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<HydraulicProtectionTem> hydraulicProtectionTemIPage = new Page<>();
        hydraulicProtectionTemIPage.setCurrent(current);
        hydraulicProtectionTemIPage.setSize(size);

        QueryWrapper<HydraulicProtectionTem> queryWrapper = new QueryWrapper<>();

        if (startDate != null && endDate != null) {
            queryWrapper.between("col_create_time", startDate, endDate);
        }

        return hydraulicProtectionTemMapper.selectPage(hydraulicProtectionTemIPage, queryWrapper);
    }


}
