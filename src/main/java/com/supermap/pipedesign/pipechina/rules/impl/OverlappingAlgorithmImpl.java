package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.OverlappingAlgorithm;
import com.supermap.pipedesign.pipechina.rules.dao.OverlappingAlgorithmMapper;
import com.supermap.pipedesign.pipechina.rules.service.IOverlappingAlgorithmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 管道与公路、铁路、已建管道交叉算法 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Service("OverlappingAlgorithmImpl")
public class OverlappingAlgorithmImpl extends ServiceImpl<OverlappingAlgorithmMapper, OverlappingAlgorithm> implements IOverlappingAlgorithmService {

    @Autowired
    private OverlappingAlgorithmMapper overlappingAlgorithmMapper;

    /**
    * 添加管道与公路、铁路、已建管道交叉算法信息
    *
    * @param overlappingAlgorithm
    * @return int
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public int insert(OverlappingAlgorithm overlappingAlgorithm) {

        //overlappingAlgorithm.setUserId(JavaUtils.getUUID36());
        //overlappingAlgorithm.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return overlappingAlgorithmMapper.insert(overlappingAlgorithm);
    }

    /**
    * 删除管道与公路、铁路、已建管道交叉算法信息
    *
    * @param overlappingAlgorithmId
    * @return int
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public int delete(String overlappingAlgorithmId) {
        return overlappingAlgorithmMapper.deleteById(overlappingAlgorithmId);
    }

    /**
    * 更新管道与公路、铁路、已建管道交叉算法信息
    *
    * @param overlappingAlgorithm
    * @return int
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public int update(OverlappingAlgorithm overlappingAlgorithm) {
        return overlappingAlgorithmMapper.updateById(overlappingAlgorithm);
    }

    /**
    * 全部查询
    *
    * @param overlappingAlgorithm
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.OverlappingAlgorithm>
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public List<OverlappingAlgorithm> list(OverlappingAlgorithm overlappingAlgorithm) {

        QueryWrapper<OverlappingAlgorithm> queryWrapper = new QueryWrapper<>();

        return overlappingAlgorithmMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<OverlappingAlgorithm> overlappingAlgorithmIPage = new Page<>();
        overlappingAlgorithmIPage.setCurrent(current);
        overlappingAlgorithmIPage.setSize(size);

        QueryWrapper<OverlappingAlgorithm> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return overlappingAlgorithmMapper.selectPage(overlappingAlgorithmIPage, queryWrapper);
    }


}
