package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.FashiCreateTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * $阀室设置规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Repository
public interface IFashiCreateTemService extends IService<FashiCreateTem> {

 /**
 * 添加$阀室设置规则信息
 *
 * @param fashiCreateTem
 * @return int
 * @Date 2023-03-10
 * @auther eomer
 */
 int insert(FashiCreateTem fashiCreateTem);

 /**
 * 删除$阀室设置规则信息
 *
 * @param fashiCreateTemId
 * @return int
 * @Date 2023-03-10
 * @auther eomer
 */
 int delete(String fashiCreateTemId);

 /**
 * 更新$阀室设置规则信息
 *
 * @param fashiCreateTem
 * @return int
 * @Date 2023-03-10
 * @auther eomer
 */
 int update(FashiCreateTem fashiCreateTem);

 /**
 * 全部查询
 *
 * @param fashiCreateTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.FashiCreateTem>
 * @Date 2023-03-10
 * @auther eomer
 */
 List<FashiCreateTem> list(FashiCreateTem fashiCreateTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-10
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
