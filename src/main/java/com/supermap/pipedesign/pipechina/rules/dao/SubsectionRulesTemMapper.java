package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.supermap.pipedesign.pipechina.rules.entity.SubsectionRulesTem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 分项统计规则模板 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@Mapper
public interface SubsectionRulesTemMapper extends BaseMapper<SubsectionRulesTem> {

    @Select("select t.* from qi_subsection_rules_tem t left join pld_md_entitytypes_tem e on t.entity_chile_id = e.pkid \n" +
            " ${ew.customSqlSegment}")
    IPage<SubsectionRulesTem> selByPage(IPage<SubsectionRulesTem> page , @Param(Constants.WRAPPER) Wrapper<SubsectionRulesTem> qqueryWrapper);

}
