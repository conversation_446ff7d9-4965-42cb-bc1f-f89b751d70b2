package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 分项统计规则模板字段表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_subsection_rules_field_tem")
@ApiModel(value="SubsectionRulesFieldTem对象", description="分项统计规则模板字段表")
public class SubsectionRulesFieldTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "分项模板id")
    @TableField("subsection_id")
    private String subsectionId;

    @ApiModelProperty(value = "字段名称")
    @TableField("field_name")
    private String fieldName;

    @ApiModelProperty(value = "字段值")
    @TableField("field_value")
    private String fieldValue;

    @ApiModelProperty(value = "前缀")
    @TableField("field_prefix")
    private String fieldPrefix;

    @ApiModelProperty(value = "算法函数")
    @TableField("field_function")
    private Integer fieldFunction;

    @ApiModelProperty(value = "实体属性")
    @TableField("field_entity_type_id")
    private String fieldEntityTypeId;

    @ApiModelProperty(value = "后缀")
    @TableField("field_suffix")
    private String fieldSuffix;

    @ApiModelProperty(value = "跟新时间")
    @TableField("updata_time")
    private Timestamp updataTime;

    @ApiModelProperty(value = "跟新时间")
    @TableField("pid")
    private String pid;

    @ApiModelProperty(value = "算法英文名称")
    @TableField("field_alias")
    private String fieldAlias;

}
