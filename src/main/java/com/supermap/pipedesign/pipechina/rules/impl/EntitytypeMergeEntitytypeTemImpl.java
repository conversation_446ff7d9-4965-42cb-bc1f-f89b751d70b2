package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeEntitytypeTem;
import com.supermap.pipedesign.pipechina.rules.dao.EntitytypeMergeEntitytypeTemMapper;
import com.supermap.pipedesign.pipechina.rules.service.IEntitytypeMergeEntitytypeTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service("EntitytypeMergeEntitytypeTemImpl")
public class EntitytypeMergeEntitytypeTemImpl extends ServiceImpl<EntitytypeMergeEntitytypeTemMapper, EntitytypeMergeEntitytypeTem> implements IEntitytypeMergeEntitytypeTemService {

    @Autowired
    private EntitytypeMergeEntitytypeTemMapper entitytypeMergeEntitytypeTemMapper;

    /**
    * 添加信息
    *
    * @param entitytypeMergeEntitytypeTem
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int insert(EntitytypeMergeEntitytypeTem entitytypeMergeEntitytypeTem) {

        //entitytypeMergeEntitytypeTem.setUserId(JavaUtils.getUUID36());
        //entitytypeMergeEntitytypeTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return entitytypeMergeEntitytypeTemMapper.insert(entitytypeMergeEntitytypeTem);
    }

    /**
    * 删除信息
    *
    * @param entitytypeMergeEntitytypeTemId
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int delete(String entitytypeMergeEntitytypeTemId) {
        return entitytypeMergeEntitytypeTemMapper.deleteById(entitytypeMergeEntitytypeTemId);
    }

    /**
    * 更新信息
    *
    * @param entitytypeMergeEntitytypeTem
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int update(EntitytypeMergeEntitytypeTem entitytypeMergeEntitytypeTem) {
        return entitytypeMergeEntitytypeTemMapper.updateById(entitytypeMergeEntitytypeTem);
    }

    /**
    * 全部查询
    *
    * @param entitytypeMergeEntitytypeTem
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeEntitytypeTem>
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public List<EntitytypeMergeEntitytypeTem> list(EntitytypeMergeEntitytypeTem entitytypeMergeEntitytypeTem) {

        QueryWrapper<EntitytypeMergeEntitytypeTem> queryWrapper = new QueryWrapper<>();

        return entitytypeMergeEntitytypeTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<EntitytypeMergeEntitytypeTem> entitytypeMergeEntitytypeTemIPage = new Page<>();
        entitytypeMergeEntitytypeTemIPage.setCurrent(current);
        entitytypeMergeEntitytypeTemIPage.setSize(size);

        QueryWrapper<EntitytypeMergeEntitytypeTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return entitytypeMergeEntitytypeTemMapper.selectPage(entitytypeMergeEntitytypeTemIPage, queryWrapper);
    }


}
