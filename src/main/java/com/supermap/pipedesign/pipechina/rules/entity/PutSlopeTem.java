package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_put_slope_tem")
@ApiModel(value="PutSlopeTem对象", description="")
public class PutSlopeTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "规范岩土类别(淤泥、黏性土、粉土、沙土、碎石土、岩石等)")
    @TableField("code_soil_type")
    private String codeSoilType;

    @ApiModelProperty(value = "规范岩土状态(坚硬、硬塑、可塑、密实、中密、稍密、未风化、中风化、强风化等)")
    @TableField("code_soil_state")
    private String codeSoilState;

    @ApiModelProperty(value = "低边坡管沟边坡坡率，坡高≤5米")
    @TableField("pipe_slope_rate_of_height_le_five_meter ")
    private Double pipeSlopeRateOfHeightLeFiveMeter;

    @ApiModelProperty(value = "高边坡管沟边坡坡率，5米＜坡高＜10米（岩15米）")
    @TableField("pipe_slope_rate_of_height_gt_five_meter ")
    private Double pipeSlopeRateOfHeightGtFiveMeter;

    @ApiModelProperty(value = "沟上焊加宽裕量（米）")
    @TableField("top_weld_ext_width")
    private Double topWeldExtWidth;

    @ApiModelProperty(value = "沟下焊加宽裕量（米）")
    @TableField("bottom_weld_ext_width")
    private Double bottomWeldExtWidth;


}
