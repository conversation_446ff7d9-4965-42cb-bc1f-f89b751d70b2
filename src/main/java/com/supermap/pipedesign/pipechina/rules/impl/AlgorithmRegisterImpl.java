package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.supermap.pipedesign.pipechina.rules.dao.AlgorithmRegisterParamsMapper;
import com.supermap.pipedesign.pipechina.rules.entity.AlgorithmRegister;
import com.supermap.pipedesign.pipechina.rules.dao.AlgorithmRegisterMapper;
import com.supermap.pipedesign.pipechina.rules.entity.AlgorithmRegisterParams;
import com.supermap.pipedesign.pipechina.rules.entity.vo.AlgorithmDataVo;

import com.supermap.pipedesign.pipechina.rules.entity.vo.AlgorithmFieldsData;
import com.supermap.pipedesign.pipechina.rules.service.IAlgorithmRegisterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 算法注册表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("AlgorithmRegisterImpl")
public class AlgorithmRegisterImpl
        extends ServiceImpl<AlgorithmRegisterMapper, AlgorithmRegister>
        implements IAlgorithmRegisterService {

    @Autowired
    private AlgorithmRegisterMapper algorithmRegisterMapper;


    @Autowired
    private AlgorithmRegisterParamsMapper algorithmRegisterParamsMapper;

    @Override
    public List<AlgorithmDataVo> selAlgorithmInfo(String algorGroup) {
        /**
         * @Description 1查列表2查算法对应的字段
         */
        QueryWrapper<AlgorithmRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("algorithm_group", algorGroup);
        List<AlgorithmRegister> algorithmRegisters
                = algorithmRegisterMapper.selectList(queryWrapper);
        List<AlgorithmDataVo> algorithmDataVos =  new ArrayList<>();
        if (algorithmRegisters != null && algorithmRegisters.size() > 0) {
            for (AlgorithmRegister algorithmRegister : algorithmRegisters) {
                AlgorithmDataVo algorithmDataVo = new AlgorithmDataVo();
                algorithmDataVo.setAlgorithmType(algorithmRegister.getAlgorithmtype());
                algorithmDataVo.setAlgorithmTitle(algorithmRegister.getAlgorithmname());
                algorithmDataVo.setShowType( algorithmRegister.getShowtypes() );
                String algorPkid = algorithmRegister.getPkid();
                algorithmDataVo.setLayerDictPid(algorPkid);
                String algorithmParam = "";
                List<AlgorithmFieldsData> algorithmFieldsDataList = new ArrayList<>();
                List<AlgorithmRegisterParams> algorithmRegisterParamList
                        = algorithmRegisterParamsMapper.selByRuleId(algorPkid);
                if (algorithmRegisterParamList != null
                        && algorithmRegisterParamList.size() > 0) {
                    for (AlgorithmRegisterParams algorithmRegisterParams : algorithmRegisterParamList) {
                        AlgorithmFieldsData algorithmFieldsData = new AlgorithmFieldsData();
                        algorithmFieldsData.setInputType(algorithmRegisterParams.getFieldType());
                        algorithmParam = algorithmParam + algorithmRegisterParams.getFieldAlias() + ",";
                        algorithmFieldsData.setName(algorithmRegisterParams.getFieldAlias());
                        algorithmFieldsData.setCode(algorithmRegisterParams.getFieldName());
                        algorithmFieldsData.setDescription(algorithmRegisterParams.getRemark());
                        if( algorithmRegisterParams.getFieldType().equals("select") ){
                            algorithmFieldsData.setDataType(algorithmRegisterParams.getFieldName()+algorithmRegisterParams.getFieldType());
                        }
                        algorithmFieldsData.setDescription( algorithmRegisterParams.getRemark() );
                        algorithmFieldsDataList.add(algorithmFieldsData);
                    }
                    algorithmParam = algorithmParam.substring(0, (algorithmParam.length() - 1));
                    algorithmDataVo.setFieldsData( algorithmFieldsDataList );
                    algorithmDataVo.setAlgorithmParam(algorithmParam);
                }
                algorithmDataVos.add( algorithmDataVo );
            }
            return  algorithmDataVos;
        }
        return  new ArrayList<>();
}


    public List<AlgorithmDataVo> selAlgorithmInfobyType(String algorGroup,String algorithmtype) {
        /**
         * @Description 1查列表2查算法对应的字段
         */
        QueryWrapper<AlgorithmRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("algorithm_group", algorGroup);
        queryWrapper.eq("algorithm_type", algorithmtype);
        List<AlgorithmRegister> algorithmRegisters
                = algorithmRegisterMapper.selectList(queryWrapper);
        List<AlgorithmDataVo> algorithmDataVos =  new ArrayList<>();
        if (algorithmRegisters != null && algorithmRegisters.size() > 0) {
            for (AlgorithmRegister algorithmRegister : algorithmRegisters) {
                AlgorithmDataVo algorithmDataVo = new AlgorithmDataVo();
                algorithmDataVo.setAlgorithmType(algorithmRegister.getAlgorithmtype());
                algorithmDataVo.setAlgorithmTitle(algorithmRegister.getAlgorithmname());
                algorithmDataVo.setShowType( algorithmRegister.getShowtypes() );
                String algorPkid = algorithmRegister.getPkid();
                algorithmDataVo.setLayerDictPid(algorPkid);
                String algorithmParam = "";
                List<AlgorithmFieldsData> algorithmFieldsDataList = new ArrayList<>();
                List<AlgorithmRegisterParams> algorithmRegisterParamList
                        = algorithmRegisterParamsMapper.selByRuleId(algorPkid);
                if (algorithmRegisterParamList != null
                        && algorithmRegisterParamList.size() > 0) {
                    for (AlgorithmRegisterParams algorithmRegisterParams : algorithmRegisterParamList) {
                        AlgorithmFieldsData algorithmFieldsData = new AlgorithmFieldsData();
                        algorithmFieldsData.setInputType(algorithmRegisterParams.getFieldType());
                        algorithmParam = algorithmParam + algorithmRegisterParams.getFieldAlias() + ",";
                        algorithmFieldsData.setName(algorithmRegisterParams.getFieldAlias());
                        algorithmFieldsData.setCode(algorithmRegisterParams.getFieldName());
                        algorithmFieldsData.setDescription(algorithmRegisterParams.getRemark());
                        if( algorithmRegisterParams.getFieldType().equals("select") ){
                            algorithmFieldsData.setDataType(algorithmRegisterParams.getFieldName()+algorithmRegisterParams.getFieldType());
                        }
                        algorithmFieldsData.setDescription( algorithmRegisterParams.getRemark() );
                        algorithmFieldsDataList.add(algorithmFieldsData);
                    }
                    algorithmParam = algorithmParam.substring(0, (algorithmParam.length() - 1));
                    algorithmDataVo.setFieldsData( algorithmFieldsDataList );
                    algorithmDataVo.setAlgorithmParam(algorithmParam);
                }
                algorithmDataVos.add( algorithmDataVo );
            }
            return  algorithmDataVos;
        }
        return  new ArrayList<>();
    }

/*    *//**
    * 全部查询
    *
    * @param algorithmRegister
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.AlgorithmRegister>
    * @Date 2023-01-12
    * @auther eomer
    *//*
    @Override
    public List<AlgorithmRegister> list(AlgorithmRegister algorithmRegister) {
        QueryWrapper<AlgorithmRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("algorithmgroup","auto_reate");
        return algorithmRegisterMapper.selectList(queryWrapper);
    }

    *//**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    *//*
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<AlgorithmRegister> algorithmRegisterIPage = new Page<>();
        algorithmRegisterIPage.setCurrent(current);
        algorithmRegisterIPage.setSize(size);

        QueryWrapper<AlgorithmRegister> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return algorithmRegisterMapper.selectPage(algorithmRegisterIPage, queryWrapper);
    }


    @Override
    public AlgorithmEx getAlgorithmInfo(String algorithmtype) {
        List<AlgorithmEx> dbAlgorithms = getDbAlgorithmRegister( algorithmRegisterMapper );
        AlgorithmEx algorithmEx = new AlgorithmEx();
    *//*String json =
        "[{\"algorithmname\":\"点与线垂直距离算法\",\"algorithmtype\":\"line_intersect_layer\",\"layerDictPid\":\"60010000000\",\"fields\":[{\"name\":\"适应图层\",\"code\":\"datesetname\",\"type\":\"layer\",\"children\":[]},{\"name\":\"交叉范围（°）\",\"code\":\"\",\"type\":\"dir\",\"children\":[{\"name\":\"交叉最小范围\",\"code\":\"intersectmin\",\"type\":\"input\",\"children\":[]},{\"name\":\"交叉最大范围\",\"code\":\"intersectmax\",\"type\":\"input\",\"children\":[]}]},{\"name\":\"交叉位置\",\"code\":\"intersectposition\",\"type\":\"combox\",\"children\":[]},{\"name\":\"距离\",\"code\":\"distance\",\"type\":\"input\",\"children\":[]}]},{\"algorithmname\":\"管线与线相交算法\",\"layerDictPid\":\"60010000000\",\"algorithmtype\":\"line_intersect_line\",\"fields\":[{\"name\":\"适应图层\",\"code\":\"datesetname\",\"type\":\"layer\",\"children\":[]},{\"name\":\"距交叉点距离\",\"code\":\"distance\",\"type\":\"input\",\"children\":[]}]},{\"algorithmname\":\"沿管线中心线创建算法\",\"layerDictPid\":\"60010000000\",\"algorithmtype\":\"on_line\",\"fields\":[{\"name\":\"间距\",\"code\":\"spacing\",\"type\":\"input\",\"children\":[]},{\"name\":\"垂直于管线的距离\",\"code\":\"distance\",\"type\":\"input\",\"children\":[]}]},{\"algorithmname\":\"管线与线角度算法\",\"layerDictPid\":\"60010000000\",\"algorithmtype\":\"lines_angle\",\"fields\":[{\"name\":\"适应图层\",\"code\":\"datesetname\",\"type\":\"layer\",\"children\":[]},{\"name\":\"角度（°）\",\"code\":\"\",\"type\":\"dir\",\"children\":[{\"name\":\"最小角度\",\"code\":\"anglemin\",\"type\":\"input\",\"children\":[]},{\"name\":\"最大角度\",\"code\":\"anglemax\",\"type\":\"input\",\"children\":[]}]}]},{\"algorithmname\":\"图层与参照物间距算法\",\"layerDictPid\":\"60010000000\",\"algorithmtype\":\"line_reference_object\",\"fields\":[{\"name\":\"适应图层\",\"code\":\"datesetname\",\"type\":\"layer\",\"children\":[]},{\"name\":\"垂直于管线的距离\",\"code\":\"distance\",\"type\":\"input\",\"children\":[]}]}]";*//*
//        List<AlgorithmEx> list = GsonUtil.GsonToList(json,AlgorithmEx.class);
        for (AlgorithmEx item : dbAlgorithms){
            if (item.getAlgorithmtype().equals(algorithmtype)){
                algorithmEx = item;
            }
        }

        return algorithmEx;
    }


    public List<AlgorithmEx> getDbAlgorithmRegister( AlgorithmRegisterMapper algorithmRegisterMapper){
        List<AlgorithmRegister> algorithmRegisters = algorithmRegisterMapper.selAll();
        List<AlgorithmEx> dbAlgorithms = new ArrayList<>();
        if( algorithmRegisters != null && algorithmRegisters.size() > 0 ){
            for( AlgorithmRegister algorithmRegister : algorithmRegisters ){
                AlgorithmEx algorithmEx = new AlgorithmEx();
                algorithmEx.setAlgorithmname( algorithmRegister.getAlgorithmname() );
                algorithmEx.setAlgorithmtype( algorithmRegister.getAlgorithmtype() );
                algorithmEx.setLayerDictPid("60010000000");
                if( algorithmRegister.getAlgorithmtype().equals("line_intersect_layer") ){
                    List<AlgorithmEx.FieldsEntity> fieldsEntities =  new ArrayList<>();
                    *//**----**//*
                    AlgorithmEx.FieldsEntity datesetname =  algorithmEx.new FieldsEntity();
                    datesetname.setCode("datesetname");
                    datesetname.setName("适应图层");
                    datesetname.setType("layer");
                    fieldsEntities.add( datesetname );
                    *//**----**//*
                    AlgorithmEx.FieldsEntity dir =  algorithmEx.new FieldsEntity();
                    dir.setCode("");
                    dir.setName("交叉范围（°）");
                    dir.setType("dir");
                    List<AlgorithmEx.FieldsEntity> dirChiList = new ArrayList<>();
                    AlgorithmEx.FieldsEntity dirChi1 =  algorithmEx.new FieldsEntity();
                    dirChi1.setCode("intersectmin");
                    dirChi1.setName("交叉最小范围");
                    dirChi1.setType("input");
                    dirChiList.add( dirChi1 );
                    AlgorithmEx.FieldsEntity dirChi2 =  algorithmEx.new FieldsEntity();
                    dirChi2.setCode("intersectmax");
                    dirChi2.setName("交叉最大范围");
                    dirChi2.setType("input");
                    dirChiList.add( dirChi2 );
                    dir.setChildren( dirChiList );
                    fieldsEntities.add( dir );
                    *//**----**//*
                    AlgorithmEx.FieldsEntity intersectposition =  algorithmEx.new FieldsEntity();
                    intersectposition.setCode("intersectposition");
                    intersectposition.setName("交叉位置");
                    intersectposition.setType("combox");
                    fieldsEntities.add( intersectposition );
                    *//**----**//*
                    AlgorithmEx.FieldsEntity distance =  algorithmEx.new FieldsEntity();
                    distance.setCode("distance");
                    distance.setName("距离");
                    distance.setType("input");
                    fieldsEntities.add( distance );
                    algorithmEx.setFields(fieldsEntities);
                }
                if( algorithmRegister.getAlgorithmtype().equals("line_intersect_line") ){
                    List<AlgorithmEx.FieldsEntity> fieldsEntities =  new ArrayList<>();
                    *//**----**//*
                    AlgorithmEx.FieldsEntity datesetname =  algorithmEx.new FieldsEntity();
                    datesetname.setCode("datesetname");
                    datesetname.setName("适应图层");
                    datesetname.setType("layer");
                    fieldsEntities.add( datesetname );
                    *//**----**//*

                    AlgorithmEx.FieldsEntity distance =  algorithmEx.new FieldsEntity();
                    distance.setCode("distance");
                    distance.setName("距交叉点距离");
                    distance.setType("input");
                    fieldsEntities.add( distance );
                    algorithmEx.setFields(fieldsEntities);
                }
                if( algorithmRegister.getAlgorithmtype().equals("on_line") ){
                    List<AlgorithmEx.FieldsEntity> fieldsEntities =  new ArrayList<>();
                    *//**----**//*
                    AlgorithmEx.FieldsEntity spacing =  algorithmEx.new FieldsEntity();
                    spacing.setCode("spacing");
                    spacing.setName("适应图层");
                    spacing.setType("input");
                    fieldsEntities.add( spacing );
                    *//**----**//*
                    AlgorithmEx.FieldsEntity distance =  algorithmEx.new FieldsEntity();
                    distance.setCode("distance");
                    distance.setName("垂直于管线的距离");
                    distance.setType("input");
                    fieldsEntities.add( distance );
                    algorithmEx.setFields(fieldsEntities);
                }
                if( algorithmRegister.getAlgorithmtype().equals("lines_angle") ){
                    List<AlgorithmEx.FieldsEntity> fieldsEntities =  new ArrayList<>();
                    *//**----**//*
                    AlgorithmEx.FieldsEntity datesetname =  algorithmEx.new FieldsEntity();
                    datesetname.setCode("datesetname");
                    datesetname.setName("适应图层");
                    datesetname.setType("layer");
                    fieldsEntities.add( datesetname );
                    *//**----**//*
                    AlgorithmEx.FieldsEntity dir =  algorithmEx.new FieldsEntity();
                    dir.setCode("");
                    dir.setName("角度（°）");
                    dir.setType("dir");
                    List<AlgorithmEx.FieldsEntity> dirChiList = new ArrayList<>();
                    AlgorithmEx.FieldsEntity dirChi1 =  algorithmEx.new FieldsEntity();
                    dirChi1.setCode("anglemin");
                    dirChi1.setName("最小角度");
                    dirChi1.setType("input");
                    dirChiList.add( dirChi1 );
                    AlgorithmEx.FieldsEntity dirChi2 =  algorithmEx.new FieldsEntity();
                    dirChi2.setCode("anglemax");
                    dirChi2.setName("最大角度");
                    dirChi2.setType("input");
                    dirChiList.add( dirChi2 );
                    dir.setChildren( dirChiList );
                    fieldsEntities.add( dir );
                    *//**----**//*
                    algorithmEx.setFields(fieldsEntities);
                }
                if( algorithmRegister.getAlgorithmtype().equals("line_reference_object") ){
                    List<AlgorithmEx.FieldsEntity> fieldsEntities =  new ArrayList<>();
                    *//**----**//*
                    AlgorithmEx.FieldsEntity datesetname =  algorithmEx.new FieldsEntity();
                    datesetname.setCode("datesetname");
                    datesetname.setName("适应图层");
                    datesetname.setType("layer");
                    fieldsEntities.add( datesetname );
                    *//**----**//*
                    AlgorithmEx.FieldsEntity distance =  algorithmEx.new FieldsEntity();
                    distance.setCode("distance");
                    distance.setName("垂直于管线的距离");
                    distance.setType("input");
                    fieldsEntities.add( distance );
                    algorithmEx.setFields(fieldsEntities);
                }
                dbAlgorithms.add( algorithmEx );
            }
        }
        return  dbAlgorithms;
    }*/
}
