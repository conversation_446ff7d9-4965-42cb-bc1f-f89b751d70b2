package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.EUFun;
import com.supermap.pipedesign.pipechina.rules.dao.EUFunMapper;
import com.supermap.pipedesign.pipechina.rules.service.IEUFunService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 算法 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Service("EUFunImpl")
public class EUFunImpl extends ServiceImpl<EUFunMapper, EUFun> implements IEUFunService {

    @Autowired
    private EUFunMapper eUFunMapper;

    /**
    * 添加算法信息
    *
    * @param eUFun
    * @return int
    * @Date 2023-03-09
    * @auther eomer
    */
    @Override
    public int insert(EUFun eUFun) {

        //eUFun.setUserId(JavaUtils.getUUID36());
        //eUFun.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return eUFunMapper.insert(eUFun);
    }

    /**
    * 删除算法信息
    *
    * @param eUFunId
    * @return int
    * @Date 2023-03-09
    * @auther eomer
    */
    @Override
    public int delete(String eUFunId) {
        return eUFunMapper.deleteById(eUFunId);
    }

    /**
    * 更新算法信息
    *
    * @param eUFun
    * @return int
    * @Date 2023-03-09
    * @auther eomer
    */
    @Override
    public int update(EUFun eUFun) {
        return eUFunMapper.updateById(eUFun);
    }

    /**
    * 全部查询
    *
    * @param eUFun
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.EUFun>
    * @Date 2023-03-09
    * @auther eomer
    */
    @Override
    public List<EUFun> list(EUFun eUFun) {

        QueryWrapper<EUFun> queryWrapper = new QueryWrapper<>();

        return eUFunMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-09
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<EUFun> eUFunIPage = new Page<>();
        eUFunIPage.setCurrent(current);
        eUFunIPage.setSize(size);

        QueryWrapper<EUFun> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return eUFunMapper.selectPage(eUFunIPage, queryWrapper);
    }


}
