package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EntitytypeDeriveVo
        implements Serializable {


    private String pkid;

    @ApiModelProperty(value = "衍生实体id")
    private String entityTypeId;

    @ApiModelProperty(value = "衍生实体名称")
    private String entityTypeName;

    @ApiModelProperty(value = "源衍生实体id")
    private String beEntityTypeId;

    @ApiModelProperty(value = "源衍生实体名称")
    private String beEntityTypeName;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "实体衍生规则名称")
    private String entitytypederiverulesname;

    @ApiModelProperty(value = "小里程处延伸距离")
    private Integer minextend;

    @ApiModelProperty(value = "大里程处延伸距离")
    private Integer maxextend;

    @ApiModelProperty(value = "水平偏移方向")
    private Integer horizontalskewingdirection;

    @ApiModelProperty(value = "偏移距离")
    private Integer horizontalskewingdist;

    private List<EntityDeriveFields> entityDeriveFieldsList;


    @Data
    public class EntityDeriveFields {

        @ApiModelProperty(value = "源衍生实体的属性id")
        private String fieldPkid;

        @ApiModelProperty(value = "源衍生实体的属性的名称")
        private String fieldName;

        @ApiModelProperty(value = "源衍生实体的属性选择的值")
        private String fieldVal;

        @ApiModelProperty(value = "源衍生实体的属性选择的值")
        private String fieldValName;

        private List<Dictionary> dictionaryList;
    }
}
