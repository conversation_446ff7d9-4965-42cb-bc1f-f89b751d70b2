package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.supermap.common.entity.DictText;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="NoTrafficAlgorithmTem对象", description="")
public class NoTrafficAlgorithmTemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @TableField("algorithm_name")
    private String algorithmName;

    @TableField("ruleid")
    private String ruleid;

    @TableField("layer")
    @DictText(keyColumn = "pkid",tableName="wbs_dictionary",target = "layer",textColumn = "dictionary_name")
    private String layer;

    @TableField("radius")
    private Integer radius;

    @TableField("type")
    private Integer type;


    private DictionaryVo  dictionaryVo;



}
