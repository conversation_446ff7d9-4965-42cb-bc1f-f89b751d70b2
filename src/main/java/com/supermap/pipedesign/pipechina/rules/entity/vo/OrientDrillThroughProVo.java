package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.supermap.pipedesign.pipechina.rules.entity.OrientDrillThrough;
import com.supermap.pipedesign.pipechina.rules.entity.OrientDrillThroughTem;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OrientDrillThroughTem对象", description="")
public class OrientDrillThroughProVo implements Serializable {

     List<OrientDrillThrough> excavationThroughTemList;


}
