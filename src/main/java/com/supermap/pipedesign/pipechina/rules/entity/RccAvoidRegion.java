package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 宜、应避开通过面数据 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rcc_avoid_region")
@ApiModel(value="RccAvoidRegion对象", description="宜、应避开通过面数据")
public class RccAvoidRegion implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "所属规则小类（所属法律法规）")
    @TableField("rule_id")
    private String ruleId;

    @ApiModelProperty(value = "适用图层")
    @TableField("layer_id")
    private String layerId;

    @ApiModelProperty(value = "距离(废)")
    @TableField("distance")
    private Integer distance;

    @ApiModelProperty(value = "管道与线的最小交叉角度规定(废)")
    @TableField("angle")
    private Integer angle;

    @ApiModelProperty(value = "是否考虑加上管经，0否，1是(废)")
    @TableField("is_caliber")
    private Integer isCaliber;

    @ApiModelProperty(value = "关联项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mId;


}
