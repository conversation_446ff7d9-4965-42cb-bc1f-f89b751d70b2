package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 管线与图层相交算法 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LineIntersectLayer对象", description="管线与图层相交算法")
public class LineIntersectLayerVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "图层名称")
    private String datesetname;

    @ApiModelProperty(value = "图层id")
    private String datesetid;

    @ApiModelProperty(value = "交叉最小范围")
    private Integer intersectmin;

    @ApiModelProperty(value = "交叉最大范围")
    private Integer intersectmax;

    @ApiModelProperty(value = "交叉位置")
    private String intersectposition;

    @ApiModelProperty(value = "距离")
    private Integer distance;

    @ApiModelProperty(value = "关联项目id")
    private String projectid;

    @ApiModelProperty(value = "元数据库中规则和算法关联表的PKID")
    private String rulealgoritid;

    @ApiModelProperty(value = "规则类型")
    private String rulealgorittype;


}
