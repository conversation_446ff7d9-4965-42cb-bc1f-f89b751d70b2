package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LineReferenceObject对象", description="")
public class LineReferenceObjectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "参照物数据集名称")
    private String datesetname;

    @ApiModelProperty(value = "参照物数据集名称")
    private String datesetid;

    @ApiModelProperty(value = "垂直于管线的距离")
    private Integer distance;

    @ApiModelProperty(value = "关联项目id")
    private String projectid;

    @ApiModelProperty(value = "元数据库中规则和算法关联表的PKID")
    private String rulealgoritid;

    @ApiModelProperty(value = "规则类型")
    private String rulealgorittype;


/*    @ApiModelProperty(value = "交叉最小范围")
    @TableField("intersectmin")
    private Integer intersectmin;

    @ApiModelProperty(value = "交叉最大范围")
    @TableField("intersectmax")
    private Integer intersectmax;

    @ApiModelProperty(value = "交叉位置")
    @TableField("intersectposition")
    private String intersectposition;*/


}
