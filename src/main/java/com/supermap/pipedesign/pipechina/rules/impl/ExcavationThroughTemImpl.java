package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.rules.dao.ExcavationThroughTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.ExcavationThroughTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.ExcavationThroughTemVo;
import com.supermap.pipedesign.pipechina.rules.service.IExcavationThroughTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Service("ExcavationThroughTemImpl")
public class ExcavationThroughTemImpl extends ServiceImpl<ExcavationThroughTemMapper, ExcavationThroughTem> implements IExcavationThroughTemService {

    @Autowired
    private ExcavationThroughTemMapper excavationThroughTemMapper;

    /**
    * 添加信息
    *
    * @param excavationThroughTem
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int insert(ExcavationThroughTem excavationThroughTem) {

        //excavationThroughTem.setUserId(JavaUtils.getUUID36());
        //excavationThroughTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return excavationThroughTemMapper.insert(excavationThroughTem);
    }

    /**
    * 删除信息
    *
    * @param excavationThroughTemId
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int delete(String excavationThroughTemId) {
        return excavationThroughTemMapper.deleteById(excavationThroughTemId);
    }

    /**
    * 更新信息
    *
    * @param excavationThroughTem
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int update(ExcavationThroughTemVo excavationThroughTem) {
        List<ExcavationThroughTem> excavationThroughTemList = excavationThroughTem.getExcavationThroughTemList();
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("type",excavationThroughTemList.get(0).getType());
        excavationThroughTemMapper.delete(queryWrapper);
        for (ExcavationThroughTem tem : excavationThroughTemList) {
            if(tem.getType() == 0){
                if (tem.getFileCode().equals("min_depth_of_open_cut_crossing_pipe_under_scour_line")
                        && tem.getValue() == null){
                    tem.setValue(1.5);
                }else if (tem.getFileCode().equals("min_depth_of_open_cut_crossing_pipe_under_riverbed")
                        && tem.getValue() == null){
                    tem.setValue(1.5);
                }
            } else if (tem.getType() == 1) {
                if (tem.getFileCode().equals("min_width_of_open_cut_crossing_section_platform")
                        && tem.getValue() == null){
                    tem.setValue(4.0);
                }else if (tem.getFileCode().equals("min_width_of_open_cut_crossing_section_work_platform")
                        && tem.getValue() == null){
                    tem.setValue(8.0);
                }else if (tem.getFileCode().equals("min_height_of_open_cut_crossing_section_step")
                        && tem.getValue() == null){
                    tem.setValue(3.0);
                }else if (tem.getFileCode().equals("max_height_of_open_cut_crossing_section_step")
                        && tem.getValue() == null){
                    tem.setValue(5.0);
                }
            } else if (tem.getType() == 2) {
                if (tem.getFileCode().equals("min_width_of_cofferdam_top")
                        && tem.getValue() == null){
                    tem.setValue(2.0);
                }else if (tem.getFileCode().equals("min_turning_radius_of_cofferdam")
                        && tem.getValue() == null){
                    tem.setValue(3.0);
                }else if (tem.getFileCode().equals("min_voerwater_level_at_the_top_of_cofferdam")
                        && tem.getValue() == null){
                    tem.setValue(1.0);
                }else if (tem.getFileCode().equals("min_slope_ratio_of_cofferdam")
                        && tem.getValue() == null){
                    tem.setValue(0.5);
                }else if (tem.getFileCode().equals("max_voerwater_level_at_the_top_of_cofferdam")
                        && tem.getValue() == null){
                    tem.setValue(1.5);
                }else if (tem.getFileCode().equals("max_slope_ratio_of_cofferdam")
                        && tem.getValue() == null){
                    tem.setValue(1.0);
                }
            } else if (tem.getType() == 3) {
                if (tem.getFileCode().equals("min_turning_radius_of_diversion_channel")
                        && tem.getValue() == null){
                    tem.setValue(3.0);
                }else if (tem.getFileCode().equals("min_width_of_diversion_channel_bottom")
                        && tem.getValue() == null){
                    tem.setValue(1.0);
                }
            } else if (tem.getType() == 4) {
                if (tem.getFileCode().equals("min_depth_under_scour_line_of_revetmen_base")
                        && tem.getValue() == null){
                    tem.setValue(1.0);
                }else if (tem.getFileCode().equals("min_depth_under_riverbed_of_flexible_bottom_revetment_base")
                        && tem.getValue() == null){
                    tem.setValue(1.0);
                }else if (tem.getFileCode().equals("max_slope_ratio_of_revetment")
                        && tem.getValue() == null){
                    tem.setValue(1.0);
                }else if (tem.getFileCode().equals("min_width_of_over_excavation_water_level_for_revetment")
                        && tem.getValue() == null){
                    tem.setValue(1.0);
                }else if (tem.getFileCode().equals("min_voerwater_level_at_the_top_of_revetment")
                        && tem.getValue() == null){
                    tem.setValue(0.5);
                }else if (tem.getFileCode().equals("min_depth_under_riverbed_of_revetment_base")
                        && tem.getValue() == null){
                    tem.setValue(1.0);
                }
            }

            excavationThroughTemMapper.insert(tem);
        }
        return 1;
    }

    /**
    * 全部查询
    *
    * @param type
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.ExcavationThroughTem>
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public List<ExcavationThroughTem> list(Integer type) {

        QueryWrapper<ExcavationThroughTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type",type);
        return excavationThroughTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<ExcavationThroughTem> excavationThroughTemIPage = new Page<>();
        excavationThroughTemIPage.setCurrent(current);
        excavationThroughTemIPage.setSize(size);

        QueryWrapper<ExcavationThroughTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return excavationThroughTemMapper.selectPage(excavationThroughTemIPage, queryWrapper);
    }


}
