package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.rules.entity.Material;
import com.supermap.pipedesign.pipechina.rules.entity.vo.EUFunVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.MeterialsVo;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 材料表计算模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-11
 */
@Repository
public interface IMaterialService extends IService<Material> {

 /**
  * 添加材料表统计模板信息
  *
  * @param vo
  * @return int
  * @Date 2023-03-09
  * @auther eomer
  */
 int insert(MeterialsVo vo, User userId);

 /**
  * @param engId     根据材料表id查详情
  * @param projectId
  * <AUTHOR>
  * @Description
  * @Date 2023/3/10 11:15
  **/
 MeterialsVo selInfoByEngId(String engId, String projectId);

 /**
  * @param funTemId  算法函数id
  *                  根据算法函数id查算法详情
  * @param projectId
  * <AUTHOR>
  * @Description
  * @Date 2023/3/10 11:47
  **/
 EUFunVo selInfoByFunId(String funTemId, String projectId);

 /**
  * 更新材料表
  * <AUTHOR>
  * @Description
  * @Date 2023/3/10 11:55
  **/
 int updateEng(MeterialsVo vo, User userId);

 /**
  * 删除材料表统计模板信息
  *
  * @param engId
  * @param projectId
  * @return int
  * @Date 2023-03-09
  * @auther eomer
  */
 int deleteEng(String engId, String projectId);


    IPage pageList(long current, long size, String stageCode, String subjectId, String searchName, String projectId);
}
