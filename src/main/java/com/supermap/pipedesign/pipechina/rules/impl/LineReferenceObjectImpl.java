package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.LineReferenceObject;
import com.supermap.pipedesign.pipechina.rules.dao.LineReferenceObjectMapper;
import com.supermap.pipedesign.pipechina.rules.service.ILineReferenceObjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Service("LineReferenceObjectImpl")
public class LineReferenceObjectImpl extends ServiceImpl<LineReferenceObjectMapper, LineReferenceObject> implements ILineReferenceObjectService {

    @Autowired
    private LineReferenceObjectMapper lineReferenceObjectMapper;

    /**
    * 添加信息
    *
    * @param lineReferenceObject
    * @return int
    * @Date 2023-01-13
    * @auther eomer
    */
    @Override
    public int insert(LineReferenceObject lineReferenceObject) {

        //lineReferenceObject.setUserId(JavaUtils.getUUID36());
        //lineReferenceObject.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return lineReferenceObjectMapper.insert(lineReferenceObject);
    }

    /**
    * 删除信息
    *
    * @param lineReferenceObjectId
    * @return int
    * @Date 2023-01-13
    * @auther eomer
    */
    @Override
    public int delete(String lineReferenceObjectId) {
        return lineReferenceObjectMapper.deleteById(lineReferenceObjectId);
    }

    /**
    * 更新信息
    *
    * @param lineReferenceObject
    * @return int
    * @Date 2023-01-13
    * @auther eomer
    */
    @Override
    public int update(LineReferenceObject lineReferenceObject) {
        return lineReferenceObjectMapper.updateById(lineReferenceObject);
    }

    /**
    * 全部查询
    *
    * @param lineReferenceObject
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.LineReferenceObject>
    * @Date 2023-01-13
    * @auther eomer
    */
    @Override
    public List<LineReferenceObject> list(LineReferenceObject lineReferenceObject) {

        QueryWrapper<LineReferenceObject> queryWrapper = new QueryWrapper<>();

        return lineReferenceObjectMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-13
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<LineReferenceObject> lineReferenceObjectIPage = new Page<>();
        lineReferenceObjectIPage.setCurrent(current);
        lineReferenceObjectIPage.setSize(size);

        QueryWrapper<LineReferenceObject> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return lineReferenceObjectMapper.selectPage(lineReferenceObjectIPage, queryWrapper);
    }


}
