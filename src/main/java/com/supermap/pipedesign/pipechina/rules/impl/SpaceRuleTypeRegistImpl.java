package com.supermap.pipedesign.pipechina.rules.impl;


import com.supermap.pipedesign.pipechina.rules.dao.RuledefDirecotriesMapper;
import com.supermap.pipedesign.pipechina.rules.dao.SpaceRuleChildRegisterMapper;
import com.supermap.pipedesign.pipechina.rules.entity.RuledefDirecotries;
import com.supermap.pipedesign.pipechina.rules.entity.SpaceRuleTypeRegist;
import com.supermap.pipedesign.pipechina.rules.dao.SpaceRuleTypeRegistMapper;
import com.supermap.pipedesign.pipechina.rules.entity.vo.RuledefDirecotriesVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.SpaceRoutVo;
import com.supermap.pipedesign.pipechina.rules.service.ISpaceRuleTypeRegistService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 空间规则类型注册表，如禁止通过区域、宜、应避开区域、间距规定、交叉规定...... 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-17
 */
@Service("SpaceRuleTypeRegistImpl")
public class SpaceRuleTypeRegistImpl
        extends ServiceImpl<SpaceRuleTypeRegistMapper, SpaceRuleTypeRegist> implements ISpaceRuleTypeRegistService {


        @Autowired
        private RuledefDirecotriesMapper ruledefDirecotriesMapper;

        @Autowired
        private SpaceRuleChildRegisterMapper spaceRuleChildRegisterMapper;



        @Override
        public RuleDicMap selTreeSpaceRout(String projectid){

            RuledefDirecotries ruledefDirecotries = ruledefDirecotriesMapper.selByProjectAndAlias(projectid,"SpaceRoutRules");
            RuleDicMap ruleDicMap =  new RuleDicMap();
            ruleDicMap.setKey( ruledefDirecotries.getPkid() );
            ruleDicMap.setName( ruledefDirecotries.getDirectoryname() );
            List<RuledefDirecotriesVo> ruledefDirecotriesVos
                    =  ruledefDirecotriesMapper.selRulesNoTreeList( ruledefDirecotries.getPkid() );
            if( ruledefDirecotriesVos != null
                    &&  ruledefDirecotriesVos.size() > 0 ){
                List<RuleDicMap> ruleDicMapList = new ArrayList<>();
                for( RuledefDirecotriesVo ruledefDirecotriesVo : ruledefDirecotriesVos ){
                    RuleDicMap childRuleDic = new RuleDicMap();
                    childRuleDic.setKey(ruledefDirecotriesVo.getPkid() );
                    childRuleDic.setName( ruledefDirecotriesVo.getDirectoryname() );
                    List<SpaceRoutVo> spaceRoutVos
                            = spaceRuleChildRegisterMapper.selByRuleParentId( ruledefDirecotriesVo.getPkid(), projectid);
                    if( spaceRoutVos != null
                            &&  spaceRoutVos.size() > 0 ){
                        List<RuleDicMap> threeChilList =  new ArrayList<>();
                        for( SpaceRoutVo spaceRoutVo : spaceRoutVos ){
                            RuleDicMap threeChild = new RuleDicMap();
                            threeChild.setKey( spaceRoutVo.getPkid() );
                            threeChild.setName( spaceRoutVo.getRuleName() );
                            threeChilList.add( threeChild );
                        }
                        childRuleDic.setRuleDicMapList( threeChilList );
                    }
                    ruleDicMapList.add( childRuleDic );
                }
                ruleDicMap.setRuleDicMapList( ruleDicMapList );
            }
        return ruleDicMap;
    }

    @Data
    public class RuleDicMap{

        private String key;

        private String name;

        private List<RuleDicMap> ruleDicMapList;
    }



}
