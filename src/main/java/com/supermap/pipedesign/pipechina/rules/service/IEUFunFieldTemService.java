package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.EUFunFieldTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 算法字段模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Repository
public interface IEUFunFieldTemService extends IService<EUFunFieldTem> {

 /**
 * 添加算法字段模板信息
 *
 * @param eUFunFieldTem
 * @return int
 * @Date 2023-03-09
 * @auther eomer
 */
 int insert(EUFunFieldTem eUFunFieldTem);

 /**
 * 删除算法字段模板信息
 *
 * @param eUFunFieldTemId
 * @return int
 * @Date 2023-03-09
 * @auther eomer
 */
 int delete(String eUFunFieldTemId);

 /**
 * 更新算法字段模板信息
 *
 * @param eUFunFieldTem
 * @return int
 * @Date 2023-03-09
 * @auther eomer
 */
 int update(EUFunFieldTem eUFunFieldTem);

 /**
 * 全部查询
 *
 * @param eUFunFieldTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.EUFunFieldTem>
 * @Date 2023-03-09
 * @auther eomer
 */
 List<EUFunFieldTem> list(EUFunFieldTem eUFunFieldTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-09
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
