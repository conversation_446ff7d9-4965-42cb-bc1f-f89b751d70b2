package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.EntitytypeDeriveStageTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Repository
public interface IEntitytypeDeriveStageTemService extends IService<EntitytypeDeriveStageTem> {

 /**
 * 添加信息
 *
 * @param entitytypeDeriveStageTem
 * @return int
 * @Date 2023-02-17
 * @auther eomer
 */
 int insert(EntitytypeDeriveStageTem entitytypeDeriveStageTem);

 /**
 * 删除信息
 *
 * @param entitytypeDeriveStageTemId
 * @return int
 * @Date 2023-02-17
 * @auther eomer
 */
 int delete(String entitytypeDeriveStageTemId);

 /**
 * 更新信息
 *
 * @param entitytypeDeriveStageTem
 * @return int
 * @Date 2023-02-17
 * @auther eomer
 */
 int update(EntitytypeDeriveStageTem entitytypeDeriveStageTem);

 /**
 * 全部查询
 *
 * @param entitytypeDeriveStageTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.EntitytypeDeriveStageTem>
 * @Date 2023-02-17
 * @auther eomer
 */
 List<EntitytypeDeriveStageTem> list(EntitytypeDeriveStageTem entitytypeDeriveStageTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-02-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
