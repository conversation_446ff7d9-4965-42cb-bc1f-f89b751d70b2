package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.rules.entity.EntitytypeDeriveFieldTem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Mapper
public interface EntitytypeDeriveFieldTemMapper extends BaseMapper<EntitytypeDeriveFieldTem> {

    @Select("select * from qi_entitytype_derive_field_tem where derive_pkid=#{derivePkid} ")
    List<EntitytypeDeriveFieldTem> selByDerivePkid(@Param(value = "derivePkid") String derivePkid );

}
