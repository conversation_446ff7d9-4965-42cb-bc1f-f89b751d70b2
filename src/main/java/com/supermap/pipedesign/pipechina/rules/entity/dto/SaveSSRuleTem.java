package com.supermap.pipedesign.pipechina.rules.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
public class SaveSSRuleTem implements Serializable {

    @ApiModelProperty(value = "规则Id列表")
    private List<String> ruleIds;

    @ApiModelProperty(value = "阶段id")
    private String stageId;

    @ApiModelProperty(value = "专题id")
    private  String subjectId;

    @ApiModelProperty(value = "1:衍生规则2:合并规则")
    private Integer type;

}
