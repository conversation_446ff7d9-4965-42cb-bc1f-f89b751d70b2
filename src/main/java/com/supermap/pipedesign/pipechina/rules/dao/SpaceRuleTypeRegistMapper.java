package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.rules.entity.SpaceRuleTypeRegist;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 空间规则类型注册表，如禁止通过区域、宜、应避开区域、间距规定、交叉规定...... Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-17
 */
@Mapper
public interface SpaceRuleTypeRegistMapper extends BaseMapper<SpaceRuleTypeRegist> {

}
