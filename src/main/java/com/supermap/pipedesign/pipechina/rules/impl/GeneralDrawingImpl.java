package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.file.impl.FileUpLoadUtils;
import com.supermap.pipedesign.pipechina.metadata.dao.EntitytypesMapper;
import com.supermap.pipedesign.pipechina.rules.dao.GeneralDrawingMapper;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawing;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawingTem;
import com.supermap.pipedesign.pipechina.rules.service.IGeneralDrawingService;
import com.supermap.tools.file.PathUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;


/**
 * <p>
 * 通用图 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Service("GeneralDrawingImpl")
public class GeneralDrawingImpl extends ServiceImpl<GeneralDrawingMapper, GeneralDrawing> implements IGeneralDrawingService {

    @Autowired
    private GeneralDrawingMapper generalDrawingMapper;

    @Autowired
    private EntitytypesMapper entitytypesMapper;

    private final String filePath= PathUtils.getDbUploadPath();

    @Resource(name = "FileUpLoadUtils")
    private FileUpLoadUtils fileUpLoadUtils;
    /**
     * 添加通用图信息
     *
     * @param file
     * @param generalDrawing
     * @return int
     * @Date 2023-03-10
     * @auther eomer
     */
    @Override
    public int insert(MultipartFile file, GeneralDrawing generalDrawing, User user) {
        File fil = fileUpLoadUtils.uploadRetrunFile(file, filePath);
        generalDrawing.setUpdateUser(user.getUsername());
        generalDrawing.setUpdateTime(new Timestamp(Calendar.getInstance().getTimeInMillis()));
        generalDrawing.setPathUrl(fil.getPath());
        generalDrawing.setFileName(fil.getName());
        return generalDrawingMapper.insert(generalDrawing);
    }

    /**
     * 删除通用图信息
     *
     * @param generalDrawingId
     * @param projectId
     * @return int
     * @Date 2023-03-10
     * @auther eomer
     */
    @Override
    public int delete(String generalDrawingId, String projectId) {
        QueryWrapper<GeneralDrawing>queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("pkid",generalDrawingId);
        queryWrapper.eq("project_id",projectId);
        QueryWrapper wrapper = new QueryWrapper<>();
        wrapper.like("reuse_diagram_id",generalDrawingId);
        wrapper.eq("project_id",projectId);
        List list = entitytypesMapper.selectList(wrapper);
        if (list == null || list.size() == 0) {
            return generalDrawingMapper.delete(queryWrapper);
        }
        throw new BusinessException("该通用图已被使用，不能删除");
    }

    /**
     * 更新通用图信息
     *
     * @param generalDrawing
     * @return int
     * @Date 2023-03-10
     * @auther eomer
     */
    @Override
    public int update(GeneralDrawing generalDrawing, User user) {
        generalDrawing.setUpdateUser(user.getUsername());
        generalDrawing.setUpdateTime(new Timestamp(Calendar.getInstance().getTimeInMillis()));
        QueryWrapper<GeneralDrawing>queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("pkid",generalDrawing.getPkid());
        queryWrapper.eq("project_id",generalDrawing.getProjectId());
        return generalDrawingMapper.update(generalDrawing,queryWrapper);
    }

    /**
     * 全部查询
     *
     * @param projectId
     * @param pid
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawing>
     * @Date 2023-03-10
     * @auther eomer
     */
    @Override
    public List<GeneralDrawing> list( String projectId, String pid) {
        QueryWrapper<GeneralDrawing> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid",pid);
        queryWrapper.eq("project_id",projectId);
        return generalDrawingMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @param projectId
     * @param searchName
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * @Date 2023-03-10
     * @auther eomer
     */
    @Override
    public IPage pageList(long current, long size, String pid, String projectId, String searchName) {

        IPage<GeneralDrawing> generalDrawingIPage = new Page<>();
        generalDrawingIPage.setCurrent(current);
        generalDrawingIPage.setSize(size);
        QueryWrapper<GeneralDrawing> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid",pid);
        queryWrapper.eq("project_id",projectId);
        queryWrapper.eq("pid",pid);
        if (StringUtils.isNotEmpty(searchName)){
            queryWrapper.like("gd_name",searchName);
        }
        return generalDrawingMapper.selectPage(generalDrawingIPage, queryWrapper);
    }

    /**
     * 查询详情
     *
     * @param id
     * @param pid
     * @param projectId
     * @return
     */
    @Override
    public List<GeneralDrawing> select(String id, String pid, String projectId) {
        QueryWrapper<GeneralDrawing> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid",id);
        queryWrapper.eq("pid",pid);
        queryWrapper.eq("project_id",projectId);
        List<GeneralDrawing> generalDrawing = generalDrawingMapper.selectList(queryWrapper);
        return generalDrawing;
    }

    @Override
    public List<GeneralDrawing> entityGeneralTree(GeneralDrawingTem generalDrawingTem, String projectId) {
        QueryWrapper<GeneralDrawing> wrap = new QueryWrapper<GeneralDrawing>().eq("project_id", projectId);
        return generalDrawingMapper.selectList(wrap);
    }

    @Override
    public GeneralDrawing downloadGeneral(String pkid, String projectId) {
        QueryWrapper<GeneralDrawing> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid",pkid);
        queryWrapper.eq("project_id",projectId);
        return generalDrawingMapper.selectOne(queryWrapper);
    }
}
