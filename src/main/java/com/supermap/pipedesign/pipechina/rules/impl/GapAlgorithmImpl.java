package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.GapAlgorithm;
import com.supermap.pipedesign.pipechina.rules.dao.GapAlgorithmMapper;
import com.supermap.pipedesign.pipechina.rules.service.IGapAlgorithmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 线面缓冲区与管线叠加分析求交，结果与道路线判断不相交。 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Service("GapAlgorithmImpl")
public class GapAlgorithmImpl extends ServiceImpl<GapAlgorithmMapper, GapAlgorithm> implements IGapAlgorithmService {

    @Autowired
    private GapAlgorithmMapper gapAlgorithmMapper;

    /**
    * 添加线面缓冲区与管线叠加分析求交，结果与道路线判断不相交。信息
    *
    * @param gapAlgorithm
    * @return int
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public int insert(GapAlgorithm gapAlgorithm) {

        //gapAlgorithm.setUserId(JavaUtils.getUUID36());
        //gapAlgorithm.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return gapAlgorithmMapper.insert(gapAlgorithm);
    }

    /**
    * 删除线面缓冲区与管线叠加分析求交，结果与道路线判断不相交。信息
    *
    * @param gapAlgorithmId
    * @return int
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public int delete(String gapAlgorithmId) {
        return gapAlgorithmMapper.deleteById(gapAlgorithmId);
    }

    /**
    * 更新线面缓冲区与管线叠加分析求交，结果与道路线判断不相交。信息
    *
    * @param gapAlgorithm
    * @return int
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public int update(GapAlgorithm gapAlgorithm) {
        return gapAlgorithmMapper.updateById(gapAlgorithm);
    }

    /**
    * 全部查询
    *
    * @param gapAlgorithm
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.GapAlgorithm>
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public List<GapAlgorithm> list(GapAlgorithm gapAlgorithm) {

        QueryWrapper<GapAlgorithm> queryWrapper = new QueryWrapper<>();

        return gapAlgorithmMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<GapAlgorithm> gapAlgorithmIPage = new Page<>();
        gapAlgorithmIPage.setCurrent(current);
        gapAlgorithmIPage.setSize(size);

        QueryWrapper<GapAlgorithm> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return gapAlgorithmMapper.selectPage(gapAlgorithmIPage, queryWrapper);
    }


}
