package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 实体衍生 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_entitytype_derive")
@ApiModel(value="EntitytypeDerive对象", description="实体衍生")
public class EntitytypeDerive implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @TableField("entity_type_pkid")
    private String entitytypepkid;

    @TableField("be_entity_type_pkid")
    private String beentitytypepkid;

    @TableField("update_user")
    private String updateuser;

    @TableField("update_time")
    private Timestamp updatetime;

    @ApiModelProperty(value = "实体类型名称")
    @TableField("entity_type_name")
    private String entitytypename;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;

    @TableField("entitytype_derive_rules_name")
    @ApiModelProperty(value = "实体衍生规则名称")
    private String entitytypederiverulesname;

    @ApiModelProperty(value = "小里程处延伸距离")
    @TableField("min_extend")
    private Integer minextend;

    @ApiModelProperty(value = "大里程处延伸距离")
    @TableField("max_extend")
    private Integer maxextend;

    @ApiModelProperty(value = "水平偏移方向")
    @TableField("horizontal_skewing_direction")
    private Integer horizontalskewingdirection;

    @ApiModelProperty(value = "偏移距离")
    @TableField("horizontal_skewing_dist")
    private Integer horizontalskewingdist;



}
