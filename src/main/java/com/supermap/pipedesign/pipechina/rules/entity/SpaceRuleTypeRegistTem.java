package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * $空间规则类型注册表，如禁止通过区域、宜、应避开区域、间距规定、交叉规定...... 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_space_rule_type_regist_tem")
@ApiModel(value="SpaceRuleTypeRegistTem对象", description="$空间规则类型注册表，如禁止通过区域、宜、应避开区域、间距规定、交叉规定......")
public class SpaceRuleTypeRegistTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @TableField("rule_name")
    private String ruleName;

    @TableField("rule_type")
    private String ruleType;


}
