package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 实体合并的合并列 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_entitytype_merge_entitytype")
@ApiModel(value="EntitytypeMergeEntitytype对象", description="实体合并的合并列")
public class EntitytypeMergeEntitytype implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @TableField("merge_pkid")
    private String mergepkid;

    @TableField("entity_type_pkid")
    private String entitytypepkid;

    @TableField("priority")
    private Integer priority;

    @ApiModelProperty(value = "实体名称")
    @TableField("entity_type_enname")
    private String entitytypeenname;

    @ApiModelProperty(value = "实体别名")
    @TableField("entitytype_alias")
    private String entitytypealias;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
