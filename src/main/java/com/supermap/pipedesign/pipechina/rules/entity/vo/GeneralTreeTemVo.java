package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 通用图目录树 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "GeneralTreeTem对象", description = "通用图目录树")
public class GeneralTreeTemVo implements Serializable {

    @TableId(value = "pkid")
    private String pkid;

    @ApiModelProperty(value = "父id")
    private String pid;

    @ApiModelProperty(value = "目录名称")
    private String name;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    private GeneralDrawingTemVo child;

    private List<GeneralTreeTemVo> children;

}
