package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.HydraulicProtectionTem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.vo.HydraulicProtectionTemVo;
import org.springframework.stereotype.Repository;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 水工保护总量表-模型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Repository
public interface IHydraulicProtectionTemService extends IService<HydraulicProtectionTem> {

 /**
 * 添加水工保护总量表-模型信息
 *
 * @param hydraulicProtectionTem
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int insert(HydraulicProtectionTem hydraulicProtectionTem);

 /**
 * 删除水工保护总量表-模型信息
 *
 * @param hydraulicProtectionTemId
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int delete(String hydraulicProtectionTemId);

 /**
 * 更新水工保护总量表-模型信息
 *
 * @param hydraulicProtectionTem
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int update(@Valid HydraulicProtectionTemVo hydraulicProtectionTem);

 /**
  * 全部查询
  *
  * @param hydraulicProtectionTem
  * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.HydraulicProtectionTem>
  * @Date 2023-03-21
  * @auther eomer
  */
 List<List<Map<String, Object>>> list(HydraulicProtectionTem hydraulicProtectionTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-21
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


    List titleList();
}
