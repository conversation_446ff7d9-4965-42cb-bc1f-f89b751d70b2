package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralSectionSlope;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralSectionSlopeTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.GeneralSectionSlopeProVo;
import org.springframework.stereotype.Repository;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 一般段放坡规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Repository
public interface IGeneralSectionSlopeService extends IService<GeneralSectionSlope> {

 /**
 * 添加一般段放坡规则信息
 *
 * @param generalSectionSlope
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int insert(GeneralSectionSlope generalSectionSlope);

 /**
 * 删除一般段放坡规则信息
 *
 * @param generalSectionSlopeId
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int delete(String generalSectionSlopeId);

 /**
 * 更新一般段放坡规则信息
 *
 * @param generalSectionSlope
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int update(@Valid GeneralSectionSlopeProVo generalSectionSlope);

 /**
 * 全部查询
 *
 * @param type
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.GeneralSectionSlope>
 * @Date 2023-03-21
 * @auther eomer
 */
 List<GeneralSectionSlope> list(Integer type, String projectId);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-21
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);



}
