package com.supermap.pipedesign.pipechina.rules.entity.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 管材设计规则 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PipeDesigntem对象", description="管材设计规则")
public class PipeDesigntemVoList implements Serializable {

    private List<PipeDesigntemVo> pipeDesigntemVoList;

}
