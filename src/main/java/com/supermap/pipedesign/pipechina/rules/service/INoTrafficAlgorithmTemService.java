package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.NoTrafficAlgorithmTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Repository
public interface INoTrafficAlgorithmTemService extends IService<NoTrafficAlgorithmTem> {

 /**
 * 添加信息
 *
 * @param noTrafficAlgorithmTem
 * @return int
 * @Date 2023-01-29
 * @auther eomer
 */
 int insert(NoTrafficAlgorithmTem noTrafficAlgorithmTem);

 /**
 * 删除信息
 *
 * @param noTrafficAlgorithmTemId
 * @return int
 * @Date 2023-01-29
 * @auther eomer
 */
 int delete(String noTrafficAlgorithmTemId);

 /**
 * 更新信息
 *
 * @param noTrafficAlgorithmTem
 * @return int
 * @Date 2023-01-29
 * @auther eomer
 */
 int update(NoTrafficAlgorithmTem noTrafficAlgorithmTem);

 /**
 * 全部查询
 *
 * @param noTrafficAlgorithmTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.NoTrafficAlgorithmTem>
 * @Date 2023-01-29
 * @auther eomer
 */
 List<NoTrafficAlgorithmTem> list(NoTrafficAlgorithmTem noTrafficAlgorithmTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-29
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
