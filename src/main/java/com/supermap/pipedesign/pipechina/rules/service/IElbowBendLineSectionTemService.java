package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.rules.entity.ElbowBendLineSectionTem;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Repository
public interface IElbowBendLineSectionTemService extends IService<ElbowBendLineSectionTem> {

    List<Dictionary> list(String titleTem);

    /**
 * 添加信息
 *
 * @param
 * @return int
 * @Date 2023-03-15
 * @auther eomer
 */
 int insert(Map<String,Object> map);

 /**
 * 删除信息
 *
 * @param elbowBendLineSectionTemId
 * @return int
 * @Date 2023-03-15
 * @auther eomer
 */
 int delete(String elbowBendLineSectionTemId);

 /**
 * 更新信息
 *
 * @param
 * @return int
 * @Date 2023-03-15
 * @auther eomer
 */
 int update(ElbowBendLineSectionTem elbowBendLineSectionTem);

 /**
 * 全部查询
 *
 * @param elbowBendLineSectionTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.ElbowBendLineSectionTem>
 * @Date 2023-03-15
 * @auther eomer
 */
 List<Map<String,Object>> list(ElbowBendLineSectionTem elbowBendLineSectionTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-15
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
