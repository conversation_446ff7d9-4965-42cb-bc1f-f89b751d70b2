package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.rules.dao.ElbowBendLineSectionMapper;
import com.supermap.pipedesign.pipechina.rules.entity.ElbowBendLineSection;
import com.supermap.pipedesign.pipechina.rules.service.IElbowBendLineSectionService;
import com.supermap.tools.gson.GsonUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Service("ElbowBendLineSectionImpl")
@RequiredArgsConstructor
public class ElbowBendLineSectionImpl extends ServiceImpl<ElbowBendLineSectionMapper, ElbowBendLineSection> implements IElbowBendLineSectionService {

    private final ElbowBendLineSectionMapper elbowBendLineSectionMapper;

    /**
    * 添加信息
    *
    * @param
    * @return int
    * @Date 2023-03-15
    * @auther eomer
    */
    @Override
    public int insert(Map<String,Object> map,String projectId) {
        QueryWrapper<ElbowBendLineSection> queryWrapper = new QueryWrapper<ElbowBendLineSection>();
        queryWrapper.eq("project_id", projectId);
        elbowBendLineSectionMapper.delete(queryWrapper);
        List<Map> list = GsonUtil.ObjectToList(map.get("list"), Map.class);
        for (Map<String, Object> mapTem : list) {
            int row = (int) Math.round((Double) mapTem.get("row"));
            mapTem.remove("row");
            for (Map.Entry<String, Object> key : mapTem.entrySet()) {
                ElbowBendLineSection tem = new ElbowBendLineSection();
                tem.setPkid(UUID.randomUUID().toString());
                tem.setBendCode(key.getKey());
                tem.setFieldValue(key.getValue().toString());
                tem.setRow(row);
                tem.setProjectId(projectId);
                elbowBendLineSectionMapper.insert(tem);

            }
        }
        return 1;
    }

    /**
    * 删除信息
    *
    * @param elbowBendLineSectionId
    * @return int
    * @Date 2023-03-15
    * @auther eomer
    */
    @Override
    public int delete(String elbowBendLineSectionId) {
        return elbowBendLineSectionMapper.deleteById(elbowBendLineSectionId);
    }

    /**
    * 更新信息
    *
    * @param elbowBendLineSection
    * @return int
    * @Date 2023-03-15
    * @auther eomer
    */
    @Override
    public int update(ElbowBendLineSection elbowBendLineSection) {
        return elbowBendLineSectionMapper.updateById(elbowBendLineSection);
    }

    /**
     * 全部查询
     *
     * @param elbowBendLineSectionTem
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.ElbowBendLineSectionTem>
     * @Date 2023-03-15
     * @auther eomer
     */
    @Override
    public List<Map<String,Object>> list(ElbowBendLineSection elbowBendLineSectionTem, String projectId) {

        QueryWrapper<ElbowBendLineSection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id",projectId);
        List<ElbowBendLineSection> elbowBendLineSectionTems = elbowBendLineSectionMapper.selectList(queryWrapper);
        List<List<ElbowBendLineSection> >elbowBendLineSectionTemList = new ArrayList<>();
        //elbowBendLineSectionTemList.add(new ArrayList<>());
        //行号分组循环
        int count= 0;
        int row=0;
        Map<Integer, Integer> map = new HashMap<>();
        for (ElbowBendLineSection lineSectionTem : elbowBendLineSectionTems) {
            if (lineSectionTem.getRow()==1){
                row++;
            }
        }
        for (ElbowBendLineSection bendLineSectionTem : elbowBendLineSectionTems) {

            Integer bendLineSection = map.get(bendLineSectionTem.getRow());
            if (bendLineSection==null){
                map.put(bendLineSectionTem.getRow(),count);
                ArrayList<ElbowBendLineSection> objects = new ArrayList<>();
                objects.add(bendLineSectionTem);
                elbowBendLineSectionTemList.add(objects);
            }else{
                List<ElbowBendLineSection> objects = elbowBendLineSectionTemList.get(count);
                objects.add(bendLineSectionTem);

            }
            if (elbowBendLineSectionTemList.get(count).size()==row){
                count ++;
                //elbowBendLineSectionTemList.add(new ArrayList<>());
            }
        }
        List<Map<String,Object>> mapList=new ArrayList<>();
        for (List<ElbowBendLineSection> sectionTemList : elbowBendLineSectionTemList) {
            Map<String,Object> data= new HashMap<>();

            for (ElbowBendLineSection bendLineSectionTem : sectionTemList) {
                data.put(bendLineSectionTem.getBendCode(),bendLineSectionTem.getFieldValue());
            }
            mapList.add(data);
        }
        return mapList;
    }


    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-15
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<ElbowBendLineSection> elbowBendLineSectionIPage = new Page<>();
        elbowBendLineSectionIPage.setCurrent(current);
        elbowBendLineSectionIPage.setSize(size);

        QueryWrapper<ElbowBendLineSection> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return elbowBendLineSectionMapper.selectPage(elbowBendLineSectionIPage, queryWrapper);
    }


}
