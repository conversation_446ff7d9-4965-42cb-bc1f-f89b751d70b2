package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.Field;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 质检字段表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface IFieldService extends IService<Field> {

 /**
 * 添加质检字段表信息
 *
 * @param field
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(Field field);

 /**
 * 删除质检字段表信息
 *
 * @param fieldId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String fieldId);

 /**
 * 更新质检字段表信息
 *
 * @param field
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(Field field);

 /**
 * 全部查询
 *
 * @param field
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.Field>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<Field> list(Field field);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
