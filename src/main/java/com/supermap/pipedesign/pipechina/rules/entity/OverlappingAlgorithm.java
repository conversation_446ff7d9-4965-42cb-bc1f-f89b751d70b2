package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 管道与公路、铁路、已建管道交叉算法 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_overlapping_algorithm")
@ApiModel(value="OverlappingAlgorithm对象", description="管道与公路、铁路、已建管道交叉算法")
public class OverlappingAlgorithm implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "算法名称")
    @TableField("algorithm_name")
    private String algorithmName;

    @ApiModelProperty(value = "所属规则小类（所属法律法规）")
    @TableField("ruleid")
    private String ruleid;

    @ApiModelProperty(value = "规则范围")
    @TableField("layer")
    private String layer;

    @ApiModelProperty(value = "三维中用到")
    @TableField("distance")
    private Integer distance;

    @ApiModelProperty(value = "管道与线的最小交叉角度规定")
    @TableField("angle")
    private Integer angle;

}
