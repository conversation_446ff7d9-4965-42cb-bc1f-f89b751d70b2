package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_elbow_bend_line_section")
@ApiModel(value="ElbowBendLineSection对象", description="")
public class ElbowBendLineSection implements Serializable {

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "字典code")
    @TableField("bend_code")
    private String bendCode;

    @ApiModelProperty(value = "值")
    @TableField("field_value")
    private String fieldValue;

    @ApiModelProperty(value = "行号")
    @TableField("row")
    private Integer row;

    @ApiModelProperty(value = "表头序号")
    @TableField("filed_index")
    private Integer filedIndex;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;


}
