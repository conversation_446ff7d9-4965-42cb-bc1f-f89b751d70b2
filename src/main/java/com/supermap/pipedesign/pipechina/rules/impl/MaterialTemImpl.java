package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.UserMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.rules.dao.EUFunFieldTemMapper;
import com.supermap.pipedesign.pipechina.rules.dao.EUFunTemMapper;
import com.supermap.pipedesign.pipechina.rules.dao.MaterialTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.EUFunFieldTem;
import com.supermap.pipedesign.pipechina.rules.entity.EUFunTem;
import com.supermap.pipedesign.pipechina.rules.entity.MaterialTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.EUFunTemVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.MeterialsTemVo;
import com.supermap.pipedesign.pipechina.rules.service.IMaterialTemService;
import com.supermap.tools.base.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.util.*;


/**
 * <p>
 * 材料表计算模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-11
 */
@Service("MaterialTemImpl")
public class MaterialTemImpl extends ServiceImpl<MaterialTemMapper, MaterialTem> implements IMaterialTemService {

    private final MaterialTemMapper materialTemMapper;

    private final UserMapper userMapper;
    private final EUFunTemMapper euFunTemMapper;

    private final EUFunFieldTemMapper euFunFieldTemMapper;

    public MaterialTemImpl(MaterialTemMapper materialTemMapper, UserMapper userMapper, EUFunTemMapper euFunTemMapper, EUFunFieldTemMapper euFunFieldTemMapper) {
        this.materialTemMapper = materialTemMapper;
        this.userMapper = userMapper;
        this.euFunTemMapper = euFunTemMapper;
        this.euFunFieldTemMapper = euFunFieldTemMapper;
    }

    /**
     * 添加材料表计算模板信息
     *
     * @param vo
     * @return int
     * @Date 2023-03-11
     * @auther eomer
     */
    @Override
    public int insert(MeterialsTemVo vo, User user) {
        // 插入工程量统计模板
        String engTemId = UUID.randomUUID().toString();
        MaterialTem engTem = vo.getMaterialTem();
        engTem.setPkid(engTemId);
        engTem.setSequenceState(1);
        engTem.setCreateTime(new Date(Calendar.getInstance().getTimeInMillis()));
        engTem.setUpdateTime(new Date(Calendar.getInstance().getTimeInMillis()));
        engTem.setCreateId(user.getUserid());
        engTem.setUpdateId(user.getUserid());
        engTem.setCreateName(user.getUsername());
        engTem.setUpdateName(user.getUsername());
        materialTemMapper.insert(engTem);
        // 插入 算法模板、算法字段模板
        List<EUFunTemVo> funTemVoList = vo.getFunTemVoList();
        for (EUFunTemVo funTemVo : funTemVoList) {
            EUFunTem funTem = funTemVo.getFunTem();
            String funTemId = UUID.randomUUID().toString();
            funTem.setPkid(funTemId);
            funTem.setEngineeringUnitId(engTemId);
            euFunTemMapper.insert(funTem);
            List<EUFunFieldTem> fieldTemList = funTemVo.getFieldTemList();
            for (EUFunFieldTem fieldTem : fieldTemList) {
                fieldTem.setPkid(UUID.randomUUID().toString());
                fieldTem.setEUFunId(funTemId);
                euFunFieldTemMapper.insert(fieldTem);
            }
        }
        return 1;
    }

    /**
     * 删除材料表计算模板信息
     *
     * @param materialTemId
     * @return int
     * @Date 2023-03-11
     * @auther eomer
     */
    @Override
    public int delete(String materialTemId) {
        QueryWrapper<EUFunTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("engineering_unit_id",materialTemId);
        List<EUFunTem> euFunTemList = euFunTemMapper.selectList(queryWrapper);
        for (EUFunTem fun : euFunTemList){
            QueryWrapper<EUFunFieldTem> queryWrap = new QueryWrapper<>();
            queryWrap.eq("qi_e_u_fun_id",fun.getPkid());
            euFunFieldTemMapper.delete(queryWrap);
            euFunTemMapper.deleteById(fun.getPkid());
        }
        return materialTemMapper.deleteById(materialTemId);
    }

    /**
     * 更新材料表计算模板信息
     *
     * @param vo
     * @param userId
     * @return int
     * @Date 2023-03-11
     * @auther eomer
     */
    @Override
    public int update(MeterialsTemVo vo, User user) {
        MaterialTem engTem = vo.getMaterialTem();
        engTem.setUpdateName(user.getUsername());
        engTem.setUpdateId(user.getUserid());
        engTem.setUpdateTime(new Date(Calendar.getInstance().getTimeInMillis()));
        materialTemMapper.updateById(engTem);
        //删除 算法模板、算法字段模板
        QueryWrapper<EUFunTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("engineering_unit_id",engTem.getPkid());
        List<EUFunTem> euFunTemList = euFunTemMapper.selectList(queryWrapper);
        for (EUFunTem fun : euFunTemList){
            QueryWrapper<EUFunFieldTem> queryWrap = new QueryWrapper<>();
            queryWrap.eq("qi_e_u_fun_id",fun.getPkid());
            euFunFieldTemMapper.delete(queryWrap);
            euFunTemMapper.deleteById(fun.getPkid());
        }

        //插入 算法模板、算法字段模板
        List<EUFunTemVo> funTemVoList = vo.getFunTemVoList();
        for (EUFunTemVo funTemVo : funTemVoList){
            EUFunTem funTem = funTemVo.getFunTem();
            String funTemId = UUID.randomUUID().toString();
            funTem.setPkid(funTemId);
            funTem.setEngineeringUnitId(engTem.getPkid());
            euFunTemMapper.insert(funTem);
            List<EUFunFieldTem> fieldTemList = funTemVo.getFieldTemList();
            for (EUFunFieldTem fieldTem : fieldTemList){
                fieldTem.setPkid(UUID.randomUUID().toString());
                fieldTem.setEUFunId(funTemId);
                euFunFieldTemMapper.insert(fieldTem);
            }
        }
        return 1;
    }

    /**
     * 全部查询
     *
     * @param stageCode
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.MaterialTem>
     * @Date 2023-03-11
     * @auther eomer
     */
    @Override
    public List<MaterialTem> list(String stageCode, String subjectId) {
        QueryWrapper<MaterialTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("subject_id",subjectId);
        queryWrapper.notLike("stage_inner_code",stageCode);
        return materialTemMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @param stageCode
     * @param stageCode
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * @Date 2023-03-11
     * @auther eomer
     */
    @Override
    public IPage pageList(long current, long size, String stageCode, String subjectId, String searchName) {

        IPage<MaterialTem> engPage = new Page<>();
        engPage.setCurrent(current);
        engPage.setSize(size);

        QueryWrapper<MaterialTem> queryWrapper = new QueryWrapper<>();

        if (JavaUtils.isNotEmtryOrNull(stageCode)){
            queryWrapper.like("stage_inner_code",stageCode);
        }
        if (JavaUtils.isNotEmtryOrNull(subjectId)){
            queryWrapper.eq("subject_id", subjectId);
        }
        if (JavaUtils.isNotEmtryOrNull(searchName)){
            queryWrapper.and(qw ->
                    qw.like("name",searchName)
                            .or().like("describe",searchName)
                            .or().like("updateName",searchName));
        }
        IPage<MaterialTem> engineeringUnitTemIPage = materialTemMapper.selectPage(engPage, queryWrapper);
        List<MaterialTem> records = engineeringUnitTemIPage.getRecords();
        for (MaterialTem bo : records){
            String stageName = "";
            if (JavaUtils.isNotEmtryOrNull(bo.getStageInnerCode())) {
                String[] split = bo.getStageInnerCode().split(",");
                for (int i = 0; i < split.length; i++) {
                    if (split[i].equals("0")) {
                        stageName += "、" + "预可研阶段";
                        continue;
                    }
                    if (split[i].equals("1")) {
                        stageName += "、" + "可研阶段";
                        continue;
                    }
                    if (split[i].equals("2")) {
                        stageName += "、" + "初设阶段";
                        continue;
                    }
                    if (split[i].equals("3")) {
                        stageName += "、" + "施工图阶段";
                        continue;
                    }
                    if (split[i].equals("4")) {
                        stageName += "、" + "竣工图阶段";
                    }
                }
                bo.setStageInnerCode(stageName.substring(1));
            }
        }
        return engineeringUnitTemIPage;
    }

    /**
     * 材料表计算模板-删除规则列表
     * @param stageCode
     * @param materialId
     * @return
     */
    @Override
    public int deleMaterialModel(String stageCode, String materialId) {
        MaterialTem engineeringUnitTem = materialTemMapper.selectById(materialId);
        ArrayList<String> list = new ArrayList<>(Collections.singletonList(engineeringUnitTem.getStageInnerCode()));
        for (int i = 0; i < list.size(); i++){
            if (list.get(i).equals(stageCode)){
                list.remove(i);
                break;
            }
        }
        if (list.size() >0){
            engineeringUnitTem.setStageInnerCode(StringUtils.join(list,","));
        }else {
            engineeringUnitTem.setStageInnerCode(null);
        }
        return materialTemMapper.updateById(engineeringUnitTem);
    }

    /**
     * 材料表计算模板-添加规则列表
     * @param stageCode
     * @param materialId
     * @return
     */
    @Override
    public int addMaterialCode(String stageCode, String materialId) {
        String[] engIdList = materialId.split(",");
        for (String src : engIdList){
            MaterialTem engineeringUnitTem = materialTemMapper.selectById(src);
            if (JavaUtils.isNotEmtryOrNull(engineeringUnitTem.getStageInnerCode())){
                engineeringUnitTem.setStageInnerCode(engineeringUnitTem.getStageInnerCode()+","+stageCode);
            }else{
                engineeringUnitTem.setStageInnerCode(stageCode);
            }
            materialTemMapper.updateById(engineeringUnitTem);
        }
        return 1;
    }


    /**
     * @param materialId
     * 根据工程量id查详情
     * <AUTHOR>
     * @Description
     * @Date 2023/3/10 11:15
     **/
    @Override
    public MeterialsTemVo selInfoByMaterialId(String materialId) {
        //查询 工程量模板
        MeterialsTemVo vo = new MeterialsTemVo();
        MaterialTem engTem = materialTemMapper.selectById(materialId);
        vo.setMaterialTem(engTem);
        //查询 算法模板
        List<EUFunTemVo> funTemVoList = new ArrayList<>();

        QueryWrapper<EUFunTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("engineering_unit_id", materialId);
        List<EUFunTem> euFunTemList = euFunTemMapper.selectList(queryWrapper);
        for (EUFunTem funTem : euFunTemList){
            QueryWrapper<EUFunFieldTem> queryWrappe = new QueryWrapper<>();
            queryWrappe.eq("qi_e_u_fun_id",funTem.getPkid());
            List<EUFunFieldTem> euFunFieldTems = euFunFieldTemMapper.selectList(queryWrappe);
            EUFunTemVo funTemVo = new EUFunTemVo();
            funTemVo.setFieldTemList(euFunFieldTems);
            funTemVo.setFunTem(funTem);
            funTemVoList.add(funTemVo);
        }

        vo.setFunTemVoList(funTemVoList);

        return vo;
    }
}
