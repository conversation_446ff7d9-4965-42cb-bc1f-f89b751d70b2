package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.rules.dao.GeneralTreeTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralTreeTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.GeneralTreeTemVo;
import com.supermap.pipedesign.pipechina.rules.service.IGeneralTreeTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 通用图目录树 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Service("GeneralTreeTemImpl")
public class GeneralTreeTemImpl extends ServiceImpl<GeneralTreeTemMapper, GeneralTreeTem> implements IGeneralTreeTemService {

    @Autowired
    private GeneralTreeTemMapper generalTreeTemMapper;

    /**
    * 添加通用图目录树信息
    *
    * @param generalTreeTem
    * @return int
    * @Date 2023-03-29
    * @auther eomer
    */
    @Override
    public int insert(GeneralTreeTem generalTreeTem) {

        //generalTreeTem.setUserId(JavaUtils.getUUID36());
        //generalTreeTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return generalTreeTemMapper.insert(generalTreeTem);
    }

    /**
    * 删除通用图目录树信息
    *
    * @param generalTreeTemId
    * @return int
    * @Date 2023-03-29
    * @auther eomer
    */
    @Override
    public int delete(String generalTreeTemId) {
        GeneralTreeTem generalTreeTem = generalTreeTemMapper.selectById(generalTreeTemId);

        return generalTreeTemMapper.deleteById(generalTreeTemId);
    }

    /**
    * 更新通用图目录树信息
    *
    * @param generalTreeTem
    * @return int
    * @Date 2023-03-29
    * @auther eomer
    */
    @Override
    public int update(GeneralTreeTem generalTreeTem) {
        return generalTreeTemMapper.updateById(generalTreeTem);
    }

    @Override
    public List<GeneralTreeTemVo> listTree() {
        String pkid = "10001";
        List<GeneralTreeTemVo> generalTreeTemVos = generalTreeTemMapper.listTree(pkid);
        return generalTreeTemVos;
    }


}
