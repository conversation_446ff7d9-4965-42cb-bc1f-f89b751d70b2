package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.rules.dao.EUFunFieldTemMapper;
import com.supermap.pipedesign.pipechina.rules.dao.EUFunTemMapper;
import com.supermap.pipedesign.pipechina.rules.dao.EngineeringUnitTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.EUFunFieldTem;
import com.supermap.pipedesign.pipechina.rules.entity.EUFunTem;
import com.supermap.pipedesign.pipechina.rules.entity.EngineeringUnitTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.EUFunTemVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.EngineeringUnitTemVo;
import com.supermap.pipedesign.pipechina.rules.service.IEngineeringUnitTemService;
import com.supermap.tools.base.JavaUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.util.*;


/**
 * <p>
 * 工程量统计模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Service("EngineeringUnitTemImpl")
@RequiredArgsConstructor
public class EngineeringUnitTemImpl extends ServiceImpl<EngineeringUnitTemMapper, EngineeringUnitTem> implements IEngineeringUnitTemService {

    private final EngineeringUnitTemMapper engineeringUnitTemMapper;

    private final EUFunTemMapper euFunTemMapper;

    private final EUFunFieldTemMapper euFunFieldTemMapper;

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @Date 2023-03-09
     * @auther eomer
     */
    @Override
    public IPage pageList(long current, long size, String stageCode, String subjectId, String searchName) {

        IPage<EngineeringUnitTem> engPage = new Page<>();
        engPage.setCurrent(current);
        engPage.setSize(size);

        QueryWrapper<EngineeringUnitTem> queryWrapper = new QueryWrapper<>();

        if (JavaUtils.isNotEmtryOrNull(stageCode)){
            queryWrapper.like("stage_inner_code",stageCode);
        }
        if (JavaUtils.isNotEmtryOrNull(subjectId)){
            queryWrapper.eq("subject_id", subjectId);
        }
        if (JavaUtils.isNotEmtryOrNull(searchName)){
            queryWrapper.and(qw ->
                    qw.like("name",searchName)
                            .or().like("describe",searchName)
                            .or().like("updateName",searchName));
        }
        IPage<EngineeringUnitTem> engineeringUnitTemIPage = engineeringUnitTemMapper.selectPage(engPage, queryWrapper);
        List<EngineeringUnitTem> records = engineeringUnitTemIPage.getRecords();
        for (EngineeringUnitTem bo : records){
            String stageName = "";
            if (JavaUtils.isNotEmtryOrNull(bo.getStageInnerCode())) {
                String[] split = bo.getStageInnerCode().split(",");
                for (int i = 0; i < split.length; i++) {
                    if (split[i].equals("0")) {
                        stageName += "、" + "预可研阶段";
                        continue;
                    }
                    if (split[i].equals("1")) {
                        stageName += "、" + "可研阶段";
                        continue;
                    }
                    if (split[i].equals("2")) {
                        stageName += "、" + "初设阶段";
                        continue;
                    }
                    if (split[i].equals("3")) {
                        stageName += "、" + "施工图阶段";
                        continue;
                    }
                    if (split[i].equals("4")) {
                        stageName += "、" + "竣工图阶段";
                    }
                }
                bo.setStageInnerCode(stageName.substring(1));
            }
        }
        return engineeringUnitTemIPage;
    }

    /**
    * 添加工程量统计模板信息
    *
    * @param vo
    * @return int
    * @Date 2023-03-09
    * @auther eomer
    */
    @Override
    public int insert(EngineeringUnitTemVo vo, User user) {
        //插入工程量统计模板
        String engTemId = UUID.randomUUID().toString();
        EngineeringUnitTem engTem = vo.getEngTem();
        engTem.setPkid(engTemId);
        engTem.setSequenceState(1);
        engTem.setCreateTime(new Date(Calendar.getInstance().getTimeInMillis()));
        engTem.setUpdateTime(new Date(Calendar.getInstance().getTimeInMillis()));
        engTem.setCreateId(user.getUserid());
        engTem.setUpdateId(user.getUserid());
        engTem.setCreateName(user.getUsername());
        engTem.setUpdateName(user.getUsername());
        engineeringUnitTemMapper.insert(engTem);
        //插入 算法模板、算法字段模板
        List<EUFunTemVo> funTemVoList = vo.getFunTemVoList();
        for (EUFunTemVo funTemVo : funTemVoList){
            EUFunTem funTem = funTemVo.getFunTem();
            String funTemId = UUID.randomUUID().toString();
            funTem.setPkid(funTemId);
            funTem.setEngineeringUnitId(engTemId);
            euFunTemMapper.insert(funTem);
            List<EUFunFieldTem> fieldTemList = funTemVo.getFieldTemList();
            for (EUFunFieldTem fieldTem : fieldTemList){
                if (JavaUtils.isEmtryOrNull(fieldTem.getEntityTypesId())){
                    continue;
                }
                fieldTem.setPkid(UUID.randomUUID().toString());
                fieldTem.setEUFunId(funTemId);
                euFunFieldTemMapper.insert(fieldTem);
            }
        }
        return 1;
    }

    /**
     * @param engId
     * 根据工程量id查详情
     * <AUTHOR>
     * @Description
     * @Date 2023/3/10 11:15
     **/
    @Override
    public EngineeringUnitTemVo selInfoByEngId(String engId) {
        //查询 工程量模板
        EngineeringUnitTemVo vo = new EngineeringUnitTemVo();
        EngineeringUnitTem engTem = engineeringUnitTemMapper.selectById(engId);
        vo.setEngTem(engTem);
        //查询 算法模板
        List<EUFunTemVo> funTemVoList = new ArrayList<>();

        QueryWrapper<EUFunTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("engineering_unit_id", engId);
        List<EUFunTem> euFunTemList = euFunTemMapper.selectList(queryWrapper);
        for (EUFunTem funTem : euFunTemList){
            EUFunTemVo funTemVo = new EUFunTemVo();
            QueryWrapper<EUFunFieldTem> queryWrappe = new QueryWrapper<>();
            queryWrappe.eq("qi_e_u_fun_id",funTem.getPkid());
            List<EUFunFieldTem> euFunFieldTems = euFunFieldTemMapper.selectList(queryWrappe);
            funTemVo.setFieldTemList(euFunFieldTems);
            funTemVo.setFunTem(funTem);
            funTemVoList.add(funTemVo);
        }

        vo.setFunTemVoList(funTemVoList);

        return vo;
    }

    /**
     * @param funTemId 算法函数id
     * 根据算法函数id查算法详情
     * <AUTHOR>
     * @Description
     * @Date 2023/3/10 11:47
     **/
    @Override
    public EUFunTemVo selInfoByFunTemId(String funTemId) {
        EUFunTemVo vo = new EUFunTemVo();
        EUFunTem funTem = euFunTemMapper.selectById(funTemId);
        vo.setFunTem(funTem);
        QueryWrapper<EUFunFieldTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("qi_e_u_fun_id",funTemId);
        List<EUFunFieldTem> euFunFieldTems = euFunFieldTemMapper.selectList(queryWrapper);
        vo.setFieldTemList(euFunFieldTems);
        return vo;
    }

    /**
     * 更新工程量
     * <AUTHOR>
     * @Description
     * @Date 2023/3/10 11:55
     **/
    @Override
    public int updateEng(EngineeringUnitTemVo vo, User user) {
        EngineeringUnitTem engTem = vo.getEngTem();
        engTem.setUpdateName(user.getUsername());
        engTem.setUpdateId(user.getUserid());
        engTem.setUpdateTime(new Date(Calendar.getInstance().getTimeInMillis()));
        engineeringUnitTemMapper.updateById(engTem);
        //删除 算法模板、算法字段模板
        QueryWrapper<EUFunTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("engineering_unit_id",engTem.getPkid());
        List<EUFunTem> euFunTemList = euFunTemMapper.selectList(queryWrapper);
        for (EUFunTem fun : euFunTemList){
            QueryWrapper<EUFunFieldTem> queryWrap = new QueryWrapper<>();
            queryWrap.eq("qi_e_u_fun_id",fun.getPkid());
            euFunFieldTemMapper.delete(queryWrap);
            euFunTemMapper.deleteById(fun.getPkid());
        }

        //插入 算法模板、算法字段模板
        List<EUFunTemVo> funTemVoList = vo.getFunTemVoList();
        for (EUFunTemVo funTemVo : funTemVoList){
            EUFunTem funTem = funTemVo.getFunTem();
            String funTemId = UUID.randomUUID().toString();
            funTem.setPkid(funTemId);
            funTem.setEngineeringUnitId(engTem.getPkid());
            euFunTemMapper.insert(funTem);
            List<EUFunFieldTem> fieldTemList = funTemVo.getFieldTemList();
            for (EUFunFieldTem fieldTem : fieldTemList){
                fieldTem.setPkid(UUID.randomUUID().toString());
                fieldTem.setEUFunId(funTemId);
                euFunFieldTemMapper.insert(fieldTem);
            }
        }
        return 1;
    }

    /**
    * 删除工程量统计模板信息
    *
    * @param engId
    * @return int
    * @Date 2023-03-09
    * @auther eomer
    */
    @Override
    public int deleteEng(String engId) {
        //删除 算法模板、算法字段模板
        QueryWrapper<EUFunTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("engineering_unit_id",engId);
        List<EUFunTem> euFunTemList = euFunTemMapper.selectList(queryWrapper);
        for (EUFunTem fun : euFunTemList){
            QueryWrapper<EUFunFieldTem> queryWrap = new QueryWrapper<>();
            queryWrap.eq("qi_e_u_fun_id",fun.getPkid());
            euFunFieldTemMapper.delete(queryWrap);
            euFunTemMapper.deleteById(fun.getPkid());
        }
        return engineeringUnitTemMapper.deleteById(engId);
    }

    /**
     * @param stageCode 施工阶段
     * @param subjectId 专业id
     * 查专业下非 stageCode（施工阶段）的工程量
     * <AUTHOR>
     * @Description
     * @Date 2023/3/10 14:50
     **/
    @Override
    public List<EngineeringUnitTem> selEngListBy(String stageCode, String subjectId) {
        QueryWrapper<EngineeringUnitTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("subject_id",subjectId);
        queryWrapper.notLike("stage_inner_code",stageCode);
        return engineeringUnitTemMapper.selectList(queryWrapper);
    }

    /**
     * @param stageCode 施工阶段id
     * @param engId 工程量id
     * <AUTHOR>
     * @Description
     * @Date 2023/3/10 14:59
     **/
    @Override
    public int addStageCode(String stageCode, String engId) {
        String[] engIdList = engId.split(",");
        for (String src : engIdList){
            EngineeringUnitTem engineeringUnitTem = engineeringUnitTemMapper.selectById(src);
            if (JavaUtils.isNotEmtryOrNull(engineeringUnitTem.getStageInnerCode())){
                engineeringUnitTem.setStageInnerCode(engineeringUnitTem.getStageInnerCode()+","+stageCode);
            }else{
                engineeringUnitTem.setStageInnerCode(stageCode);
            }
            engineeringUnitTemMapper.updateById(engineeringUnitTem);
        }
        return 1;
    }

    /**
     * @param stageCode 施工阶段id
     * @param engId 工程量id
     * 删除工程量规则 此施工阶段
     * <AUTHOR>
     * @Description
     * @Date 2023/3/10 14:59
     **/
    @Override
    public int deleEngModel(String stageCode, String engId) {
        EngineeringUnitTem engineeringUnitTem = engineeringUnitTemMapper.selectById(engId);
        ArrayList<String> list = new ArrayList<>(Arrays.asList(engineeringUnitTem.getStageInnerCode()));
        for (int i = 0; i < list.size(); i++){
            if (list.get(i).equals(stageCode)){
                list.remove(i);
                break;
            }
        }
        if (list.size() >0){
            engineeringUnitTem.setStageInnerCode(StringUtils.join(list,","));
        }else {
            engineeringUnitTem.setStageInnerCode(null);
        }
        return engineeringUnitTemMapper.updateById(engineeringUnitTem);
    }
}
