package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.rules.entity.DistanceAlgorithm;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 空间规则算法表，定义了算法关联的图层和运算规则与范围 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@Mapper
public interface DistanceAlgorithmMapper extends BaseMapper<DistanceAlgorithm> {

}
