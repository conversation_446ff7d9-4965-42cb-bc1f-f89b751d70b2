package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_lining_fracture_surface_tem")
@ApiModel(value = "LiningFractureSurfaceTem对象", description = "")
public class LiningFractureSurfaceTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid", type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "隧道衬砌断面名称")
    @TableField("section_name")
    private String sectionName;

    @ApiModelProperty(value = "隧道分级(1-2级，3级)")
    @TableField("tunnel_grade")
    private String tunnelGrade;

    @ApiModelProperty(value = "隧道衬砌围岩等级(Ⅰ级，Ⅱ级，III级，IV级，V级，Ⅵ级)")
    @TableField("liner_surrongding_rock_grade")
    private String linerSurrongdingRockGrade;

    @ApiModelProperty(value = "隧道衬砌断面净宽")
    @TableField("section_width")
    private Double sectionWidth;

    @ApiModelProperty(value = "隧道衬砌断面净高")
    @TableField("section_height")
    private Double sectionHeight;

    @ApiModelProperty(value = "隧道衬砌基准断面净宽(错车道时为正常隧道断面)")
    @TableField("base_section_width")
    private Double baseSectionWidth;

    @ApiModelProperty(value = "隧道衬砌基准断面净高(错车道时为正常隧道断面)")
    @TableField("base_section_height")
    private Double baseSectionHeight;

    @ApiModelProperty(value = "直墙高")
    @TableField("straight_wall_height")
    private Double straightWallHeight;

    @ApiModelProperty(value = "排水沟宽")
    @TableField("ditch_width")
    private Double ditchWidth;

    @ApiModelProperty(value = "排水沟深")
    @TableField("ditch_height")
    private Double ditchHeight;

    @ApiModelProperty(value = "喷混凝土层厚度H1")
    @TableField("shotcrete_thickness_h1")
    private Double shotcreteThicknessH1;

    @ApiModelProperty(value = "二次衬砌厚度H2")
    @TableField("concrete_thickness_h2")
    private Double concreteThicknessH2;

    @ApiModelProperty(value = "底板厚度H4")
    @TableField("base_board_h4")
    private Double baseBoardH4;

    @ApiModelProperty(value = "直墙埋深")
    @TableField("straight_wall_deep")
    private Double straightWallDeep;

    @ApiModelProperty(value = "锚杆部位(拱部，拱.墙)")
    @TableField("bolt_position")
    private String boltPosition;

    @ApiModelProperty(value = "锚杆直径")
    @TableField("bolt_diameter")
    private Double boltDiameter;

    @ApiModelProperty(value = "锚杆长度L1")
    @TableField("bolt_length")
    private Double boltLength;

    @ApiModelProperty(value = "锚杆间距")
    @TableField("bolt_space")
    private Double boltSpace;

    @ApiModelProperty(value = "钢筋网部位(拱部，拱.墙)")
    @TableField("reinforcement_mesh_position")
    private String reinforcementMeshPosition;

    @ApiModelProperty(value = "钢筋直径")
    @TableField("reinforcement_mesh_diameter")
    private Double reinforcementMeshDiameter;

    @ApiModelProperty(value = "钢筋间距")
    @TableField("reinforcement_mesh_space")
    private Double reinforcementMeshSpace;

    @ApiModelProperty(value = "净断面")
    @TableField("net_section")
    private Double netSection;

    @ApiModelProperty(value = "毛断面")
    @TableField("gorss_section")
    private Double gorssSection;

    @ApiModelProperty(value = "砼墙")
    @TableField("concrete_wall_volume")
    private Double concreteWallVolume;

    @ApiModelProperty(value = "砼拱")
    @TableField("concrete_arch_volume")
    private Double concreteArchVolume;

    @ApiModelProperty(value = "砼底板")
    @TableField("concrete_base_board_volume")
    private Double concreteBaseBoardVolume;

    @ApiModelProperty(value = "喷射砼")
    @TableField("shotcrete_volume")
    private Double shotcreteVolume;

    @ApiModelProperty(value = "半径R")
    @TableField("arch_radius")
    private Double archRadius;

    @ApiModelProperty(value = "圆心角")
    @TableField("arch_central_angle")
    private Double archCentralAngle;

    @ApiModelProperty(value = "是否底部仰拱(0平底，1仰拱)")
    @TableField("is_base_board_arch")
    private Integer isBaseBoardArch;

    @ApiModelProperty(value = "拱顶铺底厚度")
    @TableField("base_board_arch_apex")
    private Double baseBoardArchApex;

    @ApiModelProperty(value = "拱脚铺底厚度")
    @TableField("base_board_arch_foot")
    private Double baseBoardArchFoot;

    @ApiModelProperty(value = "喷混凝土等级")
    @TableField("shotcrete_grade")
    private String shotcreteGrade;

    @ApiModelProperty(value = "仰拱铺地砼等级")
    @TableField("base_board_arch_concrete_grade")
    private String baseBoardArchConcreteGrade;

    @ApiModelProperty(value = "二次衬砌砼等级")
    @TableField("reinforced_concrete_grade")
    private String reinforcedConcreteGrade;

    @ApiModelProperty(value = "适用的管道外径")
    @TableField("suitable_pipe_diam")
    private String suitablePipeDiam;

    @ApiModelProperty(value = "是否有初期支护(0无，1有)")
    @TableField("have_initial_support")
    private Integer haveInitialSupport;

    @ApiModelProperty(value = "是否有二次衬砌(0无，1有)")
    @TableField("have_secondary_support")
    private Integer haveSecondarySupport;

    @ApiModelProperty(value = "是否有底板(0无，1有)")
    @TableField("have_base_board")
    private Integer haveBaseBoard;

    @ApiModelProperty(value = "初期支护砼强度等级")
    @TableField("initial_stage_concrete_grade")
    private String initialStageConcreteGrade;

    @ApiModelProperty(value = "底板砼强度等级")
    @TableField("bottom_plate_concrete_grade")
    private String bottomPlateConcreteGrade;

    @ApiModelProperty(value = "是否为标准（0是 1否）")
    @TableField("standard_or_not")
    private Integer standardOrNot;

}
