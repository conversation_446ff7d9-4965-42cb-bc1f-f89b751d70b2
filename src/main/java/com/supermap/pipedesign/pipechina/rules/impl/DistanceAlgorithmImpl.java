package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.DistanceAlgorithm;
import com.supermap.pipedesign.pipechina.rules.dao.DistanceAlgorithmMapper;
import com.supermap.pipedesign.pipechina.rules.service.IDistanceAlgorithmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 空间规则算法表，定义了算法关联的图层和运算规则与范围 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@Service("DistanceAlgorithmImpl")
public class DistanceAlgorithmImpl extends ServiceImpl<DistanceAlgorithmMapper, DistanceAlgorithm> implements IDistanceAlgorithmService {

    @Autowired
    private DistanceAlgorithmMapper distanceAlgorithmMapper;

    /**
    * 添加空间规则算法表，定义了算法关联的图层和运算规则与范围信息
    *
    * @param distanceAlgorithm
    * @return int
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public int insert(DistanceAlgorithm distanceAlgorithm) {

        //distanceAlgorithm.setUserId(JavaUtils.getUUID36());
        //distanceAlgorithm.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return distanceAlgorithmMapper.insert(distanceAlgorithm);
    }

    /**
    * 删除空间规则算法表，定义了算法关联的图层和运算规则与范围信息
    *
    * @param distanceAlgorithmId
    * @return int
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public int delete(String distanceAlgorithmId) {
        return distanceAlgorithmMapper.deleteById(distanceAlgorithmId);
    }

    /**
    * 更新空间规则算法表，定义了算法关联的图层和运算规则与范围信息
    *
    * @param distanceAlgorithm
    * @return int
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public int update(DistanceAlgorithm distanceAlgorithm) {
        return distanceAlgorithmMapper.updateById(distanceAlgorithm);
    }

    /**
    * 全部查询
    *
    * @param distanceAlgorithm
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.DistanceAlgorithm>
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public List<DistanceAlgorithm> list(DistanceAlgorithm distanceAlgorithm) {

        QueryWrapper<DistanceAlgorithm> queryWrapper = new QueryWrapper<>();

        return distanceAlgorithmMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-16
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<DistanceAlgorithm> distanceAlgorithmIPage = new Page<>();
        distanceAlgorithmIPage.setCurrent(current);
        distanceAlgorithmIPage.setSize(size);

        QueryWrapper<DistanceAlgorithm> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return distanceAlgorithmMapper.selectPage(distanceAlgorithmIPage, queryWrapper);
    }


}
