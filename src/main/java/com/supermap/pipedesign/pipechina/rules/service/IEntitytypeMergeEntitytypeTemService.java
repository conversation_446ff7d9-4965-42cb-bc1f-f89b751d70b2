package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeEntitytypeTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Repository
public interface IEntitytypeMergeEntitytypeTemService extends IService<EntitytypeMergeEntitytypeTem> {

 /**
 * 添加信息
 *
 * @param entitytypeMergeEntitytypeTem
 * @return int
 * @Date 2023-02-17
 * @auther eomer
 */
 int insert(EntitytypeMergeEntitytypeTem entitytypeMergeEntitytypeTem);

 /**
 * 删除信息
 *
 * @param entitytypeMergeEntitytypeTemId
 * @return int
 * @Date 2023-02-17
 * @auther eomer
 */
 int delete(String entitytypeMergeEntitytypeTemId);

 /**
 * 更新信息
 *
 * @param entitytypeMergeEntitytypeTem
 * @return int
 * @Date 2023-02-17
 * @auther eomer
 */
 int update(EntitytypeMergeEntitytypeTem entitytypeMergeEntitytypeTem);

 /**
 * 全部查询
 *
 * @param entitytypeMergeEntitytypeTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeEntitytypeTem>
 * @Date 2023-02-17
 * @auther eomer
 */
 List<EntitytypeMergeEntitytypeTem> list(EntitytypeMergeEntitytypeTem entitytypeMergeEntitytypeTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-02-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
