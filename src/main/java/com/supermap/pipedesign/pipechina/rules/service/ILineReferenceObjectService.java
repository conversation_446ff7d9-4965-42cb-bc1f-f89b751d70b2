package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.LineReferenceObject;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Repository
public interface ILineReferenceObjectService extends IService<LineReferenceObject> {

 /**
 * 添加信息
 *
 * @param lineReferenceObject
 * @return int
 * @Date 2023-01-13
 * @auther eomer
 */
 int insert(LineReferenceObject lineReferenceObject);

 /**
 * 删除信息
 *
 * @param lineReferenceObjectId
 * @return int
 * @Date 2023-01-13
 * @auther eomer
 */
 int delete(String lineReferenceObjectId);

 /**
 * 更新信息
 *
 * @param lineReferenceObject
 * @return int
 * @Date 2023-01-13
 * @auther eomer
 */
 int update(LineReferenceObject lineReferenceObject);

 /**
 * 全部查询
 *
 * @param lineReferenceObject
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.LineReferenceObject>
 * @Date 2023-01-13
 * @auther eomer
 */
 List<LineReferenceObject> list(LineReferenceObject lineReferenceObject);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-13
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
