package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import com.supermap.pipedesign.pipechina.metadata.dao.DatasetinfoMapper;
import com.supermap.pipedesign.pipechina.metadata.dao.DatasetinfotemMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.SpaceRoutLeftVo;
import com.supermap.pipedesign.pipechina.metadata.entity.Datasetinfotem;
import com.supermap.pipedesign.pipechina.rules.dao.*;
import com.supermap.pipedesign.pipechina.rules.entity.*;
import com.supermap.pipedesign.pipechina.rules.entity.vo.*;
import com.supermap.pipedesign.pipechina.rules.service.ISpaceRuleChildRegisterService;
import com.supermap.tools.gson.GsonUtil;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;


@Service("SpaceRuleChildRegisterImpl")
public class SpaceRuleChildRegisterImpl
        extends ServiceImpl<SpaceRuleChildRegisterMapper, SpaceRuleChildRegister>
        implements ISpaceRuleChildRegisterService {

    @Autowired
    private SpaceRuleChildRegisterTemMapper spaceRuleChildRegisterTemMapper;

    @Autowired
    private SpaceRuleChildRegisterMapper spaceRuleChildRegisterMapper;

    @Autowired
    private NoTrafficAlgorithmMapper noTrafficAlgorithmTemMapper;

    @Autowired
    private DistanceAlgorithmMapper distanceAlgorithmTemMapper;

    @Autowired
    private SpaceRuleTypeRegistMapper spaceRuleTypeRegistTemMapper;

    @Autowired
    private NoTrafficAlgorithmMapper noTrafficAlgorithmMapper;

    @Autowired
    private DistanceAlgorithmMapper distanceAlgorithmMapper;

    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private DatasetinfotemMapper datasetinfotemMapper;

    @Autowired
    private GapAlgorithmMapper gapAlgorithmTemMapper;

    @Autowired
    private GapAlgorithmMapper gapAlgorithmMapper;

    @Autowired
    private OverlappingAlgorithmMapper overlappingAlgorithmTemMapper;

    @Autowired
    private OverlappingAlgorithmMapper overlappingAlgorithmMapper;

    @Resource(name = "SRCRAlgorithmImpl")
    private SRCRAlgorithmImpl srcrAlgorithm;


    @Resource(name = "rules")
    private DataSource dbRules;


    @Autowired
    AlgorithmRegisterImpl algorithmRegister;

    @Autowired
    AlgorithmRegisterMapper algorithmRegisterMapper;

    @Autowired
    private SpaceRuleChildRegisterRccMapper rccTemMapper;


    @Autowired
    private SpaceRuleChildRegisterRccMapper rccMapper;


    @Override
    public IPage<SpaceRoutVo> selProSpaceRoutList(long page, long pageSize, String srCategoryId, String projectid) {
        IPage<SpaceRoutVo> spaceRoutVoIPage = new Page<>();
        spaceRoutVoIPage.setCurrent(page);
        spaceRoutVoIPage.setSize(pageSize);
        QueryWrapper<SpaceRoutVo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_parent", srCategoryId);
        queryWrapper.eq("project_id", projectid);
        IPage<SpaceRoutVo> spaceRoutVoIPageList
                = spaceRuleChildRegisterMapper.selSpaceRoutList(spaceRoutVoIPage, queryWrapper);
        return spaceRoutVoIPageList;
    }

    @Override
    public List<SpaceRoutLeftVo> selSpaceRoutLeftList() {
        List<SpaceRoutLeftVo> spaceRoutLeftVos = new ArrayList<>();
        List<SpaceRuleTypeRegist> spaceRuleTypeRegists
                = spaceRuleTypeRegistTemMapper.selectList(new QueryWrapper<>());
        if (spaceRuleTypeRegists != null && spaceRuleTypeRegists.size() > 0) {
            for (SpaceRuleTypeRegist spaceRuleTypeRegistTem : spaceRuleTypeRegists) {
                SpaceRoutLeftVo spaceRoutLeftVo = new SpaceRoutLeftVo();
                spaceRoutLeftVo.setPkid(spaceRuleTypeRegistTem.getPkid());
                spaceRoutLeftVo.setRuleType(spaceRuleTypeRegistTem.getRuleType());
                spaceRoutLeftVo.setRuleName(spaceRuleTypeRegistTem.getRuleName());
                List<SpaceRoutVo> spaceRoutVos = spaceRuleChildRegisterTemMapper.selSpaceRoutLeftList(spaceRuleTypeRegistTem.getPkid());
                if (spaceRoutVos != null && spaceRoutVos.size() > 0) {
                    spaceRoutLeftVo.setSpaceRoutVos(spaceRoutVos);
                }
                spaceRoutLeftVos.add(spaceRoutLeftVo);
            }
        }
        return spaceRoutLeftVos;
    }

    @Override
    public Map<String, Object> selSRLeftAlgorithm(String ruleId) {
        Map<String, Object> resultMap = new HashedMap<>();

        QueryWrapper<SpaceRuleChildRegister> spaceRuleChildRegisterTemQueryWrapper = new QueryWrapper<>();
        spaceRuleChildRegisterTemQueryWrapper.eq("pkid", ruleId);
        List<SpaceRuleChildRegister> spaceRuleChildRegisterTems
                = spaceRuleChildRegisterMapper.selectList(spaceRuleChildRegisterTemQueryWrapper);
        if (spaceRuleChildRegisterTems != null && spaceRuleChildRegisterTems.size() > 0) {
            resultMap.put("spaceRuleChildRegisterTems", spaceRuleChildRegisterTems);
        }

        QueryWrapper<NoTrafficAlgorithm> noTrafficAlgorithmTemQueryWrapper = new QueryWrapper<>();
        noTrafficAlgorithmTemQueryWrapper.eq("rule_id", ruleId);
        List<NoTrafficAlgorithm> noTrafficAlgorithmList
                = noTrafficAlgorithmTemMapper.selectList(noTrafficAlgorithmTemQueryWrapper);
        if (noTrafficAlgorithmList != null && noTrafficAlgorithmList.size() > 0) {
            resultMap.put("noTrafficAlgorithmList", noTrafficAlgorithmList);
        }

        QueryWrapper<DistanceAlgorithm> distanceAlgorithmTemQueryWrapper = new QueryWrapper<>();
        distanceAlgorithmTemQueryWrapper.eq("rule_id", ruleId);
        List<DistanceAlgorithm> distanceAlgorithms = distanceAlgorithmTemMapper.selectList(distanceAlgorithmTemQueryWrapper);
        if (distanceAlgorithms != null && distanceAlgorithms.size() > 0) {
            resultMap.put("distanceAlgorithms", distanceAlgorithms);
        }

        QueryWrapper<GapAlgorithm> gapAlgorithmTemQueryWrapper = new QueryWrapper<>();
        gapAlgorithmTemQueryWrapper.eq("rule_id", ruleId);
        List<GapAlgorithm> gapAlgorithms = gapAlgorithmTemMapper.selectList(gapAlgorithmTemQueryWrapper);
        if (gapAlgorithms != null && gapAlgorithms.size() > 0) {
            resultMap.put("gapAlgorithms", gapAlgorithms);
        }

        QueryWrapper<OverlappingAlgorithm> overlappingAlgorithmTemQueryWrapper = new QueryWrapper<>();
        overlappingAlgorithmTemQueryWrapper.eq("rule_id", ruleId);
        List<OverlappingAlgorithm> overlappingAlgorithms = overlappingAlgorithmTemMapper.selectList(overlappingAlgorithmTemQueryWrapper);
        if (overlappingAlgorithms != null && overlappingAlgorithms.size() > 0) {
            resultMap.put("overlappingAlgorithms", overlappingAlgorithms);
        }
        return resultMap;
    }


    @Override
    public List<Map<String, Object>> selSRLeftAlgorithmList(String ruleId) {
        List<Map<String, Object>> list = new ArrayList<>();
        String[] src = ruleId.split(",");
        for (int i = 0; i < src.length; i++) {
            Map<String, Object> resultMap = new HashedMap<>();

            QueryWrapper<SpaceRuleChildRegister> spaceRuleChildRegisterTemQueryWrapper = new QueryWrapper<>();
            spaceRuleChildRegisterTemQueryWrapper.eq("pkid", ruleId);
            List<SpaceRuleChildRegister> spaceRuleChildRegisterTems
                    = spaceRuleChildRegisterMapper.selectList(spaceRuleChildRegisterTemQueryWrapper);
            if (spaceRuleChildRegisterTems != null && spaceRuleChildRegisterTems.size() > 0) {
                resultMap.put("spaceRuleChildRegisterTems", spaceRuleChildRegisterTems);
            }

            QueryWrapper<NoTrafficAlgorithm> noTrafficAlgorithmTemQueryWrapper = new QueryWrapper<>();
            noTrafficAlgorithmTemQueryWrapper.eq("rule_id", ruleId);
            List<NoTrafficAlgorithm> noTrafficAlgorithmList
                    = noTrafficAlgorithmTemMapper.selectList(noTrafficAlgorithmTemQueryWrapper);
            if (noTrafficAlgorithmList != null && noTrafficAlgorithmList.size() > 0) {
                resultMap.put("noTrafficAlgorithmList", noTrafficAlgorithmList);
            }

            QueryWrapper<DistanceAlgorithm> distanceAlgorithmTemQueryWrapper = new QueryWrapper<>();
            distanceAlgorithmTemQueryWrapper.eq("rule_id", ruleId);
            List<DistanceAlgorithm> distanceAlgorithms = distanceAlgorithmTemMapper.selectList(distanceAlgorithmTemQueryWrapper);
            if (distanceAlgorithms != null && distanceAlgorithms.size() > 0) {
                resultMap.put("distanceAlgorithms", distanceAlgorithms);
            }

            QueryWrapper<GapAlgorithm> gapAlgorithmTemQueryWrapper = new QueryWrapper<>();
            gapAlgorithmTemQueryWrapper.eq("rule_id", ruleId);
            List<GapAlgorithm> gapAlgorithms = gapAlgorithmTemMapper.selectList(gapAlgorithmTemQueryWrapper);
            if (gapAlgorithms != null && gapAlgorithms.size() > 0) {
                resultMap.put("gapAlgorithms", gapAlgorithms);
            }

            QueryWrapper<OverlappingAlgorithm> overlappingAlgorithmTemQueryWrapper = new QueryWrapper<>();
            overlappingAlgorithmTemQueryWrapper.eq("rule_id", ruleId);
            List<OverlappingAlgorithm> overlappingAlgorithms = overlappingAlgorithmTemMapper.selectList(overlappingAlgorithmTemQueryWrapper);
            if (overlappingAlgorithms != null && overlappingAlgorithms.size() > 0) {
                resultMap.put("overlappingAlgorithms", overlappingAlgorithms);
            }
            list.add(resultMap);
        }
        return list;
    }


    /**
     * 项目级路由碰撞-创建入口
     * @param spaceRoutVo
     * @return
     */
    @Override
    public int createProSSRules(SpaceRoutVo spaceRoutVo) {
        int resultInt = 0;
        if (StringUtils.isEmpty(spaceRoutVo.getPkid())) {
            spaceRoutVo.setPkid(UUID.randomUUID().toString());
        }
        String projectId = spaceRoutVo.getProjectId();
        SpaceRuleChildRegister spaceRuleChildRegister = createSSRulesEntity(spaceRoutVo);
        spaceRuleChildRegister.setProjectId(projectId);
        resultInt = resultInt + spaceRuleChildRegisterMapper.insert(spaceRuleChildRegister);
        return resultInt;
    }

    /**
     * 项目级路由碰撞-创建-规则子表
     * @param spaceRoutVo
     * @return
     */
    private   SpaceRuleChildRegister createSSRulesEntity(SpaceRoutVo spaceRoutVo) {
        //插入规则主表
        SpaceRuleChildRegister spaceRuleChildRegister = new SpaceRuleChildRegister();
        String ruleId = spaceRoutVo.getPkid();
        spaceRuleChildRegister.setPkid(ruleId);

        //新建规则
        List<DatasetBean> dataset = spaceRoutVo.getDataset();
        for (DatasetBean datasetBean : dataset) {
            insertRulesDb(datasetBean,ruleId,spaceRoutVo.getProjectId());
        }

        spaceRuleChildRegister.setRuleName(spaceRoutVo.getRuleName());
        if (spaceRoutVo.getRuleSuitDict() != null
                && spaceRoutVo.getRuleSuitDict().size() > 0) {
            String ruleSuit = "";
            for (DictionaryVo dictionaryVo : spaceRoutVo.getRuleSuitDict()) {
                ruleSuit = ruleSuit + dictionaryVo.getPkid() + ",";
            }
            ruleSuit = ruleSuit.substring(0, (ruleSuit.length() - 1));
            spaceRuleChildRegister.setRuleSuit(ruleSuit);
        }
        spaceRuleChildRegister.setRuleContent(spaceRoutVo.getRuleContent());
        spaceRuleChildRegister.setRuleParent(spaceRoutVo.getRuleParent());
        spaceRuleChildRegister.setImplementationTime(spaceRoutVo.getImplementationTime());
        spaceRuleChildRegister.setPromptType(spaceRoutVo.getPromptType());
        spaceRuleChildRegister.setUseStatus(spaceRoutVo.getUseStatus());
        spaceRuleChildRegister.setUpdateUser("王经理");
        spaceRuleChildRegister.setUpdateUserId("1001");
        spaceRuleChildRegister.setIsAddEntitys(spaceRoutVo.getIsAddEntitys());
        Timestamp curTimestamp = new Timestamp(Calendar.getInstance().getTimeInMillis());
        spaceRuleChildRegister.setUpdateTime(curTimestamp);
        return spaceRuleChildRegister;
    }


    /**
     * 项目级路由碰撞-创建-规则子表-路由碰撞批量入算法库
     * @param spaceRoutVo
     * @return
     */
    private int insertRulesDb(DatasetBean spaceRoutVo, String ruleId, String projectId) {
        int result = 0;
        String algorithmType = spaceRoutVo.getAlgorithmType();
        //数据库名称跟pkid存库
        SpaceRuleChildRegisterRcc spaceRuleChildRegisterRcc = new SpaceRuleChildRegisterRcc();
        spaceRuleChildRegisterRcc.setPkid(UUID.randomUUID().toString());
        spaceRuleChildRegisterRcc.setRuleid(ruleId);
        spaceRuleChildRegisterRcc.setAlgorithmtype(algorithmType);
        spaceRuleChildRegisterRcc.setProjectid(projectId);
        //插入有几个算法配置
        rccMapper.insert(spaceRuleChildRegisterRcc);
        List<DataBean> data = spaceRoutVo.getData();
            for (int i = 0; i < data.size(); i++) {
                data.get(i).setCol_pkid(UUID.randomUUID().toString());
                data.get(i).setCol_rule_id(ruleId);
                data.get(i).setCol_project_id(projectId);
                //插入各表算法
                result = algorithmRegisterMapper.insertAlgorithm(algorithmType,data.get(i));
            }
        return result;
    }


    /**
     * @param pkid
     * @Description 项目级路由碰撞-查询规则入口
     * <AUTHOR>
     */
    @Override
    public SpaceRoutVoSelect selProSRByPkid(String pkid,String projectid) {
        QueryWrapper queryWrapper =new QueryWrapper();
        queryWrapper.eq("pkid",pkid);
        queryWrapper.eq("project_id",projectid);
        SpaceRuleChildRegister spaceRuleChildRegister
                = spaceRuleChildRegisterMapper.selectOne(queryWrapper);


            if (spaceRuleChildRegister != null) {
                SpaceRoutVoSelect spaceRoutVo = new SpaceRoutVoSelect();
                spaceRoutVo.setPkid(spaceRuleChildRegister.getPkid());
                spaceRoutVo.setRuleName(spaceRuleChildRegister.getRuleName());
                spaceRoutVo.setImplementationTime(spaceRuleChildRegister.getImplementationTime());
                spaceRoutVo.setUpdateUser(spaceRuleChildRegister.getUpdateUser());
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 定义格式，不显示毫秒
                spaceRoutVo.setUpdateTime(df.format(spaceRuleChildRegister.getUpdateTime()));
                if (StringUtils.isNotEmpty(spaceRuleChildRegister.getRuleSuit())) {
                    List<String> ruleSuitCodeList = new ArrayList<>();
                    if (spaceRuleChildRegister.getRuleSuit().indexOf(",") > -1) {
                        String[] ruleSuitArr = spaceRuleChildRegister.getRuleSuit().split(",");
                        ruleSuitCodeList = Arrays.asList(ruleSuitArr);
                    } else {
                        ruleSuitCodeList.add(spaceRuleChildRegister.getRuleSuit());
                    }
                    List<DictionaryVo> ruleSuitDictList = new ArrayList<>();
                    for (String codePkid : ruleSuitCodeList) {
                        DictionaryVo dictionaryVo = dictionaryMapper.selByPkid(codePkid);
                        ruleSuitDictList.add(dictionaryVo);
                    }
                    spaceRoutVo.setRuleSuitDict(ruleSuitDictList);
                }
                if (StringUtils.isNotEmpty(spaceRuleChildRegister.getPromptType())) {
                    spaceRoutVo.setPromptType(spaceRuleChildRegister.getPromptType());
                    DictionaryVo dictionaryVo = dictionaryMapper.selByPkid(spaceRuleChildRegister.getPromptType());
                    if (dictionaryVo != null) {
                        spaceRoutVo.setPromptTypeName(dictionaryVo.getCode());
                    }
                }
                spaceRoutVo.setUseStatus(spaceRuleChildRegister.getUseStatus());
                spaceRoutVo.setRuleContent(spaceRuleChildRegister.getRuleContent());
                spaceRoutVo.setIsAddEntitys(spaceRuleChildRegister.getIsAddEntitys());
                spaceRoutVo.setProjectId(projectid);
                selNoTrafficDistanceAlgorithm(spaceRoutVo);
                return spaceRoutVo;
            }

        return new SpaceRoutVoSelect();
    }

    @Autowired
    private DatasetinfoMapper datasetinfoMapper;
    /**
     * @param spaceRoutVo
     * @Description 项目级路由碰撞 查询规则下所有算法配置数据
     * <AUTHOR>
     */
    private void selNoTrafficDistanceAlgorithm(SpaceRoutVoSelect spaceRoutVo) {
        //取出该规则的配置的算法清单
        QueryWrapper<SpaceRuleChildRegisterRcc> queryWrapperNoTra = new QueryWrapper<>();
        queryWrapperNoTra.eq("rule_id", spaceRoutVo.getPkid());
        queryWrapperNoTra.eq("project_id", spaceRoutVo.getProjectId());
        List<SpaceRuleChildRegisterRcc> spaceRuleChildRegisterRccList  = rccMapper.selectList(queryWrapperNoTra);

            List<AlgorithmDataVo> dataset = new ArrayList<>();

            for (SpaceRuleChildRegisterRcc spaceRuleChildRegisterRccL : spaceRuleChildRegisterRccList) {
                String colAlgorithmType = spaceRuleChildRegisterRccL.getAlgorithmtype();
                String colRuleId = spaceRuleChildRegisterRccL.getRuleid();

                AlgorithmDataVo algorithmDataVo =new AlgorithmDataVo();

                List<AlgorithmDataVo> route_collision = algorithmRegister.selAlgorithmInfobyType("route_collision", colAlgorithmType);
                if(route_collision.size()>0)
                {
                    algorithmDataVo = route_collision.get(0);
                }

                List<Map> maps = algorithmRegisterMapper.selectlistAlgorithm(colAlgorithmType, colRuleId,spaceRoutVo.getProjectId());
                for (Map map : maps) {
                    map.put("col_pikid",map.get("pkid"));
                    map.put("col_rule_id",map.get("rule_id"));
                    map.put("col_project_id",map.get("progect_id"));
                    map.put("col_layer_id",map.get("layer_id"));
                    map.put("col_distance",map.get("distance"));
                    map.put("col_angle",map.get("angle"));
                    map.put("col_iscaliber",map.get("is_caliber"));
                }
                List<DataBean> list = GsonUtil.ObjectToList(maps, DataBean.class);
                for (DataBean dataBean : list) {
                    Datasetinfotem datasetinfotem = datasetinfotemMapper.selectById(dataBean.getCol_layer_id());
                    dataBean.setDatasetname(datasetinfotem.getDatasetname());
                    dataBean.setDatasetalias(datasetinfotem.getDatasetalias());
                }
                algorithmDataVo.setData(list);
                dataset.add(algorithmDataVo);

            }
            spaceRoutVo.setDataset(dataset);

    }

    /**
     * @param spaceRoutVo
     * @Description 项目级路由碰撞 更新规则配置
     * <AUTHOR>
     */
    @Override
    public int updateProSRbyPkid(SpaceRoutVo spaceRoutVo) {
        int resultInt = 0;
        QueryWrapper query = new QueryWrapper();
                query.eq("pkid",spaceRoutVo.getPkid());
                query.eq("project_id",spaceRoutVo.getProjectId());
            SpaceRuleChildRegister spaceRuleChildRegisterDb
                    = spaceRuleChildRegisterMapper.selectOne(query);
            if (spaceRuleChildRegisterDb != null) {

                //取出该规则的配置的算法清单
                QueryWrapper<SpaceRuleChildRegisterRcc> queryWrapperNoTra = new QueryWrapper<>();
                queryWrapperNoTra.eq("rule_id", spaceRoutVo.getPkid());
                queryWrapperNoTra.eq("project_id", spaceRoutVo.getProjectId());
                List<SpaceRuleChildRegisterRcc> spaceRuleChildRegisterRccList = rccMapper.selectList(queryWrapperNoTra);

                for (SpaceRuleChildRegisterRcc spaceRuleChildRegisterRccL : spaceRuleChildRegisterRccList) {
                    String colAlgorithmType = spaceRuleChildRegisterRccL.getAlgorithmtype();
                    String colRuleId = spaceRuleChildRegisterRccL.getRuleid();

                    //删除每个路由碰撞的表
                    algorithmRegisterMapper.deleteAlgorithm(colAlgorithmType, colRuleId);
                }
                rccMapper.delete(queryWrapperNoTra);
                String projectId = spaceRoutVo.getProjectId();
                // 重新创建关联数据
                SpaceRuleChildRegister spaceRuleChildRegister = createSSRulesEntity(spaceRoutVo);
                spaceRuleChildRegister.setProjectId(projectId);
                //更细主表
                resultInt = resultInt + spaceRuleChildRegisterMapper.update(spaceRuleChildRegister,query);
            }
        return resultInt;
    }


    /**
     * @param pkid
     * @Description 项目级路由碰撞 删除规则配置
     * <AUTHOR>
     */
    @Override
    public int delProRulesByid(String pkid ,String projectid) {
        int resultInt = 0;
        QueryWrapper query = new QueryWrapper();
        query.eq("pkid",pkid);
        query.eq("project_id",projectid);
        SpaceRuleChildRegister spaceRuleChildRegisterDb
                = spaceRuleChildRegisterMapper.selectOne(query);
        if (spaceRuleChildRegisterDb != null) {

            //取出该规则的配置的算法清单
            QueryWrapper<SpaceRuleChildRegisterRcc> queryWrapperNoTra = new QueryWrapper<>();
            queryWrapperNoTra.eq("rule_id", pkid);
            queryWrapperNoTra.eq("project_id", projectid);
            List<SpaceRuleChildRegisterRcc> spaceRuleChildRegisterRccList = rccMapper.selectList(queryWrapperNoTra);

            for (SpaceRuleChildRegisterRcc spaceRuleChildRegisterRccL : spaceRuleChildRegisterRccList) {
                String colAlgorithmType = spaceRuleChildRegisterRccL.getAlgorithmtype();
                String colRuleId = spaceRuleChildRegisterRccL.getColruleid();

                //删除每个路由碰撞的表
                algorithmRegisterMapper.deleteAlgorithm(colAlgorithmType, colRuleId);
            }
            rccMapper.delete(queryWrapperNoTra);
            //更细主表
            resultInt = resultInt + spaceRuleChildRegisterMapper.delete(query);
        }
        return resultInt;
    }

    @Override
    public int updateStatusById(String pkid, boolean falg) {
        SpaceRuleChildRegisterTem tem = spaceRuleChildRegisterTemMapper.selectById(pkid);
        tem.setUseStatus(falg);
        return spaceRuleChildRegisterTemMapper.updateById(tem);
    }


    /**
     * @param pkid
     * 根据id更新状态 项目
     * <AUTHOR>
     * @Description
     * @Date 2023/2/25 23:04
     **/
    @Override
    public int updateStatusByIdPro(String pkid, boolean falg,String projectid) {
        QueryWrapper query=new QueryWrapper();
        query.eq("pkid",pkid);
        query.eq("project_id",projectid);
        SpaceRuleChildRegister tem = spaceRuleChildRegisterMapper.selectOne(query);
        tem.setUseStatus(falg);
        return spaceRuleChildRegisterMapper.update(tem,query);
    }


}
