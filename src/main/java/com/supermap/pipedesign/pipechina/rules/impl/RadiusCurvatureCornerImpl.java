package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.rules.dao.RadiusCurvatureCornerMapper;
import com.supermap.pipedesign.pipechina.rules.entity.RadiusCurvatureCorner;
import com.supermap.pipedesign.pipechina.rules.service.IRadiusCurvatureCornerService;
import com.supermap.tools.base.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * <p>
 * 曲率半径与转角界限规则-项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Service("RadiusCurvatureCornerImpl")
public class RadiusCurvatureCornerImpl extends ServiceImpl<RadiusCurvatureCornerMapper, RadiusCurvatureCorner> implements IRadiusCurvatureCornerService {

    @Autowired
    private RadiusCurvatureCornerMapper radiusCurvatureCornerMapper;

    /**
     * 查询曲率半径与转角界限规则
     * <AUTHOR>
     * @Description
     * @Date 2023/3/16 11:34
     **/
    @Override
    public List<RadiusCurvatureCorner> selInfoRadius(String projectId) {
        QueryWrapper<RadiusCurvatureCorner> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id",projectId);
        List<RadiusCurvatureCorner> list = radiusCurvatureCornerMapper.selectList(queryWrapper);
        return list;
    }

    /**
     * 更新曲率半径与转角界限规则
     * <AUTHOR>
     * @Description
     * @Date 2023/3/16 11:56
     **/
    @Override
    public int updateRadius(Map<String, List<RadiusCurvatureCorner>> map) {
        List<RadiusCurvatureCorner> radiusList = map.get("data");
        for (RadiusCurvatureCorner radius : radiusList){
            if (JavaUtils.isNotEmtryOrNull(radius.getPkid())){
                QueryWrapper<RadiusCurvatureCorner> queryWrapper=new QueryWrapper<>();
                queryWrapper.eq("pkid",radius.getPkid());
                queryWrapper.eq("project_id",radius.getProjectId());
                radiusCurvatureCornerMapper.update(radius,queryWrapper);
                continue;
            }
            radius.setPkid(UUID.randomUUID().toString());
            radiusCurvatureCornerMapper.insert(radius);
        }
        return 1;
    }

    /**
     * 删除
     * <AUTHOR>
     * @Description
     * @Date 2023/3/16 18:39
     **/
    @Override
    public int deletRadius(String radiusId,String projectId) {
        QueryWrapper<RadiusCurvatureCorner> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid",radiusId);
        queryWrapper.eq("project_id",projectId);
        return radiusCurvatureCornerMapper.delete(queryWrapper);
    }
}
