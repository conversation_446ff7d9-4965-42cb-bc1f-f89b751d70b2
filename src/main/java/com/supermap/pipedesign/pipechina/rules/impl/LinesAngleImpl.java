package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.LinesAngle;
import com.supermap.pipedesign.pipechina.rules.dao.LinesAngleMapper;
import com.supermap.pipedesign.pipechina.rules.service.ILinesAngleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("LinesAngleImpl")
public class LinesAngleImpl extends ServiceImpl<LinesAngleMapper, LinesAngle> implements ILinesAngleService {

    @Autowired
    private LinesAngleMapper linesAngleMapper;

    /**
    * 添加信息
    *
    * @param linesAngle
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(LinesAngle linesAngle) {

        //linesAngle.setUserId(JavaUtils.getUUID36());
        //linesAngle.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return linesAngleMapper.insert(linesAngle);
    }

    /**
    * 删除信息
    *
    * @param linesAngleId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String linesAngleId) {
        return linesAngleMapper.deleteById(linesAngleId);
    }

    /**
    * 更新信息
    *
    * @param linesAngle
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(LinesAngle linesAngle) {
        return linesAngleMapper.updateById(linesAngle);
    }

    /**
    * 全部查询
    *
    * @param linesAngle
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.LinesAngle>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<LinesAngle> list(LinesAngle linesAngle) {

        QueryWrapper<LinesAngle> queryWrapper = new QueryWrapper<>();

        return linesAngleMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<LinesAngle> linesAngleIPage = new Page<>();
        linesAngleIPage.setCurrent(current);
        linesAngleIPage.setSize(size);

        QueryWrapper<LinesAngle> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return linesAngleMapper.selectPage(linesAngleIPage, queryWrapper);
    }


}
