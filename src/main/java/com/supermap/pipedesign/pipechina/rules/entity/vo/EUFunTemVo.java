package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.supermap.pipedesign.pipechina.rules.entity.EUFunFieldTem;
import com.supermap.pipedesign.pipechina.rules.entity.EUFunTem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 算法模板 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
public class EUFunTemVo {

    @ApiModelProperty(value = "算法模板")
    EUFunTem funTem;
    @ApiModelProperty(value = "算法字段模板")
    List<EUFunFieldTem> fieldTemList;


}
