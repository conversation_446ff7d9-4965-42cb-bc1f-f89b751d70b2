package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import com.supermap.pipedesign.pipechina.rules.dao.HighConsequenceAreaIdentifyMapper;
import com.supermap.pipedesign.pipechina.rules.dao.HighConsequenceAreaIdentifyTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.HighConsequenceAreaIdentify;
import com.supermap.pipedesign.pipechina.rules.entity.HighConsequenceAreaIdentifyTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.HighConsequenceAreaIdentifyVo;
import com.supermap.pipedesign.pipechina.rules.service.IHighConsequenceAreaIdentifyService;
import com.supermap.tools.gson.GsonUtil;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
@Service("HighConsequenceAreaIdentifyImpl")
public class HighConsequenceAreaIdentifyImpl
        extends ServiceImpl<HighConsequenceAreaIdentifyMapper, HighConsequenceAreaIdentify>
        implements IHighConsequenceAreaIdentifyService {

    @Autowired
    private HighConsequenceAreaIdentifyMapper highConsequenceAreaIdentifyMapper;

    @Autowired
    private HighConsequenceAreaIdentifyTemMapper highConsequenceAreaIdentifyTemMapper;

    @Autowired
    private DictionaryMapper dictionaryMapper;


    /**
     * 全部查询
     *
     * @param highConsequenceAreaIdentify
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.HighConsequenceAreaIdentify>
     * @Date 2023-02-06
     * @auther eomer
     */
    @Override
    public Map<String, Object> list(HighConsequenceAreaIdentify highConsequenceAreaIdentify) {
        QueryWrapper<HighConsequenceAreaIdentifyTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type_val", highConsequenceAreaIdentify.getTypeval());
        queryWrapper.orderByAsc("identify_level");
        List<HighConsequenceAreaIdentifyTem> dbList
                = highConsequenceAreaIdentifyTemMapper.selectList(queryWrapper);
        Map<String, Object> resultMap = new HashedMap<>();
        resultMap.put("descList", new ArrayList<>());
        resultMap.put("specalList", new ArrayList<>());
        resultMap.put("burnExplodeList", new ArrayList<>());
        if (dbList != null && dbList.size() > 0) {
            List<HighConsequenceAreaIdentifyVo> resultList = new ArrayList<>();
            for (HighConsequenceAreaIdentifyTem highConsequenceAreaIdentify1 : dbList) {
                HighConsequenceAreaIdentifyVo highConsequenceAreaIdentifyVo
                        = GsonUtil.ObjectToEntity(highConsequenceAreaIdentify1, HighConsequenceAreaIdentifyVo.class);
                String identifyparam = highConsequenceAreaIdentify1.getIdentifyparam();
                if (StringUtils.isNotEmpty(identifyparam)) {
                    List<String> identifyparamList = new ArrayList<>();
                    if (identifyparam.indexOf(",") > -1) {
                        identifyparamList = Arrays.asList(identifyparam.split(","));
                    } else {
                        identifyparamList.add(identifyparam);
                    }
                    String namePkid = "";
                    for (String codePkid : identifyparamList) {
                        DictionaryVo dictionaryVo = dictionaryMapper.selByPkid(codePkid);
                        namePkid = namePkid + dictionaryVo.getCode() + ",";
                    }
                    highConsequenceAreaIdentifyVo.setIdentifyparamremark(namePkid);
                }
                resultList.add(highConsequenceAreaIdentifyVo);
            }
            resultMap.put("descList", resultList);
        }
        if (highConsequenceAreaIdentify.getTypeval() == 1) {
//            HighConsequenceAreaIdentify highConsequenceAreaIdentify10 =  highConsequenceAreaIdentifyMapper.selByType(10);
            HighConsequenceAreaIdentifyTem highTem = highConsequenceAreaIdentifyTemMapper.selByType(10);
            HighConsequenceAreaIdentify highConsequenceAreaIdentify10 = GsonUtil.ObjectToEntity(highTem, HighConsequenceAreaIdentify.class);
            if (StringUtils.isNotEmpty(highConsequenceAreaIdentify10.getIdentifyparam())) {
                resultMap.put("specalList", getDictionarys(highConsequenceAreaIdentify10));
            }
//            HighConsequenceAreaIdentify highConsequenceAreaIdentify11 =  highConsequenceAreaIdentifyMapper.selByType(11);
            HighConsequenceAreaIdentifyTem highTem1 = highConsequenceAreaIdentifyTemMapper.selByType(11);
            HighConsequenceAreaIdentify highConsequenceAreaIdentify11 = GsonUtil.ObjectToEntity(highTem1, HighConsequenceAreaIdentify.class);
            if (StringUtils.isNotEmpty(highConsequenceAreaIdentify11.getIdentifyparam())) {
                resultMap.put("burnExplodeList", getDictionarys(highConsequenceAreaIdentify11));
            }
        }
        if (highConsequenceAreaIdentify.getTypeval() == 2) {
            HighConsequenceAreaIdentifyTem highConsequenceAreaIdentify10 = highConsequenceAreaIdentifyTemMapper.selByType(20);
            if (StringUtils.isNotEmpty(highConsequenceAreaIdentify10.getIdentifyparam())) {
//                resultMap.put("specalList",getDictionarys(highConsequenceAreaIdentify10));
                resultMap.put("specalList", getDictionarys(GsonUtil.ObjectToEntity(highConsequenceAreaIdentify10, HighConsequenceAreaIdentify.class)));
            }
//            HighConsequenceAreaIdentify highConsequenceAreaIdentify11 =  highConsequenceAreaIdentifyMapper.selByType(21);
            HighConsequenceAreaIdentifyTem highConsequenceAreaIdentify11 = highConsequenceAreaIdentifyTemMapper.selByType(21);
            if (StringUtils.isNotEmpty(highConsequenceAreaIdentify11.getIdentifyparam())) {
//                resultMap.put("burnExplodeList",getDictionarys(highConsequenceAreaIdentify11));
                resultMap.put("burnExplodeList", getDictionarys(GsonUtil.ObjectToEntity(highConsequenceAreaIdentify11, HighConsequenceAreaIdentify.class)));
            }
        }
        return resultMap;
    }

    private List<DictionaryVo> getDictionarys(HighConsequenceAreaIdentify highConsequenceAreaIdentify) {
        List<DictionaryVo> dictionaryList = new ArrayList<>();
        String identifyParam = highConsequenceAreaIdentify.getIdentifyparam();
        List<String> codePkids = new ArrayList<>();
        if (identifyParam.indexOf(",") > -1) {
            String[] codeArr = identifyParam.split(",");
            codePkids = Arrays.asList(codeArr);
        } else {
            codePkids.add(identifyParam);
        }
        for (String codePkid : codePkids) {
            dictionaryList.add(dictionaryMapper.selByPkid(codePkid));
        }
        ;
        return dictionaryList;
    }


    @Override
    public int updateHCAI(HighConsequenceAreaIdentifyVo highConsequenceAreaIdentifyVo) {
        int resultInt = 0;
        if (highConsequenceAreaIdentifyVo.getTypeval() == 1) {
            List<String> specialList = highConsequenceAreaIdentifyVo.getSpecialList();
            if (specialList != null && specialList.size() > 0) {
                String identifyparam = StringUtils.join(specialList, ",");
                resultInt = resultInt + updateIdentifyparam(identifyparam, 10);
            } else {
                resultInt = resultInt + updateIdentifyparam("", 10);
            }
            List<String> burnAndExplode = highConsequenceAreaIdentifyVo.getBurnAndExplode();
            if (burnAndExplode != null && burnAndExplode.size() > 0) {
                String identifyparam = StringUtils.join(burnAndExplode, ",");
                resultInt = resultInt + updateIdentifyparam(identifyparam, 11);
            } else {
                resultInt = resultInt + updateIdentifyparam("", 11);
            }
        }
        if (highConsequenceAreaIdentifyVo.getTypeval() == 2) {
            List<String> specialList = highConsequenceAreaIdentifyVo.getSpecialList();
            if (specialList != null && specialList.size() > 0) {
                String identifyparam = StringUtils.join(specialList, ",");
                resultInt = resultInt + updateIdentifyparam(identifyparam, 20);
            } else {
                resultInt = resultInt + updateIdentifyparam("", 20);
            }
            List<String> burnAndExplode = highConsequenceAreaIdentifyVo.getBurnAndExplode();
            if (burnAndExplode != null && burnAndExplode.size() > 0) {
                String identifyparam = StringUtils.join(burnAndExplode, ",");
                resultInt = resultInt + updateIdentifyparam(identifyparam, 21);
            } else {
                resultInt = resultInt + updateIdentifyparam("", 21);
            }
        }
        return resultInt;
    }

    @Override
    public int updateProHCAI(HighConsequenceAreaIdentifyVo highConsequenceAreaIdentifyVo) {
        int resultInt = 0;
        String projectid = highConsequenceAreaIdentifyVo.getProjectid();
        if (highConsequenceAreaIdentifyVo.getTypeval() == 1) {
            List<String> specialList = highConsequenceAreaIdentifyVo.getSpecialList();
            if (specialList != null && specialList.size() > 0) {
                String identifyparam = StringUtils.join(specialList, ",");
                resultInt = resultInt + updateProIdentifyparam(identifyparam, 10,projectid);
            } else {
                resultInt = resultInt + updateProIdentifyparam("", 10,projectid);
            }
            List<String> burnAndExplode = highConsequenceAreaIdentifyVo.getBurnAndExplode();
            if (burnAndExplode != null && burnAndExplode.size() > 0) {
                String identifyparam = StringUtils.join(burnAndExplode, ",");
                resultInt = resultInt + updateProIdentifyparam(identifyparam, 11,projectid);
            } else {
                resultInt = resultInt + updateProIdentifyparam("", 11,projectid);
            }
        }
        if (highConsequenceAreaIdentifyVo.getTypeval() == 2) {
            List<String> specialList = highConsequenceAreaIdentifyVo.getSpecialList();
            if (specialList != null && specialList.size() > 0) {
                String identifyparam = StringUtils.join(specialList, ",");
                resultInt = resultInt + updateProIdentifyparam(identifyparam, 20,projectid);
            } else {
                resultInt = resultInt + updateProIdentifyparam("", 20,projectid);
            }
            List<String> burnAndExplode = highConsequenceAreaIdentifyVo.getBurnAndExplode();
            if (burnAndExplode != null && burnAndExplode.size() > 0) {
                String identifyparam = StringUtils.join(burnAndExplode, ",");
                resultInt = resultInt + updateProIdentifyparam(identifyparam, 21,projectid);
            } else {
                resultInt = resultInt + updateProIdentifyparam("", 21,projectid);
            }
        }
        return resultInt;
    }

    private int updateIdentifyparam(String identifyparam, Integer type) {
        HighConsequenceAreaIdentifyTem highConsequenceAreaIdentify10 = highConsequenceAreaIdentifyTemMapper.selByType(type);
        highConsequenceAreaIdentify10.setIdentifyparam(identifyparam);
        return highConsequenceAreaIdentifyTemMapper.updateById(highConsequenceAreaIdentify10);
    }


    private int updateProIdentifyparam(String identifyparam, Integer type,String projectid) {
        HighConsequenceAreaIdentify highConsequenceAreaIdentify = highConsequenceAreaIdentifyMapper.selByType(type,projectid);
        highConsequenceAreaIdentify.setIdentifyparam(identifyparam);
        QueryWrapper<HighConsequenceAreaIdentify> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid",highConsequenceAreaIdentify.getPkid());
        queryWrapper.eq("project_id",projectid);
        return highConsequenceAreaIdentifyMapper.update(highConsequenceAreaIdentify,queryWrapper);
//        return highConsequenceAreaIdentifyMapper.updateById(highConsequenceAreaIdentify);
    }


    @Override
    public Map<String,Object> listPro(HighConsequenceAreaIdentify highConsequenceAreaIdentify){
        QueryWrapper<HighConsequenceAreaIdentify> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type_val", highConsequenceAreaIdentify.getTypeval());
        String projectid = highConsequenceAreaIdentify.getProjectid();
        queryWrapper.eq("project_id",projectid );
        queryWrapper.orderByAsc("identify_level");
        List<HighConsequenceAreaIdentify> dbList
                = highConsequenceAreaIdentifyMapper.selectList(queryWrapper);
        Map<String, Object> resultMap = new HashedMap<>();
        resultMap.put("descList", new ArrayList<>());
        resultMap.put("specalList", new ArrayList<>());
        resultMap.put("burnExplodeList", new ArrayList<>());
        if (dbList != null && dbList.size() > 0) {
            List<HighConsequenceAreaIdentifyVo> resultList = new ArrayList<>();
            for (HighConsequenceAreaIdentify highConsequenceAreaIdentify1 : dbList) {
                HighConsequenceAreaIdentifyVo highConsequenceAreaIdentifyVo
                        = GsonUtil.ObjectToEntity(highConsequenceAreaIdentify1, HighConsequenceAreaIdentifyVo.class);
                String identifyparam = highConsequenceAreaIdentify1.getIdentifyparam();
                if (StringUtils.isNotEmpty(identifyparam)) {
                    List<String> identifyparamList = new ArrayList<>();
                    if (identifyparam.indexOf(",") > -1) {
                        identifyparamList = Arrays.asList(identifyparam.split(","));
                    } else {
                        identifyparamList.add(identifyparam);
                    }
                    String namePkid = "";
                    for (String codePkid : identifyparamList) {
                        DictionaryVo dictionaryVo = dictionaryMapper.selByPkid(codePkid);
                        namePkid = namePkid + dictionaryVo.getCode() + ",";
                    }
                    highConsequenceAreaIdentifyVo.setIdentifyparamremark(namePkid);
                }
                resultList.add(highConsequenceAreaIdentifyVo);
            }
            resultMap.put("descList", resultList);
        }
        if (highConsequenceAreaIdentify.getTypeval() == 1) {
//            HighConsequenceAreaIdentify highConsequenceAreaIdentify10 =  highConsequenceAreaIdentifyMapper.selByType(10);
            HighConsequenceAreaIdentify highTem = highConsequenceAreaIdentifyMapper.selByType(10,projectid);
            HighConsequenceAreaIdentify highConsequenceAreaIdentify10 = GsonUtil.ObjectToEntity(highTem, HighConsequenceAreaIdentify.class);
            if (StringUtils.isNotEmpty(highConsequenceAreaIdentify10.getIdentifyparam())) {
                resultMap.put("specalList", getDictionarys(highConsequenceAreaIdentify10));
            }
//            HighConsequenceAreaIdentify highConsequenceAreaIdentify11 =  highConsequenceAreaIdentifyMapper.selByType(11);
            HighConsequenceAreaIdentify highTem1 = highConsequenceAreaIdentifyMapper.selByType(11,projectid);
            HighConsequenceAreaIdentify highConsequenceAreaIdentify11 = GsonUtil.ObjectToEntity(highTem1, HighConsequenceAreaIdentify.class);
            if (StringUtils.isNotEmpty(highConsequenceAreaIdentify11.getIdentifyparam())) {
                resultMap.put("burnExplodeList", getDictionarys(highConsequenceAreaIdentify11));
            }
        }
        if (highConsequenceAreaIdentify.getTypeval() == 2) {
            HighConsequenceAreaIdentify highConsequenceAreaIdentify10 = highConsequenceAreaIdentifyMapper.selByType(20,projectid);
            if (StringUtils.isNotEmpty(highConsequenceAreaIdentify10.getIdentifyparam())) {
//                resultMap.put("specalList",getDictionarys(highConsequenceAreaIdentify10));
                resultMap.put("specalList", getDictionarys(GsonUtil.ObjectToEntity(highConsequenceAreaIdentify10, HighConsequenceAreaIdentify.class)));
            }
//            HighConsequenceAreaIdentify highConsequenceAreaIdentify11 =  highConsequenceAreaIdentifyMapper.selByType(21);
            HighConsequenceAreaIdentify highConsequenceAreaIdentify11 = highConsequenceAreaIdentifyMapper.selByType(21,projectid);
            if (StringUtils.isNotEmpty(highConsequenceAreaIdentify11.getIdentifyparam())) {
//                resultMap.put("burnExplodeList",getDictionarys(highConsequenceAreaIdentify11));
                resultMap.put("burnExplodeList", getDictionarys(GsonUtil.ObjectToEntity(highConsequenceAreaIdentify11, HighConsequenceAreaIdentify.class)));
            }
        }
        return resultMap;
    }


}
