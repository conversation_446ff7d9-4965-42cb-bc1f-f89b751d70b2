package com.supermap.pipedesign.pipechina.rules.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PipeDesignList {
    @ApiModelProperty(value = "壁厚（mm）")
    private String wallThickness;

    @ApiModelProperty(value = "制管形式")
    private String pipeMakingForm;

    @ApiModelProperty(value = "防腐类型")
    private String typeOfAnticorrosion;

    @ApiModelProperty(value = "防腐等级")
    private String anticorrosionGrade;

    @ApiModelProperty(value = "类型（1.穿越{1.一般段 2.穿越段} 2.弯头弯管{1.直管段 2.热煨 3.冷湾}）")
    private Integer type;

}