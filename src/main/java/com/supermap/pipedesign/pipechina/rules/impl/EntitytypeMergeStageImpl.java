package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeStage;
import com.supermap.pipedesign.pipechina.rules.dao.EntitytypeMergeStageMapper;
import com.supermap.pipedesign.pipechina.rules.service.IEntitytypeMergeStageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-11
 */
@Service("EntitytypeMergeStageImpl")
public class EntitytypeMergeStageImpl extends ServiceImpl<EntitytypeMergeStageMapper, EntitytypeMergeStage> implements IEntitytypeMergeStageService {

    @Autowired
    private EntitytypeMergeStageMapper entitytypeMergeStageMapper;

    /**
    * 添加信息
    *
    * @param entitytypeMergeStage
    * @return int
    * @Date 2023-02-11
    * @auther eomer
    */
    @Override
    public int insert(EntitytypeMergeStage entitytypeMergeStage) {

        //entitytypeMergeStage.setUserId(JavaUtils.getUUID36());
        //entitytypeMergeStage.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return entitytypeMergeStageMapper.insert(entitytypeMergeStage);
    }

    /**
    * 删除信息
    *
    * @param entitytypeMergeStageId
    * @return int
    * @Date 2023-02-11
    * @auther eomer
    */
    @Override
    public int delete(String entitytypeMergeStageId) {
        return entitytypeMergeStageMapper.deleteById(entitytypeMergeStageId);
    }

    /**
    * 更新信息
    *
    * @param entitytypeMergeStage
    * @return int
    * @Date 2023-02-11
    * @auther eomer
    */
    @Override
    public int update(EntitytypeMergeStage entitytypeMergeStage) {
        return entitytypeMergeStageMapper.updateById(entitytypeMergeStage);
    }

    /**
    * 全部查询
    *
    * @param entitytypeMergeStage
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeStage>
    * @Date 2023-02-11
    * @auther eomer
    */
    @Override
    public List<EntitytypeMergeStage> list(EntitytypeMergeStage entitytypeMergeStage) {

        QueryWrapper<EntitytypeMergeStage> queryWrapper = new QueryWrapper<>();

        return entitytypeMergeStageMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-11
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<EntitytypeMergeStage> entitytypeMergeStageIPage = new Page<>();
        entitytypeMergeStageIPage.setCurrent(current);
        entitytypeMergeStageIPage.setSize(size);

        QueryWrapper<EntitytypeMergeStage> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return entitytypeMergeStageMapper.selectPage(entitytypeMergeStageIPage, queryWrapper);
    }


}
