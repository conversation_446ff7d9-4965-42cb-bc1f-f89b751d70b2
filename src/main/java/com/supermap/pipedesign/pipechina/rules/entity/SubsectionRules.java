package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 分项统计规则模板 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_subsection_rules")
@ApiModel(value="SubsectionRules对象", description="分项统计规则模板")
public class SubsectionRules implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "规则名称")
    @TableField("rules_name")
    private String rulesName;

    @ApiModelProperty(value = "实体id")
    @TableField("entity_id")
    private String entityId;

    @ApiModelProperty(value = "子实体id")
    @TableField("entity_chile_id")
    private String entityChileId;

    @ApiModelProperty(value = "更新人")
    @TableField("update_user_id")
    private String updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Timestamp updateTime;

    @ApiModelProperty(value = "规则描述")
    @TableField("rule_desc")
    private String ruleDesc;

    @ApiModelProperty(value = "更新人")
    @TableField("update_user")
    private String updateUser;

    @ApiModelProperty(value = "引用模板")
    @TableField("cite_tem")
    private String citeTem;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "节点id")
    @TableField("rule_parent")
    private String ruleParent;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mId;

    @ApiModelProperty(value = "施工阶段编号 多阶段则 逗号分隔  0,1,2,3,4 预可研0、可研1、初设2、施工3、竣工4")
    @TableField("stage_inner_code")
    private String stageInnerCode;

    @ApiModelProperty(value = "专业ID")
    @TableField("subject_id")
    private String subjectId;

    @ApiModelProperty(value = "专业ID")
    @TableField("table_name")
    private String tableName;

    @ApiModelProperty(value = "合计")
    @TableField("total")
    private String total;

    @ApiModelProperty(value = "分组")
    @TableField("grouping")
    private String grouping;

}
