package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 地区等级划分规则 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_area_level_partition")
@ApiModel(value="AreaLevelPartition对象", description="地区等级划分规则")
public class AreaLevelPartition implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "地区等级")
    @TableField("levelval")
    private Integer levelval;

    @ApiModelProperty(value = "户数开始")
    @TableField("hu_shu_begin")
    private Integer hushubegin;

    @ApiModelProperty(value = "户数结束")
    @TableField("hu_shu_end")
    private Integer hushuend;

    @ApiModelProperty(value = "描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "1:输油管道2:LPG管道")
    @TableField("type_val")
    private Integer typeval;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "规则目录树id")
    @TableField("rule_parent")
    private String ruleParent;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mId;

    @ApiModelProperty(value = "强度设计系数")
    @TableField("design_factor")
    private Double designfactor;

}
