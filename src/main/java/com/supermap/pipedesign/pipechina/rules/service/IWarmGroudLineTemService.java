package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.WarmGroudLineTem;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  取缓地面线生成规则-模型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Repository
public interface IWarmGroudLineTemService extends IService<WarmGroudLineTem> {

 /**
  * 查询取缓地面线生成规则
  * <AUTHOR>
  * @Description
  * @Date 2023/3/16 14:40
  **/
List<WarmGroudLineTem> selInfoWarmTem();

/**
 * 更新取缓地面线生成规则
 * <AUTHOR>
 * @Description
 * @Date 2023/3/16 14:42
 **/
int updateWarmTem(Map<String,List<WarmGroudLineTem>> map);


 }
