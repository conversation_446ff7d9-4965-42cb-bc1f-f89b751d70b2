package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.OrientDrillThrough;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.vo.OrientDrillThroughProVo;
import org.springframework.stereotype.Repository;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Repository
public interface IOrientDrillThroughService extends IService<OrientDrillThrough> {

 /**
 * 添加信息
 *
 * @param orientDrillThrough
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int insert(OrientDrillThrough orientDrillThrough);

 /**
 * 删除信息
 *
 * @param orientDrillThroughId
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int delete(String orientDrillThroughId);

 /**
 * 更新信息
 *
 * @param orientDrillThrough
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int update(@Valid OrientDrillThroughProVo orientDrillThrough);

 /**
 * 全部查询
 *
 * @param type
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.OrientDrillThrough>
 * @Date 2023-03-21
 * @auther eomer
 */
 List<OrientDrillThrough> list(Integer type, String projectId);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-21
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);



}
