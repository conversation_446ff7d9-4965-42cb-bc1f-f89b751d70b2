package com.supermap.pipedesign.pipechina.rules.entity.dto;

import com.supermap.common.entity.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EntityTypeDeriveDto
        extends BaseDto {

    @ApiModelProperty(value = "专业id")
    private String subjectId;

    @ApiModelProperty(value = "阶段id")
    private String stageinnercode;

    @ApiModelProperty(value = "1已关联2未关联")
    private Integer type;

}
