package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 沿中线创建点实体算法 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_on_line_tem")
@ApiModel(value="OnLineTem对象", description="沿中线创建点实体算法")
public class OnLineTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "间距")
    @TableField("spacing")
    private int spacing;

    @ApiModelProperty(value = "垂直于管线的距离")
    @TableField("distance")
    private Integer distance;

    @ApiModelProperty(value = "关联项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "元数据库中规则和算法关联表的PKID")
    @TableField("rule_algorit_id")
    private String ruleAlgoritId;

    @ApiModelProperty(value = "方向 0是左侧1是右侧")
    @TableField("direction")
    private Integer direction;


}
