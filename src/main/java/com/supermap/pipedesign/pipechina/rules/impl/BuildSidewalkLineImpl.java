package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.BuildSidewalkLine;
import com.supermap.pipedesign.pipechina.rules.dao.BuildSidewalkLineMapper;
import com.supermap.pipedesign.pipechina.rules.entity.BuildSidewalkLineTem;
import com.supermap.pipedesign.pipechina.rules.service.IBuildSidewalkLineService;
import com.supermap.tools.gson.GsonUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.*;


/**
 * <p>
 * 施工便道线路段生成规则-项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-18
 */
@Service("BuildSidewalkLineImpl")
@RequiredArgsConstructor
public class BuildSidewalkLineImpl extends ServiceImpl<BuildSidewalkLineMapper, BuildSidewalkLine> implements IBuildSidewalkLineService {

    private final BuildSidewalkLineMapper buildSidewalkLineMapper;

    /**
    * 添加施工便道线路段生成规则-项目信息
    *
    * @param map
    * @return int
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public int insert(Map<String,Object> map,String projectId) {

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.isNotNull("row");
        queryWrapper.eq("project_id",projectId);
        List<BuildSidewalkLine> list1 = buildSidewalkLineMapper.selectList(queryWrapper);
        for (BuildSidewalkLine elbowBendLineSectionTem : list1) {
            QueryWrapper queryWrapper1 = new QueryWrapper();
            queryWrapper1.eq("pkid",elbowBendLineSectionTem.getPkid());
            queryWrapper1.eq("project_id",projectId);
            buildSidewalkLineMapper.delete(queryWrapper1);
        }
        List<Map> list = GsonUtil.ObjectToList(map.get("list"),Map.class);
        for (Map<String,Object> mapTem : list){
            int row = (int)Math.round((Double) mapTem.get("row")) ;
            mapTem.remove("row");
            for(Map.Entry<String,Object> key:mapTem.entrySet()){
                BuildSidewalkLine tem = new BuildSidewalkLine();
                tem.setBendCode(key.getKey());
                tem.setFieldValue(key.getValue().toString());
                tem.setRow(row);
                tem.setProjectId(projectId);
                tem.setPkid(UUID.randomUUID().toString());
                buildSidewalkLineMapper.insert(tem);

            }
        }
        return 1;
    }

    /**
    * 删除施工便道线路段生成规则-项目信息
    *
    * @param buildSidewalkLineId
    * @return int
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public int delete(String buildSidewalkLineId) {
        return buildSidewalkLineMapper.deleteById(buildSidewalkLineId);
    }

    /**
    * 更新施工便道线路段生成规则-项目信息
    *
    * @param buildSidewalkLineTem
    * @return int
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public int update(String buildSidewalkLineTem,String projectId) {
        BuildSidewalkLine buildSidewalkLineTem1 = new BuildSidewalkLine();
        buildSidewalkLineTem1.setFieldValue(buildSidewalkLineTem);
        buildSidewalkLineTem1.setProjectId(projectId);
        buildSidewalkLineTem1.setPkid("1");
        QueryWrapper queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("pkid","1");
        queryWrapper.eq("project_id",projectId);
        BuildSidewalkLine buildSidewalkLineTem2 = buildSidewalkLineMapper.selectOne(queryWrapper);
        if (buildSidewalkLineTem2!=null){
            return  buildSidewalkLineMapper.update(buildSidewalkLineTem1,queryWrapper);
        }
        return buildSidewalkLineMapper.insert(buildSidewalkLineTem1);
    }

    /**
     * 全部查询
     *
     * @param projectId
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.BuildSidewalkLine>
     * @Date 2023-03-18
     * @auther eomer
     */
    @Override
    public List<Map<String, Object>> list(String projectId) {

        QueryWrapper<BuildSidewalkLine> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("row");
        queryWrapper.isNotNull("row");
        queryWrapper.eq("project_id", projectId);
        List<BuildSidewalkLine> elbowBendLineSectionTems = buildSidewalkLineMapper.selectList(queryWrapper);
        List<List<BuildSidewalkLine> >elbowBendLineSectionTemList = new ArrayList<>();
        //行号分组循环
        int count= 0;
        int row=0;
        Map<Integer, Integer> map = new HashMap<>();
        for (BuildSidewalkLine lineSectionTem : elbowBendLineSectionTems) {
            if (lineSectionTem.getRow()==1){
                row++;
            }
        }
        for (BuildSidewalkLine bendLineSectionTem : elbowBendLineSectionTems) {
            Integer bendLineSection = map.get(bendLineSectionTem.getRow());
            if (bendLineSection==null){
                map.put(bendLineSectionTem.getRow(),count);
                ArrayList<BuildSidewalkLine> objects = new ArrayList<>();
                objects.add(bendLineSectionTem);
                elbowBendLineSectionTemList.add(objects);
            }else{
                List<BuildSidewalkLine> objects = elbowBendLineSectionTemList.get(count);
                objects.add(bendLineSectionTem);

            }
            if (elbowBendLineSectionTemList.get(count).size()==row){
                count ++;
            }
        }
        List<Map<String,Object>> mapList=new ArrayList<>();
        for (List<BuildSidewalkLine> sectionTemList : elbowBendLineSectionTemList) {
            Map<String,Object> data= new HashMap<>();

            for (BuildSidewalkLine bendLineSectionTem : sectionTemList) {
                data.put(bendLineSectionTem.getBendCode(),bendLineSectionTem.getFieldValue());
            }
            mapList.add(data);
        }
        return mapList;
    }


    @Override
    public BuildSidewalkLine select(String projectid) {
        QueryWrapper query = new QueryWrapper();
        query.eq("project_id", projectid);
        query.eq("pkid", "1");
        return buildSidewalkLineMapper.selectOne(query);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<BuildSidewalkLine> buildSidewalkLineIPage = new Page<>();
        buildSidewalkLineIPage.setCurrent(current);
        buildSidewalkLineIPage.setSize(size);

        QueryWrapper<BuildSidewalkLine> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return buildSidewalkLineMapper.selectPage(buildSidewalkLineIPage, queryWrapper);
    }


}
