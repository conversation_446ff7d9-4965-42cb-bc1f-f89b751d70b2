package com.supermap.pipedesign.pipechina.rules.entity.vo;


import com.supermap.common.entity.DictText;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OverlappingAlgorithmTem对象", description="")
public class OverlappingAlgorithmTemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String pkid;

    private String algorithmName;

    private String ruleid;

    @DictText(keyColumn = "pkid",tableName="wbs_dictionary",target = "layer",textColumn = "dictionary_name")
    private String layer;

    private Integer distance;

    private Integer angle;

    private DictionaryVo dictionaryVo;


}
