package com.supermap.pipedesign.pipechina.rules.service;

import com.supermap.pipedesign.pipechina.rules.entity.AreaLevelPartition;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.AreaLevelPartitionTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.AreaLevelPartitionTemVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.AreaLevelPartitionVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
@Repository
public interface IAreaLevelPartitionService extends IService<AreaLevelPartition> {

 /**
 * 全部查询
 *
 * @param areaLevelPartition
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.AreaLevelPartition>
 * @Date 2023-02-06
 * @auther eomer
 */
 List<AreaLevelPartitionTem> listTem(AreaLevelPartitionTem areaLevelPartition);


 /**
  * 全部查询
  *
  * @param areaLevelPartition
  * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.AreaLevelPartition>
  * @Date 2023-02-06
  * @auther eomer
  */
 List<AreaLevelPartition> list(AreaLevelPartition areaLevelPartition);

 int update(AreaLevelPartitionTemVo levelPartitionTemVos);

// int updatePro(AreaLevelPartitionVo levelPartitionVos,String projectid,String ruleparent);
 int updatePro(AreaLevelPartitionVo levelPartitionVos);
}
