package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 空间规则算法表，定义了算法关联的图层和运算规则与范围 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_no_traffic_algorithm")
@ApiModel(value="NoTrafficAlgorithm对象", description="空间规则算法表，定义了算法关联的图层和运算规则与范围")
public class NoTrafficAlgorithm implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "算法名称")
    @TableField("algorithm_name")
    private String algorithmName;

    @ApiModelProperty(value = "所属规则小类（所属法律法规）")
    @TableField("ruleid")
    private String ruleid;

    @ApiModelProperty(value = "规则范围")
    @TableField("layer")
    private String layer;

    @ApiModelProperty(value = "单位：米")
    @TableField("radius")
    private Integer radius;

    @ApiModelProperty(value = "0：禁止通过，1：应、宜避开")
    @TableField("type")
    private Integer type;


}
