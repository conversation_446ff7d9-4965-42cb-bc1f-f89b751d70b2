package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 空间规则算法表，定义了算法关联的图层和运算规则与范围 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_distance_algorithm")
@ApiModel(value="DistanceAlgorithm对象", description="空间规则算法表，定义了算法关联的图层和运算规则与范围")
public class DistanceAlgorithm implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "算法名称")
    @TableField("algorithm_name")
    private String algorithmName;

    @ApiModelProperty(value = "所属规则小类（所属法律法规）")
    @TableField("ruleid")
    private String ruleid;

    @ApiModelProperty(value = "适用图层")
    @TableField("layer")
    private String layer;

    @TableField("distance")
    private String distance;


}
