package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.supermap.pipedesign.pipechina.rules.entity.SyphonTransitionTem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 弯管 过渡段规则模板表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Data
public class SyphonTransitionTemVo {

    @ApiModelProperty(value= "弯管数据")
    List<SyphonTransitionTem> list;



}
