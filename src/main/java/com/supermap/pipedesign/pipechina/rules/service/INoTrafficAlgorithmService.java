package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.NoTrafficAlgorithm;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 空间规则算法表，定义了算法关联的图层和运算规则与范围 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-16
 */
@Repository
public interface INoTrafficAlgorithmService extends IService<NoTrafficAlgorithm> {

 /**
 * 添加空间规则算法表，定义了算法关联的图层和运算规则与范围信息
 *
 * @param noTrafficAlgorithm
 * @return int
 * @Date 2023-01-16
 * @auther eomer
 */
 int insert(NoTrafficAlgorithm noTrafficAlgorithm);

 /**
 * 删除空间规则算法表，定义了算法关联的图层和运算规则与范围信息
 *
 * @param noTrafficAlgorithmId
 * @return int
 * @Date 2023-01-16
 * @auther eomer
 */
 int delete(String noTrafficAlgorithmId);

 /**
 * 更新空间规则算法表，定义了算法关联的图层和运算规则与范围信息
 *
 * @param noTrafficAlgorithm
 * @return int
 * @Date 2023-01-16
 * @auther eomer
 */
 int update(NoTrafficAlgorithm noTrafficAlgorithm);

 /**
 * 全部查询
 *
 * @param noTrafficAlgorithm
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.NoTrafficAlgorithm>
 * @Date 2023-01-16
 * @auther eomer
 */
 List<NoTrafficAlgorithm> list(NoTrafficAlgorithm noTrafficAlgorithm);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-16
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
