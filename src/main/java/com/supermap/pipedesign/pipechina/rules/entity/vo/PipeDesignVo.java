package com.supermap.pipedesign.pipechina.rules.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 管材设计规则 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PipeDesigntem对象", description="管材设计规则")
public class PipeDesignVo implements Serializable {

    @ApiModelProperty(value = "主键id")
    private String pkid;

    @ApiModelProperty(value = "钢级")
    private String steelGrade;

    @ApiModelProperty(value = "管径（mm）")
    private String pipeDiameter;

    @ApiModelProperty(value = "规则（1.穿越 2.弯头弯管）")
    private Integer rules;

    @ApiModelProperty(value = "管材id")
    private String tubeId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    private List<PipeDesignList> pipeDesigntemList;

}
