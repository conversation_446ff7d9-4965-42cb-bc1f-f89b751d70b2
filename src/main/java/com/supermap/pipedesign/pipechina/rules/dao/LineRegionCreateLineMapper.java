package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.rules.entity.LineRegionCreateLine;
import com.supermap.pipedesign.pipechina.rules.entity.vo.LineRegionCreateLineVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 基于中线与面图层交叉线创建线实体 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Mapper
public interface LineRegionCreateLineMapper extends BaseMapper<LineRegionCreateLine> {

    @Select("select * from qi_line_region_create_line where rule_algorit_id=#{rulealgoritid} and project_id=#{projectid}")
    List<LineRegionCreateLineVo> selByRulealgoritid(@Param(value = "rulealgoritid") String rulealgoritid ,@Param(value = "projectid") String projectid);
}
