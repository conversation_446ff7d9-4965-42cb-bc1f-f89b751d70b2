package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.Field;
import com.supermap.pipedesign.pipechina.rules.dao.FieldMapper;
import com.supermap.pipedesign.pipechina.rules.service.IFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 质检字段表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("FieldImpl")
public class FieldImpl extends ServiceImpl<FieldMapper, Field> implements IFieldService {

    @Autowired
    private FieldMapper fieldMapper;

    /**
    * 添加质检字段表信息
    *
    * @param field
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(Field field) {

        //field.setUserId(JavaUtils.getUUID36());
        //field.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return fieldMapper.insert(field);
    }

    /**
    * 删除质检字段表信息
    *
    * @param fieldId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String fieldId) {
        return fieldMapper.deleteById(fieldId);
    }

    /**
    * 更新质检字段表信息
    *
    * @param field
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(Field field) {
        return fieldMapper.updateById(field);
    }

    /**
    * 全部查询
    *
    * @param field
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.Field>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<Field> list(Field field) {

        QueryWrapper<Field> queryWrapper = new QueryWrapper<>();

        return fieldMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<Field> fieldIPage = new Page<>();
        fieldIPage.setCurrent(current);
        fieldIPage.setSize(size);

        QueryWrapper<Field> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return fieldMapper.selectPage(fieldIPage, queryWrapper);
    }


}
