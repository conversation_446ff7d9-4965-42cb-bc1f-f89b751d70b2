package com.supermap.pipedesign.pipechina.rules.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName DirTree
 * @Description 目录树
 * @Date 2022/12/27 21:53
 * @auther eomer
 */
@Data
public class CommTree {
    @ApiModelProperty(value = "唯一编号")
    private String pkid;
    @ApiModelProperty(value = "上级目录id")
    private String pid;
    @ApiModelProperty(value = "目录名称")
    private String lableName;
    @ApiModelProperty(value = "文件路径")
    private String pathUrl;
    @ApiModelProperty(value = "项目id")
    private String projectId;
    //子集
    private List<CommTree> commTreeList;
}
