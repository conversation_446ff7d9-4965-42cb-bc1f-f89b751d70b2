package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_build_sidewalk_line_tem")
@ApiModel(value="BuildSidewalkLineTem对象", description="")
public class BuildSidewalkLineTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一主键")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "字典code")
    @TableField("bend_code")
    private String bendCode;

    @ApiModelProperty(value = "值")
    @TableField("field_value")
    private String fieldValue;

    @ApiModelProperty(value = "行号")
    @TableField("row")
    private Integer row;

    @ApiModelProperty(value = "表头序号")
    @TableField("filed_index")
    private Integer filedIndex;


}
