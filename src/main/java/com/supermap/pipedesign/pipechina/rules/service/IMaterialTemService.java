package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.rules.entity.MaterialTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.MeterialsTemVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 材料表计算模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-11
 */
@Repository
public interface IMaterialTemService extends IService<MaterialTem> {

 /**
 * 添加材料表计算模板信息
 *
 * @param vo
 * @return int
 * @Date 2023-03-11
 * @auther eomer
 */
 int insert(MeterialsTemVo vo, User userId);

 /**
 * 删除材料表计算模板信息
 *
 * @param materialTemId
 * @return int
 * @Date 2023-03-11
 * @auther eomer
 */
 int delete(String materialTemId);

 /**
  * 更新材料表计算模板信息
  *
  * @param materialTem
  * @param userId
  * @return int
  * @Date 2023-03-11
  * @auther eomer
  */
 int update(MeterialsTemVo materialTem, User userId);

 /**
 * 全部查询
 *
 * @param stageCode
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.MaterialTem>
 * @Date 2023-03-11
 * @auther eomer
 */
 List<MaterialTem> list(String stageCode, String subjectId);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param stageCode
 * @param subjectId
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-11
 * @auther eomer
 */
 IPage pageList(long current, long size, String stageCode, String subjectId, String searchName);

 /**
  * 材料表计算模板-删除规则列表
  * @param stageCode
  * @param materialId
  * @return
  */
 int deleMaterialModel(String stageCode, String materialId);

 /**
  * 材料表计算模板-添加规则列表
  * @param stageCode
  * @param materialId
  * @return
  */
 int addMaterialCode(String stageCode, String materialId);

    MeterialsTemVo selInfoByMaterialId(String materialId);
}
