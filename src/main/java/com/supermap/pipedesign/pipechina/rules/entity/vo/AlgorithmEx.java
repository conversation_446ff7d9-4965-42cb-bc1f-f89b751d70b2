package com.supermap.pipedesign.pipechina.rules.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel(value="算法信息", description="算法信息")
public class AlgorithmEx {


  @ApiModelProperty(value ="算法类型")
  private String algorithmtype;

    @ApiModelProperty(value = "算法名称")
    private String algorithmname;

    @ApiModelProperty(value = "图层对应的字典表pid")
    private String layerDictPid;

    @ApiModelProperty(value = "算法字段")
    private List<FieldsEntity> fields;



    @Data
    @ApiModel(value="算法字段", description="算法字段")
    public class FieldsEntity {

        @ApiModelProperty(value = "字段编码")
        private String code;
        @ApiModelProperty(value = "子集")
        private List<?> children;
        @ApiModelProperty(value = "字段名称")
        private String name;
        @ApiModelProperty(value = "字段类型 type: text 文本 ， combox 下拉框， layer 选择图层， dir 合并单元格 ")
        private String type;


    }
}
