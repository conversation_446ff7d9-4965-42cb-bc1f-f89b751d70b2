package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.PutSlope;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.vo.PutSlopeProVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.PutSlopeVo;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 放坡规则-项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Repository
public interface IPutSlopeService extends IService<PutSlope> {

 /**
 * 添加放坡规则-项目信息
 *
 * @param putSlope
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int insert(PutSlope putSlope);

 /**
 * 删除放坡规则-项目信息
 *
 * @param putSlopeId
 * @return int
 * @Date 2023-03-21
 * @auther eomer
 */
 int delete(String putSlopeId);


 /**
 * 全部查询
 *
 * @param projectId
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.PutSlope>
 * @Date 2023-03-21
 * @auther eomer
 */
 List<PutSlope> listPutSlopeTem(String projectId);




    int updatePutSlopeTem(PutSlopeProVo putSlopeTem);


}
