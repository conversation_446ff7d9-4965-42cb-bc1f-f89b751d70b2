package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeEntitytype;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Repository
public interface IEntitytypeMergeEntitytypeService extends IService<EntitytypeMergeEntitytype> {

 /**
 * 添加信息
 *
 * @param entitytypeMergeEntitytype
 * @return int
 * @Date 2023-02-10
 * @auther eomer
 */
 int insert(EntitytypeMergeEntitytype entitytypeMergeEntitytype);

 /**
 * 删除信息
 *
 * @param entitytypeMergeEntitytypeId
 * @return int
 * @Date 2023-02-10
 * @auther eomer
 */
 int delete(String entitytypeMergeEntitytypeId);

 /**
 * 更新信息
 *
 * @param entitytypeMergeEntitytype
 * @return int
 * @Date 2023-02-10
 * @auther eomer
 */
 int update(EntitytypeMergeEntitytype entitytypeMergeEntitytype);

 /**
 * 全部查询
 *
 * @param entitytypeMergeEntitytype
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeEntitytype>
 * @Date 2023-02-10
 * @auther eomer
 */
 List<EntitytypeMergeEntitytype> list(EntitytypeMergeEntitytype entitytypeMergeEntitytype);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-02-10
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
