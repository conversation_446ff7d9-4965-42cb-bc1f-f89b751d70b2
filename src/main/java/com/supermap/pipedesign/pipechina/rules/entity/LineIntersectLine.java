package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 基于中线转角创建点实体算法 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_line_intersect_line")
@ApiModel(value="LineIntersectLine对象", description="基于中线转角创建点实体算法")
public class LineIntersectLine implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "废弃")
    @TableField("dateset_name")
    private String datesetname;

    @ApiModelProperty(value = "废弃")
    @TableField("distance")
    private Integer distance;

    @ApiModelProperty(value = "关联项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "元数据库中规则和算法关联表的PKID")
    @TableField("rule_algorit_id")
    private String rulealgoritid;

    @ApiModelProperty(value = "最小角度")
    @TableField("angle_min")
    private Integer anglemin;

    @ApiModelProperty(value = "最大角度")
    @TableField("angle_max")
    private Integer anglemax;

    @ApiModelProperty(value = "废弃")
    @TableField("position")
    private String position;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
