package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.OrientDrillThroughTem;
import com.supermap.pipedesign.pipechina.rules.dao.OrientDrillThroughTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.vo.OrientDrillThroughTemVo;
import com.supermap.pipedesign.pipechina.rules.service.IOrientDrillThroughTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Service("OrientDrillThroughTemImpl")
public class OrientDrillThroughTemImpl extends ServiceImpl<OrientDrillThroughTemMapper, OrientDrillThroughTem> implements IOrientDrillThroughTemService {

    @Autowired
    private OrientDrillThroughTemMapper orientDrillThroughTemMapper;

    /**
    * 添加信息
    *
    * @param orientDrillThroughTem
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int insert(OrientDrillThroughTem orientDrillThroughTem) {

        //orientDrillThroughTem.setUserId(JavaUtils.getUUID36());
        //orientDrillThroughTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return orientDrillThroughTemMapper.insert(orientDrillThroughTem);
    }

    /**
    * 删除信息
    *
    * @param orientDrillThroughTemId
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int delete(String orientDrillThroughTemId) {
        return orientDrillThroughTemMapper.deleteById(orientDrillThroughTemId);
    }

    /**
    * 更新信息
    *
    * @param orientDrillThroughTem
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int update(OrientDrillThroughTemVo orientDrillThroughTem) {
        List<OrientDrillThroughTem> excavationThroughTemList = orientDrillThroughTem.getExcavationThroughTemList();
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("type",excavationThroughTemList.get(0).getType());
        orientDrillThroughTemMapper.delete(queryWrapper);
        for (OrientDrillThroughTem tem : excavationThroughTemList) {
            if (tem.getType() == 0){
                if (tem.getFileCode().equals("min_depth_of_directional_drilling_pipe_under_scour_line")
                        && tem.getValue() == null){
                    tem.setValue(6.0);
                }else if (tem.getFileCode().equals("min_depth_of_directional_drilling_pipe_under_levee")
                        && tem.getValue() == null){
                    tem.setValue(6.0);
                }else if (tem.getFileCode().equals("min_depth_of_directional_drilling_pipe_under_riverbed")
                        && tem.getValue() == null){
                    tem.setValue(10.0);
                }else if (tem.getFileCode().equals("min_length_of_the_straight_line_section_of_entry_or_exit")
                        && tem.getValue() == null){
                    tem.setValue(10.0);
                }else if (tem.getFileCode().equals("min_curvature_radius_of_directional_drilling_elastic_lay")
                        && tem.getValue() == null){
                    tem.setValue(1200.0);
                }else if (tem.getFileCode().equals("min_entry_angle_of_directional_drilling")
                        && tem.getValue() == null){
                    tem.setValue(8.0);
                }else if (tem.getFileCode().equals("max_entry_angle_of_directional_drilling")
                        && tem.getValue() == null){
                    tem.setValue(20.0);
                }else if (tem.getFileCode().equals("min_exit_angle_of_directional_drilling")
                        && tem.getValue() == null){
                    tem.setValue(4.0);
                }else if (tem.getFileCode().equals("max_exit_angle_of_directional_drilling")
                        && tem.getValue() == null){
                    tem.setValue(12.0);
                }else if (tem.getFileCode().equals("min_distance_from_entry_or_exit_point_to_the_levee")
                        && tem.getValue() == null){
                    tem.setValue(50.0);
                }else if (tem.getFileCode().equals("min_distance_from_entry_or_exit_point_to_the_river_bank")
                        && tem.getValue() == null){
                    tem.setValue(50.0);
                }
            }else if (tem.getType() == 1){
                if (tem.getFileCode().equals("min_area_of_rig_site")
                        && tem.getValue() == null){
                    tem.setValue(900.0);
                }else if (tem.getFileCode().equals("min_width_of_pullback_site")
                        && tem.getValue() == null){
                    tem.setValue(20.0);
                }else if (tem.getFileCode().equals("min_curvature_radius_of_pullback_site_elastic_laying")
                        && tem.getValue() == null){
                    tem.setValue(800.0);
                }else if (tem.getFileCode().equals("min_length_of_the_straight_line_section_of_pullback_site")
                        && tem.getValue() == null){
                    tem.setValue(100.0);
                }
            }
            orientDrillThroughTemMapper.insert(tem);
        }
        return 1;
    }

    /**
    * 全部查询
    *
    * @param type
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.OrientDrillThroughTem>
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public List<OrientDrillThroughTem> list(Integer type) {

        QueryWrapper<OrientDrillThroughTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type",type);
        return orientDrillThroughTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<OrientDrillThroughTem> orientDrillThroughTemIPage = new Page<>();
        orientDrillThroughTemIPage.setCurrent(current);
        orientDrillThroughTemIPage.setSize(size);

        QueryWrapper<OrientDrillThroughTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return orientDrillThroughTemMapper.selectPage(orientDrillThroughTemIPage, queryWrapper);
    }


}
