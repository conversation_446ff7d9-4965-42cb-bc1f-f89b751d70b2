package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.rules.entity.SubsectionRulesFieldTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.SubsectionRulesFieldTemVo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 * 分项统计规则模板字段表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@Mapper
public interface SubsectionRulesFieldTemMapper extends BaseMapper<SubsectionRulesFieldTem> {
//    @Results(id="SubsectionRulesFieldTemVo",value = {
//            @Result(id = true,column = "pkid",property = "pkid"),
//            @Result(column = "pid",property = "pid"),
//            @Result(column = "field_prefix",property = "fieldPrefix"),
//            @Result(column = "field_function",property = "fieldFunction"),
//            @Result(column = "field_entity_type_id",property = "fieldEntityTypeId"),
//            @Result(column = "field_suffix",property = "fieldSuffix"),
//            @Result(column = "pid",property = "fieldList",javaType = List.class,
//                    many=@Many(select="com.supermap.pipedesign.pipechina.rules.dao.SubsectionRulesFieldTemMapper.selectInfoByRulesId"))})
    @Select("select * from qi_subsection_rules_field_tem where subsection_id = #{subsectionId} and pid is null ")
    List<SubsectionRulesFieldTemVo> selectInfoByRulesId(@Param("subsectionId") String subsectionId );

}
