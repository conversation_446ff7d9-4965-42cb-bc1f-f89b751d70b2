package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.SpaceRuleChildRegisterTem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.vo.SpaceRoutVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.SpaceRoutVoSelect;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Repository
public interface ISpaceRuleChildRegisterTemService extends IService<SpaceRuleChildRegisterTem> {

 /**
 * 添加信息
 *
 * @param spaceRuleChildRegisterTem
 * @return int
 * @Date 2023-01-29
 * @auther eomer
 */
 int insert(SpaceRuleChildRegisterTem spaceRuleChildRegisterTem);

 /**
 * 删除信息
 *
 * @param spaceRuleChildRegisterTemId
 * @return int
 * @Date 2023-01-29
 * @auther eomer
 */
 int delete(String spaceRuleChildRegisterTemId);

 /**
 * 更新信息
 *
 * @param spaceRuleChildRegisterTem
 * @return int
 * @Date 2023-01-29
 * @auther eomer
 */
 int update(SpaceRuleChildRegisterTem spaceRuleChildRegisterTem);

 /**
 * 全部查询
 *
 * @param spaceRuleChildRegisterTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.SpaceRuleChildRegisterTem>
 * @Date 2023-01-29
 * @auther eomer
 */
 List<SpaceRuleChildRegisterTem> list(SpaceRuleChildRegisterTem spaceRuleChildRegisterTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-29
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);



 IPage<SpaceRoutVo> selSpaceRoutList(long page,long pageSize,String srCategoryId);

 int createSSRules(SpaceRoutVo spaceRoutVo);

 SpaceRoutVoSelect selSRByPkid(String pkid);

 int updateSRbyPkid( SpaceRoutVo spaceRoutVo);

 int delRulesByid(String pkid);

 }
