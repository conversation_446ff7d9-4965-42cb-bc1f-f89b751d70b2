package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.rules.entity.LineReferenceObject;
import com.supermap.pipedesign.pipechina.rules.entity.LineReferenceObjectTem;
import com.supermap.pipedesign.pipechina.rules.entity.vo.LineIntersectLayerVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.LineReferenceObjectVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Mapper
public interface LineReferenceObjectTemMapper extends BaseMapper<LineReferenceObjectTem> {

    @Select("select * from qi_line_reference_object_tem where rule_algorit_id=#{rulealgoritid}")
    List<LineReferenceObjectVo> selByRulealgoritid(@Param(value = "rulealgoritid") String rulealgoritid );


}
