package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 路由碰撞规则关联表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_space_rule_child_register_rcc")
@ApiModel(value="SpaceRuleChildRegisterRcc对象", description="路由碰撞规则关联表")
public class SpaceRuleChildRegisterRcc implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "规则id")
    @TableField("rule_id")
    private String ruleid;

    @ApiModelProperty(value = "算法id")
    @TableField("algorithm_id")
    private String colruleid;

    @ApiModelProperty(value = "各种算法的算法类型")
    @TableField("algorithm_type")
    private String algorithmtype;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
