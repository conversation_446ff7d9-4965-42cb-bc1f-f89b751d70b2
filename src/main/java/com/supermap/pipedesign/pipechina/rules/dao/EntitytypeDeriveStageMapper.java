package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.rules.entity.EntitytypeDeriveStage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-11
 */
@Mapper
public interface EntitytypeDeriveStageMapper extends BaseMapper<EntitytypeDeriveStage> {


    @Select("select derive_pkid from qi_entitytype_derive_stage where stage_id=#{stageid} and subject_id=#{subjectid} ")
    List<String> selDerivePkidBySSid(@Param(value = "stageid") String stageId,@Param(value = "subjectid") String subjectId);


}
