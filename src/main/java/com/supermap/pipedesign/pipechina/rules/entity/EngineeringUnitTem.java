package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Date;

/**
 * <p>
 * 工程量统计模板 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_engineering_unit_tem")
@ApiModel(value="EngineeringUnitTem对象", description="工程量统计模板")
public class EngineeringUnitTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "pkid")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "专业ID")
    @TableField("subject_id")
    private String subjectId;

    @ApiModelProperty(value = "关联项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "施工阶段内部编号( 0,1,2,3,4)")
    @TableField("stage_inner_code")
    private String stageInnerCode;

    @ApiModelProperty(value = "规则名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "单位")
    @TableField("unit")
    private String unit;

    @ApiModelProperty(value = "描述")
    @TableField("describe")
    private String describe;

    @ApiModelProperty(value = "更新人ID")
    @TableField("update_id")
    private String updateId;

    @ApiModelProperty(value = "更新人姓名")
    @TableField("update_name")
    private String updateName;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @ApiModelProperty(value = "录入人ID")
    @TableField("create_id")
    private String createId;

    @ApiModelProperty(value = "录入人姓名")
    @TableField("create_name")
    private String createName;

    @ApiModelProperty(value = "录入时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "序列启用时间")
    @TableField("sequence_start")
    private Date sequenceStart;

    @ApiModelProperty(value = "序列失效时间")
    @TableField("sequence_end")
    private Date sequenceEnd;

    @ApiModelProperty(value = "序列状态(1:有效;-1:删除;0:未使用)")
    @TableField("sequence_state")
    private Integer sequenceState;

    @ApiModelProperty(value = "序列上一版本ID")
    @TableField("sequence_orgid")
    private String sequenceOrgid;


}
