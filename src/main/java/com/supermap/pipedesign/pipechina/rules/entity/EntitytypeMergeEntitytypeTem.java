package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 实体合并的合并列 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_entitytype_merge_entitytype_tem")
@ApiModel(value="EntitytypeMergeEntitytypeTem对象", description="实体合并的合并列")
public class EntitytypeMergeEntitytypeTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @TableField("merge_pkid")
    private String mergepkid;

    @TableField("entity_type_pkid")
    private String entitytypepkid;

    @TableField("priority")
    private Integer priority;

    @TableField("entity_type_enname")
    private String entitytypeenname;

    @TableField("entitytype_alias")
    private String entitytypealias;


}
