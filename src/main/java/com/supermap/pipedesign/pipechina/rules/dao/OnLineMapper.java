package com.supermap.pipedesign.pipechina.rules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.rules.entity.OnLine;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Mapper
public interface OnLineMapper extends BaseMapper<OnLine> {

    @Select("select * from qi_on_line where project_id=#{projectid}")
    List<OnLine>  selByProjectId(@Param(value = "projectid") String projectId);
}
