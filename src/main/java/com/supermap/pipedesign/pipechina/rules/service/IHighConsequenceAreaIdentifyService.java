package com.supermap.pipedesign.pipechina.rules.service;

import com.supermap.pipedesign.pipechina.rules.entity.HighConsequenceAreaIdentify;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.vo.HighConsequenceAreaIdentifyVo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
@Repository
public interface IHighConsequenceAreaIdentifyService extends IService<HighConsequenceAreaIdentify> {


 /**
 * 全部查询
 *
 * @param highConsequenceAreaIdentify
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.HighConsequenceAreaIdentify>
 * @Date 2023-02-06
 * @auther eomer
 */
 Map<String,Object> list(HighConsequenceAreaIdentify highConsequenceAreaIdentify);

 /**
  * 全部查询
  *
  * @param highConsequenceAreaIdentify
  * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.HighConsequenceAreaIdentify>
  * @Date 2023-02-06
  * @auther eomer
  */
 Map<String,Object> listPro(HighConsequenceAreaIdentify highConsequenceAreaIdentify);


 int updateHCAI( HighConsequenceAreaIdentifyVo highConsequenceAreaIdentifyVo );


 int updateProHCAI(HighConsequenceAreaIdentifyVo highConsequenceAreaIdentifyVo);
}
