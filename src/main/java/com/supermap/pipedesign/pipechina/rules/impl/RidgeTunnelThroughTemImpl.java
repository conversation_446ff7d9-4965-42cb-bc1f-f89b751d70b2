package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.entity.RidgeTunnelThroughTem;
import com.supermap.pipedesign.pipechina.rules.dao.RidgeTunnelThroughTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.vo.RidgeTunnelThroughTemVo;
import com.supermap.pipedesign.pipechina.rules.service.IRidgeTunnelThroughTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 山岭隧道穿越规则-模型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Service("RidgeTunnelThroughTemImpl")
public class RidgeTunnelThroughTemImpl extends ServiceImpl<RidgeTunnelThroughTemMapper, RidgeTunnelThroughTem> implements IRidgeTunnelThroughTemService {

    @Autowired
    private RidgeTunnelThroughTemMapper ridgeTunnelThroughTemMapper;

    /**
    * 添加山岭隧道穿越规则-模型信息
    *
    * @param ridgeTunnelThroughTem
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int insert(RidgeTunnelThroughTem ridgeTunnelThroughTem) {

        //ridgeTunnelThroughTem.setUserId(JavaUtils.getUUID36());
        //ridgeTunnelThroughTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return ridgeTunnelThroughTemMapper.insert(ridgeTunnelThroughTem);
    }

    /**
    * 删除山岭隧道穿越规则-模型信息
    *
    * @param ridgeTunnelThroughTemId
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int delete(String ridgeTunnelThroughTemId) {
        return ridgeTunnelThroughTemMapper.deleteById(ridgeTunnelThroughTemId);
    }

    /**
    * 更新山岭隧道穿越规则-模型信息
    *
    * @param ridgeTunnelThroughTem
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int update(@Valid RidgeTunnelThroughTemVo ridgeTunnelThroughTem) {
        List<RidgeTunnelThroughTem> excavationThroughTemList = ridgeTunnelThroughTem.getRidgeTunnelThroughTemList();
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("type",excavationThroughTemList.get(0).getType());
        ridgeTunnelThroughTemMapper.delete(queryWrapper);
        for (RidgeTunnelThroughTem tem : excavationThroughTemList) {
            if(tem.getType() == 0){
                if (tem.getFileCode().equals("max_longitudinal_line_slope_of_mountain_tunnel")
                        && tem.getValue() == null){
                    tem.setValue(70.0);
                }else if (tem.getFileCode().equals("min_longitudinal_line_slope_of_mountain_tunnel")
                        && tem.getValue() == null){
                    tem.setValue(0.3);
                }
            } else if (tem.getType() == 1){
                if (tem.getFileCode().equals("max_extension_length_from_the_area_with_poor_surrounding")
                        && tem.getValue() == null){
                    tem.setValue(10.0);
                }else if (tem.getFileCode().equals("min_extension_length_from_the_area_with_poor_surrounding")
                        && tem.getValue() == null){
                    tem.setValue(5.0);
                }
            }
            ridgeTunnelThroughTemMapper.insert(tem);
        }
        return 1;
    }

    /**
    * 全部查询
    *
    * @param type
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.RidgeTunnelThroughTem>
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public List<RidgeTunnelThroughTem> list(Integer type) {

        QueryWrapper<RidgeTunnelThroughTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type",type);
        return ridgeTunnelThroughTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<RidgeTunnelThroughTem> ridgeTunnelThroughTemIPage = new Page<>();
        ridgeTunnelThroughTemIPage.setCurrent(current);
        ridgeTunnelThroughTemIPage.setSize(size);

        QueryWrapper<RidgeTunnelThroughTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return ridgeTunnelThroughTemMapper.selectPage(ridgeTunnelThroughTemIPage, queryWrapper);
    }


}
