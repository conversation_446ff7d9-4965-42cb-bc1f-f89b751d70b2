package com.supermap.pipedesign.pipechina.rules.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
public  class AlgorithmDataVo {

    @ApiModelProperty(value = "算法标题")
    private String algorithmTitle;

    @ApiModelProperty(value = "算法类型")
    private String algorithmType;

    @ApiModelProperty(value = "图层对应的字典表pid")
    private String layerDictPid;

    @ApiModelProperty(value = "算法参数")
    private String algorithmParam;

    @ApiModelProperty(value = "展示形式1点2线3面")
    private String showType;

    private List<AlgorithmFieldsData> fieldsData;

    private List<DataBean> data;

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }


}


