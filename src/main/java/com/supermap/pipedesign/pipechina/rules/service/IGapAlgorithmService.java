package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.GapAlgorithm;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 线面缓冲区与管线叠加分析求交，结果与道路线判断不相交。 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Repository
public interface IGapAlgorithmService extends IService<GapAlgorithm> {

 /**
 * 添加线面缓冲区与管线叠加分析求交，结果与道路线判断不相交。信息
 *
 * @param gapAlgorithm
 * @return int
 * @Date 2023-02-14
 * @auther eomer
 */
 int insert(GapAlgorithm gapAlgorithm);

 /**
 * 删除线面缓冲区与管线叠加分析求交，结果与道路线判断不相交。信息
 *
 * @param gapAlgorithmId
 * @return int
 * @Date 2023-02-14
 * @auther eomer
 */
 int delete(String gapAlgorithmId);

 /**
 * 更新线面缓冲区与管线叠加分析求交，结果与道路线判断不相交。信息
 *
 * @param gapAlgorithm
 * @return int
 * @Date 2023-02-14
 * @auther eomer
 */
 int update(GapAlgorithm gapAlgorithm);

 /**
 * 全部查询
 *
 * @param gapAlgorithm
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.GapAlgorithm>
 * @Date 2023-02-14
 * @auther eomer
 */
 List<GapAlgorithm> list(GapAlgorithm gapAlgorithm);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-02-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
