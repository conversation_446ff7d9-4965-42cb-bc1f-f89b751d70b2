package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawing;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawingTem;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 通用图 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Repository
public interface IGeneralDrawingService extends IService<GeneralDrawing> {

 /**
  * 添加通用图信息
  *
  * @param file
  * @param generalDrawing
  * @param userid
  * @return int
  * @Date 2023-03-10
  * @auther eomer
  */
 int insert(MultipartFile file, GeneralDrawing generalDrawing, User userid);

 /**
  * 删除通用图信息
  *
  * @param generalDrawingId
  * @param projectId
  * @return int
  * @Date 2023-03-10
  * @auther eomer
  */
 int delete(String generalDrawingId, String projectId);

 /**
  * 更新通用图信息
  *
  * @param generalDrawing
  * @param userid
  * @return int
  * @Date 2023-03-10
  * @auther eomer
  */
 int update(GeneralDrawing generalDrawing, User userid);

 /**
  * 全部查询
  *
  * @param projectId
  * @param pid
  * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawing>
  * @Date 2023-03-10
  * @auther eomer
  */
 List<GeneralDrawing> list(String projectId, String pid);

 /**
  * 分页查询
  *
  * @param current
  * @param size
  * @param projectId
  * @param searchName
  * @return com.baomidou.mybatisplus.core.metadata.IPage
  * @Date 2023-03-10
  * @auther eomer
  */
 IPage pageList(long current, long size, String pid, String projectId, String searchName);


 List<GeneralDrawing> select(String id, String pid, String projectId);


    List<GeneralDrawing> entityGeneralTree(GeneralDrawingTem generalDrawingTem, String projectId);

 GeneralDrawing downloadGeneral(String pkid, String projectId);
}
