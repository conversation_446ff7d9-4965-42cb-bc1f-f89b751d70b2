package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.BuildSidewalkLine;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 施工便道线路段生成规则-项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-18
 */
@Repository
public interface IBuildSidewalkLineService extends IService<BuildSidewalkLine> {

 /**
 * 添加施工便道线路段生成规则-项目信息
 *
 * @param map
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int insert(Map<String, Object> map, String projectid);

 /**
 * 删除施工便道线路段生成规则-项目信息
 *
 * @param buildSidewalkLineId
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int delete(String buildSidewalkLineId);

 /**
 * 更新施工便道线路段生成规则-项目信息
 *
 * @param fieldValue
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int update(String fieldValue, String projectid);

 /**
  * 全部查询
  *
  * @param buildSidewalkLine
  * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.BuildSidewalkLine>
  * @Date 2023-03-18
  * @auther eomer
  */
 List<Map<String, Object>> list(String buildSidewalkLine);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-18
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 BuildSidewalkLine select(String projectid);



}
