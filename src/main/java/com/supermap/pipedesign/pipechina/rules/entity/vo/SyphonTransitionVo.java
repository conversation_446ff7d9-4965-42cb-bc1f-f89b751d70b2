package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.supermap.pipedesign.pipechina.rules.entity.SyphonTransition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 弯管 过渡段规则 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Data
public class SyphonTransitionVo {


    @ApiModelProperty(value = "弯管过渡段规则")
    private List<SyphonTransition> list;


}
