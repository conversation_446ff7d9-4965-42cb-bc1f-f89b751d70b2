package com.supermap.pipedesign.pipechina.rules.entity.vo;

import com.supermap.pipedesign.pipechina.rules.entity.EngineeringUnitTem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 工程量统计模板 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
@Data
public class EngineeringUnitTemVo{

    @ApiModelProperty(value = "工程量")
    EngineeringUnitTem engTem;

    @ApiModelProperty(value = "算法函数")
    List<EUFunTemVo> funTemVoList;




}
