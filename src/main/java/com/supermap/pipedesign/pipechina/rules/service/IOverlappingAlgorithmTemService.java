package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.OverlappingAlgorithmTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Repository
public interface IOverlappingAlgorithmTemService extends IService<OverlappingAlgorithmTem> {

 /**
 * 添加信息
 *
 * @param overlappingAlgorithmTem
 * @return int
 * @Date 2023-02-14
 * @auther eomer
 */
 int insert(OverlappingAlgorithmTem overlappingAlgorithmTem);

 /**
 * 删除信息
 *
 * @param overlappingAlgorithmTemId
 * @return int
 * @Date 2023-02-14
 * @auther eomer
 */
 int delete(String overlappingAlgorithmTemId);

 /**
 * 更新信息
 *
 * @param overlappingAlgorithmTem
 * @return int
 * @Date 2023-02-14
 * @auther eomer
 */
 int update(OverlappingAlgorithmTem overlappingAlgorithmTem);

 /**
 * 全部查询
 *
 * @param overlappingAlgorithmTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.OverlappingAlgorithmTem>
 * @Date 2023-02-14
 * @auther eomer
 */
 List<OverlappingAlgorithmTem> list(OverlappingAlgorithmTem overlappingAlgorithmTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-02-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
