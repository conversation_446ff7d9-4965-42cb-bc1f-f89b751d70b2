package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_orient_drill_through")
@ApiModel(value="OrientDrillThrough对象", description="")
public class OrientDrillThrough implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    @ApiModelProperty(value = "规则code")
    @TableField("file_code")
    private String fileCode;

    @ApiModelProperty(value = "规则名称")
    @TableField("file_name")
    private String fileName;

    @ApiModelProperty(value = "数值")
    @TableField("value")
    private Double value;

    @ApiModelProperty(value = "类型（1.纵断面，2.开挖。。。）")
    @TableField("type")
    private Integer type;


}
