package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.ElbowBendLineSection;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Repository
public interface IElbowBendLineSectionService extends IService<ElbowBendLineSection> {

 /**
 * 添加信息
 *
 * @param projectId
 * @return int
 * @Date 2023-03-15
 * @auther eomer
 */
 int insert(Map<String,Object> map,String projectId);

 /**
 * 删除信息
 *
 * @param elbowBendLineSectionId
 * @return int
 * @Date 2023-03-15
 * @auther eomer
 */
 int delete(String elbowBendLineSectionId);

 /**
 * 更新信息
 *
 * @param elbowBendLineSection
 * @return int
 * @Date 2023-03-15
 * @auther eomer
 */
 int update(ElbowBendLineSection elbowBendLineSection);

 /**
 * 全部查询
 *
 * @param elbowBendLineSection
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.ElbowBendLineSection>
 * @Date 2023-03-15
 * @auther eomer
 */

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-15
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


    List<Map<String, Object>> list(ElbowBendLineSection elbowBendLineSectionTem, String projectId);
}
