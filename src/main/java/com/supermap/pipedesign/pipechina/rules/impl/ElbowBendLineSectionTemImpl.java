package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.rules.dao.ElbowBendLineSectionTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.ElbowBendLineSectionTem;
import com.supermap.pipedesign.pipechina.rules.service.IElbowBendLineSectionTemService;
import com.supermap.tools.gson.GsonUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Service("ElbowBendLineSectionTemImpl")
@RequiredArgsConstructor
public class ElbowBendLineSectionTemImpl extends ServiceImpl<ElbowBendLineSectionTemMapper, ElbowBendLineSectionTem> implements IElbowBendLineSectionTemService {


    private final ElbowBendLineSectionTemMapper elbowBendLineSectionTemMapper;

    private final DictionaryMapper dictionaryMapper;

    /**
     * 全部查询
     *
     * @param titleTem
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.TitleTem>
     * @Date 2023-03-15
     * @auther eomer
     */
    @Override
    public List<Dictionary> list(String titleTem) {
        QueryWrapper<Dictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("sort");
        queryWrapper.eq("pid", "8f571f24-c6db-4613-9903-d038384c6e71");
        List<Dictionary> dictionaries = dictionaryMapper.selectList(queryWrapper);
        Dictionary dictionary = new Dictionary();
        dictionary.setDictionaryname("弯管类型");
        dictionary.setDictionarycode("wanguanleixing");
        dictionary.setSort(0);
        Dictionary dictionary1 = new Dictionary();
        dictionary1.setDictionaryname("管径(mm)");
        dictionary1.setDictionarycode("guanjing");
        dictionary1.setSort(1);
        dictionaries.add(0, dictionary);
        dictionaries.add(1, dictionary1);
        return dictionaries;
    }


    /**
     * 添加信息
     *
     * @param
     * @return int
     * @Date 2023-03-15
     * @auther eomer
     */
    @Override
    public int insert(Map<String, Object> map) {
        QueryWrapper<ElbowBendLineSectionTem> queryWrapper = new QueryWrapper<ElbowBendLineSectionTem>();
        List<ElbowBendLineSectionTem> list1 = elbowBendLineSectionTemMapper.selectList(queryWrapper);
        for (ElbowBendLineSectionTem elbowBendLineSectionTem : list1) {
            elbowBendLineSectionTemMapper.deleteById(elbowBendLineSectionTem);
        }
        List<Map> list = GsonUtil.ObjectToList(map.get("list"), Map.class);
        for (Map<String, Object> mapTem : list) {
            int row = (int) Math.round((Double) mapTem.get("row"));
            mapTem.remove("row");
            for (Map.Entry<String, Object> key : mapTem.entrySet()) {
                ElbowBendLineSectionTem tem = new ElbowBendLineSectionTem();
                tem.setBendCode(key.getKey());
                tem.setFieldValue(key.getValue().toString());
                tem.setRow(row);
                elbowBendLineSectionTemMapper.insert(tem);

            }
        }
        return 1;
    }

    /**
     * 删除信息
     *
     * @param elbowBendLineSectionTemId
     * @return int
     * @Date 2023-03-15
     * @auther eomer
     */
    @Override
    public int delete(String elbowBendLineSectionTemId) {
        return elbowBendLineSectionTemMapper.deleteById(elbowBendLineSectionTemId);
    }

    /**
     * 更新信息
     *
     * @param elbowBendLineSectionTem
     * @return int
     * @Date 2023-03-15
     * @auther eomer
     */
    @Override
    public int update(ElbowBendLineSectionTem elbowBendLineSectionTem) {
        return elbowBendLineSectionTemMapper.updateById(elbowBendLineSectionTem);
    }

    /**
     * 全部查询
     *
     * @param elbowBendLineSectionTem
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.ElbowBendLineSectionTem>
     * @Date 2023-03-15
     * @auther eomer
     */
    @Override
    public List<Map<String, Object>> list(ElbowBendLineSectionTem elbowBendLineSectionTem) {
        List<Dictionary> titleList = list("");
        QueryWrapper<ElbowBendLineSectionTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("row");
        List<ElbowBendLineSectionTem> elbowBendLineSectionTems = elbowBendLineSectionTemMapper.selectList(queryWrapper);
        List<List<ElbowBendLineSectionTem>> elbowBendLineSectionTemList = new ArrayList<>();
        // elbowBendLineSectionTemList.add(new ArrayList<>());
        // 行号分组循环
        int count = 0;
        int row=0;
        Map<Integer, Integer> map = new HashMap<>();
        for (ElbowBendLineSectionTem lineSectionTem : elbowBendLineSectionTems) {
            if (lineSectionTem.getRow()==1){
                row++;
            }
        }
        for (ElbowBendLineSectionTem bendLineSectionTem : elbowBendLineSectionTems) {


            Integer bendLineSection = map.get(bendLineSectionTem.getRow());
            if (bendLineSection == null) {
                map.put(bendLineSectionTem.getRow(), count);
                ArrayList<ElbowBendLineSectionTem> objects = new ArrayList<>();
                objects.add(bendLineSectionTem);
                elbowBendLineSectionTemList.add(objects);
            } else {
                List<ElbowBendLineSectionTem> objects = elbowBendLineSectionTemList.get(count);
                objects.add(bendLineSectionTem);

            }
            if (elbowBendLineSectionTemList.get(count).size() == row) {
                count++;
                // elbowBendLineSectionTemList.add(new ArrayList<>());
            }
        }
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (List<ElbowBendLineSectionTem> sectionTemList : elbowBendLineSectionTemList) {
            Map<String, Object> data = new HashMap<>();

            for (ElbowBendLineSectionTem bendLineSectionTem : sectionTemList) {
                data.put(bendLineSectionTem.getBendCode(), bendLineSectionTem.getFieldValue());
            }
            mapList.add(data);
        }
        return mapList;
    }

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @param startDate
     * @param endDate
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * @Date 2023-03-15
     * @auther eomer
     */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<ElbowBendLineSectionTem> elbowBendLineSectionTemIPage = new Page<>();
        elbowBendLineSectionTemIPage.setCurrent(current);
        elbowBendLineSectionTemIPage.setSize(size);

        QueryWrapper<ElbowBendLineSectionTem> queryWrapper = new QueryWrapper<>();

        if (startDate != null && endDate != null) {
            queryWrapper.between("col_create_time", startDate, endDate);
        }

        return elbowBendLineSectionTemMapper.selectPage(elbowBendLineSectionTemIPage, queryWrapper);
    }


}
