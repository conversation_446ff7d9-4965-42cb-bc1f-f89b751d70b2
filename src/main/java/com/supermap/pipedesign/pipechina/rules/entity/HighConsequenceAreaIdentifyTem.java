package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * $高后果区识别规则-模型 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_high_consequence_area_identify_tem")
@ApiModel(value="HighConsequenceAreaIdentifyTem对象", description="$高后果区识别规则-模型")
public class HighConsequenceAreaIdentifyTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "识别项")
    @TableField("identify_item")
    private String identifyitem;

    @ApiModelProperty(value = "高后果区等级")
    @TableField("identify_level")
    private Integer identifylevel;

    @ApiModelProperty(value = "特殊场所和易燃易爆的配置")
    @TableField("identify_param")
    private String identifyparam;

    @ApiModelProperty(value = "1输油(10特殊场所,11易燃易爆)2输气(20特殊场所,21易燃易爆)")
    @TableField("type_val")
    private Integer typeval;


}
