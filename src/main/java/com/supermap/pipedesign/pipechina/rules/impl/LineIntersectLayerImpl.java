package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.rules.dao.*;
import com.supermap.pipedesign.pipechina.rules.entity.*;
import com.supermap.pipedesign.pipechina.rules.service.ILineIntersectLayerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 管线与图层相交算法 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("LineIntersectLayerImpl")
public class LineIntersectLayerImpl extends ServiceImpl<LineIntersectLayerMapper, LineIntersectLayer> implements ILineIntersectLayerService {


    @Autowired
    private LineIntersectLayerMapper lineIntersectLayerMapper;


    /**
    * 添加管线与图层相交算法信息
    *
    * @param lineIntersectLayer
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(LineIntersectLayer lineIntersectLayer) {

        //lineIntersectLayer.setUserId(JavaUtils.getUUID36());
        //lineIntersectLayer.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return lineIntersectLayerMapper.insert(lineIntersectLayer);
    }

    /**
    * 删除管线与图层相交算法信息
    *
    * @param lineIntersectLayerId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String lineIntersectLayerId) {
        return lineIntersectLayerMapper.deleteById(lineIntersectLayerId);
    }

    /**
    * 更新管线与图层相交算法信息
    *
    * @param lineIntersectLayer
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(LineIntersectLayer lineIntersectLayer) {
        return lineIntersectLayerMapper.updateById(lineIntersectLayer);
    }

    /**
    * 全部查询
    *
    * @param lineIntersectLayer
    * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.LineIntersectLayer>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<LineIntersectLayer> list(LineIntersectLayer lineIntersectLayer) {

        QueryWrapper<LineIntersectLayer> queryWrapper = new QueryWrapper<>();

        return lineIntersectLayerMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<LineIntersectLayer> lineIntersectLayerIPage = new Page<>();
        lineIntersectLayerIPage.setCurrent(current);
        lineIntersectLayerIPage.setSize(size);

        QueryWrapper<LineIntersectLayer> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return lineIntersectLayerMapper.selectPage(lineIntersectLayerIPage, queryWrapper);
    }





}
