package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.rules.entity.PutSlopeTem;
import com.supermap.pipedesign.pipechina.rules.dao.PutSlopeTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.vo.PutSlopeVo;
import com.supermap.pipedesign.pipechina.rules.service.IPutSlopeTemService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Service("PutSlopeTemImpl")
@RequiredArgsConstructor
public class PutSlopeTemImpl extends ServiceImpl<PutSlopeTemMapper, PutSlopeTem> implements IPutSlopeTemService {

    private final PutSlopeTemMapper putSlopeTemMapper;

    /**
    * 添加信息
    *
    * @param putSlopeTem
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int insert(PutSlopeTem putSlopeTem) {

        //putSlopeTem.setUserId(JavaUtils.getUUID36());
        //putSlopeTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return putSlopeTemMapper.insert(putSlopeTem);
    }

    /**
    * 删除信息
    *
    * @param putSlopeTemId
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int delete(String putSlopeTemId) {
        return putSlopeTemMapper.deleteById(putSlopeTemId);
    }

    /**
    * 更新信息
    *
    * @param putSlopeTem
    * @return int
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public int updatePutSlopeTem(@Valid PutSlopeVo putSlopeTem) {
        QueryWrapper<PutSlopeTem> queryWrapper= new QueryWrapper<>();
        putSlopeTemMapper.delete(queryWrapper);
        List<PutSlopeTem> putSlopeTemList = putSlopeTem.getPutSlopeTemList();
        for (PutSlopeTem slopeTem : putSlopeTemList) {
            if (slopeTem.getPkid()==null) {
                slopeTem.setPkid(UUID.randomUUID().toString());
            }
          putSlopeTemMapper.insert(slopeTem);
        }
        return 1;
    }

    /**
     * 全部查询
     *
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.PutSlopeTem>
     * @Date 2023-03-21
     * @auther eomer
     */
    @Override
    public List<PutSlopeTem> listPutSlopeTem() {

        QueryWrapper<PutSlopeTem> queryWrapper = new QueryWrapper<>();
        return putSlopeTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-21
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<PutSlopeTem> putSlopeTemIPage = new Page<>();
        putSlopeTemIPage.setCurrent(current);
        putSlopeTemIPage.setSize(size);

        QueryWrapper<PutSlopeTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return putSlopeTemMapper.selectPage(putSlopeTemIPage, queryWrapper);
    }

    private final DictionaryMapper dictionaryMapper;
    @Override
    public List dictionaryList() {
        QueryWrapper<Dictionary> queryWrapperType = new QueryWrapper<>();
        queryWrapperType.eq("pid","60056301004001003");
        List<Dictionary> dictionariesType = dictionaryMapper.selectList(queryWrapperType);
        QueryWrapper<Dictionary> queryWrapperStatus = new QueryWrapper<>();
        queryWrapperStatus.eq("pid","60056301004001004");
        List<Dictionary> dictionariesStatus = dictionaryMapper.selectList(queryWrapperStatus);
        List dictionaryList = new ArrayList<>();
        dictionaryList.add(dictionariesType);
        dictionaryList.add(dictionariesStatus);
        return dictionaryList;
    }
}
