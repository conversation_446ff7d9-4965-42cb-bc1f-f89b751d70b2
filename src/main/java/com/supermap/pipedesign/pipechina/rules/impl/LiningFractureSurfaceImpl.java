package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.rules.entity.LiningFractureSurface;
import com.supermap.pipedesign.pipechina.rules.dao.LiningFractureSurfaceMapper;
import com.supermap.pipedesign.pipechina.rules.service.ILiningFractureSurfaceService;
import com.supermap.tools.base.JavaUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;


/**
 * <p>
 * 衬砌断面规则-项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Service("LiningFractureSurfaceImpl")
@RequiredArgsConstructor
public class LiningFractureSurfaceImpl extends ServiceImpl<LiningFractureSurfaceMapper, LiningFractureSurface> implements ILiningFractureSurfaceService {

    private final LiningFractureSurfaceMapper liningFractureSurfaceMapper;

    /**
     * 添加衬砌断面规则-项目信息
     *
     * @param liningFractureSurface
     * @return int
     * @Date 2023-03-22
     * @auther eomer
     */
    @Override
    public int insert(LiningFractureSurface liningFractureSurface) {
        QueryWrapper<LiningFractureSurface> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid", liningFractureSurface.getPkid());
        queryWrapper.eq("project_id", liningFractureSurface.getProjectId());
        LiningFractureSurface liningFractureSurfaceTem1 = liningFractureSurfaceMapper.selectOne(queryWrapper);
        if (liningFractureSurfaceTem1 == null) {
            insertLining(liningFractureSurface);
        } else if (!liningFractureSurfaceTem1.getBaseSectionWidth().equals(liningFractureSurface.getBaseSectionWidth())) {
            insertLining(liningFractureSurface);
        } else if (!liningFractureSurfaceTem1.getBaseSectionHeight().equals(liningFractureSurface.getBaseSectionHeight())) {
            insertLining(liningFractureSurface);
        } else if (!liningFractureSurfaceTem1.getLinerSurrongdingRockGrade().equals(liningFractureSurface.getLinerSurrongdingRockGrade())) {
            insertLining(liningFractureSurface);
        } else if(!liningFractureSurfaceTem1.getSectionHeight().equals(liningFractureSurface.getSectionHeight())){
            insertLining(liningFractureSurface);
        }else if(!liningFractureSurfaceTem1.getSectionWidth().equals(liningFractureSurface.getSectionWidth())){
            insertLining(liningFractureSurface);
        }else if(liningFractureSurfaceTem1.getSectionName()!=null&&!liningFractureSurfaceTem1.getSectionName().equals(liningFractureSurface.getSectionName())){
            insertLining(liningFractureSurface);
        }
        else {
            liningFractureSurfaceMapper.update(liningFractureSurface, queryWrapper);
        }
        return 1;
    }

    /**
     * 删除衬砌断面规则-项目信息
     *
     * @param liningFractureSurfaceId
     * @param projectId
     * @return int
     * @Date 2023-03-22
     * @auther eomer
     */
    @Override
    public int delete(String liningFractureSurfaceId, String projectId) {
        QueryWrapper<LiningFractureSurface> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid", liningFractureSurfaceId);
        queryWrapper.eq("project_id", projectId);
        return liningFractureSurfaceMapper.delete(queryWrapper);
    }

    /**
     * 更新衬砌断面规则-项目信息
     *
     * @param liningFractureSurface
     * @return int
     * @Date 2023-03-22
     * @auther eomer
     */
    @Override
    public int update(LiningFractureSurface liningFractureSurface) {
        QueryWrapper<LiningFractureSurface> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid", liningFractureSurface.getPkid());
        queryWrapper.eq("project_id", liningFractureSurface.getProjectId());
        LiningFractureSurface liningFractureSurfaceTem1 = liningFractureSurfaceMapper.selectOne(queryWrapper);
        if (liningFractureSurfaceTem1 == null) {
            insertLining(liningFractureSurface);
        } else if (!liningFractureSurfaceTem1.getBaseSectionWidth().equals(liningFractureSurface.getBaseSectionWidth())) {
            insertLining(liningFractureSurface);
        } else if (!liningFractureSurfaceTem1.getBaseSectionHeight().equals(liningFractureSurface.getBaseSectionHeight())) {
            insertLining(liningFractureSurface);
        } else if (!liningFractureSurfaceTem1.getBaseBoardArchConcreteGrade().equals(liningFractureSurface.getBaseBoardArchConcreteGrade())) {
            insertLining(liningFractureSurface);
        } else if(!liningFractureSurfaceTem1.getSectionHeight().equals(liningFractureSurface.getSectionHeight())){
            insertLining(liningFractureSurface);
        }else if(!liningFractureSurfaceTem1.getSectionWidth().equals(liningFractureSurface.getSectionWidth())){
            insertLining(liningFractureSurface);
        }else if(!liningFractureSurfaceTem1.getSectionName().equals(liningFractureSurface.getSectionName())){
            insertLining(liningFractureSurface);
        }
        else {
            liningFractureSurfaceMapper.update(liningFractureSurface, queryWrapper);
        }
        return 1;
    }

    private void insertLining(LiningFractureSurface liningFractureSurface) {
        liningFractureSurface.setPkid(UUID.randomUUID().toString());
        liningFractureSurfaceMapper.insert(liningFractureSurface);
    }

    /**
     * 全部查询
     *
     * @param projectId
     * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.LiningFractureSurface>
     * @Date 2023-03-22
     * @auther eomer
     */
    @Override
    public List<LiningFractureSurface> list(String projectId) {

        QueryWrapper<LiningFractureSurface> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        return liningFractureSurfaceMapper.selectList(queryWrapper);
    }

    @Override
    public LiningFractureSurface selectById(String pkid, String projectId) {
        QueryWrapper<LiningFractureSurface> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid", pkid);
        queryWrapper.eq("project_id", projectId);
        return liningFractureSurfaceMapper.selectOne(queryWrapper);
    }

    private final DictionaryMapper  dictionaryMapper;
    @Override
    public List selectTree(String projectId) {
        QueryWrapper<LiningFractureSurface> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct base_section_width,base_section_height");
        queryWrapper.eq("project_id", projectId);
        List<LiningFractureSurface> liningFractureSurfaceTems = liningFractureSurfaceMapper.selectList(queryWrapper);
        List<Object> objects = new ArrayList<>();
        for (LiningFractureSurface liningFractureSurfaceTem : liningFractureSurfaceTems) {
            Map<String, Object> map = new HashMap<>();
            map.put("name", "隧道" + liningFractureSurfaceTem.getBaseSectionWidth() + "m×" + liningFractureSurfaceTem.getBaseSectionHeight() + "m");
            QueryWrapper<LiningFractureSurface> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.select("distinct base_section_width,base_section_height,liner_surrongding_rock_grade");
            queryWrapper1.eq("base_section_width", liningFractureSurfaceTem.getBaseSectionWidth());
            queryWrapper1.eq("base_section_height", liningFractureSurfaceTem.getBaseSectionHeight());
            queryWrapper1.eq("standard_or_not", 0);
            queryWrapper1.eq("project_id", projectId);
            List<LiningFractureSurface> liningFractureSurfaceTems1 = liningFractureSurfaceMapper.selectList(queryWrapper1);
            ArrayList<Object> objects2 = new ArrayList<>();
            for (LiningFractureSurface fractureSurfaceTem : liningFractureSurfaceTems1) {
                List<Object> objects3 = new ArrayList<>();
                Map<String, Object> properties = new HashMap<>();
                if (fractureSurfaceTem.getLinerSurrongdingRockGrade() != null) {
                    QueryWrapper<Dictionary> queryWrapper3 = new QueryWrapper<>();
                    queryWrapper3.eq("dictionary_code", fractureSurfaceTem.getLinerSurrongdingRockGrade());
                    Dictionary dictionary = dictionaryMapper.selectOne(queryWrapper3);
                    properties.put("name", dictionary.getDictionaryname() + "围岩衬砌");
                    QueryWrapper<LiningFractureSurface> queryWrapper2 = new QueryWrapper<>();
                    queryWrapper1.select("distinct base_section_width,base_section_height,section_width,section_height,liner_surrongding_rock_grade");
                    queryWrapper2.eq("base_section_width", fractureSurfaceTem.getBaseSectionWidth());
                    queryWrapper2.eq("base_section_height", fractureSurfaceTem.getBaseSectionHeight());
                    queryWrapper2.eq("liner_surrongding_rock_grade", fractureSurfaceTem.getLinerSurrongdingRockGrade());
                    queryWrapper2.eq("project_id", projectId);
                    List<LiningFractureSurface> liningFractureSurfaceTems2 = liningFractureSurfaceMapper.selectList(queryWrapper2);
                    for (LiningFractureSurface surfaceTem : liningFractureSurfaceTems2) {
                        Map<String, String> properties3 = new HashMap<>();
                        Double baseSectionWidth = surfaceTem.getSectionWidth();
                        Double baseSectionHeight = surfaceTem.getSectionHeight();
                        properties3.put("pkid", surfaceTem.getPkid());
                        properties3.put("name", "衬砌" + baseSectionWidth + "m×" + baseSectionHeight + "m");
                        objects3.add(properties3);
                    }
                    properties.put("child", objects3);
                    objects2.add(properties);
                }

            }
            QueryWrapper<LiningFractureSurface> queryOne = new QueryWrapper<>();
            queryOne.select("distinct base_section_width,base_section_height,section_name");
            queryOne.eq("base_section_width", liningFractureSurfaceTem.getBaseSectionWidth());
            queryOne.eq("base_section_height", liningFractureSurfaceTem.getBaseSectionHeight());
            queryOne.eq("standard_or_not", 1);
            queryOne.eq("project_id", projectId);
            List<LiningFractureSurface> listOne = liningFractureSurfaceMapper.selectList(queryOne);
            if (!JavaUtils.isEmtryOrNull(listOne)) {
                for (LiningFractureSurface temOne : listOne) {
                    List<Object> objects4 = new ArrayList<>();
                    Map<String, Object> properties2 = new HashMap<>();

                    properties2.put("name", temOne.getSectionName());
                    QueryWrapper<LiningFractureSurface> queryWrapper3 = new QueryWrapper<>();
                    queryWrapper3.eq("base_section_width", temOne.getBaseSectionWidth());
                    queryWrapper3.eq("base_section_height", temOne.getBaseSectionHeight());
                    queryWrapper3.eq("section_name", temOne.getSectionName());
                    queryWrapper3.eq("project_id", projectId);
                    List<LiningFractureSurface> liningFractureSurfaceTems3 = liningFractureSurfaceMapper.selectList(queryWrapper3);
                    for (LiningFractureSurface surfaceTemOne : liningFractureSurfaceTems3) {
                        Map<String, String> properties3 = new HashMap<>();
                        Double baseSectionWidth = surfaceTemOne.getSectionWidth();
                        Double baseSectionHeight = surfaceTemOne.getSectionHeight();
                        properties3.put("pkid", surfaceTemOne.getPkid());
                        properties3.put("name", baseSectionWidth + "m×" + baseSectionHeight + "m");
                        objects4.add(properties3);
                    }
                    properties2.put("child", objects4);
                    objects2.add(properties2);
                }


            }
            map.put("child", objects2);
            objects.add(map);
        }
        return objects;
    }
}
