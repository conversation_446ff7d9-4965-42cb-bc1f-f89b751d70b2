package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.rules.dao.LongitudinalLineMapper;
import com.supermap.pipedesign.pipechina.rules.entity.LongitudinalLine;
import com.supermap.pipedesign.pipechina.rules.service.ILongitudinalLineService;
import com.supermap.tools.base.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;


/**
 * <p>
 * 纵管线生成规则-项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Service("LongitudinalLineImpl")
public class LongitudinalLineImpl extends ServiceImpl<LongitudinalLineMapper, LongitudinalLine> implements ILongitudinalLineService {

    @Autowired
    private LongitudinalLineMapper longitudinalLineMapper;

    /**
     * 根据项目id
     * 查询纵管线
     * <AUTHOR>
     * @Description
     * @Date 2023/3/16 10:49
     **/
    @Override
    public LongitudinalLine getInfo(String projectId) {
        QueryWrapper<LongitudinalLine> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id",projectId);
        return longitudinalLineMapper.selectOne(queryWrapper);
    }

    /**
     * 更新纵管线
     * <AUTHOR>
     * @Description
     * @Date 2023/3/16 10:52
     **/
    @Override
    public int updateLongitudinalLine(LongitudinalLine longitudinalLine) {
        if (JavaUtils.isEmtryOrNull(longitudinalLine.getPkid())){
            longitudinalLine.setPkid(UUID.randomUUID().toString());
            return longitudinalLineMapper.insert(longitudinalLine);
        }
        QueryWrapper<LongitudinalLine> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid",longitudinalLine.getPkid());
        queryWrapper.eq("project_id",longitudinalLine.getProjectId());
        return longitudinalLineMapper.update(longitudinalLine,queryWrapper);
    }
}
