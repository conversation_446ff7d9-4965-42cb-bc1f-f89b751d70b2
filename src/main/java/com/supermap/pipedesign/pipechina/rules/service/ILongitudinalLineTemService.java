package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.entity.LongitudinalLineTem;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 纵管线生成规则-模型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Repository
public interface ILongitudinalLineTemService extends IService<LongitudinalLineTem> {

/**
 * 查询纵管线
 * <AUTHOR>
 * @Description
 * @Date 2023/3/16 10:49
 **/
 LongitudinalLineTem getInfo();


 /**
  * 更新纵管线
  * <AUTHOR>
  * @Description
  * @Date 2023/3/16 10:52
  **/
 int updateLongitudinalLineTem(LongitudinalLineTem longitudinalLineTem);


 }
