package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeStage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-11
 */
@Repository
public interface IEntitytypeMergeStageService extends IService<EntitytypeMergeStage> {

 /**
 * 添加信息
 *
 * @param entitytypeMergeStage
 * @return int
 * @Date 2023-02-11
 * @auther eomer
 */
 int insert(EntitytypeMergeStage entitytypeMergeStage);

 /**
 * 删除信息
 *
 * @param entitytypeMergeStageId
 * @return int
 * @Date 2023-02-11
 * @auther eomer
 */
 int delete(String entitytypeMergeStageId);

 /**
 * 更新信息
 *
 * @param entitytypeMergeStage
 * @return int
 * @Date 2023-02-11
 * @auther eomer
 */
 int update(EntitytypeMergeStage entitytypeMergeStage);

 /**
 * 全部查询
 *
 * @param entitytypeMergeStage
 * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.EntitytypeMergeStage>
 * @Date 2023-02-11
 * @auther eomer
 */
 List<EntitytypeMergeStage> list(EntitytypeMergeStage entitytypeMergeStage);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-02-11
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
