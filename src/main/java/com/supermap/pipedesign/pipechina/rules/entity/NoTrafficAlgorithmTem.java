package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.supermap.common.entity.DictText;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_no_traffic_algorithm_tem")
@ApiModel(value="NoTrafficAlgorithmTem对象", description="")
public class NoTrafficAlgorithmTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @TableField("algorithm_name")
    private String algorithmName;

    @TableField("ruleid")
    private String ruleid;

    @TableField("layer")
    private String layer;

    @TableField("radius")
    private Integer radius;

    @TableField("type")
    private Integer type;


}
