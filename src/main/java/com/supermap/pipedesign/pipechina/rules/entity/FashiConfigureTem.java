package com.supermap.pipedesign.pipechina.rules.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * $阀室设置规则 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("qi_fashi_configure_tem")
@ApiModel(value="FashiConfigureTem对象", description="$阀室设置规则")
public class FashiConfigureTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "地区等级")
    @TableField("levelval")
    private Integer levelval;

    @ApiModelProperty(value = "阀室间距")
    @TableField("distance")
    private Integer distance;

    @ApiModelProperty(value = "调增")
    @TableField("incremental")
    private Integer incremental;

    @ApiModelProperty(value = "说明")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "(1 输油管道; 2 输气管道; 3 LPG管道)")
    @TableField("type_val")
    private Integer typeval;


}
