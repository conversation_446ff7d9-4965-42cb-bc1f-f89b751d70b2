package com.supermap.pipedesign.pipechina.rules.service;

import com.supermap.pipedesign.pipechina.rules.entity.SpaceRuleTypeRegist;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.rules.impl.SpaceRuleTypeRegistImpl;
import org.springframework.stereotype.Repository;


/**
 * <p>
 * 空间规则类型注册表，如禁止通过区域、宜、应避开区域、间距规定、交叉规定...... 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-17
 */
@Repository
public interface ISpaceRuleTypeRegistService extends IService<SpaceRuleTypeRegist> {

       SpaceRuleTypeRegistImpl.RuleDicMap selTreeSpaceRout(String projectid);

 }
