package com.supermap.pipedesign.pipechina.rules.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.rules.entity.SubsectionRules;
import com.supermap.pipedesign.pipechina.rules.entity.SubsectionRulesField;
import com.supermap.pipedesign.pipechina.rules.entity.dto.QuantitiesDto;
import com.supermap.pipedesign.pipechina.rules.entity.vo.SubsectionRulesFieldVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.SubsectionRulesVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 分项统计规则模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@Repository
public interface ISubsectionRulesService extends IService<SubsectionRules> {

 /**
  * 添加分项统计规则模板信息
  *
  * @param subsectionRules
  * @return int
  * @Date 2023-03-07
  * @auther eomer
  */
 int insert(SubsectionRules subsectionRules);

 /**
  * 删除分项统计规则模板信息
  *
  * @param subsectionRulesId
  * @return int
  * @Date 2023-03-07
  * @auther eomer
  */
 int delete(String subsectionRulesId, String projectId);

 /**
  * 更新分项统计规则模板信息
  *
  * @param subsectionRules
  * @return int
  * @Date 2023-03-07
  * @auther eomer
  */
 int update(SubsectionRules subsectionRules);

 /**
  * 全部查询
  *
  * @return java.util.List<com.supermap.pipedesign.pipechina.rules.entity.SubsectionRules>
  * @Date 2023-03-07
  * @auther eomer
  */
 List<SubsectionRules> list(String stageCode, String subjectId,String projectId);

 /**
  * 分页查询
  *
  * @return com.baomidou.mybatisplus.core.metadata.IPage
  * @Date 2023-03-07
  * @auther eomer
  */
 IPage pageList(QuantitiesDto dto);

 /**
  * 分项统计-根据id查看
  * @param pkid
  * @param projectId
  * @return
  */
 SubsectionRulesVo selInfoById(String pkid, String projectId);


 /**
  * 分项统计-根据id查看字段
  * @param pkid
  * @param fieldName
  * @param projectId
  * @return
  */
 SubsectionRulesFieldVo selectFieldById(String pkid, String fieldName, String projectId);

 /**
  * 分项统计-根据id查看算法
  * @param pkid
  * @param projectId
  * @return
  */
 SubsectionRulesField selectAlgById(String pkid, String projectId);

 int createProQTTRules(SubsectionRulesVo subsectionRulesTemVo, User userId);

 int updateProQTTRules(SubsectionRulesVo subsectionRulesTemVo, User userId);
}
