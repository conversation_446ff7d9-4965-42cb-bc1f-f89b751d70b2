package com.supermap.pipedesign.pipechina.rules.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.rules.dao.GeneralTreeMapper;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralTree;
import com.supermap.pipedesign.pipechina.rules.entity.vo.GeneralTreeVo;
import com.supermap.pipedesign.pipechina.rules.service.IGeneralTreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 通用图目录树 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Service("GeneralTreeImpl")
public class GeneralTreeImpl extends ServiceImpl<GeneralTreeMapper, GeneralTree> implements IGeneralTreeService {

    @Autowired
    private GeneralTreeMapper generalTreeMapper;

    /**
    * 添加通用图目录树信息
    *
    * @param generalTree
    * @return int
    * @Date 2023-03-29
    * @auther eomer
    */
    @Override
    public int insert(GeneralTree generalTree) {

        //generalTree.setUserId(JavaUtils.getUUID36());
        //generalTree.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return generalTreeMapper.insert(generalTree);
    }

    /**
    * 删除通用图目录树信息
    *
    * @param generalTreeId
    * @return int
    * @Date 2023-03-29
    * @auther eomer
    */
    @Override
    public int delete(String generalTreeId) {
        return generalTreeMapper.deleteById(generalTreeId);
    }

    /**
    * 更新通用图目录树信息
    *
    * @param generalTree
    * @return int
    * @Date 2023-03-29
    * @auther eomer
    */
    @Override
    public int update(GeneralTree generalTree) {
        QueryWrapper <GeneralTree>queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid",generalTree.getPkid());
        queryWrapper.eq("project_id",generalTree.getProjectId());
        return generalTreeMapper.update(generalTree,queryWrapper);
    }


    @Override
    public List<GeneralTreeVo> listTree(String projectId) {
        String pkid = "10001";
        List<GeneralTreeVo> generalTreeTemVos = generalTreeMapper.listTree(pkid,projectId);
        return generalTreeTemVos;
    }
}
