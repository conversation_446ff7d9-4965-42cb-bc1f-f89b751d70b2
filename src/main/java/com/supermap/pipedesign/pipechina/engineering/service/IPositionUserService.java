package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.PositionUser;

import java.util.List;

/**
 * <p>
 * 岗位用户关联 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-08
 */
public interface IPositionUserService extends IService<PositionUser> {
    /**
     * 添加岗位用户关联
     * @param positionUser
     * @return
     */
    int insert(PositionUser positionUser);

    /**
     * 批量添加岗位用户关联
     * @param positionUserList
     * @return
     */
    int insertBatch(List<PositionUser> positionUserList);

    /**
     * 删除岗位用户关联
     * @param pkid
     * @return
     */
    int delete(String pkid);

    /**
     * 批量删除岗位用户关联
     * @param pkids
     * @return
     */
    int deleteBatch(List<String> pkids);

    /**
     * 更新岗位用户关联
     * @param positionUser
     * @return
     */
    int update(PositionUser positionUser);

    /**
     * 岗位用户关联全部列表
     * @return
     */
    List<PositionUser> queryList();

    /**
     * 通过岗位编码查询用户列表
     * @param code
     * @return
     */
    List<PositionUser> queryByPositionCode(String code);

}
