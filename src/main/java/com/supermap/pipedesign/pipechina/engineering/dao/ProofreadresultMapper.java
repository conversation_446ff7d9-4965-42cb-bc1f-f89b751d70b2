package com.supermap.pipedesign.pipechina.engineering.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Proofreadresult;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.ProofreadresultVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 评审结果表(存储各类校审评审结果) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface ProofreadresultMapper extends BaseMapper<Proofreadresult> {

    /**
     * 意见总数
     * <AUTHOR>
     * @since 2023-03-24
     * @return 校审单ID+数量
     */
    @Select("select review_id,count(pkid) from wbs_proofread_result " +
            "where (reply_id is null or reply_id = '') group by review_id")
    List<ProofreadresultVo> countOpiniontotal();
    /**
     * 待回复意见数
     * <AUTHOR>
     * @since 2023-03-24
     * @return 校审单ID+数量
     */
    @Select("select review_id,count(pkid) from wbs_proofread_result " +
            "where (reply_id is null or reply_id ='') and pkid not in " +
            "(select comment_id from wbs_proofread_reply ) group by review_id")
    List<ProofreadresultVo> countReplytotal();
    /**
     * 待验证意见数
     * <AUTHOR>
     * @since 2023-03-24
     * @return 校审单ID+数量
     */
    @Select("select review_id,count(pkid) from wbs_proofread_result " +
            "where (reply_id is null or reply_id ='') and pkid not in " +
            "(select comment_id from  wbs_proofread_result t " +
            "left join wbs_proofread_reply p on t.reply_id = p.pkid " +
            "where (reply_id is not null and reply_id !='') group by comment_id) " +
            "group by review_id")
    List<ProofreadresultVo> countVerifytotal();

}
