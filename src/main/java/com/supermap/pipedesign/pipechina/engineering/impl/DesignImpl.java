package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.engineering.dao.*;
import com.supermap.pipedesign.pipechina.engineering.entity.*;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DesignTaskPersionVo;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DesignTaskVo;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DesignVo;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DesignsubTaskVo;
import com.supermap.pipedesign.pipechina.engineering.service.IDesignService;
import com.supermap.pipedesign.pipechina.engineering.service.ISmallVersionManageService;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.CenterlineOkMapper;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.gson.GsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


/**
 * <p>
 * 设计段 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("DesignImpl")
public class DesignImpl extends ServiceImpl<DesignMapper, Design> implements IDesignService {

    @Autowired
    private DesignMapper designMapper;

    @Autowired
    private CenterlineOkMapper centerlineOkMapper;

    @Resource(name = "SmallVersionManageImpl")
    private ISmallVersionManageService versionService;

    /**
    * 添加设计段信息
    *
    * @param design
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int insert(Design design,User user) {

        design.setPkid(UUID.randomUUID().toString());
        design.setCreateid(user.getPkid());
        design.setCreatename(user.getUsername());
        design.setCreatetime(JavaUtils.getTimestamp());
        design.setIsroutcompare("0");
        if (JavaUtils.isNotEmtryOrNull(design.getPid())){
            design.setDesignType(3);
        }
        //起始桩号
//        String startPoint = design.getStakepointnostart();
//        //终点桩号
//        String endPoint = design.getStakepointnoend();
//        QueryWrapper<Design> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("project_id",design.getProjectid());
//        if (design.getDesignType() == 0){
//            //查询是否已存在全段
//            queryWrapper.eq("design_type",0);
//            List<Design> designs = designMapper.selectList(queryWrapper);
//            if (!JavaUtils.isEmtryOrNull(designs)){
//                throw new BusinessException("已存在全段，请假查");
//            }
//        } else {
//            //查全段以外所有设计段
//            queryWrapper.ne("design_type",0);
//            List<Design> list = designMapper.selectList(queryWrapper);
//            for (Design des : list){
//                String stakepointnostart = des.getStakepointnostart();
//                String stakepointnoend = des.getStakepointnoend();
//                QueryWrapper<CenterlineOk> queryWrapper1 = new QueryWrapper<>();
//                queryWrapper1.eq("project_id",des.getProjectid());
//                queryWrapper1.between("stake_point_no",stakepointnostart,stakepointnoend);
//                List<String> list1 = centerlineOkMapper.designPoint(queryWrapper1);
//                if (list1.contains(startPoint)){
//                    if (!startPoint.equals(stakepointnostart)){
//                        throw new BusinessException("桩号交叉，请检查");
//                    }
//                    if (!endPoint.equals(stakepointnoend)){
//                        throw new BusinessException("桩号交叉，请检查");
//                    }
//                }
//            }
//        }
        //单出图归属的一般段id,单出图为虚拟设计段，不进行单独设计
        if (design.getDesignType() == 3){
            //是否虚拟设计端(0真实,1虚拟)
            design.setIsroutcompare("1");
        }
        // 设计段类型(0全段、1一般段、2大中型穿跨越、3单出图)
        if (design.getDesignType() == 1 || design.getDesignType() == 2) {
            QueryWrapper<Design> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_id",design.getProjectid());
            queryWrapper.eq("design_type",0);
            Design selectOne = designMapper.selectOne(queryWrapper);
            if (selectOne == null) {
                throw new BusinessException("缺少全段数据，请核查！");
            }
            // 复制全段数据，新增
            versionService.copyDesign(design, selectOne.getPkid());
        }

        return designMapper.insert(design);
    }

    /**
    * 删除设计段信息
    *
    * @param designId
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int delete(String designId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("design_id",designId);
        designsubTaskMapper.delete(queryWrapper);
        designsubPersonMapper.delete(queryWrapper);
        return designMapper.deleteById(designId);
    }

    /**
    * 更新设计段信息
    *
    * @param design
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int update(Design design, User user) {
        design.setCreatetime(JavaUtils.getTimestamp());
        design.setCreateid(user.getPkid());
        design.setCreatename(user.getUsername());
        return designMapper.updateById(design);
    }

    /**
    * 全部查询
    *
    * @param design
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Design>
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<Design> list(Design design) {

        QueryWrapper<Design> queryWrapper = new QueryWrapper<>();

        return designMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<Design> designIPage = new Page<>();
        designIPage.setCurrent(current);
        designIPage.setSize(size);

        QueryWrapper<Design> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return designMapper.selectPage(designIPage, queryWrapper);
    }

    /**
     * 根绝子工程id查设计段
     * @param id
     * @return
     */
    @Override
    public List<Design> getListBySubid(String id){
        Design design = new Design();
        design.setSubprojectid(id);
        QueryWrapper<Design> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(design);
        return designMapper.selectList(queryWrapper);
    }

    /**
     * 根据设计段id查设计段
     * @param id
     * @return
     */
    @Autowired
    private DesignunitMapper designunitMapper;
    @Autowired
    private ESUnitMapper eSUnitMapper;
    @Autowired
    private DesignsubPersonMapper designsubPersonMapper;
    @Autowired
    private ProofreadsheetsMapper proofreadsheetsMapper;
    @Autowired
    private SubjectsMapper subjectsMapper;
    @Autowired
    private DesignsubTaskMapper designsubTaskMapper;
    @Override
    public DesignVo getByDesignId(String id){
        Design design = designMapper.selectById(id);
        QueryWrapper<Designunit> dunit = new QueryWrapper<>();
        dunit.eq("design_id",id);
        List<Designunit> designunits = designunitMapper.selectList(dunit);
        StringBuilder sb = new StringBuilder();
        if(designunits.size() > 0){
            List<String> list = new ArrayList<>();
            for (Designunit ds : designunits) {
                list.add(ds.getProjectunitid());
            }
            QueryWrapper<ESUnit> qunit = new QueryWrapper<>();
            qunit.in("pkid", list);
            List<ESUnit> esUnits = eSUnitMapper.selectList(qunit);
            for (ESUnit unit : esUnits) {
                sb.append(unit.getProjectunitname()).append("、");
            }
        }
        DesignVo vo = GsonUtil.ObjectToEntity(design,DesignVo.class);
        vo.setUnitname(null);
        if (sb.length() > 0) {
            vo.setUnitname(StringUtils.substringBeforeLast(sb.toString(),"、"));
        }
        List<DesignsubTaskVo> listVo = new ArrayList<>();

        QueryWrapper<DesignsubTask> qtask = new QueryWrapper<>();
        qtask.eq("design_id",id);
        List<DesignsubTask> designsubTaskList = designsubTaskMapper.selectList(qtask);
        //設計段人员
        QueryWrapper<DesignsubPerson> dsper = new QueryWrapper<>();
        dsper.eq("design_id",id);
        List<DesignsubPerson> personList = designsubPersonMapper.selectList(dsper);
        //设计任务校审人员
        QueryWrapper<Proofreadsheets> qf = new QueryWrapper<>();
        qf.eq("design_id",id);
        List<Proofreadsheets> proofreadsheets = proofreadsheetsMapper.selectList(qf);
        //专业
        List<Subjects> subjectList = subjectsMapper.selectsub();

        for (Subjects sub : subjectList) {
            DesignsubTaskVo taskVo = new DesignsubTaskVo();
            taskVo.setSubjectid(sub.getPkid());
            taskVo.setSubjectname(sub.getSubjectname());
            List<String> persion = new ArrayList<>();

            for (DesignsubTask dtask : designsubTaskList) {
                for (DesignsubPerson dperson : personList) {
                    if (dperson.getDesignsubtaskid().equals(dtask.getPkid()) && dperson.getSubjectid().trim().equals(sub.getPkid())){
                        persion.add(dperson.getDesignerid());
                    }
                }
                //各专业人员
                taskVo.setPersonid(persion);
//                for (Proofreadsheets pros : proofreadsheets) {
//                    if (pros.getDesignsubtaskid().equals(dtask.getPkid())){
//                        taskVo.setApproveduserid(pros.getApproveduserid());
//                        taskVo.setReviewuserid(pros.getReviewuserid());
//                        taskVo.setProofreaduserid(pros.getProofreaduserid());
//                    }
//                }
            }
            listVo.add(taskVo);
        }
        vo.setPersonlist(listVo);
//        designMapper.selectById(id);
        return vo;
    }

    /**
     * 根项目id查设计段
     * @param projectId
     * @return
     */
    @Override
    public List<Design> getDesginByProjectId(String projectId){
        QueryWrapper<Design> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id",projectId);
        queryWrapper.isNull("pid");
        return designMapper.selectList(queryWrapper);
    }

    /**
     * <AUTHOR>
     * @Description
     * @Date 2023/4/17 18:03
     **/
    @Override
    public List<Design> getListByDesginId( String desginId) {
        QueryWrapper<Design> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid",desginId);
        return designMapper.selectList(queryWrapper);
    }

    @Autowired
    private UserMapper userMapper;

    @Override
    public DesignTaskVo selInfoByDesignId(String designId,User user) {
        DesignTaskVo vo = new DesignTaskVo();

        Design design = designMapper.selectById(designId);
        vo.setDesign(design);
        QueryWrapper<Subjects> querys = new QueryWrapper<>();
        if (JavaUtils.isEmtryOrNull(user.getRole()) || !user.getRole().equals("项目经理")){
            querys.eq("pkid",user.getSubjectid());
        }
        //专业
        List<Subjects> subjectList = subjectsMapper.selectList(querys);
//        List<Subjects> subjectList = subjectsMapper.selectsub();

        //设计人员
        QueryWrapper<DesignsubPerson> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("design_id",designId);
        List<DesignsubPerson> personList = designsubPersonMapper.selectList(queryWrapper);
        List<DesignTaskPersionVo> plist = new ArrayList<>();
        for (Subjects sub : subjectList){
            DesignTaskPersionVo pvo = new DesignTaskPersionVo();
            pvo.setSubjectid(sub.getPkid());
            pvo.setSubjectname(sub.getSubjectname());
            pvo.setSubjectalias(sub.getSubjectalias());
            pvo.setSubjectcode(sub.getSubjectcode());
            pvo.setDesignid(design.getPkid());
            pvo.setProjectid(design.getProjectid());
            for (DesignsubPerson per : personList){
                if (sub.getPkid().equals(per.getSubjectid())){
                    if (per.getDesignerrole() == 0){
                        pvo.setPersonid(per.getDesignerid());
                    }else if (per.getDesignerrole() == 1){
                        pvo.setProofreaduserid(per.getDesignerid());
                    }else if (per.getDesignerrole() == 2){
                        pvo.setReviewuserid(per.getDesignerid());
                    }else if (per.getDesignerrole() == 3){
                        pvo.setApproveduserid(per.getDesignerid());
                    }else if (per.getDesignerrole() == 4){
                        pvo.setCheckserid(per.getDesignerid());
                    }
                }
            }
            plist.add(pvo);
        }
        vo.setPersionList(plist);
        return vo;
    }

    @Override
    public int updateStats(String designId, Integer status) {
        Design design = designMapper.selectById(designId);
        design.setDesignstatus(status);
        return designMapper.updateById(design);
    }
}
