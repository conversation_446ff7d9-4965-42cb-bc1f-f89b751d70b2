package com.supermap.pipedesign.pipechina.engineering.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignsubTask;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DesignsubTaskBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设计单元专业任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface DesignsubTaskMapper extends BaseMapper<DesignsubTask> {

    @Select("select t.pkid,t.project_id,t.sub_project_id,w.project_code,w.engineering_name,t.task_name,t.subject_name,d.designer_name as designername,t.start_date,t.end_date,t.engineering_status \n" +
            " ,t.subject_code,t.design_id from wbs_designsub_task t \n" +
//            " ,wbs_engineering w, wbs_designsub_person d where t.project_id = w.pkid and t.pkid =  d.design_sub_task_id" +
            " left join wbs_engineering w on t.project_id = w.pkid" +
            " left join wbs_designsub_person d on t.pkid =  d.design_sub_task_id " +
            " ${ew.customSqlSegment}" )
    IPage<DesignsubTaskBo> selectInfoPage(IPage<DesignsubTaskBo> page, @Param(Constants.WRAPPER) Wrapper<DesignsubTaskBo> queryWrapper);

    // 根据任务id获取项目名称、子工程名称
    @Select("select CONCAT_WS('-',a.project_name,b.sub_name,c.design_name) taskname,t.project_id from wbs_designsub_task t \n" +
            " left join wbs_engineering a on t.project_id = a.pkid\n" +
            " left join wbs_engineering_sub b on t.sub_project_id=b.pkid\n" +
            " left join wbs_design c on  c.pkid=t.design_id\n" +
            " where t.pkid = #{taskId} limit 1 ")
    Map selectNameById(@Param("taskId") String taskId);


    @Select("select t.* from wbs_designsub_task t left join wbs_engineering w on t.project_id = w.pkid where w.pkid is not null")
    List<DesignsubTask> getTask();


    @Select("select t.pkid,t.project_id,t.sub_project_id,w.project_code,w.engineering_name,t.task_name,t.subject_name,d.designer_name as designername,t.start_date,t.end_date,t.engineering_status \n" +
            " from wbs_designsub_task t \n" +
            " left join wbs_engineering w on t.project_id = w.pkid" +
            " left join wbs_designsub_person d on t.pkid =  d.design_sub_task_id " +
            "where t.engineering_status = 'A' AND d.designer_id = #{userId} AND w.pkid is not null AND d.designer_role = '0'")
    List<DesignsubTaskBo> selectTaskList(String userId);

    @Select("select t.pkid,t.project_id,t.sub_project_id,w.project_code,w.engineering_name,t.task_name,t.subject_name,d.designer_name as  designername,t.start_date,t.end_date,t.engineering_status \n" +
            " from wbs_designsub_task t \n" +
            " left join wbs_engineering w on t.project_id = w.pkid" +
            " left join wbs_designsub_person d on t.pkid =  d.design_sub_task_id " +
            "where t.engineering_status = 'A' AND  w.pkid is not null AND d.designer_role = '0'")
    List<DesignsubTaskBo> selectTasksList();

    // 一般段根据 ID查询

}
