package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Map;

@Data
public class InspectionVo {
    //smid
    @TableField(exist = false)
    private Long smid;
    //纵坐标x
    @TableField(exist = false)
    private Double smx;
    //横坐标y
    @TableField(exist = false)
    private Double smy;
    //桩号
    @TableField(exist = false)
    private String stakepointno;
    //设计人员id
    @TableField(exist = false)
    private String designerid;
    //设计时间
    @TableField(exist = false)
    @DateTimeFormat
    private Date modifytime;
    //转角
    @TableField(exist = false)
    private Double rotateangle;
    //描述
    @TableField(exist = false)
    private String desc;
    //里程
    @TableField(exist = false)
    private Double milege;
    //项目id
    @TableField(exist = false)
    private String project;
    //子工程id
    @TableField(exist = false)
    private String subprojectid;
    //高程
    @TableField(exist = false)
    private Double altitude;
//文件传输
    @TableField(exist = false)
    private Map<String, InspectionVo> files;









}
