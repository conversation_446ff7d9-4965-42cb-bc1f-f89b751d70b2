package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFileuploadrecord;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工作包成果文件记录存储任务对应每个文件的上传文件路径、上传人、版本等信息。) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IWorkbagFileuploadrecordService extends IService<WorkbagFileuploadrecord> {

 /**
 * 添加工作包成果文件记录存储任务对应每个文件的上传文件路径、上传人、版本等信息。)信息
 *
 * @param workbagFileuploadrecord
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(WorkbagFileuploadrecord workbagFileuploadrecord);

 /**
 * 删除工作包成果文件记录存储任务对应每个文件的上传文件路径、上传人、版本等信息。)信息
 *
 * @param workbagFileuploadrecordId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String workbagFileuploadrecordId);

 /**
 * 更新工作包成果文件记录存储任务对应每个文件的上传文件路径、上传人、版本等信息。)信息
 *
 * @param workbagFileuploadrecord
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(WorkbagFileuploadrecord workbagFileuploadrecord);

 /**
 * 全部查询
 *
 * @param workbagFileuploadrecord
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFileuploadrecord>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<WorkbagFileuploadrecord> list(WorkbagFileuploadrecord workbagFileuploadrecord);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);

 /**
  * 质检入库
  * @param files 质检文件集合
  * @param submitentrustid 委托单id
  * @return
  */
 int fileAdd(List<MultipartFile> files, String partid,String submitentrustid, String projectId,String dirID,String dirType,String mapJson,String url,String range,String rule,String subjectcode,String entrustFileId);

 /*
  * <AUTHOR>
  * @Description
  * @Date 2022/12/31 9:25
  * 通过fileId查文件路径
  **/
 String getPathByFileId(String fileId);
 /**
  * 质检树右键查看地图---通过fileid查询反馈csv文件
  * @Date 2023-2-25
  * @auther SQQ
  */
 Map<String ,String> getFileCSV(String fileid);

 Map<String, String> getFilePath(String fileid);

}
