package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * $职责岗位 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_position")
@ApiModel(value="Position对象", description="$职责岗位")
public class Position implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "岗位名称")
    @TableField("position_name")
    private String positionName;

    @ApiModelProperty(value = "岗位描述")
    @TableField("position_desc")
    private String positionDesc;

    @ApiModelProperty(value = "岗位编码")
    @TableField("position_code")
    private String positionCode;

}
