package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.SubmitEntrustContent;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 委托内容 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Repository
public interface ISubmitEntrustContentService extends IService<SubmitEntrustContent> {

 /**
 * 添加委托内容信息
 *
 * @param submitEntrustContent
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int insert(SubmitEntrustContent submitEntrustContent);

 /**
 * 删除委托内容信息
 *
 * @param submitEntrustContentId
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int delete(String submitEntrustContentId);

 /**
 * 更新委托内容信息
 *
 * @param submitEntrustContent
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int update(SubmitEntrustContent submitEntrustContent);

 /**
 * 全部查询
 *
 * @param submitEntrustContent
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.SubmitEntrustContent>
 * @Date 2023-04-14
 * @auther eomer
 */
 List<SubmitEntrustContent> list(SubmitEntrustContent submitEntrustContent);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-04-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
