package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目各专业负责人 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_engineering_subject_persion")
@ApiModel(value="EngineeringSubjectPersion对象", description="项目各专业负责人")
public class EngineeringSubjectPersion implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "工程编号")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "专业设计人员")
    @TableField("designer_id")
    private String designerId;

    @ApiModelProperty(value = "设计人员名称")
    @TableField("designer_name")
    private String designerName;

    @ApiModelProperty(value = "人员类型(0为设计人员,1为校对人员,2为审核人员,3为审定人员,4为审查人员")
    @TableField("designer_role")
    private Integer designerRole;

    @ApiModelProperty(value = "是否专业负责人(0普通，1是专业负责人)")
    @TableField("is_leader")
    private Integer isLeader;

    @ApiModelProperty(value = "编辑锁定状态(1为编辑锁定,0为未)")
    @TableField("is_editlock")
    private Integer isEditlock;

    @ApiModelProperty(value = "专业名称")
    @TableField("subject_name")
    private String subjectName;

    @ApiModelProperty(value = "专业别名")
    @TableField("subject_alias")
    private String subjectAlias;

    @ApiModelProperty(value = "专业编码")
    @TableField("subject_code")
    private String subjectCode;

    @ApiModelProperty(value = "专业ID")
    @TableField("subject_id")
    private String subjectId;


}
