package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.EngineeringVersionsMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.EngineeringVersions;
import com.supermap.pipedesign.pipechina.engineering.service.IEngineeringVersionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 存储项目的大版本定版记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Service("EngineeringVersionsImpl")
public class EngineeringVersionsImpl extends ServiceImpl<EngineeringVersionsMapper, EngineeringVersions> implements IEngineeringVersionsService {

    @Autowired
    private EngineeringVersionsMapper engineeringVersionsMapper;

    /**
    * 添加存储项目的大版本定版记录信息
    *
    * @param engineeringVersions
    * @return int
    * @Date 2023-04-12
    * @auther eomer
    */
    @Override
    public int insert(EngineeringVersions engineeringVersions) {

        //engineeringVersions.setUserId(JavaUtils.getUUID36());
        //engineeringVersions.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return engineeringVersionsMapper.insert(engineeringVersions);
    }

    /**
    * 删除存储项目的大版本定版记录信息
    *
    * @param engineeringVersionsId
    * @return int
    * @Date 2023-04-12
    * @auther eomer
    */
    @Override
    public int delete(String engineeringVersionsId) {
        return engineeringVersionsMapper.deleteById(engineeringVersionsId);
    }

    /**
    * 更新存储项目的大版本定版记录信息
    *
    * @param engineeringVersions
    * @return int
    * @Date 2023-04-12
    * @auther eomer
    */
    @Override
    public int update(EngineeringVersions engineeringVersions) {
        return engineeringVersionsMapper.updateById(engineeringVersions);
    }

    /**
    * 全部查询
    *
    * @param engineeringVersions
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.EngineeringVersions>
    * @Date 2023-04-12
    * @auther eomer
    */
    @Override
    public List<EngineeringVersions> list(EngineeringVersions engineeringVersions) {

        QueryWrapper<EngineeringVersions> queryWrapper = new QueryWrapper<>();

        return engineeringVersionsMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-04-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<EngineeringVersions> engineeringVersionsIPage = new Page<>();
        engineeringVersionsIPage.setCurrent(current);
        engineeringVersionsIPage.setSize(size);

        QueryWrapper<EngineeringVersions> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return engineeringVersionsMapper.selectPage(engineeringVersionsIPage, queryWrapper);
    }


}
