package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.supermap.pipedesign.pipechina.engineering.entity.ESLeader;
import com.supermap.pipedesign.pipechina.engineering.entity.ESUnit;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 子工程表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Data
public class EngineeringSubVo {


    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "工程ID")
    private String projectid;

    @ApiModelProperty(value = "子工程名称")
    private String subname;

    @ApiModelProperty(value = "子工程名称编码")
    private String subcode;

    @ApiModelProperty(value = "描述")
    private String subdesc;

    @ApiModelProperty(value = "是否为设计单元 1为设计单元 0为非")
    private Integer isdesign;

    @ApiModelProperty(value = "录入人ID")
    private String createid;

    @ApiModelProperty(value = "录入人姓名")
    private String createname;

    @ApiModelProperty(value = "录入时间")
    private Timestamp createtime;

    @ApiModelProperty(value = "起始桩点编号")
    private String stakepointnostart;

    @ApiModelProperty(value = "终点桩点编号")
    private String stakepointnoend;

    @ApiModelProperty(value = "起始桩号里程")
    private String stakenomileagestart;

    @ApiModelProperty(value = "终点桩号里程")
    private String stakenomileageend;

    @ApiModelProperty(value = "起始桩号里程")
    private String stakemileagestart;

    @ApiModelProperty(value = "终点桩号里程")
    private String stakemileageend;

    @ApiModelProperty(value = "项目状态")
    private String projectstatecode;

    @ApiModelProperty(value = "项目状态名称")
    private String projectstatename;

    @ApiModelProperty(value = "项目代号")
    private String projectdnum;

    @ApiModelProperty(value = "项目代号名称")
    private String projectdnumname;

    @ApiModelProperty(value = "地理坐标编码")
    private String coordinatecode;

    @ApiModelProperty(value = "地理坐标名称")
    private String coordinatename;

    @ApiModelProperty(value = "子项目负责人id")
    private String submanagercode;

    @ApiModelProperty(value = "子项目负责人")
    private String submanagername;

    //工程单元   unitlist
    @TableField(exist = false)
    private List<ESUnit> unitList;

    //子工程各专业负责人   unitlist
    @TableField(exist = false)
    private List<ESLeader> leaderList;



}
