package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @autthor sqq
 * @date 2023/3/30
 * @Description
 * 专业-质检规则数据
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_subject_data")
@ApiModel(value="SubjectData", description="专业-质检规则数据")
public class SubjectData implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "专业ID")
    @TableField("subject_id")
    private String subjectId;

    @ApiModelProperty(value = "规则名称")
    @TableField("data_name")
    private String dataName;

    @ApiModelProperty(value = "规则编码")
    @TableField("data_code")
    private String dataCode;

    @ApiModelProperty(value = "规则函数")
    @TableField("data_fun")
    private String dataFun;





}
