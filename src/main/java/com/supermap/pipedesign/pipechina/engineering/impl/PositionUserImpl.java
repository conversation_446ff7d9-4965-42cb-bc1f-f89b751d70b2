package com.supermap.pipedesign.pipechina.engineering.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.engineering.dao.PositionUserMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Position;
import com.supermap.pipedesign.pipechina.engineering.entity.PositionUser;
import com.supermap.pipedesign.pipechina.engineering.service.IPositionUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * <p>
 * 岗位用户关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-08
 */
@Service("PositionUserImpl")
public class PositionUserImpl extends ServiceImpl<PositionUserMapper, PositionUser> implements IPositionUserService {
    @Autowired
    private PositionUserMapper positionUserMapper;

    @Override
    public int insert(PositionUser positionUser) {
        return positionUserMapper.insert(positionUser);
    }

    @Override
    public int insertBatch(List<PositionUser> positionUserList) {
        int result = 0;
        if(ObjectUtil.isNotEmpty(positionUserList) && positionUserList.size() > 0){
            for (int i = 0; i < positionUserList.size(); i++) {
                PositionUser positionUser = positionUserList.get(i);
                result = insert(positionUser);
            }
        }else{
            throw new BusinessException("岗位用户关联列表为空");
        }
        return result;
    }

    @Override
    public int delete(String pkid) {
        return positionUserMapper.deleteById(pkid);
    }

    @Override
    public int deleteBatch(List<String> pkids) {
        int result = 0;
        if(ObjectUtil.isNotEmpty(pkids) && pkids.size() > 0){
            for (int i = 0; i < pkids.size(); i++) {
                result = delete(pkids.get(i));
            }
        }else{
            throw new BusinessException("岗位用户关联ID为空");
        }
        return result;
    }

    @Override
    public int update(PositionUser positionUser) {
        return positionUserMapper.updateById(positionUser);
    }

    @Override
    public List<PositionUser> queryList() {
        QueryWrapper<PositionUser> queryWrapper = new QueryWrapper<>();
        return positionUserMapper.selectList(queryWrapper);
    }

    @Override
    public List<PositionUser> queryByPositionCode(String code) {
        QueryWrapper<PositionUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("position_code", code);
        return positionUserMapper.selectList(queryWrapper);
    }
}
