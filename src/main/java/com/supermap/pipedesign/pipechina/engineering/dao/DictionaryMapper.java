package com.supermap.pipedesign.pipechina.engineering.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.EntityDictionaryVo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 * 字典表(存储与项目及任务分解有关的字典表) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface DictionaryMapper extends BaseMapper<Dictionary> {

    @Select("select pkid,dictionary_code as key,dictionary_name as code from wbs_dictionary where pid=#{pid} order by sort asc")
    List<DictionaryVo> selCodeByKey(@Param("pid") String pid);


    @Select("select pkid,dictionary_code as key,dictionary_name as code from wbs_dictionary where pkid=#{pkid}")
    DictionaryVo selByPkid(@Param("pkid") String pkid);

    @Select("select pkid,dictionary_code as key,dictionary_name as code from wbs_dictionary ${ew.customSqlSegment}")
    List<DictionaryVo> selPidIsNull( @Param(Constants.WRAPPER) Wrapper<DictionaryVo> queryWrapper);

    @Select("select pkid, pid, dictionary_name as dictionaryname, dictionary_code as dictionarycode,sort from wbs_dictionary where pid = #{pkid} order by sort asc")
    @Results(id="EntityDictionaryVo",value = {
            @Result(id = true,column = "pkid",property = "pkid"),
            @Result(column = "pkid",property = "dictList",javaType = List.class,
                    many=@Many(select="com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper.selEntityDict"))
    })
    List<EntityDictionaryVo> selEntityDict(@Param("pkid") String pkid);

    @Select("select dictionary_name from wbs_dictionary where pid=#{pkid}")
    List<String> selByRangeId(@Param("pkid")String pkid);



}
