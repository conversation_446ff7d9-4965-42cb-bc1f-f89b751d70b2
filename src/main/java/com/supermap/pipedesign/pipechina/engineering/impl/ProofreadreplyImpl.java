package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.ProofreadreplyMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Proofreadreply;
import com.supermap.pipedesign.pipechina.engineering.entity.Proofreadresult;
import com.supermap.pipedesign.pipechina.engineering.service.IProofreadreplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 意见回复 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Service("ProofreadreplyImpl")
public class ProofreadreplyImpl extends ServiceImpl<ProofreadreplyMapper, Proofreadreply> implements IProofreadreplyService {
    @Autowired
    private ProofreadreplyMapper proofreadreplyMapper;

    @Override
    public int insert(Proofreadreply proofreadreply) {
        proofreadreply.setCreatetime(new Timestamp(System.currentTimeMillis()));
        return proofreadreplyMapper.insert(proofreadreply);
    }

    @Override
    public int update(Proofreadreply proofreadreply) {
        return proofreadreplyMapper.updateById(proofreadreply);
    }

    @Override
    public List<Proofreadreply> list(Proofreadreply proofreadreply) {
        QueryWrapper<Proofreadreply> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("create_time");
        return proofreadreplyMapper.selectList(queryWrapper);
    }
}
