package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignsubTaskVersion;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title DesignsubTaskVersionEx
 * @date 2023/04/17 20:33
 * @description TODO
 */
@Data
public class DesignsubTaskVersionEx extends DesignsubTaskVersion {

    @ApiModelProperty(value = "提交人")
    @TableField("user_name")
    private String userName;
}

