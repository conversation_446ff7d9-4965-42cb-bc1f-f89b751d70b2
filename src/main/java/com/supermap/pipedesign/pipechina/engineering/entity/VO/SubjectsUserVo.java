package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 专业划分表(定义各个(设计)专业) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Data
public class SubjectsUserVo {


    @ApiModelProperty(value = "专业id")
    private String subjectid;

    @ApiModelProperty(value = "专业名称")
    private String subjectname;



//    @ApiModelProperty(value = "专业相对人员listid")
//    private List<String> personlist;

    @ApiModelProperty(value = "专业相对人员id")
    private String personid;



}
