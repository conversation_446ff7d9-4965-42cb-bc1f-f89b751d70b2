package com.supermap.pipedesign.pipechina.engineering.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.engineering.dao.RefMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.UserMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Dto.UserDto;
import com.supermap.pipedesign.pipechina.engineering.entity.Ref;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.service.IUserService;
import com.supermap.tools.base.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("UserImpl")
public class UserImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Autowired
    private UserMapper userMapper;

    /**
    * 添加用户表信息
    *
    * @param user
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int insert(User user) {

        //user.setUserId(JavaUtils.getUUID36());
        //user.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return userMapper.insert(user);
    }

    @Override
    public int insertBatch(List<User> userList) {
        int result = 0;
        if(ObjectUtil.isNotEmpty(userList) && userList.size() > 0){
            for (int i = 0; i < userList.size(); i++) {
                User user = userList.get(i);
                result = insert(user);
            }
        }else{
            throw new BusinessException("专业用户列表为空");
        }
        return result;
    }

    /**
    * 删除用户表信息
    *
    * @param userId
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int delete(String userId) {
        return userMapper.deleteById(userId);
    }

    @Override
    public int deleteBatch(List<String> pkids) {
        int result = 0;
        if(ObjectUtil.isNotEmpty(pkids) && pkids.size() > 0){
            for (int i = 0; i < pkids.size(); i++) {
                String pkid = pkids.get(i);
                result = delete(pkid);
            }
        }else{
            throw new BusinessException("专业用户关联ID为空");
        }
        return result;
    }

    /**
    * 更新用户表信息
    *
    * @param user
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int update(User user) {
        return userMapper.updateById(user);
    }

    /**
    * 全部查询
    *
    * @param user
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.User>
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<User> list(User user) {

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        return userMapper.selectList(queryWrapper);
    }

    @Override
    public List<User> list(UserDto user) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        if(StrUtil.isNotEmpty(user.getProjectid())){
            queryWrapper.eq("project_id", user.getProjectid());
        }
        if(StrUtil.isNotEmpty(user.getOrgid())){
            queryWrapper.eq("org_id", user.getOrgid());
        }
        if(StrUtil.isNotEmpty(user.getInventedid())){
            queryWrapper.eq("invented_id", user.getInventedid());
        }
        if(StrUtil.isNotEmpty(user.getSubjectid())){
            queryWrapper.eq("subject_id", user.getSubjectid());
        }
        List<User> userList = userMapper.selectList(queryWrapper);
        //用户列表去重
        userList = userList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(User::getUserid))), ArrayList::new));
        return userList;
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate, String projectid) {

        IPage<User> userIPage = new Page<>();
        userIPage.setCurrent(current);
        userIPage.setSize(size);

        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }
        if(StrUtil.isNotEmpty(projectid)){
            queryWrapper.eq("project_id", projectid);
        }
        return userMapper.selectPage(userIPage, queryWrapper);
    }

    /**
     * 查登录用户专业人员
     * @param subjectid 专业id
     * @return
     */
    @Override
    public List<User> getuserboxbysubjectid(String subjectid){
        User user = new User();
        user.setSubjectid(subjectid);
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("pkid","user_name");
        queryWrapper.setEntity(user);
        return  userMapper.selectList(queryWrapper);
    }

    @Override
    public User checkLong(User user){
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_name",user.getUsername());
//        QueryWrapper.eq("username",user.getUsername());
        User user1 = userMapper.selectOne(queryWrapper);
        if(StringUtils.isNotBlank(user1.getPkid())){

            return user1;
        }
        return null;

    }
    /**
     * 查登录用户的专业编码
     * @param userId 用户id
     * @return
     */
    @Override
    public Map<String,String> getSubjectcode(String userId) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id",userId);
        User user1 = userMapper.selectOne(queryWrapper);
        Map<String,String> map=new HashMap<>();
        map.put(userId,user1.getSubjectcode());
        return map;
    }
    @Autowired
    private RefMapper refMapper;
    @Override
    public String userName(String userId) {
        User user = userMapper.selectById(userId);
        if (user == null){
            user = userMapper.selUserByUserId(userId);
            if (user == null){
                List<Ref> refs = refMapper.selectByUserId(userId);
                if (!JavaUtils.isEmtryOrNull(refs) && refs.size()>0){
                    user = new User();
                    user.setPkid(userId);
                    user.setUsername(refs.get(0).getUserName());
                }
            }
        }
        if (user != null && JavaUtils.isNotEmtryOrNull(user.getUsername())){
            return user.getUsername();
        }
        return null;
    }
}
