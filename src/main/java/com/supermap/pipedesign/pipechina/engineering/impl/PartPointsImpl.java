package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.DesignunitMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.PartPointsMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.PartPoints;
import com.supermap.pipedesign.pipechina.engineering.service.IPartPointsService;
import com.supermap.pipedesign.pipechina.sdx.entity.Centrallinepile;
import com.supermap.pipedesign.pipechina.sdx.service.ICentrallinepileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 委托单桩点顺序表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("PartPointsImpl")
public class PartPointsImpl extends ServiceImpl<PartPointsMapper, PartPoints> implements IPartPointsService {

    @Autowired
    private PartPointsMapper partPointsMapper;

    @Autowired
    private DesignunitMapper designunitMapper;

    @Resource(name = "CentrallinepileImpl")
    private ICentrallinepileService centrallinepileService;

    /**
    * 添加委托单桩点顺序表信息
    *
    * @param partPoints
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int insert(PartPoints partPoints) {

        //partPoints.setUserId(JavaUtils.getUUID36());
        //partPoints.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return partPointsMapper.insert(partPoints);
    }

    /**
    * 删除委托单桩点顺序表信息
    *
    * @param partPointsId
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int delete(String partPointsId) {
        return partPointsMapper.deleteById(partPointsId);
    }

    /**
    * 更新委托单桩点顺序表信息
    *
    * @param partPoints
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int update(PartPoints partPoints) {
        return partPointsMapper.updateById(partPoints);
    }

    /**
    * 全部查询
    *
    * @param partPoints
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.PartPoints>
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<PartPoints> list(PartPoints partPoints) {

        QueryWrapper<PartPoints> queryWrapper = new QueryWrapper<>();

        return partPointsMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<PartPoints> partPointsIPage = new Page<>();
        partPointsIPage.setCurrent(current);
        partPointsIPage.setSize(size);

        QueryWrapper<PartPoints> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return partPointsMapper.selectPage(partPointsIPage, queryWrapper);
    }
    /**
     * 受托阶段表
     * 添加桩点顺序
     * @param partid
     * @param startpoint
     * @param endpoint
     * @return
     */
    public int addpartpoin(String partid, String subprojectid,String startpoint, String endpoint){
        //WBS_DesignUnit
        //根据子工程id查设计任务id
//        QueryWrapper<Designunit> queryWrapper = new QueryWrapper<>();
//        queryWrapper.select("pkid");
//        queryWrapper.eq("subprojectid",subprojectid);
//        List<Designunit> designunits = designunitMapper.selectList(queryWrapper);

        List<Centrallinepile> getlistbypoint = centrallinepileService.getlistbypoint(startpoint, endpoint);
        for (Centrallinepile lin:getlistbypoint) {
            PartPoints partPoints = new PartPoints();
            partPoints.setPartid(partid);
            partPoints.setStakepointno(lin.getStakenumber());
            int total = partPointsMapper.insert(partPoints);
            if(total != 1){
                return 0;
            }
        }


        return 1;
    }


}
