package com.supermap.pipedesign.pipechina.engineering.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.WbsMetadatainfoMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.WbsMetadatainfo;
import com.supermap.pipedesign.pipechina.engineering.service.IWbsMetadatainfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *记录各数据的元数据信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-2-17
 */
@Service("WbsMetadatainfoImpl")
public class WbsMetadatainfoImpl extends ServiceImpl<WbsMetadatainfoMapper, WbsMetadatainfo> implements IWbsMetadatainfoService {
@Autowired WbsMetadatainfoMapper wbsMetadatainfoMapper;

    /**
     * 元数据质检成功后的表数据入库操作
     * @param wbsMetadatainfoList
     * <AUTHOR>
     *2023.02.20
     */
    @Override
    public int insertWbsMetadatainfoList(List<WbsMetadatainfo> wbsMetadatainfoList) {
        int i=0;
        for (WbsMetadatainfo wbsMetadatainfo : wbsMetadatainfoList) {
            i++;
            wbsMetadatainfoMapper.insert(wbsMetadatainfo);
        }

        return i;
    }
    /**
     * 元数据查询列表
     * @param
     * <AUTHOR>
     *2023.02.20
     */
    @Override
    public List<WbsMetadatainfo> getList(String fileid) {
        return wbsMetadatainfoMapper.getList( fileid);
    }


}
