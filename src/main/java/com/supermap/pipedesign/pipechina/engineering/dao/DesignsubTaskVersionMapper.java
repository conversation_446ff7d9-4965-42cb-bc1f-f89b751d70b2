package com.supermap.pipedesign.pipechina.engineering.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.supermap.pipedesign.pipechina.demo.entity.Demo;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignsubTaskVersion;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DesignsubTaskVersionEx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设计任务小版本 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Mapper
public interface DesignsubTaskVersionMapper extends BaseMapper<DesignsubTaskVersion> {


    @Select("select t.*,b.user_name from wbs_designsub_task_version t " +
            " left join wbs_designsub_task a on a.pkid=t.design_sub_task_id" +
            " left join wbs_user b on b.user_id=t.create_user_id ${ew.customSqlSegment} ")
    List<DesignsubTaskVersionEx> getSmallVersions(@Param(Constants.WRAPPER) Wrapper<DesignsubTaskVersion> queryWrapper);

}
