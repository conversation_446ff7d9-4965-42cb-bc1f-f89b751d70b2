package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.engineering.entity.Constructionstage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 施工阶段表(定义预可行性研究，可行性研究，初步设计，施工图设计，竣工图设计等阶段,0,1,2,3,4,5) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IConstructionstageService extends IService<Constructionstage> {

 /**
 * 添加施工阶段表(定义预可行性研究，可行性研究，初步设计，施工图设计，竣工图设计等阶段,0,1,2,3,4,5)信息
 *
 * @param constructionstage
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Constructionstage constructionstage);

 /**
 * 删除施工阶段表(定义预可行性研究，可行性研究，初步设计，施工图设计，竣工图设计等阶段,0,1,2,3,4,5)信息
 *
 * @param constructionstageId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String constructionstageId);

 /**
 * 更新施工阶段表(定义预可行性研究，可行性研究，初步设计，施工图设计，竣工图设计等阶段,0,1,2,3,4,5)信息
 *
 * @param constructionstage
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Constructionstage constructionstage);

 /**
 * 全部查询
 *
 * @param constructionstage
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Constructionstage>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Constructionstage> list(Constructionstage constructionstage);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
