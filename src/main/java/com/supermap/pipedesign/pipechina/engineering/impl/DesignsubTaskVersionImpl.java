package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.DesignsubTaskMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.DesignsubTaskVersionMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignsubTask;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignsubTaskVersion;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DesignsubTaskVersionEx;
import com.supermap.pipedesign.pipechina.engineering.service.IDesignsubTaskVersionService;
import com.supermap.tools.base.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 设计任务小版本 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Service("DesignsubTaskVersionImpl")
public class DesignsubTaskVersionImpl extends ServiceImpl<DesignsubTaskVersionMapper, DesignsubTaskVersion> implements IDesignsubTaskVersionService {

    @Autowired
    private DesignsubTaskVersionMapper designsubTaskVersionMapper;
    @Autowired
    private DesignsubTaskMapper designsubTaskMapper;

    @Override
    public List<DesignsubTaskVersionEx> getSmallVersions(String projectId, String designId, String subjectId, String versionDesc, String startDate, String endDate){

        // where a.project_id='' and a.design_id='' and a.subject_code='' and t.version_desc='' and t.create_date between '' and  ''
        QueryWrapper<DesignsubTaskVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("a.project_id",projectId);
        queryWrapper.eq("a.design_id",designId);
        queryWrapper.eq("a.subject_code",subjectId);
        if (StringUtils.isNotBlank(versionDesc)) {
            queryWrapper.like("t.version_desc",versionDesc);
        }
        // todo 这里暂不使用时间过滤，后面添加
        /*if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            queryWrapper.between("t.create_date",JavaUtils.stringToDate(startDate), JavaUtils.stringToDate(endDate));
        }*/
        return designsubTaskVersionMapper.getSmallVersions(queryWrapper);
    }

    /**
    * 添加设计任务小版本信息
    *
    * @param projectId 项目ID
    * @param designSubTaskId 任务ID
    * @return int
    * @Date 2023-03-31
    * @auther eomer
    */
    @Override
    public String insert(String projectId, String designSubTaskId, String userId, String versionText) {

        // 获取最新版本
        QueryWrapper<DesignsubTaskVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id",projectId);
        queryWrapper.eq("design_sub_task_id",designSubTaskId);
        queryWrapper.orderByDesc("version");
        queryWrapper.last(" limit 1 ");
        DesignsubTaskVersion designsubTaskVersion = designsubTaskVersionMapper.selectOne(queryWrapper);

        DesignsubTaskVersion taskVersion = new DesignsubTaskVersion();

        taskVersion.setPkid(JavaUtils.getUUID36());
        if (designsubTaskVersion == null) {
            taskVersion.setVersion("v-0.0.00001");
        } else {
            String version = designsubTaskVersion.getVersion();
            int v = Integer.parseInt(version.substring(6));
            taskVersion.setVersion("v-0.0."+String.format("%05d",(v+1)));
        }

        taskVersion.setProjectId(projectId);
        taskVersion.setDesignSubTaskId(designSubTaskId);
        taskVersion.setCreateUserId(userId);
        taskVersion.setVersionDesc(versionText);
        taskVersion.setCreateDate(new Timestamp(Calendar.getInstance().getTimeInMillis()));
        DesignsubTask designsubTask = designsubTaskMapper.selectById(designSubTaskId);
        taskVersion.setSubProjectId(designsubTask.getSubprojectid());
        taskVersion.setDesignId(designsubTask.getDesignid());

        designsubTaskVersionMapper.insert(taskVersion);
        return taskVersion.getVersion();
    }

    /**
    * 删除设计任务小版本信息
    *
    * @param designsubTaskVersionId
    * @return int
    * @Date 2023-03-31
    * @auther eomer
    */
    @Override
    public int delete(String designsubTaskVersionId) {
        return designsubTaskVersionMapper.deleteById(designsubTaskVersionId);
    }

    /**
    * 更新设计任务小版本信息
    *
    * @param designsubTaskVersion
    * @return int
    * @Date 2023-03-31
    * @auther eomer
    */
    @Override
    public int update(DesignsubTaskVersion designsubTaskVersion) {
        return designsubTaskVersionMapper.updateById(designsubTaskVersion);
    }

    /**
    * 全部查询
    *
    * @param designsubTaskVersion
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.DesignsubTaskVersion>
    * @Date 2023-03-31
    * @auther eomer
    */
    @Override
    public List<DesignsubTaskVersion> list(DesignsubTaskVersion designsubTaskVersion) {

        QueryWrapper<DesignsubTaskVersion> queryWrapper = new QueryWrapper<>();

        return designsubTaskVersionMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-31
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<DesignsubTaskVersion> designsubTaskVersionIPage = new Page<>();
        designsubTaskVersionIPage.setCurrent(current);
        designsubTaskVersionIPage.setSize(size);

        QueryWrapper<DesignsubTaskVersion> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return designsubTaskVersionMapper.selectPage(designsubTaskVersionIPage, queryWrapper);
    }


}
