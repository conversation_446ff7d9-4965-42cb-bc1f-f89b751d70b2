package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 专业划分表(定义各个(设计)专业) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Data
public class SubjectsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "专业名称")
    private String subjectname;

    @ApiModelProperty(value = "专业别名")
    private String subjectalias;

    @ApiModelProperty(value = "专业描述")
    private String subjectdesc;

    @ApiModelProperty(value = "顺序号(用来排序)")
    private Integer orderindex;

    @ApiModelProperty(value = "专业编码")
    private String subjectcode;

    @TableField(exist = false)
    private List<User> userlist;



}
