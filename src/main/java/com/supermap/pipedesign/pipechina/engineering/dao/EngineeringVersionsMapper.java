package com.supermap.pipedesign.pipechina.engineering.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.EngineeringVersions;
import com.supermap.pipedesign.pipechina.rules.entity.vo.DataBean;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 存储项目的大版本定版记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Mapper
public interface EngineeringVersionsMapper extends BaseMapper<EngineeringVersions> {

/*    @Insert("insert into ${tableNames} values (#{param.project_id},#{param.version},#{param.create_date},#{param.create_user_id},#{param.version_desc})")
    int insertEngineeringVersion(@Param("tableNames") String tableNames, @Param("param") DataBean param);*/

    //int insertEngineeringVersion(T t);
}
