package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.SubmitentrustPart;
import com.supermap.common.entity.DirTree;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.SubmitentruspareVo;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 委托阶段表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface ISubmitentrustPartService extends IService<SubmitentrustPart> {

 /**
  * 添加委托阶段表信息
  *
  * @param submitentrustPart
  * @return int
  * @Date 2022-12-17
  * @auther eomer
  */
 int insert(SubmitentrustPart submitentrustPart);

 /**
  * 删除委托阶段表信息
  *
  * @param submitentrustPartId
  * @return int
  * @Date 2022-12-17
  * @auther eomer
  */
 int delete(String submitentrustPartId);

 /**
  * 更新委托阶段表信息
  *
  * @param submitentrustPart
  * @return int
  * @Date 2022-12-17
  * @auther eomer
  */
 int update(SubmitentrustPart submitentrustPart);

 /**
  * 全部查询
  *
  * @param submitentrustPart
  * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.SubmitentrustPart>
  * @Date 2022-12-17
  * @auther eomer
  */
 List<SubmitentrustPart> list(SubmitentrustPart submitentrustPart);

 /**
  * 分页查询
  *
  * @param current
  * @param size
  * @param startDate
  * @param endDate
  * @return com.baomidou.mybatisplus.core.metadata.IPage
  * @Date 2022-12-17
  * @auther eomer
  */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 /**
  * 通过目录id类型返回树结果
  *
  * @param submitentrustid    委托单id
  * @param  userid   用户id
  * @return
  */
 DirTree getSubmitentrustPartTree(String submitentrustid, String userid);

 /**
  * 委托单id查阶段
  * @param submitentrustid
  * @return
  */
 List<SubmitentruspareVo> getPartByTrusId(String submitentrustid);
 /**
  * @Date 2023-2-23
  * @auther SQQ
  * 查询质检存储目录下拉树
  */
 DirTree getStorageDirectoryTree(String submitentrustid);

    SubmitentrustPart getTrusPartDetails(String partId);
}