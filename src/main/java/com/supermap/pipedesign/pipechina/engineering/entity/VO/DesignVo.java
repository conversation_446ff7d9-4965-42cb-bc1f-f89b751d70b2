package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 设计段 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Data
public class DesignVo {


    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "设计单元名称")
    private String designname;

    @ApiModelProperty(value = "工程ID")
    private String projectid;

    @ApiModelProperty(value = "子工程编号")
    private String subprojectid;

    @ApiModelProperty(value = "录入人ID")
    private String createid;

    @ApiModelProperty(value = "录入人姓名")
    private String createname;

    @ApiModelProperty(value = "录入时间")
    private Timestamp createtime;

    @ApiModelProperty(value = "起始桩点编号")
    private String stakepointnostart;

    @ApiModelProperty(value = "终点桩点编号")
    private String stakepointnoend;

    @ApiModelProperty(value = "起始桩号里程")
    private String stakenomileagestart;

    @ApiModelProperty(value = "终点桩号里程")
    private String stakenomileageend;

    @ApiModelProperty(value = "起始桩号里程")
    private String stakemileagestart;

    @ApiModelProperty(value = "终点桩号里程")
    private String stakemileageend;

    @ApiModelProperty(value = "坐标系编号")
    private String srid;

    @ApiModelProperty(value = "度带编号")
    private String crossingzoneid;

    @ApiModelProperty(value = "是否虚拟设计端(0真实,1虚拟)")
    private String isroutcompare;


    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "关联工程单元")
    private String unitname;

    @ApiModelProperty(value = "各负责人集合")
    private List<DesignsubTaskVo> personlist;


}
