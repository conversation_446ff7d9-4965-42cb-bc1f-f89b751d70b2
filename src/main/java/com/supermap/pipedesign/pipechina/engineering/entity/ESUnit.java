package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 工程单元 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_e_s_unit")
@ApiModel(value="ESUnit对象", description="工程单元")
public class ESUnit implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "工程编号")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "子工程编号")
    @TableField("sub_project_id")
    private String subprojectid;

    @ApiModelProperty(value = "工程单元名称")
    @TableField("project_unit_name")
    private String projectunitname;

    @ApiModelProperty(value = "工程单元编码")
    @TableField("poject_unit_code")
    private String pojectunitcode;

    @ApiModelProperty(value = "工程单元描述")
    @TableField("project_unit_desc")
    private String projectunitdesc;

    @ApiModelProperty(value = "起始桩点编号")
    @TableField("stake_point_no_start")
    private String stakepointnostart;

    @ApiModelProperty(value = "终点桩点编号")
    @TableField("stake_point_no_end")
    private String stakepointnoend;

    @ApiModelProperty(value = "起始桩号里程")
    @TableField("stake_no_mileage_start")
    private String stakenomileagestart;

    @ApiModelProperty(value = "终点桩号里程")
    @TableField("stake_no_mileage_end")
    private String stakenomileageend;

    @ApiModelProperty(value = "起始桩号里程")
    @TableField("stake_mileage_start")
    private String stakemileagestart;

    @ApiModelProperty(value = "终点桩号里程")
    @TableField("stake_mileage_end")
    private String stakemileageend;

    @ApiModelProperty(value = "类型 枚举(线路、穿跨域)")
    @TableField("data_type")
    private String datatype;

    @ApiModelProperty(value = "类型名 枚举(线路、穿跨域)")
    @TableField("data_type_name")
    private String datatypename;

    @ApiModelProperty(value = "删除标志（0未删除，1已删除）")
    @TableField("delete_flag")
    private Integer deleteflag;

    @ApiModelProperty(value = "坐标系编号")
    @TableField("srid")
    private String srid;

    @ApiModelProperty(value = "度带编号")
    @TableField("crossing_zone_id")
    private String crossingzoneid;

//    @ApiModelProperty(value = "设计段id")
//    @TableField("design_id")
//    private String designid;
}
