package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.MessageNotification;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Repository
public interface IMessageNotificationService extends IService<MessageNotification> {

 /**
 * 添加信息
 *
 * @param messageNotification
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int insert(MessageNotification messageNotification);

 /**
 * 删除信息
 *
 * @param messageNotificationId
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int delete(String messageNotificationId);

 /**
 * 更新信息
 *
 * @param messageNotification
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int update(MessageNotification messageNotification);

 /**
 * 全部查询
 *
 * @param messageNotification
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.MessageNotification>
 * @Date 2023-04-14
 * @auther eomer
 */
 List<MessageNotification> list(MessageNotification messageNotification);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-04-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
