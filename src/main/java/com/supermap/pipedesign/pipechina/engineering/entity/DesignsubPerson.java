package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 设计单元各专业设计人员 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_designsub_person")
@ApiModel(value="DesignsubPerson对象", description="设计单元各专业设计人员")
public class DesignsubPerson implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "工程编号")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "子工程编号")
    @TableField("sub_project_id")
    private String subprojectid;

    @ApiModelProperty(value = "设计段ID")
    @TableField("design_id")
    private String designid;

    @ApiModelProperty(value = "设计单元专业任务ID")
    @TableField("design_sub_task_id")
    private String designsubtaskid;

    @ApiModelProperty(value = "专业设计人员")
    @TableField("designer_id")
    private String designerid;

    @ApiModelProperty(value = "设计人员名称")
    @TableField("designer_name")
    private String designername;

    @ApiModelProperty(value = "人员类型(0为设计人员,1为校对人员,2为审核人员,3为审定人员,4为审查人员")
    @TableField("designer_role")
    private Integer designerrole;

    @ApiModelProperty(value = "是否专业负责人")
    @TableField("is_leader")
    private Integer isleader;

    @ApiModelProperty(value = "编辑锁定状态(1为编辑锁定,0为未)")
    @TableField("is_editlock")
    private Integer iseditlock;

    @ApiModelProperty(value = "专业名称")
    @TableField("subject_name")
    private String subjectname;

    @ApiModelProperty(value = "专业别名")
    @TableField("subject_alias")
    private String subjectalias;

    @ApiModelProperty(value = "专业编码")
    @TableField("subject_code")
    private String subjectcode;

    @ApiModelProperty(value = "专业ID")
    @TableField("subject_id")
    private String subjectid;


}
