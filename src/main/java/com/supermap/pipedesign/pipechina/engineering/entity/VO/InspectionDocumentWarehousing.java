package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.File;
import java.util.List;

@Data
public class InspectionDocumentWarehousing {
    //质检入库文件集合
    @ApiModelProperty(value = "质检入库文件集合")
    private List<File> files;
    //项目id
    @ApiModelProperty(value = "项目id")
    private  String projectId;
    //节点id
    @ApiModelProperty(value = "节点id")
    private String id;
    //父节点id
    @ApiModelProperty(value = "父节点id")
    private String pId;
    //数据类型0为设计，1为测量，2为勘察
    @ApiModelProperty(value = "数据类型0为设计，1为测量，2为勘察")
    private String dirType;
    //目录类型0为子工程，1为工程单元，2为功能区
    @ApiModelProperty(value = "目录类型0为子工程，1为工程单元，2为功能区")
    private String type;
    //编码
    @ApiModelProperty(value = "文件编码")
    private String code;
    @ApiModelProperty(value = "用户id")
    private String userId;


}
