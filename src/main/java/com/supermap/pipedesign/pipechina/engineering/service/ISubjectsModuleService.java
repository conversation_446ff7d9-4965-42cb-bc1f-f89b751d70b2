package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.Dto.SubjectsModuleDto;
import com.supermap.pipedesign.pipechina.engineering.entity.PositionUser;
import com.supermap.pipedesign.pipechina.engineering.entity.SubjectsModule;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * $专业模块菜单关联 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Repository
public interface ISubjectsModuleService extends IService<SubjectsModule> {

    /**
     * 添加专业模块关联
     * @param subjectsModule
     * @return
     */
    int insert(SubjectsModule subjectsModule);

    /**
     * 批量添加专业模块关联
     * @param subjectsModuleDto
     * @return
     */
    int insertBatch(SubjectsModuleDto subjectsModuleDto);

    /**
     * 删除专业模块关联
     * @param pkid
     * @return
     */
    int delete(String pkid);

    /**
     * 批量删除专业模块关联
     * @param pkids
     * @return
     */
    int deleteBatch(List<String> pkids);

    /**
     * 更新专业模块关联
     * @param subjectsModule
     * @return
     */
    int update(SubjectsModule subjectsModule);

    /**
     * 专业模块列表分页查询
     *
     * @param current
     * @param size
     * @return
     */
    IPage pageList(long current, long size);

    /**
     * 通过项目ID&专业ID获取模块列表
     * @param projectid
     * @param subjectid
     * @return
     */
    List<String> getModuleList(String projectid, String subjectid);

}
