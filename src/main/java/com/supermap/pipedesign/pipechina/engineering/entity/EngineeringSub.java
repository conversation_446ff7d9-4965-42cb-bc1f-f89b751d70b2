package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 子工程表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_engineering_sub")
@ApiModel(value="EngineeringSub对象", description="子工程表")
public class EngineeringSub implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "工程ID")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "子工程名称")
    @TableField("sub_name")
    private String subname;

    @ApiModelProperty(value = "子工程名称编码")
    @TableField("sub_code")
    private String subcode;

    @ApiModelProperty(value = "描述")
    @TableField("sub_desc")
    private String subdesc;

    @ApiModelProperty(value = "是否为设计单元 1为设计单元 0为非")
    @TableField("is_design")
    private Integer isdesign;

    @ApiModelProperty(value = "录入人ID")
    @TableField("create_id")
    private String createid;

    @ApiModelProperty(value = "录入人姓名")
    @TableField("create_name")
    private String createname;

    @ApiModelProperty(value = "录入时间")
    @TableField("create_time")
    private Timestamp createtime;

    @ApiModelProperty(value = "起始桩点编号")
    @TableField("stake_point_no_start")
    private String stakepointnostart;

    @ApiModelProperty(value = "终点桩点编号")
    @TableField("stake_point_no_end")
    private String stakepointnoend;

    @ApiModelProperty(value = "起始桩号里程")
    @TableField("stake_no_mileage_start")
    private String stakenomileagestart;

    @ApiModelProperty(value = "终点桩号里程")
    @TableField("stake_no_mileage_end")
    private String stakenomileageend;

    @ApiModelProperty(value = "起始桩号里程")
    @TableField("stake_mileage_start")
    private String stakemileagestart;

    @ApiModelProperty(value = "终点桩号里程")
    @TableField("stake_mileage_end")
    private String stakemileageend;

    @ApiModelProperty(value = "项目状态")
    @TableField("project_state_code")
    private String projectstatecode;

    @ApiModelProperty(value = "项目状态名称")
    @TableField("project_state_name")
    private String projectstatename;

    @ApiModelProperty(value = "项目代号")
    @TableField("project_d_num")
    private String projectdnum;

    @ApiModelProperty(value = "项目代号名称")
    @TableField("project_d_num_name")
    private String projectdnumname;

    @ApiModelProperty(value = "地理坐标编码")
    @TableField("coordinate_code")
    private String coordinatecode;

    @ApiModelProperty(value = "地理坐标名称")
    @TableField("coordinate_name")
    private String coordinatename;

    @ApiModelProperty(value = "子项目负责人id")
    @TableField("sub_manager_code")
    private String submanagercode;

    @ApiModelProperty(value = "子项目负责人")
    @TableField("sub_manager_name")
    private String submanagername;


}
