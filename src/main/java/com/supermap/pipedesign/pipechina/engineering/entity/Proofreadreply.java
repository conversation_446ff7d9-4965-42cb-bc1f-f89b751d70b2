package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 意见回复表（设计回复意见记录）实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_proofread_reply")
@ApiModel(value="Proofreadreply对象", description="意见回复表（设计回复意见记录）")
public class Proofreadreply implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid", type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "校审单编号")
    @TableField("review_id")
    private String reviewid;

    @ApiModelProperty(value = "意见ID")
    @TableField("comment_id")
    private String commentid;

    @ApiModelProperty(value = "回复人ID")
    @TableField("user_id")
    private String userid;

    @ApiModelProperty(value = "填写时间")
    @TableField("create_time")
    private Timestamp createtime;

    @ApiModelProperty(value = "回复内容")
    @TableField("reply")
    private String reply;

    @ApiModelProperty(value = "状态(1=采纳 2=部分采纳 3=澄清 4=不采纳)")
    @TableField("state")
    private String state;
}
