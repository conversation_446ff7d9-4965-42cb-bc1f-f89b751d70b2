package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.TmpDirectoryMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.TmpDirectorydatarefMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.TmpDirectory;
import com.supermap.pipedesign.pipechina.engineering.entity.TmpDirectorydataref;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.ImportEntity;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.ImportFileEntity;
import com.supermap.pipedesign.pipechina.engineering.service.ITmpDirectorydatarefService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;


/**
 * <p>
 * 质检临时目录数据关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("TmpDirectorydatarefImpl")
public class TmpDirectorydatarefImpl extends ServiceImpl<TmpDirectorydatarefMapper, TmpDirectorydataref> implements ITmpDirectorydatarefService {

    @Autowired
    private TmpDirectorydatarefMapper tmpDirectorydatarefMapper;
    //质检临时目录
    @Autowired
    private TmpDirectoryMapper tmpDirectoryMapper;
    /**
    * 添加质检临时目录数据关联表信息
    *
    * @param tmpDirectorydataref
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int insert(TmpDirectorydataref tmpDirectorydataref) {

        //tmpDirectorydataref.setUserId(JavaUtils.getUUID36());
        //tmpDirectorydataref.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return tmpDirectorydatarefMapper.insert(tmpDirectorydataref);
    }

    /**
    * 删除质检临时目录数据关联表信息
    *
    * @param tmpDirectorydatarefId
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int delete(String tmpDirectorydatarefId) {
        return tmpDirectorydatarefMapper.deleteById(tmpDirectorydatarefId);
    }

    /**
    * 更新质检临时目录数据关联表信息
    *
    * @param tmpDirectorydataref
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int update(TmpDirectorydataref tmpDirectorydataref) {
        return tmpDirectorydatarefMapper.updateById(tmpDirectorydataref);
    }

    /**
    * 全部查询
    *
    * @param tmpDirectorydataref
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.TmpDirectorydataref>
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<TmpDirectorydataref> list(TmpDirectorydataref tmpDirectorydataref) {

        QueryWrapper<TmpDirectorydataref> queryWrapper = new QueryWrapper<>();

        return tmpDirectorydatarefMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<TmpDirectorydataref> tmpDirectorydatarefIPage = new Page<>();
        tmpDirectorydatarefIPage.setCurrent(current);
        tmpDirectorydatarefIPage.setSize(size);

        QueryWrapper<TmpDirectorydataref> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return tmpDirectorydatarefMapper.selectPage(tmpDirectorydatarefIPage, queryWrapper);
    }

    /**
     * 添加临时目录数据集信息
     * @param importEntity
     * @return
     */
        @Override
        public int addImportFile(ImportEntity importEntity) {
            //质检临时目录表
            TmpDirectory tmpDirectory=new TmpDirectory();
            //设置主键
            String uuid= UUID.randomUUID().toString();
            //写死的父id
            tmpDirectory.setParentid("0.1");
            //设置主键
            tmpDirectory.setPkid(uuid);
            //设置用户id
            tmpDirectory.setUserid(importEntity.getUserId());
            //设置目录名称
            tmpDirectory.setDirectoryname(importEntity.getBatchCode());
            //设置目录类型为批次号目录
            tmpDirectory.setType("1");
            //设置目录所属委托单id
            tmpDirectory.setSubmitentrustid(importEntity.getEntrustId());
            //设置所属专业code
            tmpDirectory.setSubjectscode(importEntity.getSubjectsCode());
            //添加目录质检目录
            tmpDirectoryMapper.insert(tmpDirectory);
            //多插入
            for (ImportFileEntity importFileEntity:importEntity.getImportFileEntityList()) {
                //临时目录关联表
                TmpDirectorydataref tmpDirectorydataref=new TmpDirectorydataref();
                //目录编号
                tmpDirectorydataref.setDirectoryid(uuid);
                //批次号
                tmpDirectorydataref.setBatchcode(importEntity.getBatchCode());
                //数据源存储路径
                tmpDirectorydataref.setDatasourcepath(importFileEntity.getFileTargetPath());
                //数集名称
                tmpDirectorydataref.setDatasetname(importFileEntity.getOriginData());
                tmpDirectorydatarefMapper.insert(tmpDirectorydataref);
            }
            return 0;
        }


}
