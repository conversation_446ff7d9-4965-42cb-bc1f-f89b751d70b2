package com.supermap.pipedesign.pipechina.engineering.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.*;
import com.supermap.pipedesign.pipechina.engineering.entity.*;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DesignSubDropdownUserVO;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.SubjectsTaskVo;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.SubjectsVo;
import com.supermap.pipedesign.pipechina.engineering.service.ISubjectsService;
import com.supermap.pipedesign.pipechina.metadata.service.IEntitytypesService;
import com.supermap.tools.base.JavaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <p>
 * 专业划分表(定义各个(设计)专业) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("SubjectsImpl")
@Slf4j
public class SubjectsImpl extends ServiceImpl<SubjectsMapper, Subjects> implements ISubjectsService {

    @Autowired
    private SubjectsMapper subjectsMapper;
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DesignsubTaskMapper designsubTaskMapper;

    @Autowired
    private DesignsubPersonMapper designsubPersonMapper;

    @Autowired
    private ProofreadsheetsMapper proofreadsheetsMapper;

    @Resource(name = "EntitytypesImpl")
    private IEntitytypesService entitytypesService;

    /**
     * 添加专业划分表(定义各个(设计)专业)信息
     *
     * @param subjects
     * @return int
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public int insert(Subjects subjects) {

        //subjects.setUserId(JavaUtils.getUUID36());
        //subjects.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return subjectsMapper.insert(subjects);
    }

    /**
     * 删除专业划分表(定义各个(设计)专业)信息
     *
     * @param subjectsId
     * @return int
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public int delete(String subjectsId) {
        return subjectsMapper.deleteById(subjectsId);
    }

    /**
     * 更新专业划分表(定义各个(设计)专业)信息
     *
     * @param subjects
     * @return int
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public int update(Subjects subjects) {
        return subjectsMapper.updateById(subjects);
    }

    /**
     * 全部查询
     *
     * @param subjects
     * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Subjects>
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public List<Subjects> list(Subjects subjects) {

        QueryWrapper<Subjects> queryWrapper = new QueryWrapper<>();

        return subjectsMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @param startDate
     * @param endDate
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<Subjects> subjectsIPage = new Page<>();
        subjectsIPage.setCurrent(current);
        subjectsIPage.setSize(size);

        QueryWrapper<Subjects> queryWrapper = new QueryWrapper<>();

        if (startDate != null && endDate != null) {
            queryWrapper.between("col_create_time", startDate, endDate);
        }

        return subjectsMapper.selectPage(subjectsIPage, queryWrapper);
    }

    /**
     * 专业下拉框
     *
     * @return
     */
    @Override
    public List<Subjects> getsubjectscombox(String isdevise) {
        QueryWrapper<Subjects> queryWrapper = new QueryWrapper<>();
        if (isdevise == null) {
            isdevise = "1";
        }
        queryWrapper.eq("subject_desc", isdevise);

        return subjectsMapper.selectList(queryWrapper);
    }

    /**
     * 各专业人员下拉
     *
     * @return
     */
    @Override
    public List<SubjectsVo> getdropdown(User userId) {


        QueryWrapper<Subjects> queryWrapper = new QueryWrapper<>();
        if (JavaUtils.isEmtryOrNull(userId.getRole()) || !userId.getRole().equals("项目经理")){
            queryWrapper.eq("pkid",userId.getSubjectid());
        }
        //查询sub全部id ，name数据

        List<Subjects> sub = subjectsMapper.selectList(queryWrapper);
//        List<Subjects> sub = subjectsMapper.selectsub();
        //查询user全部外键id name数据
        List<User> user = userMapper.selectuser();
        //返回结果对象
        List<SubjectsVo> result = new ArrayList<>();
        for (int i = 0; i < sub.size(); i++) {
            //创建实体对象
//            Subjects subjects = new Subjects();
            SubjectsVo subjects = new SubjectsVo();
            subjects.setSubjectname(sub.get(i).getSubjectname());
            subjects.setPkid(sub.get(i).getPkid());
            List<User> usernames = new ArrayList<>();
            for (int j = 0; j < user.size(); j++) {
                if (JavaUtils.isNotEmtryOrNull(user.get(j).getSubjectid())){
                    if (user.get(j).getSubjectid().equals(sub.get(i).getPkid())) {
//                    usernames.add(user.get(j).getUserid());
//                    usernames.add(user.get(j).getUsername());
                        usernames.add(user.get(j));
                    }
                }
            }
            subjects.setUserlist(usernames);
//            subjects.setUserNames(usernames);
            result.add(subjects);
        }
        return result;
    }

    /**
     * @description:
     * @author: duanshaojie
     * @date: 2023/3/8 15:24
     * @param: designSubtaskId 子任务id
     * @return: java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.VO.SubjectsVo>
     */
    @Override
    public List<SubjectsTaskVo> getDropdownByDesignId(String designId) {
        List<Subjects> sub = subjectsMapper.selectList(new QueryWrapper<>());
        //查询user全部外键id name数据
        List<User> userList = userMapper.selectuser();
        /**
         * 查询当前 设计单元专业任务
         */
        QueryWrapper<DesignsubTask> designsubTaskQueryWrapper = new QueryWrapper<>();
        designsubTaskQueryWrapper.eq("design_id", designId);
        List<DesignsubTask> designsubTask = designsubTaskMapper.selectList(designsubTaskQueryWrapper);
        /**
         * 如果不为空 获取当前设计单元各专业负责人 将已选中的人状态设为选中
         */
        //返回结果对象
        List<SubjectsTaskVo> result = new ArrayList<>();

        Map<String, List<DesignsubPerson>> stringListMap = new HashMap<>(32);

        if (CollectionUtil.isNotEmpty(designsubTask)) {
            /**
             * 根据专业区分选中人员数组
             */
            QueryWrapper<DesignsubPerson> designsubPersonQueryWrapper = new QueryWrapper<>();
            designsubPersonQueryWrapper.eq("design_id", designId);
            List<DesignsubPerson> personList = designsubPersonMapper.selectList(designsubPersonQueryWrapper);
            for (DesignsubPerson designsubPerson : personList) {
                if (CollectionUtil.isNotEmpty(stringListMap.get(designsubPerson.getSubjectcode()))) {
                    stringListMap.get(designsubPerson.getSubjectcode()).add(designsubPerson);
                } else {
                    stringListMap.put(designsubPerson.getSubjectcode(), Stream.of(designsubPerson).collect(Collectors.toList()));
                }
            }
        }
        /**
         * 外层循环 专业
         * 内层循环 匹配是否当前专业
         *
         */
        for (Subjects subject : sub) {
            SubjectsTaskVo subjects = new SubjectsTaskVo();
            subjects.setSubjectname(subject.getSubjectname());
            subjects.setPkid(subject.getPkid());
            log.debug("当前设计单id{} 当前专专业:{}", designId, subject);
            subjects.setDesignUserList(new ArrayList<>());
            subjects.setDesignUser(new ArrayList<>());
            subjects.setProofreadUserList(new ArrayList<>());
            subjects.setAuditUserList(new ArrayList<>());
            subjects.setApproveUserList(new ArrayList<>());
            //查询当前专业审批单
            Proofreadsheets proofreadsheets = proofreadsheetsMapper.selectProofreadSheetsByDesignIdAndSubjectName(designId, subject.getSubjectname());
            for (User user : userList) {
                /**
                 * 查找当前用户在本设计段中是否有设计任务
                 */
                QueryWrapper<DesignsubPerson> designsubPersonQueryWrapper = new QueryWrapper<>();
                designsubPersonQueryWrapper.eq("design_id", designId);
                designsubPersonQueryWrapper.eq("subject_name", subject.getSubjectname());
                designsubPersonQueryWrapper.eq("designer_id", user.getPkid());
                List<DesignsubPerson> personList = designsubPersonMapper.selectList(designsubPersonQueryWrapper);
                /**
                 * 数据匹配后可在下拉框中 有当前设计任务单标识为选中状态
                 */
                if (user.getSubjectid().equals(subject.getPkid())) {
                    if (CollectionUtil.isNotEmpty(personList)) {
                        subjects.getDesignUser().add(DesignSubDropdownUserVO.getDesignSubDropdownUserVO(user, true));
                    } else {
                        subjects.getDesignUserList().add(DesignSubDropdownUserVO.getDesignSubDropdownUserVO(user, true));
                    }
                }
                /**
                 * 如果当前专业有审批单,查看当前员工是否与审批单匹配
                 */
                if (Objects.nonNull(proofreadsheets)) {
                    if (user.getPkid().equals(proofreadsheets.getProofreaduserid())) {
                        subjects.getProofreadUserList().add(DesignSubDropdownUserVO.getDesignSubDropdownUserVO(user, true));
                    }
                    if (user.getPkid().equals(proofreadsheets.getReviewuserid())) {
                        subjects.getAuditUserList().add(DesignSubDropdownUserVO.getDesignSubDropdownUserVO(user, true));
                    }
                    if (user.getPkid().equals(proofreadsheets.getApproveduserid())) {
                        subjects.getDesignUserList().add(DesignSubDropdownUserVO.getDesignSubDropdownUserVO(user, true));
                    }
                }
            }

            result.add(subjects);
        }
        return result;

    }

    @Override
    public List<Subjects> selByUserRole(User user) {
        QueryWrapper<Subjects> queryWrapper = new QueryWrapper<>();
        if (JavaUtils.isEmtryOrNull(user.getRole()) || !user.getRole().equals("项目经理")){
            queryWrapper.eq("pkid",user.getSubjectid());
        }
        List<Subjects> subjects = subjectsMapper.selectList(queryWrapper);
        return subjects;
    }
}
