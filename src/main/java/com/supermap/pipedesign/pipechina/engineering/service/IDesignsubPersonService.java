package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignsubPerson;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 设计单元各专业设计人员 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IDesignsubPersonService extends IService<DesignsubPerson> {

 /**
 * 添加设计单元各专业设计人员信息
 *
 * @param designsubPerson
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(DesignsubPerson designsubPerson);

 /**
 * 删除设计单元各专业设计人员信息
 *
 * @param designsubPersonId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String designsubPersonId);

 /**
 * 更新设计单元各专业设计人员信息
 *
 * @param designsubPerson
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(DesignsubPerson designsubPerson);

 /**
 * 全部查询
 *
 * @param designsubPerson
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.DesignsubPerson>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<DesignsubPerson> list(DesignsubPerson designsubPerson);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
