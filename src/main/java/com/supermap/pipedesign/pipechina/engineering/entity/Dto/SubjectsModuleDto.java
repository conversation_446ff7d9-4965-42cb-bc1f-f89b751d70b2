package com.supermap.pipedesign.pipechina.engineering.entity.Dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * $专业模块菜单关联 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Data
public class SubjectsModuleDto {
    @ApiModelProperty(value = "专业ID")
    private String subjectid;
    @ApiModelProperty(value = "专业编码")
    private String subjectcode;
    @ApiModelProperty(value = "专业名称")
    private String subjectname;
    @ApiModelProperty(value = "模块列表（批量添加）")
    List<ModuleBo> moduleList;
    @ApiModelProperty(value = "专业模块关联ID（批量删除）")
    List<String> pkidList;


}
