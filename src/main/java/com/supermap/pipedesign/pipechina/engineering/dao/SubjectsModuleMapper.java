package com.supermap.pipedesign.pipechina.engineering.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.SubjectsModule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 专业模块菜单关联 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Mapper
public interface SubjectsModuleMapper extends BaseMapper<SubjectsModule> {
    @Select("SELECT sm.module_id FROM wbs_subjects_module sm" +
            " LEFT JOIN wbs_user u ON sm.subject_id = u.subject_id\n" +
            "WHERE u.project_id = #{projectid} AND u.subject_id = #{subjectid} ")
    List<String> moduleList(@Param("projectid") String projectid, @Param("subjectid") String subjectid);
}
