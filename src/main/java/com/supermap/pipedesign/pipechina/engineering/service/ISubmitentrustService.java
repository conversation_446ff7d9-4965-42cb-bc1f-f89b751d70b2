package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.SubjectData;
import com.supermap.pipedesign.pipechina.engineering.entity.Submitentrust;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.ProjectTree;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.SubmitentrustPartVo;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.SubmitentrustVo;
import com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFileuploadrecord;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 互提资料委托单(存储互提资料对应委托单信息) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface ISubmitentrustService extends IService<Submitentrust> {

 /**
  * 添加互提资料委托单(存储互提资料对应委托单信息)信息
  *
  * @param
  * @return int
  * @Date 2022-12-17
  * @auther eomer
  */
 int insert(SubmitentrustVo vo);

 /**
 * 删除互提资料委托单(存储互提资料对应委托单信息)信息
 *
 * @param submitentrustId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String submitentrustId);

 /**
 * 更新互提资料委托单(存储互提资料对应委托单信息)信息
 *
 * @param submitentrust
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Submitentrust submitentrust);

 /**
 * 全部查询
 *
 * @param submitentrust
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Submitentrust>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Submitentrust> list(Submitentrust submitentrust);

 /**
  * 分页查询
  *
  * @param current
  * @param size
  * @param startDate
  * @param endDate
  * @param user
  * @return com.baomidou.mybatisplus.core.metadata.IPage
  * @Date 2022-12-17
  * @auther eomer
  */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate, String projectname , String sendname , String status, User user);

 /**
  * 发起人下拉框
  * @return
  */
 List<Submitentrust> getsendid();

 /**
  * 通过项目id获取委托列表
  * @param projectid
  * @return
  */
 List<ProjectTree> getByProjectid(String projectid);

 /*
  * <AUTHOR>
  * @Description
  * @Date 2022/12/29 17:45
  * 解析中线成果表返回桩号
  **/
 List<ProjectTree> listByFile(MultipartFile file) throws Exception;

 /*
  * <AUTHOR>
  * @Description
  * @Date 2022/12/29 21:43
  * 上传委托单
  **/
 String uploadFile(MultipartFile file, User user);

 /*
  * <AUTHOR>
  * @Description
  * @Date 2023/1/8 14:41
  * 根绝委托单id查项目级联关系
  **/
 ProjectTree getTrustListById(String projectId);

 /**
  * 通过专业ID获取质检规则
  * @param subjectid
  * @return 质检规则列表
  */
 List<SubjectData> getSubListBySubjectid(String subjectid);


 Submitentrust getTrusDetails(String entrustId);

    List<Map<String, Object>> getPileInfo(SubmitentrustPartVo submitentrustPartVo);

 SubmitentrustVo getInfoById(String pkid);

    WorkbagFileuploadrecord downloadContent(String pkid);

}
