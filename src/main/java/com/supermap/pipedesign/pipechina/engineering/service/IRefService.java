package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.Ref;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * obs项目-部门-人员-岗位关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Repository
public interface IRefService extends IService<Ref> {

 /**
 * 添加obs项目-部门-人员-岗位关联表信息
 *
 * @param ref
 * @return int
 * @Date 2023-04-20
 * @auther eomer
 */
 int insert(Ref ref);

 /**
 * 删除obs项目-部门-人员-岗位关联表信息
 *
 * @param refId
 * @return int
 * @Date 2023-04-20
 * @auther eomer
 */
 int delete(String refId);

 /**
 * 更新obs项目-部门-人员-岗位关联表信息
 *
 * @param ref
 * @return int
 * @Date 2023-04-20
 * @auther eomer
 */
 int update(Ref ref);

 /**
 * 全部查询
 *
 * @param ref
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Ref>
 * @Date 2023-04-20
 * @auther eomer
 */
 List<Ref> list(Ref ref);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-04-20
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
