package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignDb;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("auth_design_db")
@ApiModel(value="DesignDb对象", description="")
public class DesignDbEx extends DesignDb {

    //a.task_name,a.subject_name,d.user_name
    @ApiModelProperty(value = "设计段名称")
    @TableField("task_name")
    private String taskName;

    @ApiModelProperty(value = "专业名称")
    @TableField("subject_name")
    private String subjectName;

    @ApiModelProperty(value = "归属人")
    @TableField("user_name")
    private String userName;


}
