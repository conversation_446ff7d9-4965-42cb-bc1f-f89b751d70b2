package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * $字典表(存储与项目及任务分解有关的字典表) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_dictionary")
@ApiModel(value="Dictionary对象", description="$字典表(存储与项目及任务分解有关的字典表)")
public class Dictionary implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "上级编号")
    @TableField("pid")
    private String pid;

    @ApiModelProperty(value = "字典名称")
    @TableField("dictionary_name")
    private String dictionaryname;

    @ApiModelProperty(value = "字典编码(作为枚举值来用)")
    @TableField("dictionary_code")
    private String dictionarycode;

    @ApiModelProperty(value = "上级字典编码")
    @TableField("dictionary_pcode")
    private String dictionarypcode;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private int sort;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "集合标识")
    @TableField("group_code")
    private String groupcode;

    @ApiModelProperty(value = "集合名称")
    @TableField("group_name")
    private String groupname;

    @ApiModelProperty(value = "是否启用")
    @TableField("enabled")
    private boolean enabled;


}
