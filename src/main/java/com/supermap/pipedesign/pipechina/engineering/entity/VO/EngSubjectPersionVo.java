package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 项目各专业负责人
 */
@Data
public class EngSubjectPersionVo {
    @ApiModelProperty(value = "工程编号")
    private String projectId;

    @ApiModelProperty(value = "专业id")
    private String subjectId;

    @ApiModelProperty(value = "专业名称")
    private String subjectName;

    @ApiModelProperty(value = "专业别名")
    private String subjectAlias;

    @ApiModelProperty(value = "专业编码")
    private String subjectCode;

    @ApiModelProperty(value = "设计人员id")
    private List<String> personid;
}
