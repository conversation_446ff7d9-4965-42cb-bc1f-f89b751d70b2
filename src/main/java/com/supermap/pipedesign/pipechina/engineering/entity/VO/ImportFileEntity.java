package com.supermap.pipedesign.pipechina.engineering.entity.VO;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
public class ImportFileEntity {

    @ApiModelProperty(value = "源数据-文件名称(包含编码+文件类型名)")
      private String originData;

    @ApiModelProperty(value = "数据定义-文件类型(中线成果表、元数据等)")
    private String dataDefine;

    @ApiModelProperty(value = "数据类型-文件后缀")
    private String fileType;

    @ApiModelProperty(value = "状态(0已经上传，1还未上传)")
    private String state;

    @ApiModelProperty(value = "文件源路径)")
    private String fileOriginPath;

    @ApiModelProperty(value = "文件导入目标路径")
    private String fileTargetPath;

    @ApiModelProperty(value = "节点类型（1项目分解节点，2质检文件）")
    private int rootType;

    @ApiModelProperty(value = "质检规则")
    private String queRule;
    @ApiModelProperty(value = "obs文件ID或NAS路径 原始数据")
    private String fileidOrnasPath;
    @ApiModelProperty(value = "iserverUrl服务地址在线服务")
    private String iserverUrl;
    @ApiModelProperty(value = "生成缓存地址 缓存数据")
    private String cacheUrl;
   @ApiModelProperty(value = "文件清单id")
    private String filePkid;
   @ApiModelProperty(value = "委托id")
    private String submitentrustid;

   @ApiModelProperty(value = "阶段id")
    private String partId;
   @ApiModelProperty(value = "委托内容的起始桩号")
    private String  startNum;
   @ApiModelProperty(value = "委托内容的结束桩号")
    private String endNum;


}
