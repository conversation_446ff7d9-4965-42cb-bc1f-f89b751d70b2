package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import com.supermap.pipedesign.pipechina.engineering.service.IDictionaryService;
import com.supermap.pipedesign.pipechina.metadata.dao.DatasetinfotemMapper;
import com.supermap.tools.base.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 字典表(存储与项目及任务分解有关的字典表) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("DictionaryImpl")
public class DictionaryImpl
        extends ServiceImpl<DictionaryMapper, Dictionary>
        implements IDictionaryService {

    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private DatasetinfotemMapper datasetinfotemMapper;

    /**
     * 根据上级编码查Dictionary
     * @param dictionarypcode
     * @return
     */
    @Override
    public List<Dictionary> listByType(String dictionarypcode) {
        QueryWrapper<Dictionary> queryWrapper = new QueryWrapper<>();
        Dictionary dictionary = new Dictionary();
        dictionary.setDictionarypcode(dictionarypcode);
        queryWrapper.select("pkid","dictionary_name","dictionary_code");
        queryWrapper.setEntity(dictionary);
        return dictionaryMapper.selectList(queryWrapper);
    }

    @Override
    public List<DictionaryVo> selCodeByKey(String key){
        return dictionaryMapper.selCodeByKey( key );
    }

    @Override
    public List<DictionaryVo> selPidIsNull(){
        QueryWrapper<DictionaryVo> dictionaryVoQueryWrapper = new QueryWrapper<>();
        dictionaryVoQueryWrapper.isNull("pid");
        List<DictionaryVo> dictionaryVos = dictionaryMapper.selPidIsNull(dictionaryVoQueryWrapper);
        return dictionaryVos;
    }



    @Override
    public List<DictionaryVo> getSplitStr(String codeStr ){
        List<String> ruleSuitCodeList = new ArrayList<>();
        if( codeStr.indexOf(",") > -1 ){
            String[] ruleSuitArr = codeStr.split(",");
            ruleSuitCodeList = Arrays.asList( ruleSuitArr );
        }else{
            ruleSuitCodeList.add(codeStr );
        }
        List<DictionaryVo> ruleSuitDictList = new ArrayList<>();
        for( String codePkid : ruleSuitCodeList ){
            DictionaryVo dictionaryVo = dictionaryMapper.selByPkid(codePkid);
            ruleSuitDictList.add( dictionaryVo );
        }
        return  ruleSuitDictList;
    }

    @Override
    public List<Map> algorithTemLayer( String type, String searchName){
        if (JavaUtils.isNotEmtryOrNull(searchName)){
            return  datasetinfotemMapper.selByCategory("common_layer",Integer.parseInt(type),searchName);
        }
        List<Map> common_layer = datasetinfotemMapper.selByCategoryType("common_layer", Integer.parseInt(type));
        for (Map map : common_layer) {
            map.put("datasetname",map.get("data_set_name"));
            map.put("datasetalias",map.get("data_set_alias"));
        }

        return common_layer;
    }


}
