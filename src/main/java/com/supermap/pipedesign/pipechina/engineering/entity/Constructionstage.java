package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * $施工阶段表(定义预可行性研究，可行性研究，初步设计，施工图设计，竣工图设计等阶段,0,1,2,3,4,5) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_construction_stage")
@ApiModel(value="Constructionstage对象", description="$施工阶段表(定义预可行性研究，可行性研究，初步设计，施工图设计，竣工图设计等阶段,0,1,2,3,4,5)")
public class Constructionstage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "施工阶段名称")
    @TableField("stage_name")
    private String stageName;

    @ApiModelProperty(value = "施工阶段内部编号( 0,1,2,3,4 用于为对应的实体库进行编号，以便在建库时使用该编号作为库名的一部分。为了不至于导致库名过长，暂时先用数字编号。)")
    @TableField("stage_inner_code")
    private Integer stageInnerCode;

    @ApiModelProperty(value = "施工阶段描述")
    @TableField("stage_desc")
    private String stageDesc;


}
