package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <p>
 * 校审评审单(定义校审评审任务清单	注意：校审不止针对文件，还需要针对模型、成果文件、规则等的校审。都需要考虑。) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Data
public class ProofreadsheetsBo {

    @ApiModelProperty(value = "页数")
    private long current;
    @ApiModelProperty(value = "每页数量")
    private long size;
    @ApiModelProperty(value = "开始时间")
    private Timestamp startDate;
    @ApiModelProperty(value = "结束时间")
    private Timestamp endDate;
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "主题")
    private String themename;

    @ApiModelProperty(value = "校审/评审类型")
    private String reviewtype;
    @ApiModelProperty(value = "当前节点")
    private String nodestates;
    @ApiModelProperty(value = "单子类型(1=校审 2=评审)")
    private String sheetstype;


}
