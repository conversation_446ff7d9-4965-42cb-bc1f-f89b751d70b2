package com.supermap.pipedesign.pipechina.engineering.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Proofreadreffiles;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 校审评审关联文件表(存储每个校审单关联的文件，以及每个文件对应的批注数据集信息) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface ProofreadreffilesMapper extends BaseMapper<Proofreadreffiles> {
    @Select("select a.*, c.upload_userid, c.upload_date, c.file_version from wbs_proofread_reffiles a \n" +
            " left join wbs_workbag_file b on a.fileid = b.pkid \n "+
            " left join wbs_workbag_file_upload_record c on b.pkid = c.fileid order by c.upload_date asc")
    List<Proofreadreffiles> setProofreadreffilesListAll();

}
