package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 存储项目的大版本定版记录 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_engineering_versions")
@ApiModel(value="EngineeringVersions对象", description="存储项目的大版本定版记录")
public class EngineeringVersions implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "项目编号")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "项目定版的大版本号")
    @TableField("version")
    private String version;

    @ApiModelProperty(value = "定版时间")
    @TableField("create_date")
    private Timestamp createDate;

    @ApiModelProperty(value = "定版人ID")
    @TableField("create_user_id")
    private String createUserId;

    @ApiModelProperty(value = "更新内容")
    @TableField("version_desc")
    private String versionDesc;


}
