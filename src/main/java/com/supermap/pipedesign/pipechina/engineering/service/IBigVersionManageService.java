package com.supermap.pipedesign.pipechina.engineering.service;

/**
 * 设计成果版本管理业务处理类
 * <AUTHOR>
 * @since  2023-3-29
 */
public interface IBigVersionManageService {

    /**
     * 生成项目成果预览工作空间(连接生产库待定版数据源)
     * @param strProjectId  项目ID
     * @return  生成的工作空间文件压缩包路径
     */
    String viewVersion(String strProjectId);

    /**
     * 为项目生成确定版本的工作空间和数据
     * @param strProjectId  项目ID
     * @param strUserID     操作人ID
     * @return  发布的服务地址JSON字符串
     */
    String finalizeVersion(String strProjectId, String strVersion, String strUserID);
}
