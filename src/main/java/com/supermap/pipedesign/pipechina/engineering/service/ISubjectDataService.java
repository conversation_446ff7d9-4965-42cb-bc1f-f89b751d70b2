package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.SubjectData;
import com.supermap.pipedesign.pipechina.engineering.entity.Subjects;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @autthor sqq
 * @date 2023/3/30
 * @Description
 */
@Repository
public interface ISubjectDataService extends IService<SubjectData> {
    int insert(SubjectData subjectData);

    List<String> getRuleList(String userId);
}
