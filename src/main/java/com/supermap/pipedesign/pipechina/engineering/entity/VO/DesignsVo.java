package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import com.supermap.pipedesign.pipechina.engineering.entity.SubjectData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @autthor sqq
 * @date 2023/3/30
 * @Description
 * 专业-质检规则数据
 */
@Data
public class DesignsVo implements Serializable {

    List<SubjectData> subjectDataList;
    @ApiModelProperty(value = "唯一编号")
    private String pkid;
    @ApiModelProperty(value = "设计单元名称")
    private String designname;
    @ApiModelProperty(value = "工程ID")
    private String projectid;
    @ApiModelProperty(value = "起始桩点编号")
    private String stakepointnostart;
    @ApiModelProperty(value = "终点桩点编号")
    private String stakepointnoend;
    @ApiModelProperty(value = "设计段类型(0全段、1一般段、2大中型穿跨越、3单出图)")
    private Integer designType;

}
