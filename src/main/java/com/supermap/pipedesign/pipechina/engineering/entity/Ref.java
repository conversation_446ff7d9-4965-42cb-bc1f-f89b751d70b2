package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * obs项目-部门-人员-岗位关联表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("obs_ref")
@ApiModel(value="Ref对象", description="obs项目-部门-人员-岗位关联表")
public class Ref implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "项目编号")
    @TableField("eps_code")
    private String epsCode;

    @ApiModelProperty(value = "虚拟部门")
    @TableField("invented_dept")
    private String inventedDept;

    @ApiModelProperty(value = "虚拟部门编号")
    @TableField("invented_dept_code")
    private String inventedDeptCode;

    @ApiModelProperty(value = "用户编号")
    @TableField("user_code")
    private String userCode;

    @ApiModelProperty(value = "用户名称")
    @TableField("user_name")
    private String userName;

    @ApiModelProperty(value = "部门编号")
    @TableField("dept_code")
    private String deptCode;

    @ApiModelProperty(value = "部门名称")
    @TableField("dept_name")
    private String deptName;

    @ApiModelProperty(value = "组织编号")
    @TableField("org_code")
    private String orgCode;

    @ApiModelProperty(value = "组织名称")
    @TableField("org_name")
    private String orgName;

    @ApiModelProperty(value = "岗位")
    @TableField("role")
    private String role;

    @ApiModelProperty(value = "虚拟部门id")
    @TableField("invented_dept_id")
    private String inventedDeptId;


}
