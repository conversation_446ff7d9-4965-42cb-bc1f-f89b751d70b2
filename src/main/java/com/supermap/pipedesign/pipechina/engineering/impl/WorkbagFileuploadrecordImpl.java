package com.supermap.pipedesign.pipechina.engineering.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.engineering.dao.*;
import com.supermap.pipedesign.pipechina.engineering.entity.*;
import com.supermap.pipedesign.pipechina.engineering.service.IEngineeringService;
import com.supermap.pipedesign.pipechina.engineering.service.IWbsMetadatainfoService;
import com.supermap.pipedesign.pipechina.engineering.service.IWorkbagFileuploadrecordService;
import com.supermap.pipedesign.pipechina.sdx.service.ICenterlineService;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.file.ImportExcel;
import com.supermap.tools.file.XLSX2CSV;
import org.apache.commons.io.FileUtils;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import javax.annotation.Resource;
import javax.xml.parsers.ParserConfigurationException;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <p>
 * 工作包成果文件记录存储任务对应每个文件的上传文件路径、上传人、版本等信息。) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("WorkbagFileuploadrecordImpl")
public class WorkbagFileuploadrecordImpl extends ServiceImpl<WorkbagFileuploadrecordMapper, WorkbagFileuploadrecord> implements IWorkbagFileuploadrecordService {
    //文件记录存储
    @Autowired
    private WorkbagFileuploadrecordMapper workbagFileuploadrecordMapper;
    //委托阶段Mapper
    @Autowired
    private SubmitentrustPartMapper submitentrustPartMapper;

    @Autowired
    private SubmitentrustMapper submitentrustMapper;
    //规则对应成果表名
    @Autowired
    private RulesTableMapper rulesTableMapper;
    //文件清单
    @Autowired
    private WorkbagFileMapper workbagFileMapper;
    @Resource(name = "CenterlineImpl")
    private ICenterlineService centerlineService;
    @Resource(name = "WbsMetadatainfoImpl")
    private IWbsMetadatainfoService wbsMetadatainfoImpl;
    //反馈清单id
    @Autowired
    private SubmitentrustFileMapper submitentrustFileMapper;


    @Resource(name = "EngineeringImpl")
    private IEngineeringService engineeringImpl;

    /**
     * 添加工作包成果文件记录存储任务对应每个文件的上传文件路径、上传人、版本等信息。)信息
     *
     * @param workbagFileuploadrecord
     * @return int
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public int insert(WorkbagFileuploadrecord workbagFileuploadrecord) {

        //workbagFileuploadrecord.setUserId(JavaUtils.getUUID36());
        //workbagFileuploadrecord.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return workbagFileuploadrecordMapper.insert(workbagFileuploadrecord);
    }

    /**
     * 删除工作包成果文件记录存储任务对应每个文件的上传文件路径、上传人、版本等信息。)信息
     *
     * @param workbagFileuploadrecordId
     * @return int
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public int delete(String workbagFileuploadrecordId) {
        return workbagFileuploadrecordMapper.deleteById(workbagFileuploadrecordId);
    }

    /**
     * 更新工作包成果文件记录存储任务对应每个文件的上传文件路径、上传人、版本等信息。)信息
     *
     * @param workbagFileuploadrecord
     * @return int
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public int update(WorkbagFileuploadrecord workbagFileuploadrecord) {
        return workbagFileuploadrecordMapper.updateById(workbagFileuploadrecord);
    }

    /**
     * 全部查询
     *
     * @param workbagFileuploadrecord
     * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFileuploadrecord>
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public List<WorkbagFileuploadrecord> list(WorkbagFileuploadrecord workbagFileuploadrecord) {

        QueryWrapper<WorkbagFileuploadrecord> queryWrapper = new QueryWrapper<>();

        return workbagFileuploadrecordMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @param startDate
     * @param endDate
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * @Date 2022-12-17
     * @auther eomer
     */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<WorkbagFileuploadrecord> workbagFileuploadrecordIPage = new Page<>();
        workbagFileuploadrecordIPage.setCurrent(current);
        workbagFileuploadrecordIPage.setSize(size);

        QueryWrapper<WorkbagFileuploadrecord> queryWrapper = new QueryWrapper<>();

        if (startDate != null && endDate != null) {
            queryWrapper.between("col_create_time", startDate, endDate);
        }
        return workbagFileuploadrecordMapper.selectPage(workbagFileuploadrecordIPage, queryWrapper);
    }

    /**
     * 质检文件入库
     *
     * @param files           接收文件集合
     * @param submitentrustid 委托单id
     * @param partid          委托段
     * @param projectId       项目
     * @return
     */
    @Override
    public int fileAdd(List<MultipartFile> files, String partid, String submitentrustid, String projectId, String dirID, String dirType, String mapJson, String url, String range, String rule1, String subjectcode,String entrustFileId) {
        String mapJson1 = URLDecoder.decode(mapJson, StandardCharsets.UTF_8);
        String rule = URLDecoder.decode(rule1, StandardCharsets.UTF_8);
        //将json字符串转成Map
        Map map = JSONUtil.toBean(mapJson1, Map.class);
        int result = 0;
        //接收excel表格数据
        List<Map<String, String>> maps = new ArrayList<>();
        //接收excel多个sheet页数据
        List<Map<String,List<Map<String, String>>>>  sheetsMap = new ArrayList<>();
        //上传到指定目录
        String uploadDir = "D:\\file\\target\\";
        File fileDir = new File("D:\\file\\target");
        if (!fileDir.exists()) {
            fileDir.mkdirs();
        }
        //工作包清单
        WorkbagFile workbagFile = new WorkbagFile();
        //工作清单记录表
        WorkbagFileuploadrecord workbagFileuploadrecord = new WorkbagFileuploadrecord();
        QueryWrapper<SubmitentrustPart> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sub_miten_trust_id", submitentrustid);
        //根据委托单id查询工程id
        SubmitentrustPart submitentrustPart = submitentrustPartMapper.selectOne(queryWrapper);
        // List<SubmitentrustPart> submitentrustPartList=submitentrustPartMapper.selectList(queryWrapper);
        //根据委托id查详情
        Submitentrust submitentrust = submitentrustMapper.selectById(submitentrustid);
        //设置文件清单主键id
        String uuid = UUID.randomUUID().toString();
        //设置主键
        workbagFile.setPkid(uuid);
        //添加文件类型（0子工程，1工程单元，2功能区）
        workbagFile.setFileidtype(submitentrustPart.getFileidtype());
        if (submitentrust.getAcceptsubject().equals("线路")) {
            workbagFile.setDatatype(0);
        } else if (submitentrust.getAcceptsubject().equals("勘察")) {
            workbagFile.setDatatype(1);
        } else if (submitentrust.getAcceptsubject().equals("测量")) {
            workbagFile.setDatatype(2);
        }
//        if (dirType.equals("8")) {
//            //添加文件类型（0子工程，1工程单元，2功能区）
//            workbagFile.setFileidtype(submitentrustPart.getFileidtype());
//        } else if (dirType.equals("0")) {
//            workbagFile.setFileidtype(0);
//        } else if (dirType.equals("1")) {
//            workbagFile.setFileidtype(1);
//        } else if (dirType.equals("2")) {
//            workbagFile.setFileidtype(2);
//        } else if (dirType.equals("4")) {
//            workbagFile.setFileidtype(4);
//        }
        //添加子工程id
        workbagFile.setSubprojectid(submitentrustPart.getSubprojectid());
        //添加项目id
        workbagFile.setProjectid(projectId);
        //服务路径
        workbagFile.setServiceaddress(url);
        //循环遍历文件
        for (MultipartFile file : files) {
            File file1 = new File("D:\\file\\" + file.getName());
            try {
                FileUtils.copyInputStreamToFile(file.getInputStream(), file1);
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (file1.isDirectory()) {
                List<File> bindFiles = new ArrayList<>();
                File baseFile = new File(file1.getPath());
                File[] filess = baseFile.listFiles();
                Collections.addAll(bindFiles, filess);
                for (File file11 : bindFiles) {
                    if (file11.getName().contains("元数据") || file11.getName().contains("Metadata")) {
                        try {
                            wbsMetadatainfoImpl.insertWbsMetadatainfoList(ImportExcel.redExcelLeft(uploadDir + file11.getName(), uuid));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
              }
            File serverFile;
            //文件名称
            String fileName = file.getOriginalFilename();
            fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8);//解码
            //根据文件名称获取需要的obs的文件id或者是nas路径
            workbagFile.setFileidornaspath((String) map.get(fileName));
            //获取iserverurl
            workbagFile.setIserverurl((String) map.get("iserverUrl"));
            //获取生成缓存路径
            workbagFile.setCacheurl((String) map.get("cacheURL"));
            //保存质检规则类型
            workbagFile.setQualityType(rule);
            //获取影像范围
            workbagFile.setRange(range);
            //接收文件存储
            String filesuffix = fileName.substring(fileName.lastIndexOf("."));
            String savePath = "";
            try {
                if (file.isEmpty()) {
                    throw new Exception("未找到上传文件!");
                } else {
                    //如果目录不存在，自动创建文件夹
                    File dir = new File(uploadDir);
                    if (!dir.exists()) {
                        dir.mkdir();
                    }
                    //保存路径
                    savePath = uploadDir + fileName;
                    //保存文件对象
                    serverFile = new File(savePath);
                    FileUtils.copyInputStreamToFile(file.getInputStream(), serverFile);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
//            if (dirType.equals("8")) {
//                //判断fileidtyp类型添加相应的成果文件信息
//                if (submitentrustPart.getFileidtype() == 1) {
//                    //添加工程单元id
//                    workbagFile.setProjectunitid(submitentrustPart.getProjectunitid());
//                    workbagFileMapper.deletePrFiled(projectId, submitentrustPart.getSubprojectid(), submitentrustPart.getProjectunitid(), fileName);
//                } else if (submitentrustPart.getFileidtype() == 2) {
//                    //添加工程单元id
//                    workbagFile.setProjectunitid(submitentrustPart.getProjectunitid());
//                    //添加功能区id
//                    workbagFile.setFunctionalareaid(submitentrustPart.getFunctionalareaid());
//                    workbagFileMapper.deleteFrFiled(projectId, submitentrustPart.getSubprojectid(), submitentrustPart.getProjectunitid(), submitentrustPart.getFunctionalareaid(), fileName);
//                } else if (submitentrustPart.getFileidtype() == 0) {
//                    workbagFileMapper.deleteSrFiled(fileName, projectId, submitentrustPart.getSubprojectid());
//                }
//            } else if (dirType.equals("4")) {
//                workbagFileMapper.deleteProjectFiled(projectId, fileName);
//            } else if (dirType.equals("0")) {
//                workbagFileMapper.deleteSrFiled(fileName, projectId, submitentrustPart.getSubprojectid());
//            } else if (dirType.equals("1")) {
//                //添加工程单元id
//                workbagFile.setProjectunitid(submitentrustPart.getProjectunitid());
//                workbagFileMapper.deletePrFiled(projectId, submitentrustPart.getSubprojectid(), submitentrustPart.getProjectunitid(), fileName);
//            } else if (dirType.equals("2")) {
//                //添加工程单元id
//                workbagFile.setProjectunitid(submitentrustPart.getProjectunitid());
//                //添加功能区id
//                workbagFile.setFunctionalareaid(submitentrustPart.getFunctionalareaid());
//                workbagFileMapper.deleteFrFiled(projectId, submitentrustPart.getSubprojectid(), submitentrustPart.getProjectunitid(), submitentrustPart.getFunctionalareaid(), fileName);
//            }
            // 元数据处理
            if (fileName.contains("元数据") || fileName.contains("Metadata")) {
                try {
                    wbsMetadatainfoImpl.insertWbsMetadatainfoList(ImportExcel.redExcelLeft(uploadDir + fileName, uuid));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                files.remove(file);
                //非元数据处理
            } else {
                //文件后缀
                if (".xls".equals(filesuffix) || ".xlsx".equals(filesuffix)) {
                    try {
                        if (rule.contains("水工保护")) {
                            sheetsMap = ImportExcel.redExcelSheets(savePath);
                            // 成果入库
                            centerlineService.sheetslToTable(sheetsMap, projectId);
                        } else {
                            maps = ImportExcel.redExcel(savePath);
                            // 成果入库
                            centerlineService.excelToTable(maps, rule, projectId);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //文件名称
                    workbagFile.setFilename(fileName);
                    //文件后缀
                    workbagFile.setFilesuffix(filesuffix);
                    //添加文件清单
                    workbagFileMapper.insert(workbagFile);
//                } else if (".udb".equals(filesuffix) || filesuffix.equals(".shp") || filesuffix.equals(".dwg") ||
//                        filesuffix.equals(".tif") || filesuffix.equals(".osgb") || filesuffix.equals(".las") || fileName.contains("目录")) {
                }else{
                    //文件名称
                    workbagFile.setFilename(fileName);
                    //文件后缀
                    workbagFile.setFilesuffix(filesuffix);
                    //添加文件清单
                    workbagFileMapper.insert(workbagFile);
                }
                //文件清单ID
                workbagFileuploadrecord.setFileid(uuid);

                //更新反馈清单
                SubmitentrustFile submitentrustFile=new SubmitentrustFile();
                submitentrustFile.setWorkBagFileId(uuid);
                submitentrustFile.setPkid(entrustFileId);
                submitentrustFileMapper.updateById(submitentrustFile);
                //文件路径
                workbagFileuploadrecord.setFileurl(savePath);
                workbagFileuploadrecord.setProjectId(projectId);
                //获取当前时间设置上传时间
                Date data = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                workbagFileuploadrecord.setUploaddate(Timestamp.valueOf(sdf.format(data)));
                //写死的上传人id 王经理id：1
                workbagFileuploadrecord.setUploaduserid("1");
                //添加文件清单记录
                result = workbagFileuploadrecordMapper.insert(workbagFileuploadrecord);

                // TODO 添加版本更新
                engineeringImpl.updateEngVersion(projectId);
            }
        }
        return result;
    }

    @Override
    public String getPathByFileId(String fileId) {
        QueryWrapper<WorkbagFileuploadrecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("file_url");
        queryWrapper.eq("fileid", fileId);
        List<WorkbagFileuploadrecord> list = workbagFileuploadrecordMapper.selectList(queryWrapper);
        if (list.size() > 0) {
            return list.get(0).getFileurl();
        } else {
            throw new BusinessException("未找到文件，请检查");
        }
    }

    /**
     * 质检树右键查看地图---通过fileid查询反馈csv文件
     *
     * @Date 2023-2-25
     * @auther SQQ
     */
    @Override
    public Map<String, String> getFileCSV(String fileid) {
        Map<String, String> map = new HashMap<>();
        String filePath = workbagFileMapper.getPach(fileid);
        File file = new File(filePath);
        String name = file.getName().substring(0, file.getName().lastIndexOf("."));
        String outputFilePath = "D:\\file\\target\\" + name + ".csv";
        map.put("url", outputFilePath);
        XLSX2CSV xlsx2csv = null;
        try {
            xlsx2csv = new XLSX2CSV(filePath, outputFilePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            xlsx2csv.process();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (OpenXML4JException e) {
            e.printStackTrace();
        } catch (ParserConfigurationException e) {
            e.printStackTrace();
        } catch (SAXException e) {
            e.printStackTrace();
        }
        //返回前端的cvs文件流
        return map;
    }

    @Override
    public Map<String, String> getFilePath(String fileid) {
        Map<String, String> map = new HashMap<>();
        String filePath = workbagFileMapper.getPach(fileid);
        QueryWrapper<WorkbagFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid", fileid);
        WorkbagFile workbagFile = workbagFileMapper.selectOne(queryWrapper);
        map.put(fileid, filePath);
        map.put("idOrPath", workbagFile.getFileidornaspath());
        map.put("rule", workbagFile.getQualityType());
        return map;
    }

    @Autowired
    private ESUFMWorkbagMapper esufmWorkbagMapper;


    public int addFileRecord(File file, String bagId,String userId) {
        WorkbagFileuploadrecord record = new WorkbagFileuploadrecord();
        record.setPkid(UUID.randomUUID().toString());
        record.setFileid(bagId);
        record.setFileurl(file.getPath());
        record.setUploaddate(JavaUtils.getTimestamp());
        record.setUploaduserid(userId);
        int result = workbagFileuploadrecordMapper.insert(record);
        WorkbagFile workbagFile = workbagFileMapper.selectById(bagId);
        ESUFMWorkbag esufmWorkbag = esufmWorkbagMapper.selectById(workbagFile.getWorkbagid());
        workbagFile.setProjectid(esufmWorkbag.getProjectid());
        workbagFile.setSubprojectid(esufmWorkbag.getSubprojectid());
        workbagFile.setProjectunitid(esufmWorkbag.getProjectunitid());
        workbagFile.setFunctionalareaid(esufmWorkbag.getFunctionalareaid());
        workbagFile.setFilesuffix("."+FileUtil.extName(file.getName()));
        workbagFile.setFileStatus(1);

        workbagFileMapper.updateById(workbagFile);

        return result;
    }
}
