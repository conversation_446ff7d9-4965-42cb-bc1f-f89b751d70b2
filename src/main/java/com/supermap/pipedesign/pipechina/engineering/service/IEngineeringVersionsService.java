package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.EngineeringVersions;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 存储项目的大版本定版记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Repository
public interface IEngineeringVersionsService extends IService<EngineeringVersions> {

 /**
 * 添加存储项目的大版本定版记录信息
 *
 * @param engineeringVersions
 * @return int
 * @Date 2023-04-12
 * @auther eomer
 */
 int insert(EngineeringVersions engineeringVersions);

 /**
 * 删除存储项目的大版本定版记录信息
 *
 * @param engineeringVersionsId
 * @return int
 * @Date 2023-04-12
 * @auther eomer
 */
 int delete(String engineeringVersionsId);

 /**
 * 更新存储项目的大版本定版记录信息
 *
 * @param engineeringVersions
 * @return int
 * @Date 2023-04-12
 * @auther eomer
 */
 int update(EngineeringVersions engineeringVersions);

 /**
 * 全部查询
 *
 * @param engineeringVersions
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.EngineeringVersions>
 * @Date 2023-04-12
 * @auther eomer
 */
 List<EngineeringVersions> list(EngineeringVersions engineeringVersions);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-04-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
