package com.supermap.pipedesign.pipechina.engineering.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Constructunits;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 参建单位信息(存储参建单位的详细信息。	因为从MDM中最多只能获取到参见单位的基本信息，所以还需要一张表存储参建单位的详细信息。)			注意：这部分要跟集成商去对，由集成商总负责！！！ Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface ConstructunitsMapper extends BaseMapper<Constructunits> {

}
