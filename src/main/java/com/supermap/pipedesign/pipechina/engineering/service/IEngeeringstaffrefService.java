package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.engineering.entity.Engeeringstaffref;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 项目成员、同一个项目可能会关联多个人。需要单独建立一个表来存。 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IEngeeringstaffrefService extends IService<Engeeringstaffref> {

 /**
 * 添加项目成员、同一个项目可能会关联多个人。需要单独建立一个表来存。信息
 *
 * @param engeeringstaffref
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Engeeringstaffref engeeringstaffref);

 /**
 * 删除项目成员、同一个项目可能会关联多个人。需要单独建立一个表来存。信息
 *
 * @param engeeringstaffrefId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String engeeringstaffrefId);

 /**
 * 更新项目成员、同一个项目可能会关联多个人。需要单独建立一个表来存。信息
 *
 * @param engeeringstaffref
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Engeeringstaffref engeeringstaffref);

 /**
 * 全部查询
 *
 * @param engeeringstaffref
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Engeeringstaffref>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Engeeringstaffref> list(Engeeringstaffref engeeringstaffref);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
