package com.supermap.pipedesign.pipechina.engineering.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.engineering.entity.Proofreadresult;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 评审结果表(存储各类校审评审结果) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IProofreadresultService extends IService<Proofreadresult> {

 /**
 * 添加评审结果表(存储各类校审评审结果)信息
 *
 * @param proofreadresult
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Proofreadresult proofreadresult);

 /**
 * 删除评审结果表(存储各类校审评审结果)信息
 *
 * @param proofreadresultId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String proofreadresultId);

 /**
 * 更新评审结果表(存储各类校审评审结果)信息
 *
 * @param proofreadresult
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Proofreadresult proofreadresult);

 /**
  * 全部查询
  *
  * @param proofreadresult
  * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Proofreadresult>
  * @Date 2022-12-17
  * @auther eomer
  */
 List<Proofreadresult> list(Proofreadresult proofreadresult);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate, String commenttype, String userid, String fileid);

 /**
  * 意见详情（过程）
  * @param pkid 意见ID
  * @Date 2023-03-22
  * @auther zhdy
  * @return
  */
 JSONObject details(String pkid);


 }
