package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.Dto.WorkbagFileBo;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFile;
import com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFileuploadrecord;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 工作包文件清单(定义工作包下包含的文件清单，每个清单对应最新的成功文件。) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IWorkbagFileService extends IService<WorkbagFile> {

 /**
 * 添加工作包文件清单(定义工作包下包含的文件清单，每个清单对应最新的成功文件。)信息
 *
 * @param workbagFile
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(WorkbagFile workbagFile);

 /**
 * 删除工作包文件清单(定义工作包下包含的文件清单，每个清单对应最新的成功文件。)信息
 *
 * @param pkid
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String pkid);

 /**
 * 更新工作包文件清单(定义工作包下包含的文件清单，每个清单对应最新的成功文件。)信息
 *
 * @param workbagFile
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(WorkbagFile workbagFile);

 /**
 * 全部查询
 *
 * @param workbagFile
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFile>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<WorkbagFile> list(WorkbagFile workbagFile);

 /**
 * 分页查询
 *
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(WorkbagFileBo bo);


    IPage pageList(long current, long size, String fileName, String fileType, String subject, String startTime, String endTime, String projectId, User user);

 WorkbagFileuploadrecord selectInfo(String id);
/**
 * 模板文件入库
 */
 String dwgToWorkBag(String projectId, String templateId, String temCode, String oldName, String filePath, User userId);

 /**
  * tdr文件清单导入
  * <AUTHOR>
  * @Description
  * @Date 2023/4/17 10:13
  **/
 int tdrImprot(MultipartFile file, String projectId);

 /**
  * 工作包添加文件
  * <AUTHOR>
  * @Description
  * @Date 2023/4/17 14:47
  **/
 int addFileRecords(MultipartFile file, String bagId, String userId);

 /**
  *
  * 根据工作包id查文件下载id
  * <AUTHOR>
  * @Description
  * @Date 2023/4/24 17:55
  **/
 WorkbagFile seleFilePath(String workBagFileId);
 }
