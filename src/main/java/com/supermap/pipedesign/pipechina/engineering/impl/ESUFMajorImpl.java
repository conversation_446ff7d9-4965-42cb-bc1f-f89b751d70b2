package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.ESUFMajorMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.ESUFMajor;
import com.supermap.pipedesign.pipechina.engineering.service.IESUFMajorService;
import com.supermap.tools.base.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 专业项 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("ESUFMajorImpl")
public class ESUFMajorImpl extends ServiceImpl<ESUFMajorMapper, ESUFMajor> implements IESUFMajorService {

    @Autowired
    private ESUFMajorMapper eSUFMajorMapper;

    /**
    * 添加专业项信息
    *
    * @param eSUFMajor
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int insert(ESUFMajor eSUFMajor) {

        //eSUFMajor.setUserId(JavaUtils.getUUID36());
        //eSUFMajor.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return eSUFMajorMapper.insert(eSUFMajor);
    }

    /**
    * 删除专业项信息
    *
    * @param eSUFMajorId
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int delete(String eSUFMajorId) {
        return eSUFMajorMapper.deleteById(eSUFMajorId);
    }

    /**
    * 更新专业项信息
    *
    * @param eSUFMajor
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int update(ESUFMajor eSUFMajor) {
        return eSUFMajorMapper.updateById(eSUFMajor);
    }

    /**
    * 全部查询
    *
    * @param eSUFMajor
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.ESUFMajor>
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<ESUFMajor> list(ESUFMajor eSUFMajor) {

        QueryWrapper<ESUFMajor> queryWrapper = new QueryWrapper<>();

        return eSUFMajorMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param functionalareaid
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<ESUFMajor> pageList(String functionalareaid ,String wbsName,String wbsCode) {

        QueryWrapper<ESUFMajor> queryWrapper = new QueryWrapper<>();
        //添加条件上一级id  functionalareaid
        queryWrapper.lambda().eq(ESUFMajor::getFunctionalareaid,functionalareaid);
        if (JavaUtils.isNotEmtryOrNull(wbsName)){
            queryWrapper.like("subject_name",wbsName);
        }
        if (JavaUtils.isNotEmtryOrNull(wbsCode)){
            queryWrapper.like("subject_code",wbsCode);
        }
        //未删除条件
        queryWrapper.lambda().eq(ESUFMajor::getDeleteflag,0L);

        return eSUFMajorMapper.selectList(queryWrapper);
    }
    /**
     * 查询专业项详细信息
     *
     * @param pkid
     * @Date 2022-12-14
     * @auther eomer
     */
    public ESUFMajor select(String pkid) {
        return eSUFMajorMapper.selectById(pkid);
    }

}
