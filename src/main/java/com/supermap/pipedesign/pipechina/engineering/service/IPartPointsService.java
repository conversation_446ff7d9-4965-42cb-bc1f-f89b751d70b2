package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.engineering.entity.PartPoints;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 委托单桩点顺序表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IPartPointsService extends IService<PartPoints> {

 /**
 * 添加委托单桩点顺序表信息
 *
 * @param partPoints
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(PartPoints partPoints);

 /**
 * 删除委托单桩点顺序表信息
 *
 * @param partPointsId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String partPointsId);

 /**
 * 更新委托单桩点顺序表信息
 *
 * @param partPoints
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(PartPoints partPoints);

 /**
 * 全部查询
 *
 * @param partPoints
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.PartPoints>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<PartPoints> list(PartPoints partPoints);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);

 /**
  * 受托阶段表
  * 添加桩点顺序
  * @param partid
  * @param startpoint
  * @param endpoint
  * @return
  */
 int addpartpoin(String partid, String subprojectid, String startpoint, String endpoint);


 }
