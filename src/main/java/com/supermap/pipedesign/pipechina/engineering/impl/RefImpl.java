package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.RefMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Ref;
import com.supermap.pipedesign.pipechina.engineering.service.IRefService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * obs项目-部门-人员-岗位关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Service("RefImpl")
public class RefImpl extends ServiceImpl<RefMapper, Ref> implements IRefService {

    @Autowired
    private RefMapper refMapper;

    /**
    * 添加obs项目-部门-人员-岗位关联表信息
    *
    * @param ref
    * @return int
    * @Date 2023-04-20
    * @auther eomer
    */
    @Override
    public int insert(Ref ref) {

        //ref.setUserId(JavaUtils.getUUID36());
        //ref.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return refMapper.insert(ref);
    }

    /**
    * 删除obs项目-部门-人员-岗位关联表信息
    *
    * @param refId
    * @return int
    * @Date 2023-04-20
    * @auther eomer
    */
    @Override
    public int delete(String refId) {
        return refMapper.deleteById(refId);
    }

    /**
    * 更新obs项目-部门-人员-岗位关联表信息
    *
    * @param ref
    * @return int
    * @Date 2023-04-20
    * @auther eomer
    */
    @Override
    public int update(Ref ref) {
        return refMapper.updateById(ref);
    }

    /**
    * 全部查询
    *
    * @param ref
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Ref>
    * @Date 2023-04-20
    * @auther eomer
    */
    @Override
    public List<Ref> list(Ref ref) {

        QueryWrapper<Ref> queryWrapper = new QueryWrapper<>();

        return refMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-04-20
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<Ref> refIPage = new Page<>();
        refIPage.setCurrent(current);
        refIPage.setSize(size);

        QueryWrapper<Ref> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return refMapper.selectPage(refIPage, queryWrapper);
    }


}
