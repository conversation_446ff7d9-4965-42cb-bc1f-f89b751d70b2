package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @BelongsProject: gm-api
 * @BelongsPackage: com.supermap.pipedesign.pipechina.engineering.entity.VO
 * @Author: du<PERSON><PERSON><PERSON>e
 * @CreateTime: 2023-03-08  17:07
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class SubjectsTaskVo extends SubjectsVo {

    @ApiModelProperty(value = "选中设计人员")
    private List<DesignSubDropdownUserVO> designUser;

    @ApiModelProperty(value = "设计人员")
    private List<DesignSubDropdownUserVO> designUserList;

    @ApiModelProperty(value = "选中校对人员")
    private List<DesignSubDropdownUserVO> proofreadUser;

    @ApiModelProperty(value = "校对人员")
    private List<DesignSubDropdownUserVO> proofreadUserList;

    @ApiModelProperty(value = "选中审核人员")
    private List<DesignSubDropdownUserVO> auditUser;

    @ApiModelProperty(value = "审核人员")
    private List<DesignSubDropdownUserVO> auditUserList;

    @ApiModelProperty(value = "选中核准人员")
    private List<DesignSubDropdownUserVO> approveUser;

    @ApiModelProperty(value = "核准人员")
    private List<DesignSubDropdownUserVO> approveUserList;

}
