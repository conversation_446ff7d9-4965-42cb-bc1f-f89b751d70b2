package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.EngineeringSub;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.EngineeringSubVo;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.ProjectTree;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 子工程表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IEngineeringSubService extends IService<EngineeringSub> {

 /**
 * 添加子工程表信息
 *
 * @param engineeringSub
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(EngineeringSub engineeringSub);

 /**
 * 删除子工程表信息
 *
 * @param engineeringSubId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String engineeringSubId);

 /**
 * 更新子工程表信息
 *
 * @param engineeringSub
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(EngineeringSub engineeringSub);

 /**
 * 全部查询
 *
 * @param engineeringSub
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.EngineeringSub>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<EngineeringSub> list(EngineeringSub engineeringSub);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);

 /**
  * 根据项目pkid查子项目列表
  * @param pkid
  * @return
  */
 List<EngineeringSub> getByProjectId(String pkid,String wbsName,String wbsCode);

 /**
  * 根据项目查子工程、功能单元、功能区
  * @return
  */
 ProjectTree sublist(String projectid);

 EngineeringSubVo getBySubId(String subId);



 }
