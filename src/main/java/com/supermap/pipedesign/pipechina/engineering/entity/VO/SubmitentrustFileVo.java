package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 互提资料反馈文件清单 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
public class SubmitentrustFileVo implements Serializable {

    @ApiModelProperty(value = "成果文件清单ID")
    private String workBagFileId;

    @ApiModelProperty(value = "文件编码")
    private String fileCode;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "上传时间")
    private Timestamp uploadDate;

    @ApiModelProperty(value = "文件类型")
    private String type;

}
