package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.engineering.entity.TmpDirectory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.ImportFileEntity;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 质检临时目录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface ITmpDirectoryService extends IService<TmpDirectory> {

 /**
 * 添加质检临时目录表信息
 *
 * @param tmpDirectory
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(TmpDirectory tmpDirectory);

 /**
 * 删除质检临时目录表信息
 *
 * @param tmpDirectoryId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String pkid);

 /**
 * 更新质检临时目录表信息
 *
 * @param tmpDirectory
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(TmpDirectory tmpDirectory);

 /**
 * 全部查询
 *
 * @param tmpDirectory
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.TmpDirectory>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<TmpDirectory> list(TmpDirectory tmpDirectory);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);

}
