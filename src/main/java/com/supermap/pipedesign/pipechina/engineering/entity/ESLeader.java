package com.supermap.pipedesign.pipechina.engineering.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 子工程各专业负责人 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("wbs_e_s_leader")
@ApiModel(value="ESLeader对象", description="子工程各专业负责人")
public class ESLeader implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "工程ID")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "子工程ID(该字段对应“工程项目数据库”的“子工程”的PKID)")
    @TableField("sub_project_id")
    private String subprojectid;

    @ApiModelProperty(value = "专业名称")
    @TableField("subject_name")
    private String subjectname;

    @ApiModelProperty(value = "专业别名")
    @TableField("subject_alias")
    private String subjectalias;

    @ApiModelProperty(value = "专业编码")
    @TableField("subject_code")
    private String subjectcode;

    @ApiModelProperty(value = "专业ID")
    @TableField("subject_id")
    private String subjectid;

    @ApiModelProperty(value = "专业顺序号(用来排序)")
    @TableField("order_index")
    private Integer orderindex;

    @ApiModelProperty(value = "负责人ID")
    @TableField("designer_id")
    private String designerid;

    @ApiModelProperty(value = "负责人名称")
    @TableField("designer_name")
    private String designername;


}
