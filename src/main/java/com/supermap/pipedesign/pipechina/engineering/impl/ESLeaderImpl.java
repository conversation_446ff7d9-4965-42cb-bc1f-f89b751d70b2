package com.supermap.pipedesign.pipechina.engineering.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.engineering.dao.ESLeaderMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.SubjectsMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.UserMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.ESLeader;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.service.IESLeaderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 子工程各专业负责人 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("ESLeaderImpl")
public class ESLeaderImpl extends ServiceImpl<ESLeaderMapper, ESLeader> implements IESLeaderService {

    @Autowired
    private ESLeaderMapper eSLeaderMapper;

    @Autowired
    private SubjectsMapper subjectsMapper;

    @Autowired
    private UserMapper userMapper;

    /**
    * 添加子工程各专业负责人信息
    *
    * @param eSLeader
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<ESLeader> insert(List<ESLeader> eSLeader) {

        List<ESLeader> list = null;
        int result = 0;
        for (ESLeader lea:eSLeader) {
            String subprojectid = lea.getSubprojectid();
            if(StringUtils.isNotBlank(lea.getDesignerid())){
                User user = userMapper.selectById(lea.getDesignerid());

                QueryWrapper<ESLeader> qw = new QueryWrapper<>();
                qw.eq("sub_project_id",lea.getSubprojectid());
                qw.eq("subject_id",lea.getSubjectid());
                ESLeader esLeader = eSLeaderMapper.selectOne(qw);
                if (esLeader != null){
                    //有跟新
                    esLeader.setDesignerid(lea.getDesignerid());
                    esLeader.setDesignername(user.getUsername());
                    result = eSLeaderMapper.updateById(esLeader);
                }else {
                    //没有添加
                    lea.setDesignername(user.getUsername());
                    lea.setPkid(null);
                    result = eSLeaderMapper.insert(lea);
                }
                if(result != 1){
                    throw new BusinessException("负责人添加失败，请检查");
                }
            }
        }
//        if (result == 1){
//            //专业
//            List<Subjects> subjectList = subjectsMapper.selectsub();
//
//            qw = new QueryWrapper<>();
//            qw.eq("subprojectid",subprojectid);
//            List<ESLeader> esLeaderList = eSLeaderMapper.selectList(qw);
//
//            for ( Subjects sub : subjectList) {
//                ESLeader leader = new ESLeader();
//                leader.setSubjectid(sub.getPkid());
//                leader.setSubjectname(sub.getSubjectname());
//                for (ESLeader led : esLeaderList) {
//                    if (led.getSubjectid().equals(sub.getPkid())) {
//                        leader.setDesignerid(led.getDesignerid());
//                    }
//                }
//                list.add(leader);
//            }
//
//        }
        return list;
    }

    /**
    * 删除子工程各专业负责人信息
    *
    * @param eSLeaderId
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int delete(String eSLeaderId) {
        return eSLeaderMapper.deleteById(eSLeaderId);
    }

    /**
    * 更新子工程各专业负责人信息
    *
    * @param eSLeader
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int update(ESLeader eSLeader) {
        return eSLeaderMapper.updateById(eSLeader);
    }

    /**
    * 全部查询
    *
    * @param eSLeader
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.ESLeader>
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<ESLeader> list(ESLeader eSLeader) {

        QueryWrapper<ESLeader> queryWrapper = new QueryWrapper<>();

        return eSLeaderMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<ESLeader> eSLeaderIPage = new Page<>();
        eSLeaderIPage.setCurrent(current);
        eSLeaderIPage.setSize(size);

        QueryWrapper<ESLeader> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return eSLeaderMapper.selectPage(eSLeaderIPage, queryWrapper);
    }


}
