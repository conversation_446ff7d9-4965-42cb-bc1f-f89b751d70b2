package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FileRoot {

    @ApiModelProperty(value = "文件路径")
    private String fileurl;
    @ApiModelProperty(value = "唯一编号")
    private String pkid;
    @ApiModelProperty(value = "文件名称")
    private String filename;
    @ApiModelProperty(value = "文件对应质检规则")
    private String qualityType;
    @ApiModelProperty(value = "iserverUrl服务地址")
    private String iserverUrl;
    @ApiModelProperty(value = "文件ID或NAS路径")
    private String fileidOrnasPath;
    @ApiModelProperty(value = "生成缓存地址")
    private String cacheUrl;





}
