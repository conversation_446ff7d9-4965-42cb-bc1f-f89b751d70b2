package com.supermap.pipedesign.pipechina.engineering.entity.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Data
public class TemplatesVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "项目ID")
    private String projectid;

    @ApiModelProperty(value = "模板名称")
    private String templatename;

    @ApiModelProperty(value = "模板类型(枚举值。1:图纸模板;2:文档模板)")
    private String templatetype;

    @ApiModelProperty(value = "创建人")
    private String createuserid;

    @ApiModelProperty(value = "创建人")
    private String username;

    @ApiModelProperty(value = "创建时间")
    private Timestamp createtime;

    @ApiModelProperty(value = "审核状态(枚举值)")
    private Integer auditstate;

    @ApiModelProperty(value = "模板描述信息")
    private String templatedesc;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "模板具体类型")
    private String type;


}
