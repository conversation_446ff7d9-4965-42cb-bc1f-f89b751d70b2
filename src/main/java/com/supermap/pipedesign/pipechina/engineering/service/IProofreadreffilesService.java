package com.supermap.pipedesign.pipechina.engineering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.engineering.entity.Proofreadreffiles;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.Proofreadsheets;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 校审评审关联文件表(存储每个校审单关联的文件，以及每个文件对应的批注数据集信息) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IProofreadreffilesService extends IService<Proofreadreffiles> {

 /**
 * 添加校审评审关联文件表(存储每个校审单关联的文件，以及每个文件对应的批注数据集信息)信息
 *
 * @param proofreadreffiles
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Proofreadreffiles proofreadreffiles);

 /**
 * 删除校审评审关联文件表(存储每个校审单关联的文件，以及每个文件对应的批注数据集信息)信息
 *
 * @param proofreadreffilesId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String proofreadreffilesId);

 /**
 * 更新校审评审关联文件表(存储每个校审单关联的文件，以及每个文件对应的批注数据集信息)信息
 *
 * @param proofreadreffiles
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Proofreadreffiles proofreadreffiles);

 /**
 * 全部查询
 *
 * @param proofreadreffiles
 * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Proofreadreffiles>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Proofreadreffiles> list(Proofreadreffiles proofreadreffiles);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);

 /**
  * 项目文件列表
  * <AUTHOR>
  * @Date 2023/03/22
  * @param proofreadsheets
  * @return
  */
 List<Proofreadreffiles> selWorkbagFileList(Proofreadsheets proofreadsheets);


 }
