package com.supermap.pipedesign.pipechina.engineering.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.supermap.pipedesign.pipechina.engineering.entity.Proofreadsheets;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.ProofreadsheetsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 校审评审单(定义校审评审任务清单	注意：校审不止针对文件，还需要针对模型、成果文件、规则等的校审。都需要考虑。) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface ProofreadsheetsMapper extends BaseMapper<Proofreadsheets> {

    @Select("select t.*, case when t.review_type = '1' then '设计成果校审' when t.review_type = '2' then '委托单校审' end as reviewtypename," +
            " e.engineering_name as projectname,s.sub_name as subprojectname,u.project_unit_name,f.function_area_name \n" +
            " from wbs_proofread_sheets t \n" +
            " left join wbs_engineering e on t.project_id = e.pkid \n" +
            " left join wbs_engineering_sub s on t.sub_project_id = s.pkid \n" +
            " left join wbs_e_s_unit u on t.design_id = u.pkid \n" +
            " left join wbs_e_s_u_functionarea f on t.design_sub_task_id = f.pkid ${ew.customSqlSegment}")
    IPage<ProofreadsheetsVo> pageList(IPage<ProofreadsheetsVo> page, @Param(Constants.WRAPPER) Wrapper<ProofreadsheetsVo> queryWrapper);


    @Select("select t.*, case when t.review_type = '1' then '设计成果校审' when t.review_type = '2' then '委托单校审' end as reviewtypename," +
            " e.engineering_name as projectname,s.sub_name as subprojectname,u.project_unit_name,f.function_area_name \n" +
            " from wbs_proofread_sheets t \n" +
            " left join wbs_engineering e on t.project_id = e.pkid \n" +
            " left join wbs_engineering_sub s on t.sub_project_id = s.pkid \n" +
            " left join wbs_e_s_unit u on t.design_id = u.pkid \n" +
            " left join wbs_e_s_u_functionarea f on t.design_sub_task_id = f.pkid ${ew.customSqlSegment}")
    List<ProofreadsheetsVo> list( @Param(Constants.WRAPPER) Wrapper<ProofreadsheetsVo> queryWrapper);


    @Select("SELECT\n" +
            "s.proofread_user_id,\n" +
            "s.review_userid,\n" +
            "s.approved_user_id,\n" +
            "s.examination_userid\n" +
            "FROM\n" +
            "\twbs_proofread_sheets AS s,\n" +
            "\twbs_designsub_task AS T \n" +
            "WHERE\n" +
            "\ts.design_sub_task_id = T.pkid \n" +
            "\tAND t.design_id= #{designId}\n" +
            "\tAND T.subject_name = #{subjectName}\n" +
            "\tLIMIT 1")
    Proofreadsheets selectProofreadSheetsByDesignIdAndSubjectName(@Param("designId") String designId, @Param("subjectName") String subjectName);

}
