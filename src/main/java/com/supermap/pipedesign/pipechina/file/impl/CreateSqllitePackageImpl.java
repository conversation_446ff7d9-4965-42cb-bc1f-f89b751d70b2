package com.supermap.pipedesign.pipechina.file.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sun.org.apache.xpath.internal.operations.Bool;
import com.supermap.base.entity.Result;
import com.supermap.config.exception.BusinessException;
import com.supermap.data.*;
import com.supermap.data.FieldInfo;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.TemplatefilesMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.TemplatesMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.MdPartsPic;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.Templatefiles;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.Templates;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IMdPartsPicService;
import com.supermap.pipedesign.pipechina.engineering.dao.*;
import com.supermap.pipedesign.pipechina.engineering.entity.*;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DesignsubTaskVersionEx;
import com.supermap.pipedesign.pipechina.engineering.service.IDesignDbService;
import com.supermap.pipedesign.pipechina.engineering.service.IMeasureSurveyDataManageService;
import com.supermap.pipedesign.pipechina.engineering.service.ISmallVersionManageService;
import com.supermap.pipedesign.pipechina.file.service.ICreateSqllitePackageService;
import com.supermap.pipedesign.pipechina.metadata.dao.MdEntitytypeStylesPartsPicMapper;
import com.supermap.pipedesign.pipechina.metadata.dao.MdEntitytypeThemeStylesPartsPicMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.*;
import com.supermap.pipedesign.pipechina.metadata.service.*;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawing;
import com.supermap.pipedesign.pipechina.rules.service.IGeneralDrawingService;
import com.supermap.pipedesign.pipechina.sqlite.service.IProjectToSqliteService;
import com.supermap.pipedesign.pipechina.utils.ExcelUtils;
import com.supermap.realspace.Camera;
import com.supermap.realspace.Scene;
import com.supermap.tools.base.FileGenerateUtils;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.base.RedisCommonUtil;
import com.supermap.tools.file.FileUtils;
import com.supermap.tools.file.PathUtils;
/*import org.apache.logging.log4j.Logger;*/
import com.supermap.tools.map.OpenDB;
import com.supermap.tools.sqlite.SqliteUtils;
import com.supermap.tools.sqlite.vo.FiledInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service(value = "CreateSqllitePackageImpl")
public class CreateSqllitePackageImpl
        implements ICreateSqllitePackageService {

    @Autowired
    private RedisCommonUtil _redisCommonUtil;

    @Resource(name = "ProjectToSqliteImpl")
    private IProjectToSqliteService projectToSqliteImpl;

    @Resource(name = "WorkspaceSeedsServiceImpl")
    private IWorkspaceSeedsService workspaceSeedsService;

    @Resource(name = "DesignDbImpl")
    private IDesignDbService designDbService;

    @Resource(name = "EntitytypesImpl")
    private IEntitytypesService entitytypesService;

    @Resource(name = "SmallVersionManageImpl")
    private ISmallVersionManageService versionService;

    @Resource(name = "MeasureSurveyDataManageServiceImpl")
    private IMeasureSurveyDataManageService measureSurveyDataManageService;

    @Resource(name = "DatasetinfoImpl")
    private IDatasetinfoService iDatasetinfoService;

    @Resource(name = "DatasetfieldImpl")
    private IDatasetfieldService iDatasetfieldService;

    //风格
    @Resource(name = "EntitytypestylesImpl")
    private IEntitytypestylesService entitytypestylesService;
    //专题
    @Resource(name = "EntitytypethemestylesImpl")
    private IEntitytypethemestylesService entitytypethemestylesService;

    @Resource(name = "GeneralDrawingImpl")
    private IGeneralDrawingService generalDrawingService;

    @Autowired
    private EngineeringMapper engineeringMapper;

    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private DesignMapper designMapper;

    @Autowired
    private MdEntitytypeStylesPartsPicMapper mdEntitytypeStylesPartsPicMapper;

    @Autowired
    private MdEntitytypeThemeStylesPartsPicMapper mdEntitytypeThemeStylesPartsPicMapper;

    //@Autowired
    //private DesignsubTaskMapper designsubTaskMapper;

    @Autowired
    private DesignsubTaskVersionMapper designsubTaskVersionMapper;

    @Autowired
    private DesignsubTaskMapper designsubTaskMapper;

    @Autowired
    private TemplatesMapper templatesMapper;

    @Autowired
    private TemplatefilesMapper templatefilesMapper;
    @Autowired
    private WorkbagFileuploadrecordMapper workbagFileuploadrecordMapper;

    @Value("${workseed.generationmethod}")
    private String strWorkspaceGenerationMethod;

    //最大可启动的工作空间生成器进程数
    @Value("${workseed.maxparallelprocesscount}")
    private int iMaxParallelProcessCount;

    @Value("${download.data.puretabulardatasetnames}")
    private String strPureTabluarDatasetNames;
    /**
     * @描述 1:复制sqllite数据 2:初始化种子文件 3:打zip包
     * @日期 2023/03/24 16:00
     * @作者 eomer
     **/
    @Async
    @Override
    public String getProjectDB(String projectid,String strExecuteTime) {
        String strKey = projectid + strExecuteTime;
        try {
            List<String> resultList = new ArrayList<>();
            long lBeginTime1 = System.currentTimeMillis();
            long lBeginTime = System.currentTimeMillis();
            //创建数据目录结构并复制文件
            String filePath = PathUtils.initDirectory(projectid);
            _redisCommonUtil.setKey(strKey, "正在复制项目数据");
            lBeginTime = System.currentTimeMillis();
            projectToSqliteImpl.copyTableByProjectId(projectid, filePath);
            //resultList.addAll(projectToSqliteImpl.copyTableByProjectId(projectid, filePath));
            log.info("复制项目数据耗时："+(System.currentTimeMillis()-lBeginTime)/1000);
            lBeginTime = System.currentTimeMillis();
            _redisCommonUtil.setKey(strKey, "项目数据复制完成");
            _redisCommonUtil.setKey(strKey, "正在复制dwg");
            copyDwgByProjectId(projectid, filePath);
            //resultList.addAll(copyDwgByProjectId(projectid, filePath));
            log.info("dwg复制完成耗时："+(System.currentTimeMillis()-lBeginTime)/1000);
            lBeginTime = System.currentTimeMillis();
            _redisCommonUtil.setKey(strKey, "dwg复制完成");
            _redisCommonUtil.setKey(strKey, "正在复制模板");
            copyTemplate(filePath, projectid);
            //resultList.addAll(copyTemplate(filePath, projectid));
            log.info("模板复制完成耗时："+(System.currentTimeMillis()-lBeginTime)/1000);
            lBeginTime = System.currentTimeMillis();
            _redisCommonUtil.setKey(strKey, "模板复制完成");
            _redisCommonUtil.setKey(strKey, "正在添加版本信息");
            Map<String, Object> map = new HashMap<>();
            Engineering engineering = engineeringMapper.selectById(projectid);
            map.put("项目名称", engineering.getProjectname());
            map.put("项目ID", engineering.getPkid());
            if (JavaUtils.isNotEmtryOrNull(engineering.getEngineeringdesiplinetype())) {
                Dictionary dictionary = dictionaryMapper.selectById(engineering.getEngineeringdesiplinetype());
                if (dictionary != null) {
                    map.put("项目类型", dictionary.getDictionaryname());
                }
            } else {
                map.put("项目类型", "");
            }
            map.put("项目经理", engineering.getProjectmanager());
            map.put("下载时间", JavaUtils.dateTimeToString(new Date()));
            DesignDb designDb = designDbService.getVersion("auth_entities", projectid);
            if (designDb != null && designDb.getDbVersion()!=null) {
                map.put("version", designDb.getDbVersion().toString());
            }
            FileUtils.setInfoToVersion(filePath + File.separator + "version.json", map);
            log.info("添加版本信息完成耗时："+(System.currentTimeMillis()-lBeginTime)/1000);
            lBeginTime = System.currentTimeMillis();
            _redisCommonUtil.setKey(strKey, "添加版本信息完成");
            _redisCommonUtil.setKey(strKey, "正在压缩文件");
            String zipFile = PathUtils.getPackagePath() + File.separator + strKey + ".zip";
            OutputStream fileOutputStream = new FileOutputStream(zipFile);
            FileGenerateUtils.toZip(filePath, fileOutputStream, true);
            PathUtils.deleteFolder(filePath);   //压缩完毕后删除被压缩的文件夹
            log.info("Design文件夹文件创建完成耗时："+(System.currentTimeMillis()-lBeginTime1)/1000);
            if(resultList!=null&&resultList.size()>0){
                _redisCommonUtil.setKey(strKey, String.join(System.lineSeparator(),resultList));
                return "";
            }else{
                _redisCommonUtil.setKey(strKey, "文件压缩完毕，开始下载");
                return zipFile;
            }
        } catch (Exception exception) {
            log.error("创建design文件夹下的文件，异常。",exception);
            exception.printStackTrace();
        }
        return "";
    }

    @Override
    public String getTrustDB(String projectid) {
        try {
            String filePath = PathUtils.initTrustDirectory(projectid);
            String dbPath = filePath + File.separator + "trust.udbx";
            try {
//                System.out.println("质检数据。。。");
                versionService.copyTrustByVersion(projectid, "", dbPath);
//                System.out.println("写入版本数据完成");
            } catch (Exception e) {
                log.info("写入版本失败！");
                e.printStackTrace();
            }
            return dbPath;
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return "";
    }

    @Async
    @Override
    public String getDesignDB(String projectid, String desginId,String strExecuteTime) {
        String strTaskKey = desginId.trim() + "-" + strExecuteTime;

        StringBuilder resultSB = new StringBuilder();
        try {
            log.info("下载设计段数据，开始，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId);
            long start1 = System.currentTimeMillis();
            long start = System.currentTimeMillis();
            String filePath = PathUtils.initDirectory(projectid, desginId);

            Design design = designMapper.selectById(desginId);

            //第一次生成173s，EntitiesAchivments.udbx耗时166
            //已经生成8s
            resultSB.append(workspaceSeedsService.createProjectDB(projectid,desginId,strTaskKey,filePath));
            //workspaceSeedsService.createProjectDB(projectid,desginId,strTaskKey,filePath);

            log.info("createProjectDB,耗时:" + (System.currentTimeMillis() - start) / 1000.0 + "s");
            start = System.currentTimeMillis();

            _redisCommonUtil.setKey(strTaskKey, "正在写入勘察测量数据");
            String strSurveyDBFilePath = measureSurveyDataManageService.fetchMeasureSurveyDatas(projectid, desginId, filePath + File.separator + "surveymeasurement.udbx", 0);
            if(strSurveyDBFilePath.indexOf("exception")>-1) {
                FileUtils.copyFile(PathUtils.getDesignPath() + File.separator + "surveymeasurement.db", filePath, true);
            }
            com.alibaba.fastjson.JSONObject jsonSurveyResult = com.alibaba.fastjson.JSONObject.parseObject(strSurveyDBFilePath);
            if(jsonSurveyResult.containsKey("result")) {
                String strFilePath = jsonSurveyResult.getString("result");
                String strDBPath = strFilePath.substring(0, strFilePath.lastIndexOf(".udbx")) + ".db";
                Path udbxPath = Paths.get(strFilePath);
                Path dbPath = Paths.get(strDBPath);
                try{
//                    Files.move(udbxPath, dbPath, StandardCopyOption.REPLACE_EXISTING);
                    Files.createFile(dbPath); // 创建同名db属性库
                }catch (Exception e){
                    log.error("写入勘察测绘数据，异常，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId,e);
                }

                Workspace workspace = new Workspace();
                Datasource datasource = OpenDB.openUDBX(workspace, strFilePath);
                Datasets datasets = datasource.getDatasets();

                for (int k = 0; k < datasets.getCount(); k++) {
                    DatasetVector datasetVector = (DatasetVector) datasets.get(k);
                    String tableName = datasetVector.getName();
                    log.info("勘察测量数据正在插入：" + tableName);
                    // 建表
                    StringBuilder createTableQuery = new StringBuilder("CREATE TABLE IF NOT EXISTS " + tableName + " (");
                    FieldInfos fieldInfos = datasetVector.getFieldInfos();
                    List<String> fieldNameList = new ArrayList<>(0);
                    // 建字段
                    for (int f = 0; f < fieldInfos.getCount(); f++) {
                        com.supermap.data.FieldInfo fieldInfo = fieldInfos.get(f);

                        createTableQuery.append(fieldInfo.getName());
                        if (fieldInfo.getType().equals(FieldType.TEXT) || fieldInfo.getType().equals(FieldType.DATETIME) || fieldInfo.getType().equals(FieldType.DATE) || fieldInfo.getType().equals(FieldType.WTEXT) || fieldInfo.getType().equals(FieldType.CHAR))
                            createTableQuery.append(" text,");
                        else if (fieldInfo.getType().equals(FieldType.INT16) || fieldInfo.getType().equals(FieldType.INT32) || fieldInfo.getType().equals(FieldType.INT64))
                            createTableQuery.append(" integer,");
                        else if (fieldInfo.getType().equals(FieldType.SINGLE) || fieldInfo.getType().equals(FieldType.DOUBLE))
                            createTableQuery.append(" real,");
                        else if (fieldInfo.getType().equals(FieldType.LONGBINARY))
                            createTableQuery.append(" blob,");
                        fieldNameList.add(fieldInfo.getName());
                    }
                    createTableQuery.deleteCharAt(createTableQuery.length() - 1);
                    createTableQuery.append(")");
                    SqliteUtils.createTable(strDBPath, createTableQuery.toString());

                    // 插入数据
                    Recordset recordset = datasetVector.getRecordset(false, CursorType.STATIC);
                    if (recordset.getRecordCount() > 0) {

                        log.info("写入勘察测绘数据，总数{}，taskkey:{}，projectid:{}，desginid:{}",recordset.getRecordCount(),strTaskKey,projectid,desginId);
                        recordset.moveFirst();
                        List<String> sqlList = new ArrayList<>();
                        while (!recordset.isEOF()) {
                            StringBuilder names = new StringBuilder();
                            StringBuilder values = new StringBuilder();
                            String executeSql = "INSERT INTO " + tableName;
                            for (int j = 0; j < recordset.getFieldInfos().getCount(); j++) {
                                FieldInfo fieldInfo = recordset.getFieldInfos().get(j);
                                names.append(fieldInfo.getName()).append(",");
                                FieldType fieldType = fieldInfo.getType();
                                if (fieldType.equals(FieldType.TEXT) || fieldType.equals(FieldType.DATETIME) || fieldType.equals(FieldType.DATE) || fieldType.equals(FieldType.WTEXT) || fieldType.equals(FieldType.CHAR)) {
                                    values.append("'").append(recordset.getFieldValue(j)).append("'").append(",");
                                } else if (fieldType.equals(FieldType.LONGBINARY)) { // 处理属性表中的二进制字段,转换成字符串
                                    byte[] blob = (byte[])recordset.getFieldValue(j);
                                    if (blob != null) {
                                        values.append("'").append(new String(blob,StandardCharsets.UTF_8)).append("'").append(",");
                                    } else {
                                        values.append(recordset.getFieldValue(j)).append(",");
                                    }
                                } else {
                                    values.append(recordset.getFieldValue(j)).append(",");
                                }
                            }
                            if (names.length() > 0 && values.length() > 0) {
                                names = new StringBuilder(names.substring(0, names.length() - 1));
                                values = new StringBuilder(values.substring(0, values.length() - 1));

                                try {
                                    executeSql = executeSql + " (" + names + ") VALUES (" + values + ");";
                                    sqlList.add(executeSql);
                                    if (sqlList.size() == 1000) {
                                        SqliteUtils.batchInsert(strDBPath, tableName, sqlList);
                                        log.info("写入勘察测绘数据，本次入库数{}，taskkey:{}，projectid:{}，desginid:{}",sqlList.size(),strTaskKey,projectid,desginId);
                                        sqlList = new ArrayList<>();

                                    }
                                    //SqliteUtils.executeUpdate(strDBPath, executeSql);
                                } catch (Exception e) {
                                    log.error("写入勘察测绘数据，sqlList.add异常，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId,e);
                                }
                            }
                            recordset.moveNext();
                        }
                        if(sqlList.size()>0){
                            SqliteUtils.batchInsert(strDBPath,tableName,sqlList);
                            log.info("写入勘察测绘数据，本次入库数{}，taskkey:{}，projectid:{}，desginid:{}",sqlList.size(),strTaskKey,projectid,desginId);

                        }
                    }
                    int count = SqliteUtils.queryCount(strDBPath,String.format("select count(*) from %s",tableName));
                    if(count!=recordset.getRecordCount()){
                        resultSB.append(String.format("\n勘察测绘数据%s未完全写入,源数据共%s，目标数据共%s",tableName,recordset.getRecordCount(),count));
                    }
                    recordset.dispose();
                    recordset.close();
                }
                datasource.close();
                workspace.close();
            }
            log.info("写入勘察测量数据完成");
            //FileUtils.copyFile(PathUtils.getDesignPath() + File.separator + "EntitiesAchivments.udbx", filePath, true);

            log.info("写入勘察测量数据完成,耗时:" + (System.currentTimeMillis() - start) / 1000.0 + "s");
            start = System.currentTimeMillis();
            //复制版本数据
            _redisCommonUtil.setKey(strTaskKey, "正在写入版本数据");
            String dbPath = filePath + File.separator + "EntitiesAchivments.udbx";
            String dbPath2 = filePath + File.separator + "tabular.udbx";

            /*QueryWrapper<DesignsubTask> queryMapperDesignTask = new QueryWrapper<>();
            queryMapperDesignTask.eq("project_id", projectid);
            queryMapperDesignTask.eq("design_id", desginId);
            List<DesignsubTask> lstTasks = designsubTaskMapper.selectList(queryMapperDesignTask);*/
            Map<String, String> mapTaskVersions = new HashMap<>();
            QueryWrapper<DesignsubTaskVersion> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("t.project_id", projectid);
            queryWrapper.eq("t.design_id", desginId);
            queryWrapper.orderByDesc("create_date");
            List<DesignsubTaskVersionEx> lstVersions = designsubTaskVersionMapper.getSmallVersions(queryWrapper);

            String strSmallVersion = "";
            if (null != lstVersions && lstVersions.size() > 0) {
                strSmallVersion = lstVersions.get(0).getVersion();
            }

            Double dblLeft = 0d, dblRight = 0d, dblTop = 0d, dblBottom = 0d;
            int iEPSGCode = 3857;
            try {
                String strCopiedResult = versionService.copyDesignByVersion(projectid, desginId,strSmallVersion, dbPath, dbPath2, strTaskKey);
                int iCopiedResult = 0;
                try{
                    com.alibaba.fastjson.JSONObject jsonObjectResult = com.alibaba.fastjson.JSONObject.parseObject(strCopiedResult);
                    iCopiedResult = jsonObjectResult.getInteger("CopiedCount");
                    if(jsonObjectResult.containsKey("message")){
                        resultSB.append("\n"+jsonObjectResult.getString("message"));
                    }
                }catch (Exception e){}
                if (iCopiedResult < 1) {
                    _redisCommonUtil.setKey(strTaskKey, "写入版本数据失败！原因：打开生产库失败！");
                    log.error("为任务" + strTaskKey + "写入版本数据失败！原因：打开生产库失败！");
                    return "error";
                }/* else if (iCopiedDatasetCount == 0) {
                        _redisCommonUtil.setKey(strKey, "写入版本数据失败：原因：没有任何版本数据！");
                        logger.error("为任务"+strKey+"写入版本数据失败！原因：没有任何版本数据！");
                        return "error";
                    } else*/
                _redisCommonUtil.setKey(strTaskKey, "写入版本数据完成");
                //判断tabular.xlsx中对应的数据集有没有版本记录，如果没有的话，就从tabulars_initial_datas.xlsx中读取对应的初始值放入tabular.udbx对应的数据集中
                strCopiedResult = insertTabularDataInitialDatas(dbPath2, dbPath, strTaskKey);

                iCopiedResult = 0;
                com.alibaba.fastjson.JSONObject jsonObjectResult = com.alibaba.fastjson.JSONObject.parseObject(strCopiedResult);
                iCopiedResult = jsonObjectResult.getInteger("CopiedCount");
                if(jsonObjectResult.containsKey("message")){
                    resultSB.append("\n"+jsonObjectResult.getString("message"));
                }
            } catch (Exception e) {
                log.error("写入版本数据，异常，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId,e);
                _redisCommonUtil.setKeyAndTime(strTaskKey, "写入版本数据失败：原因：" + e.getMessage(), 10, TimeUnit.MINUTES);
                e.printStackTrace();
            }

            //查询设计段
            Map<String, Object> map = new HashMap<>();
            if(null!=design) {
                map.put("设计段名称", design.getDesignname());
                map.put("设计段ID", desginId);
                if (design.getDesignType() == 0) {
                    map.put("设计段类型", "全段");
                } else if (design.getDesignType() == 1) {
                    map.put("设计段类型", "一般段");
                } else if (design.getDesignType() == 2) {
                    map.put("设计段类型", "大中型穿跨越");
                } else {
                    map.put("设计段类型", "");
                }
                map.put("起始桩号", design.getStakepointnostart());
                map.put("终点桩号", design.getStakepointnoend());
            }
            map.put("下载时间", JavaUtils.dateTimeToString(new Date()));
            //获取各专业最新小版本信息
            Map<String, String> mapSubjectsVersions = new HashMap<>();
            if(null!=design) {
                QueryWrapper<DesignsubTask> queryWrapperSubTask = new QueryWrapper<>();
                queryWrapperSubTask.eq("design_id", design.getPkid());
                List<DesignsubTask> lstDesignSubTask = designsubTaskMapper.selectList(queryWrapperSubTask);
                if (null != lstDesignSubTask && lstDesignSubTask.size() > 0) {
                    for (int i = 0; i < lstDesignSubTask.size(); i++) {
                        String strTaskId = lstDesignSubTask.get(i).getPkid();
                        String strSubjectId = lstDesignSubTask.get(i).getSubjectcode();
                        QueryWrapper<DesignsubTaskVersion> queryWrapperVersion = new QueryWrapper<>();
                        queryWrapperVersion.eq("design_sub_task_id", strTaskId);
                        queryWrapperVersion.orderByDesc("create_date");
                        List<DesignsubTaskVersion> lstVersion = designsubTaskVersionMapper.selectList(queryWrapperVersion);
                        if (null != lstVersion && lstVersion.size() > 0) {
                            mapSubjectsVersions.put(strSubjectId, lstVersion.get(0).getVersion());
                        } else {
                            // 未上传过版本的任务添加一个默认的超小版本作为专业的初始化版本
                            mapSubjectsVersions.put(strSubjectId, "v-0.0.0.01");
                        }
                    }
                }
                map.put("subjects_versions", mapSubjectsVersions);
            }
            log.info("写入版本-获取各专业最新小版本信息,耗时:" + (System.currentTimeMillis() - start) / 1000.0 + "s");
            start = System.currentTimeMillis();

            //从db-design目录下获取db和udbx的创建时间，也就是实体结构的版本信息
            String strDBVersion = "";
            String strDBVersionPath = PathUtils.getDesignPath() + File.separator + "version.json";
            File file = new File(strDBVersionPath);
            if(file.exists()) {
                String strJSONVersion = org.apache.commons.io.FileUtils.readFileToString(file, Charset.forName("UTF-8"));
                JSONObject jsonObjVersion = new JSONObject(strJSONVersion);
                if(jsonObjVersion.getStr("version")!=null)
                    strDBVersion = jsonObjVersion.getStr("version");
            }
            map.put("db-version", strDBVersion);

            log.info("写入版本-db-version,耗时:" + (System.currentTimeMillis() - start) / 1000.0 + "s");
            start = System.currentTimeMillis();

            String strDBUpdateVersion = "";
            String strDBFilePath = PathUtils.getDesignPath() + File.separator + projectid + File.separator + "entitiesachivments.db";
            file = new File(strDBFilePath);
            if(file.exists()){
                long lastModified = file.lastModified();
                Date date = new Date(lastModified);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = sdf.format(date);
                map.put("db-update-time", formattedDate);
            }
            String strUDBXFilePath = PathUtils.getDesignPath() + File.separator + projectid + File.separator + "EntitiesAchivments.udbx";
            file = new File(strUDBXFilePath);
            if(file.exists()){
                long lastModified = file.lastModified();
                Date date = new Date(lastModified);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = sdf.format(date);
                map.put("udbx-update-time", formattedDate);
            }
            String strSXWUFilePath = PathUtils.getDesignPath() + File.separator + projectid + File.separator + "EntityDesign.sxwu";
            file = new File(strSXWUFilePath);
            if(file.exists()){
                long lastModified = file.lastModified();
                Date date = new Date(lastModified);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = sdf.format(date);
                map.put("workspace-update-time", formattedDate);
            }

            String strSRID = "4490";
            if (design!=null&&design.getSrid() != null && design.getSrid().trim().length() > 0) {
                strSRID = design.getSrid();
            }
            // 添加数据版本信息
            DesignDb designDb = designDbService.getVersion("auth_entities", projectid, desginId);
            if (designDb != null) {
                map.put("data-version", designDb.getDbVersion().toString());
            }

            FileUtils.setInfoToVersion(filePath + File.separator + "version.json", map);

            log.info("写入版本-project,耗时:" + (System.currentTimeMillis() - start) / 1000.0 + "s");
            start = System.currentTimeMillis();

            log.info("写入版本,耗时:" + (System.currentTimeMillis() - start) / 1000.0 + "s");
            start = System.currentTimeMillis();

            PrjCoordSys prjCoordSys = new PrjCoordSys();
            if (null != strSRID && strSRID.equals("900913")) {
                try {
//                    prjCoordSys.fromFile(PathUtils.getDesignPath() + File.separator + "TDTUserDefineCoordSystem.xml", PrjFileType.SUPERMAP);
                    prjCoordSys = new PrjCoordSys(3857);
                } catch (Exception e) {
                    log.error("为地图设置天地图坐标系，异常，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId,e);
//                    strMsg = "为地图设置天地图坐标系失败！";
//                    _redisCommonUtil.setKey(strKey, strMsg);
                    return null;
                }
            } else if (null != strSRID && strSRID.trim().length() > 0) {
                try {
                    prjCoordSys = new PrjCoordSys(Integer.parseInt(strSRID));
                } catch (Exception e) {
                    log.error("构造EPSGCode为{}的坐标系对象失败，异常，请检查项目库WBS_Design表的SRID字段值。taskkey:{}，projectid:{}，desginid:{}",strSRID,strTaskKey,projectid,desginId,e);
                    return null;
                }
            }
            if (prjCoordSys == null) {
                log.error("构造EPSGCode为{}的坐标系对象失败，prjCoordSys为空，请检查项目库WBS_Design表的SRID字段值。taskkey:{}，projectid:{}，desginid:{}",strSRID,strTaskKey,projectid,desginId);
                return null;
            }

            //通过地图投影为UDBX数据源设置投影
            Workspace workspace = new Workspace();
            WorkspaceConnectionInfo workspaceConnectionInfo = new WorkspaceConnectionInfo();
            workspaceConnectionInfo.setServer(filePath + File.separator + "EntityDesign.sxwu");
            workspaceConnectionInfo.setType(WorkspaceType.SXWU);
            workspace.open(workspaceConnectionInfo);
            if (workspace.getDatasources().getCount() > 0) {
                com.supermap.mapping.Map mapEntity = new com.supermap.mapping.Map(workspace);
                mapEntity.fromXML(workspace.getMaps().getMapXML(0));
                mapEntity.setPrjCoordSys(prjCoordSys);
                workspace.getMaps().setMapXML(0, mapEntity.toXML());
                workspace.getDatasources().get(0).setPrjCoordSys(prjCoordSys);
                workspace.getDatasources().get(0).refresh();
                workspace.save();
            }
            workspace.close();
            //工作空间生成完毕之后对工作空间文件进行备份，从而在设计端破坏了工作空间后可以有办法恢复
            FileUtils.copyFile(filePath + File.separator + "EntityDesign.sxwu", filePath + File.separator + "EntityDesign_bak.sxwu", true);
            FileUtils.copyFile(filePath + File.separator + "entitiesachivments.db", filePath + File.separator + "entitiesachivments_version.db", true);
            FileUtils.copyFile(filePath + File.separator + "EntitiesAchivments.udbx", filePath + File.separator + "EntitiesAchivments_version.udbx", true);

            _redisCommonUtil.setKey(strTaskKey, "正在压缩文件");
            String zipFile = PathUtils.getPackagePath() + File.separator + strTaskKey + ".zip";
            FileGenerateUtils.toZip(filePath, new FileOutputStream(zipFile));

            PathUtils.deleteFolder(filePath);   //压缩完毕后删除被压缩的文件夹

            log.info("项目数据完成,耗时:" + (System.currentTimeMillis() - start1) / 1000.0 + "s");
            if(resultSB.toString().trim().replace("\n", "").length()>0){
                _redisCommonUtil.setKeyAndTime(strTaskKey, resultSB.toString(), 10, TimeUnit.MINUTES);
            }else {
                _redisCommonUtil.setKeyAndTime(strTaskKey, "文件压缩完毕，开始下载", 10, TimeUnit.MINUTES);
            }
            return zipFile;
        } catch (Exception exception) {
            log.error("下载设计段数据，异常，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId,exception);
        }
        return "";
    }

    @Override
    public String getDesignDBProcess(String key){
        return _redisCommonUtil.getKey(key);
    }

    /**
     * <p>获取指定版本数据</p>
     *
     * <AUTHOR>
     * @version V1.0.0
     * @date 2023/4/16 23:24
     */
    @Override
    public String getVersionByNo(String projectid, String desginTaskId, String version) {
        try {
            String filePath = PathUtils.initDirectoryVersion(projectid, desginTaskId, version);
            String dbPath = filePath + File.separator + "version.udbx";
            try {
                log.info("复制版本数据。。。");
                versionService.copyDesignBySmallVersion(projectid, desginTaskId, version, dbPath);
                log.info("写入版本数据完成");
            } catch (Exception e) {
                log.info("版本失败！");
            }
            return dbPath;
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return "";
    }

    /**
     * @描述 复制模板
     * @日期 2023/04/11 15:11
     * @作者 eomer
     **/
    private List<String> copyTemplate(String filePath,String projectid) throws IOException {
        String rootPath = System.getProperty("user.dir");
        List<String> resultList = new ArrayList<>();
        log.info("项目模板下载路径：---" + rootPath);
        String tarPath = filePath + File.separator + "template";
        // 目录初始化
        PathUtils.initPath(tarPath);
        //
        String srcPath = PathUtils.getTemplatePath();
        //查询模板文档模板
        List<String> list = templatesMapper.selWorldTemplate(projectid);
        if (!JavaUtils.isEmtryOrNull(list)) {
            //从模板文件关联表查文件表id
            QueryWrapper<Templatefiles> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_id", projectid);
            queryWrapper.in("template_id", list);
            List<String> fileIdList = templatefilesMapper.selFileIdByTemplate(queryWrapper);
            if (!JavaUtils.isEmtryOrNull(fileIdList)) {
                QueryWrapper<WorkbagFileuploadrecord> fileQuery = new QueryWrapper<>();
                fileQuery.in("pkid", fileIdList).or().in("fileid", fileIdList);
                List<WorkbagFileuploadrecord> fileList = workbagFileuploadrecordMapper.selectList(fileQuery);
                if (!JavaUtils.isEmtryOrNull(fileList)) {
                    for (WorkbagFileuploadrecord rec : fileList) {
                        if (JavaUtils.isNotEmtryOrNull(rec.getFileurl())) {
                            FileGenerateUtils.copyFiles(rootPath + File.separator + rec.getFileurl(), tarPath);
                            String checkResult = FileUtils.checkFile(rootPath + File.separator + rec.getFileurl(), tarPath);
                            if (StringUtils.isNotEmpty(checkResult)) {
                                resultList.add(checkResult);
                            }
                        }
                    }
                }
            }
        }
//        FileGenerateUtils.copyDirDwg(srcPath, tarPath);


        //查询项目下的模板id
        List<String> tempList = templatesMapper.selTemplate(projectid);
        if (!tempList.isEmpty()) {
            for (String tempId : tempList) {
                //模板数据
                String dwgPath = filePath + File.separator + "dwg";
                // 目录初始化
                PathUtils.initPath(dwgPath);
                //根据项目id和模板id查文件id
                QueryWrapper<Templatefiles> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectid);
                queryWrapper.eq("template_id", tempId);
                List<String> fileIdList = templatefilesMapper.selFileIdByTemplate(queryWrapper);
                if (!fileIdList.isEmpty()) {
                    QueryWrapper<WorkbagFileuploadrecord> fileQuery = new QueryWrapper<>();
                    fileQuery.in("fileid", fileIdList);
                    List<WorkbagFileuploadrecord> fileList = workbagFileuploadrecordMapper.selectList(fileQuery);
                    if (!fileList.isEmpty()) {
                        for (WorkbagFileuploadrecord rec : fileList) {
                            if (JavaUtils.isNotEmtryOrNull(rec.getFileurl())) {
                                FileGenerateUtils.copyFiles(rootPath + File.separator + rec.getFileurl(), dwgPath);
                                String checkResult = FileUtils.checkFile(rootPath + File.separator + rec.getFileurl(), tarPath);
                                if (StringUtils.isNotEmpty(checkResult)) {
                                    resultList.add(checkResult);
                                }
                            }
                        }
                    }
                }

            }
        }
        return resultList;
    }




    /**
     * @描述 复制dwg
     * @日期 2023/04/07 11:26
     * @作者 eomer
     **/
    private List<String> copyDwgByProjectId(String projectid, String filePath){
        String rootPath = System.getProperty("user.dir");
        List<String> resultList = new ArrayList<>();
        log.info("项目文件下载路径：---"+rootPath);
        String stylePath = filePath + File.separator + "style";//风格
        String themestylePath = filePath + File.separator + "theme";// 专题
        String partsPicPath = filePath + File.separator + "parts";// 贴图
        String drawingPath = filePath + File.separator + "general";// 通用图
        // 目录初始化
        PathUtils.initPath(stylePath);
        PathUtils.initPath(themestylePath);
        PathUtils.initPath(partsPicPath);
        PathUtils.initPath(drawingPath);

        // 查询项目下的实体
        List<Entitytypes> entitytypesList = entitytypesService.getListByProjectId(projectid);
        //风格
        QueryWrapper<Entitytypestyles> stylesQueryWrapper = new QueryWrapper<>();
        stylesQueryWrapper.eq("project_id",projectid);
        List<Entitytypestyles> stylesList = entitytypestylesService.list(stylesQueryWrapper);
        QueryWrapper<Entitytypethemestyles> themestylesQueryWrapper = new QueryWrapper<>();
        themestylesQueryWrapper.eq("project_id",projectid);
        List<Entitytypethemestyles> themestylesList = entitytypethemestylesService.list(themestylesQueryWrapper);
        //风格
        for (int i = 0; i < entitytypesList.size(); i++) {
            Entitytypes entitytypes = entitytypesList.get(i);
            //风格
            /*QueryWrapper<Entitytypestyles> stylesQueryWrapper = new QueryWrapper<>();
            stylesQueryWrapper.eq("entity_type_id",entitytypes.getPkid());
            stylesQueryWrapper.eq("project_id",projectid);
            List<Entitytypestyles> stylesList = entitytypestylesService.list(stylesQueryWrapper);*/
            //复制
            for (Entitytypestyles entitytypestyles : stylesList) {
                if (!entitytypestyles.getEntitytypeid().equalsIgnoreCase(entitytypes.getPkid())) {
                    continue;
                }
                if (JavaUtils.isNotEmtryOrNull(entitytypestyles.getCadcolumniconurl())) {
                    FileUtils.copyFile(rootPath + File.separator + entitytypestyles.getCadcolumniconurl(), stylePath, true);
                    String checkResult = FileUtils.checkFile(rootPath + File.separator + entitytypestyles.getCadcolumniconurl(), stylePath);
                    if(StringUtils.isNotEmpty(checkResult)){
                        resultList.add(checkResult);
                    }
                }
                if (JavaUtils.isNotEmtryOrNull(entitytypestyles.getCadcrosssectioniconurl())) {
                    FileUtils.copyFile(rootPath + File.separator + entitytypestyles.getCadcrosssectioniconurl(), stylePath, true);
                    String checkResult = FileUtils.checkFile(rootPath + File.separator + entitytypestyles.getCadcrosssectioniconurl(), stylePath);
                    if(StringUtils.isNotEmpty(checkResult)){
                        resultList.add(checkResult);
                    }
                }
                if (JavaUtils.isNotEmtryOrNull(entitytypestyles.getCadplanariconurl())) {
                    FileUtils.copyFile(rootPath + File.separator + entitytypestyles.getCadplanariconurl(), stylePath, true);
                    String checkResult = FileUtils.checkFile(rootPath + File.separator + entitytypestyles.getCadplanariconurl(), stylePath);
                    if(StringUtils.isNotEmpty(checkResult)){
                        resultList.add(checkResult);
                    }
                }
                if (JavaUtils.isNotEmtryOrNull(entitytypestyles.getCadverticalsectioniconurl())) {
                    FileUtils.copyFile(rootPath + File.separator + entitytypestyles.getCadverticalsectioniconurl(), stylePath, true);
                    String checkResult = FileUtils.checkFile(rootPath + File.separator + entitytypestyles.getCadverticalsectioniconurl(), stylePath);
                    if(StringUtils.isNotEmpty(checkResult)){
                        resultList.add(checkResult);
                    }
                }
            }
            //专题
            /*QueryWrapper<Entitytypethemestyles> themestylesQueryWrapper = new QueryWrapper<>();
            themestylesQueryWrapper.eq("entity_type_id",entitytypes.getPkid());
            themestylesQueryWrapper.eq("project_id",projectid);
            List<Entitytypethemestyles> themestylesList = entitytypethemestylesService.list(themestylesQueryWrapper);*/
            //复制
            for (Entitytypethemestyles entitytypethemestyles : themestylesList) {
                if(!entitytypethemestyles.getEntitytypeid().equalsIgnoreCase(entitytypes.getPkid())){
                    continue;
                }
                if (JavaUtils.isNotEmtryOrNull(entitytypethemestyles.getCadcolumniconurl())) {
                    FileUtils.copyFile(rootPath + File.separator + entitytypethemestyles.getCadcolumniconurl(), themestylePath, true);
                    String checkResult = FileUtils.checkFile(rootPath + File.separator + entitytypethemestyles.getCadcolumniconurl(), themestylePath);
                    if(StringUtils.isNotEmpty(checkResult)){
                        resultList.add(checkResult);
                    }
                }
                if (JavaUtils.isNotEmtryOrNull(entitytypethemestyles.getCadcrosssectioniconurl())) {
                    FileUtils.copyFile(rootPath + File.separator + entitytypethemestyles.getCadcrosssectioniconurl(), themestylePath, true);
                    String checkResult = FileUtils.checkFile(rootPath + File.separator + entitytypethemestyles.getCadcrosssectioniconurl(), themestylePath);
                    if(StringUtils.isNotEmpty(checkResult)){
                        resultList.add(checkResult);
                    }
                }
                if (JavaUtils.isNotEmtryOrNull(entitytypethemestyles.getCadplanariconurl())) {
                    FileUtils.copyFile(rootPath + File.separator + entitytypethemestyles.getCadplanariconurl(), themestylePath, true);
                    String checkResult = FileUtils.checkFile(rootPath + File.separator + entitytypethemestyles.getCadplanariconurl(), themestylePath);
                    if(StringUtils.isNotEmpty(checkResult)){
                        resultList.add(checkResult);
                    }
                }
                if (JavaUtils.isNotEmtryOrNull(entitytypethemestyles.getCadverticalsectioniconurl())) {
                    FileUtils.copyFile(rootPath + File.separator + entitytypethemestyles.getCadverticalsectioniconurl(), themestylePath, true);
                    String checkResult = FileUtils.checkFile(rootPath + File.separator + entitytypethemestyles.getCadcolumniconurl(), themestylePath);
                    if(StringUtils.isNotEmpty(checkResult)){
                        resultList.add(checkResult);
                    }
                }

            }
        }

        //专题
        //实体部件贴图
        QueryWrapper queryParts = new QueryWrapper();
        queryParts.eq("project_id",projectid);
        List<MdEntitytypeStylesPartsPic> stylesPartsPicList = mdEntitytypeStylesPartsPicMapper.selectList(queryParts);
        List<MdEntitytypeThemeStylesPartsPic> themeStylesPartsPicList = mdEntitytypeThemeStylesPartsPicMapper.selectList(queryParts);
        for (MdEntitytypeStylesPartsPic stylesPartsPic : stylesPartsPicList){
            if (JavaUtils.isNotEmtryOrNull(stylesPartsPic.getPicUrl())) {
                FileUtils.copyFile(rootPath + File.separator + stylesPartsPic.getPicUrl(), partsPicPath, true);
                String checkResult = FileUtils.checkFile(rootPath + File.separator + File.separator + stylesPartsPic.getPicUrl(), partsPicPath);
                if (StringUtils.isNotEmpty(checkResult)) {
                    resultList.add(checkResult);
                }
            }
        }

        for (MdEntitytypeThemeStylesPartsPic themeStylesPartsPic : themeStylesPartsPicList){
            if (JavaUtils.isNotEmtryOrNull(themeStylesPartsPic.getPicUrl())) {
                FileUtils.copyFile(rootPath + File.separator + themeStylesPartsPic.getPicUrl(), partsPicPath, true);
                String checkResult = FileUtils.checkFile(rootPath + File.separator + themeStylesPartsPic.getPicUrl(), partsPicPath);
                if (StringUtils.isNotEmpty(checkResult)) {
                    resultList.add(checkResult);
                }
            }
        }
        //默认风格部件贴图
//        QueryWrapper<MdPartsPic> partsQueryWrapper = new QueryWrapper<>();
//        partsQueryWrapper.eq("project_id",projectid);
//        List<MdPartsPic> partsPics = partsPicService.list(partsQueryWrapper);
//        for (MdPartsPic partsPic : partsPics) {
//            FileUtils.copyFile(partsPic.getPicUrl(), partsPicPath,true);
//        }
        // 通用图 规则库 qi_general_drawing
        QueryWrapper<GeneralDrawing> drawingQueryWrapper = new QueryWrapper<>();
        drawingQueryWrapper.eq("project_id",projectid);
        List<GeneralDrawing> generalDrawings = generalDrawingService.list(drawingQueryWrapper);
        for (GeneralDrawing generalDrawing : generalDrawings) {
            if (JavaUtils.isNotEmtryOrNull(generalDrawing.getPathUrl())) {
                FileUtils.copyFile(rootPath + File.separator + generalDrawing.getPathUrl(), drawingPath, true);
                String checkResult = FileUtils.checkFile(rootPath + File.separator + generalDrawing.getPathUrl(), drawingPath);
                if (StringUtils.isNotEmpty(checkResult)) {
                    resultList.add(checkResult);
                }
            }
        }
        return resultList;
    }

    @Override
    public String getProjectDBAgain(String projectid) {
        try {
            //创建数据并复制文件
            String filePath = PathUtils.initDirectoryAgain(projectid);
            File file = new File(filePath);
            if (!file.exists()){
                file.mkdirs();
            }
            log.info("项目数据复制开始");
            projectToSqliteImpl.copyTableByProjectIdAgain(filePath,projectid);
            log.info("项目数据复制完成");
            filePath = filePath+File.separator+"engineering.db";
//            File file = new File(filePath);
//            System.out.println("开始压缩文件");
//            /String zipFile = PathUtils.getPackagePath() + File.separator+"" + projectid +"-"+ JavaUtils.getDateTimeSSS() + ".zip";
//            OutputStream fileOutputStream = new FileOutputStream(zipFile);
//            FileGenerateUtils.toZip(filePath, fileOutputStream, true);
//            System.out.println("压缩完成，开始下载");
            return filePath;
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return null;
    }

    @Override
    public String symBols() {
        String tarPath = PathUtils.getPackagePath()+File.separator+"symbols-"+JavaUtils.getCurrentTimeMillis();
        String srcPath_sym = PathUtils.getDbCommonPath()+File.separator+"EntityDesign.sym";
        String srcPath_bru = PathUtils.getDbCommonPath()+File.separator+"EntityDesign.bru";
        String srcPath_lsl = PathUtils.getDbCommonPath()+File.separator+"LineLibrary.lsl";
        //复制到指定目录
        try {
            FileGenerateUtils.copyFiles(srcPath_sym, tarPath);//+File.separator+"EntityDesign.sym"
            FileGenerateUtils.copyFiles(srcPath_bru, tarPath);//+File.separator+"EntityDesign.sym"
            FileGenerateUtils.copyFiles(srcPath_lsl, tarPath);//+File.separator+"EntityDesign.sym"
            // 添加版本信息
            DesignDb designDb = designDbService.getVersion("auth_symbols", null);
            if (designDb!=null) {
                FileUtils.setVersion(tarPath+File.separator+"version.json",designDb.getDbVersion().toString());
            }
            log.info("开始压缩文件");
            String zipFile = PathUtils.getPackagePath() + File.separator+"" + "symbols" +"-"+ JavaUtils.getDateTimeSSS() + ".zip";
            OutputStream fileOutputStream = new FileOutputStream(zipFile);
            FileGenerateUtils.toZip(tarPath, fileOutputStream, true);
            log.info("压缩完成，开始下载");
            return zipFile;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getTemplateFiles(){
        String strSrcPath = PathUtils.getTemplatePath();
        File fileSrcPath = new File(PathUtils.getPackagePath());
        if(!fileSrcPath.exists())
            fileSrcPath.mkdir();
        String zipFile = PathUtils.getPackagePath() + File.separator+"" + "templatefiles" +"-"+ JavaUtils.getDateTimeSSS() + ".zip";
        try {
            FileGenerateUtils.toZip(strSrcPath, new FileOutputStream(zipFile));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return null;
        }
        return zipFile;
    }

    /**
     * 判断tabular.udbx中的纯表格类数据集是否从项目对应的小版本库中读取并填入了版本数据，如果没有的话就用tabulars_initial_datas.xlsx中对应的初始值来填充
     * @param strTabularUDBXPath    项目对应的tabular.udbx的绝对路径
     * @param strAchivementsUDBXPath    项目对应的EntitiesAchivements.udbx的绝对路径
     * @param strTaskKey        redis的key，用于向前端反馈执行进度和问题
     * @return  成功复制的记录数
     */
    private String insertTabularDataInitialDatas(String strTabularUDBXPath, String strAchivementsUDBXPath, String strTaskKey) {
        int iResult = 0;
        List<String> resultList = new ArrayList<>();
        Workspace workspace = new Workspace();
        DatasourceConnectionInfo connectionInfoTabular = new DatasourceConnectionInfo();
        connectionInfoTabular.setEngineType(EngineType.UDBX);
        connectionInfoTabular.setServer(strTabularUDBXPath);
        connectionInfoTabular.setAlias("tabular");
        Datasource datasource = workspace.getDatasources().open(connectionInfoTabular);
        if (datasource == null || !datasource.isOpened())
            return "{\"CopiedCount\":0}";
        DatasourceConnectionInfo connectionInfoAchivements = new DatasourceConnectionInfo();
        connectionInfoAchivements.setEngineType(EngineType.UDBX);
        connectionInfoAchivements.setServer(strAchivementsUDBXPath);
        connectionInfoAchivements.setAlias("achivements");
        Datasource datasourceAchivements = workspace.getDatasources().open(connectionInfoAchivements);
        if(null==datasourceAchivements||!datasourceAchivements.isOpened())
            return "{\"CopiedCount\":0}";
        String strTabularTemplatesPath = PathUtils.getTemplatePath() + File.separator + "tabulars_initial_datas.xlsx";
        File fileTabularTemplates = new File(strTabularTemplatesPath);
        if (!fileTabularTemplates.exists())
            return "{\"CopiedCount\":0}";
        InputStream in = FileUtil.getInputStream(fileTabularTemplates);
        java.util.Map<String, java.util.Map<Integer, List<String>>> mapTabularDatas = null;
        java.util.Map<String, java.util.Map<Integer, List<String>>> mapTabularHeaderDatas = null;
        try {
            mapTabularDatas = ExcelUtils.getExcel(in, 7, strTabularTemplatesPath);
            if (mapTabularDatas == null || mapTabularDatas.isEmpty())
                return "{\"CopiedCount\":0}";
            in = FileUtil.getInputStream(fileTabularTemplates);
            mapTabularHeaderDatas = ExcelUtils.getExcelTopAndData(in, 7, strTabularTemplatesPath);
            if (mapTabularHeaderDatas == null || mapTabularHeaderDatas.isEmpty())
                return "{\"CopiedCount\":0}";
        } catch (Exception e) {
        }

        String[] arrPureTabluarDatasetNames = strPureTabluarDatasetNames.split(",");
        for (int i = 0; i < arrPureTabluarDatasetNames.length; i++) {
            if (!datasource.getDatasets().contains(arrPureTabluarDatasetNames[i])) {
                if (datasourceAchivements.getDatasets().contains(arrPureTabluarDatasetNames[i])) {
                    Dataset datasetToBeCopied = datasourceAchivements.getDatasets().get(arrPureTabluarDatasetNames[i]);
                    datasource.copyDataset(datasetToBeCopied, datasetToBeCopied.getName(), datasetToBeCopied.getEncodeType());
                }else{
                    log.info("EntitiesAchivements.udbx中没有包含数据集"+arrPureTabluarDatasetNames[i]);
                    _redisCommonUtil.setKey(strTaskKey, "EntitiesAchivements.udbx中没有包含数据集"+arrPureTabluarDatasetNames[i]+"，请删除项目缓存文件夹后重新下载项目。");
                }
            }

            DatasetVector datasetVector = (DatasetVector) datasource.getDatasets().get(arrPureTabluarDatasetNames[i]);
            int count = datasetVector.getRecordCount();
            int resultCount = 0;
            if (datasetVector!=null&&datasetVector.getRecordCount() < 1) {
                Recordset recordset = datasetVector.getRecordset(false, CursorType.DYNAMIC);
                Iterator<String> iterator = mapTabularDatas.keySet().iterator();
                while (iterator.hasNext()) {
                    String strSheetName = iterator.next();        //拿到每个sheet的名字，也就是数据集名称
                    if (strSheetName.equalsIgnoreCase(datasetVector.getName())) {
                        java.util.Map<Integer, List<String>> mapRowDatas = mapTabularDatas.get(strSheetName);
                        List<String> lstTitles = mapTabularHeaderDatas.get(strSheetName).get(0);
                        recordset.getBatch().setMaxRecordCount(5000);
                        recordset.getBatch().begin();
                        for (int j = 1; j <= mapRowDatas.keySet().size(); j++) {
                            List<String> lstRowDatas = mapRowDatas.get(j);
                            Map<String, Object> mapValues = new HashMap<>();
                            for (int k = 0; k < lstTitles.size(); k++) {
                                if(recordset.getFieldInfos().indexOf(lstTitles.get(k))>=0&&recordset.getFieldInfos().get(lstTitles.get(k)).getType().equals(FieldType.LONGBINARY)
                                  &&lstRowDatas.get(k) instanceof String){
                                    if(!lstRowDatas.get(k).isEmpty()) {
                                        String strValue = String.valueOf(lstRowDatas.get(k));
                                        mapValues.put(lstRowDatas.get(k), strValue.getBytes(StandardCharsets.UTF_8));
                                    }
                                }else{
                                    mapValues.put(lstTitles.get(k), lstRowDatas.get(k));
                                }
                                if(recordset.getFieldInfos().indexOf(lstTitles.get(k))<0) {
                                    log.info("数据集" + arrPureTabluarDatasetNames[i] + "中字段" + lstTitles.get(k) + "没有对应的注册信息！");
                                }
                            }
                            if (recordset.addNew(null, mapValues)) {
                                iResult++;
                                resultCount++;
                            }
                        }
                        recordset.getBatch().update();
                        count = datasetVector.getRecordCount() - count;
                        if(count!=resultCount){
                            resultList.add(String.format("数据集%s新增数据量%s与源数据量%s不一致",arrPureTabluarDatasetNames[i],count,recordset));
                        }
                    }
                }
                recordset.dispose();
            }else if(!datasource.getDatasets().contains(arrPureTabluarDatasetNames[i])){
                _redisCommonUtil.setKey(strTaskKey, "没有将数据集"+arrPureTabluarDatasetNames[i]+"结构从EntitiesAchivements.udbx中复制到要下载的udbx中！");
            }
        }

        datasource.close();
        workspace.close();
        try {
            in.close();
        } catch (IOException e) {
        }
        String strResult = "{\"CopiedCount\":" + iResult;
        if(resultList!=null&&resultList.size()>0) {
            strResult += ",\"message:\"";
            strResult += "\"" + String.join(System.lineSeparator(), resultList) + "\"";
        }
        strResult += "}";
        return strResult;
    }
}
