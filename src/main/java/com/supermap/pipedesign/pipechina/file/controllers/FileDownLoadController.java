package com.supermap.pipedesign.pipechina.file.controllers;

import com.supermap.base.controller.BaseWeb;
import com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFile;
import com.supermap.pipedesign.pipechina.engineering.service.IWorkbagFileService;
import com.supermap.pipedesign.pipechina.engineering.service.IWorkbagFileuploadrecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/pipechina/services/downfile")
@Api(tags = "文件下载、导出")
public class FileDownLoadController extends BaseWeb {
    @Resource(name = "WorkbagFileuploadrecordImpl")
    private IWorkbagFileuploadrecordService workbagFileuploadrecordService;

    @Resource(name = "WorkbagFileImpl")
    private IWorkbagFileService workbagFileService;


    @ApiOperation("通过文件清单包id下载成果")
    @GetMapping("/downFileByFileId")
    public void downFileByFileId(@ApiParam(name = "fileid", value = "文件清单ID") @RequestParam String fileid){
        //根据fileid查path
        String filePath = workbagFileuploadrecordService.getPathByFileId(fileid);
        downLoadByFilePath(filePath);
    }

    @ApiOperation("通过路径下载文件")
    @GetMapping("/downFileByFilePath")
    public void downFileByFilePath2(@ApiParam(name = "filePath", value = "文件路径") @RequestParam String filePath){
        downLoadByFilePath(filePath);
    }

    @ApiOperation("通过工作包文件清单id查询下载id")
    @GetMapping("/selFilePath")
    public WorkbagFile selFilePath(@ApiParam(name = "workbaFileId", value = "工作包id") @RequestParam String workbaFileId){

        WorkbagFile workbagFile = workbagFileService.seleFilePath(workbaFileId);
        return workbagFile;
    }


}
