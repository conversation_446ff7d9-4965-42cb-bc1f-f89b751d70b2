package com.supermap.pipedesign.pipechina.file.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.*;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.*;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.TemplatefilesComVo;
import com.supermap.pipedesign.pipechina.engineering.dao.DesignDbMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.RefMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.UserMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.WorkbagFileuploadrecordMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignDb;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFileuploadrecord;
import com.supermap.pipedesign.pipechina.engineering.service.IDesignDbService;
import com.supermap.pipedesign.pipechina.engineering.service.IWorkbagFileService;
import com.supermap.pipedesign.pipechina.file.service.IPartsDownLoadService;
import com.supermap.pipedesign.pipechina.sqlite.service.IProjectToSqliteService;
import com.supermap.tools.base.FileGenerateUtils;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.file.FileUtils;
import com.supermap.tools.file.PathUtils;
import com.supermap.tools.gson.GsonUtil;
import com.supermap.tools.sqlite.SqliteUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service(value = "PartsDownLoadImpl")
public class PartsDownLoadImpl implements IPartsDownLoadService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DesignDbMapper designDbMapper;

    @Resource(name = "DesignDbImpl")
    private IDesignDbService designDbService;

    @Resource(name="ProjectToSqliteImpl")
    private IProjectToSqliteService projectToSqliteService;

    @Resource(name = "FileUpLoadUtils")
    private FileUpLoadUtils fileUpLoadUtils;

    @Autowired
    private TemplatefilesTemMapper templatefilesTemMapper;

    @Autowired
    private TemplatefilesMapper templatefilesMapper;

    @Autowired
    private WorkbagFileuploadrecordMapper workbagFileuploadrecordMapper;


    @Autowired
    private RefMapper refMapper;

    /**
     * 是否需要下载db
     * <AUTHOR>
     * @Description
     * @Date 2023/3/19 15:55
     **/
    @Override
    public Map<String, Object> checkDown(User user, String projectId, String authKey) {
        Map<String, Object> map = new HashMap<>();
        QueryWrapper<DesignDb> queryWrapper = new QueryWrapper<>();
        if (JavaUtils.isNotEmtryOrNull(authKey)){
            queryWrapper.eq("auth_key",authKey);
        }else{
            queryWrapper.eq("auth_key","auth_parts");
        }

        if (!StringUtils.isBlank(projectId)) {
            queryWrapper.eq("project_id",projectId);
        }else{
            queryWrapper.isNull("project_id");
        }
        DesignDb designDbs = designDbMapper.selectOne(queryWrapper);
        //designDbs 为空 权限被占用 查看是否为当前用户占用
        // 如果不是当前用户 返回占用者
        if (designDbs == null){
            designDbs = new DesignDb();
            if (JavaUtils.isNotEmtryOrNull(authKey)){
                designDbs.setAuthKey(authKey);
            }else{
                designDbs.setAuthKey("auth_parts");
            }
//            designDbs.setAuthKey("auth_parts");
            if (!StringUtils.isBlank(projectId)) {
                designDbs.setProjectId(projectId);
            }
            designDbs.setUserId(user.getUserid());
            designDbs.setDbVersion(JavaUtils.getTimestamp());
            designDbs.setVersionTime(JavaUtils.getTimestamp());
            designDbMapper.insert(designDbs);
            //throw new BusinessException("缺少权限配置");
        }
        // 判断当前权限
        if (StringUtils.isBlank(designDbs.getUserId())) {
            designDbs.setUserId(user.getUserid());
            designDbMapper.updateById(designDbs);
            map.put("success",true);
        }else{
            if (designDbs.getUserId().equals(user.getUserid())) {
                map.put("success",true);
            }else{
                map.put("success", false);
                map.put("userName", user.getUsername());
            }
        }
        map.put("version",designDbs.getDbVersion().toString());
        return map;
    }

    @Override
    public String selVersion(String projectId, String authKey) {
        QueryWrapper<DesignDb> queryWrapper = new QueryWrapper<>();
        if (JavaUtils.isNotEmtryOrNull(authKey)){
            queryWrapper.eq("auth_key",authKey);
        }else{
            queryWrapper.eq("auth_key","auth_parts");
        }

        if (JavaUtils.isNotEmtryOrNull(projectId)) {
            queryWrapper.eq("project_id",projectId);
        }else{
            queryWrapper.isNull("project_id");
        }
        DesignDb designDbs = designDbMapper.selectOne(queryWrapper);
        if (designDbs != null){
            return designDbs.getDbVersion().toString();
        }
        return null;
    }

    @Override
    public String downDrawDb(String projectId, String templateId) {
        try{
            //创建数据并复制文件
            String filePath = PathUtils.initDirectoryDraw(projectId,templateId);
            //查询版本
            DesignDb designDb = designDbService.getVersionDraw("auth_draw",projectId);
            // 添加版本信息
            if (designDb!=null) {
                FileUtils.setVersion(filePath+File.separator+"version.json",designDb.getDbVersion().toString());
            }
            //压缩包下模板dwg文件存放路径
            String dwgFilePath = filePath+File.separator+"dwg";
            //压缩包下构件库dwg文件存放路径
            String dwgCompFilePath = filePath+File.separator+"components";

            //查询模板dwg文件
            //项目id 为空 查系统模板
            if (JavaUtils.isEmtryOrNull(projectId)){
                //查模板关联文件Id
                List<String> list = templatefilesTemMapper.selFileIdByTemplateId(templateId);
                //根据文件id，文件清单查路径
                if (JavaUtils.isEmtryOrNull(list) && list.size()>0){
                    QueryWrapper<WorkbagFileuploadrecord> queryWrapper = new QueryWrapper<>();
                    queryWrapper.in("fileid",list);
                    List<String> filePathList = workbagFileuploadrecordMapper.selPathByFileId(queryWrapper);
                    if (!JavaUtils.isEmtryOrNull(filePathList)){
                        for (String str : filePathList){
                            FileGenerateUtils.copyPartsFile(str, dwgFilePath);
                        }
                    }
                }

            }else {
                //查模板关联文件Id
                List<String> list = templatefilesMapper.selFileIdByTemplateId(projectId,templateId);
                //根据文件id，文件清单查路径
                if (!JavaUtils.isEmtryOrNull(list)){
                    QueryWrapper<WorkbagFileuploadrecord> queryWrapper = new QueryWrapper<>();
                    queryWrapper.in("fileid",list);
                    List<String> filePathList = workbagFileuploadrecordMapper.selPathByFileId(queryWrapper);
                    if (!JavaUtils.isEmtryOrNull(filePathList)){
                        for (String str : filePathList){
                            FileGenerateUtils.copyPartsFile(str, dwgFilePath);
                        }
                    }
                }
            }
            //构件库文件 路径
            String compDwgPath = PathUtils.getDbUploadPath()+File.separator+"template"+File.separator+"components";
            //模板下构件库文件路径
            String tempDwgPath = PathUtils.getDbUploadPath()+File.separator+"template";
            if (JavaUtils.isNotEmtryOrNull(projectId)){
                tempDwgPath = tempDwgPath + File.separator + projectId;
            }
            tempDwgPath = tempDwgPath + File.separator + templateId;
            File file = new File(tempDwgPath);
            if (file.exists()) {
                FileGenerateUtils.copyDirDwg(tempDwgPath, dwgCompFilePath);
            }else {
                //不存在复制到模板下
                FileGenerateUtils.copyDirDwg(compDwgPath, tempDwgPath);
                //添加到压缩文件路径
                FileGenerateUtils.copyDirDwg(compDwgPath, dwgCompFilePath);
            }

            System.out.println("项目数据复制开始");
            projectToSqliteService.copyTabledraw(filePath,projectId,templateId);
            
            System.out.println("项目数据复制完成");
            System.out.println("开始压缩文件");
            String zipFile = PathUtils.getDrawPath() + File.separator+"draw-" + JavaUtils.getDateTimeSSS() + ".zip";
            OutputStream fileOutputStream = new FileOutputStream(zipFile);
//            FileGenerateUtils.toZipDir(filePath, fileOutputStream);
            FileGenerateUtils.toZip(filePath, fileOutputStream, true);
            System.out.println("压缩完成，开始下载");
            return zipFile;
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public String downMaterial() {
        try{
            //创建数据并复制文件
            String filePath = PathUtils.initDirectoryMaterial();

            //查询版本
            DesignDb designDb = designDbService.getVersion("auth_material");
            // 添加版本信息
            if (designDb!=null) {
                FileUtils.setVersion(filePath+File.separator+"version.json",designDb.getDbVersion().toString());
            }
            System.out.println("开始压缩文件");
            String zipFile = PathUtils.getPackagePath() + File.separator+"material-" + JavaUtils.getDateTimeSSS() + ".zip";
            OutputStream fileOutputStream = new FileOutputStream(zipFile);
            FileGenerateUtils.toZip(filePath, fileOutputStream);
            System.out.println("压缩完成，开始下载");
            return zipFile;
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    @Autowired
    private ComponentsTemMapper componentsTemMapper;

    @Autowired
    private ComponentsMapper componentsMapper;

    @Autowired
    private TemplateLocateTemMapper templateLocateTemMapper;

    @Autowired
    private OffsetDistanceTemMapper offsetDistanceTemMapper;

    @Autowired
    private ColumnConfigTemMapper columnConfigTemMapper;

    @Autowired
    private FontStyleConfigTemMapper fontStyleConfigTemMapper;
    
    @Autowired
    private LineStyleConfigTemMapper lineStyleConfigTemMapper;

    @Autowired
    private BlockMapper blockMapper;

    @Autowired
    private PropertyBlockMapper propertyBlockMapper;


    @Autowired
    private TemplateLocateMapper templateLocateMapper;

    @Autowired
    private OffsetDistanceMapper offsetDistanceMapper;

    @Autowired
    private ColumnConfigMapper columnConfigMapper;

    @Autowired
    private FontStyleConfigMapper fontStyleConfigMapper;

    @Autowired
    private LineStyleConfigMapper lineStyleConfigMapper;


    @Override
    public Map<String, Object> uploadDraw(MultipartFile file, User user, String projectId, String templateId) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> fileMap = new HashMap<>();
        int i = 1;
        DesignDb designDbs = designDbService.getVersionDraw("auth_draw",projectId);
        // 操作权限校验
        if (designDbs != null && !user.getUserid().equals(designDbs.getUserId())) {
            map.put("error", "非法操作，缺少操作权限");
            map.put("userName", user.getUsername());
            map.put("success", false);
            return map;
        }
        //上传 更新版本
        String savePath = PathUtils.getDrawPath();
        String filePath = fileUpLoadUtils.uploadRetrunPath(file,savePath).getPath();
        try {
            if (JavaUtils.isNotEmtryOrNull(projectId)){
                fileMap = drawUpdateByProjectId(projectId, filePath, templateId);
            }else{
                fileMap = drawUpdateBy(filePath, templateId);
            }

        } catch (Exception throwables) {
            throwables.printStackTrace();
        }
        // 解除占用
        designDbs.setUserId(null);
        //designDbs.setVersionTime(-1);
        Timestamp timestamp = JavaUtils.getTimestamp();
        designDbs.setDbVersion(timestamp);
        int updateInt = designDbMapper.updateById(designDbs);
//        int updateInt = designDbService.update(designDbs);
        System.out.println("解除占用 -> " + updateInt);
        map.put("success", true);
        map.put("version", designDbs.getDbVersion().toString());
        map.put("filePath",fileMap);

        return map;
    }
    public Map<String, Object> drawUpdateByProjectId(String projectId,String filePath,String templateId) throws SQLException, InstantiationException, IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        int i = 1;
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("template_id",templateId);
        queryWrapper.eq("project_id",projectId);
        //模板定位
        String localsql = "select * from drawing_template_locate where template_id='"+templateId+"' and project_id='"+projectId+"'";
        List<TemplateLocate> locateList = SqliteUtils.executeQuery(filePath, localsql, TemplateLocateTem.class);
        if (!JavaUtils.isEmtryOrNull(locateList) && locateList.size() >0 ){
            TemplateLocate locateTem = locateList.get(0);
            TemplateLocate locateTem1 = templateLocateMapper.selectOne(queryWrapper);
            if (locateTem.getCreateTime() != null && locateTem1.getCreateTime() != null){
                if(locateTem.getCreateTime().after(locateTem1.getCreateTime())){
                    templateLocateMapper.update(locateTem1,queryWrapper);
                }
            }

        }
        //子便宜距离
        String offsql = "select * from sub_offset_distance where template_id='"+templateId+"' and project_id='"+projectId+"'";
        List<OffsetDistance> offList = SqliteUtils.executeQuery(filePath, offsql, OffsetDistance.class);

        if (!JavaUtils.isEmtryOrNull(offList) && offList.size()>0){
            OffsetDistance offTem = offList.get(0);
            OffsetDistance offTem1 = offsetDistanceMapper.selectOne(queryWrapper);
            if (offTem.getCreateTime() != null && offTem1.getCreateTime() != null){
                if(offTem.getCreateTime().after(offTem1.getCreateTime())){
                    offsetDistanceMapper.update(offTem,queryWrapper);
                }
            }

        }

        //拉盲目定义
        String colsql = "select * from drawing_column_config where template_id='"+templateId+"' and project_id='"+projectId+"'";
        List<ColumnConfig> colList = SqliteUtils.executeQuery(filePath, colsql, ColumnConfig.class);
        if (!JavaUtils.isEmtryOrNull(colList) && colList.size()>0){
            List<ColumnConfig> colList1 = columnConfigMapper.selectList(queryWrapper);
            if (!JavaUtils.isEmtryOrNull(colList1) && colList1.size()>0)
            for (ColumnConfig coltem : colList){
                for (ColumnConfig coltem1 : colList1){
                    if (coltem.getCreateTime() != null && coltem1.getCreateTime() != null){
                        if (coltem.getPkid().equals(coltem1.getPkid()) && coltem.getCreateTime().after(coltem1.getCreateTime())){
                            columnConfigMapper.update(coltem,queryWrapper);
                            break;
                        }
                    }

                }
            }
        }

//            文字样式
        String fontsql = "select * from drawing_font_style_config where template_id='"+templateId+"' and project_id='"+projectId+"'";
        List<FontStyleConfig> fontList = SqliteUtils.executeQuery(filePath, fontsql, FontStyleConfig.class);
        if (!JavaUtils.isEmtryOrNull(fontList) && fontList.size()>0){
            List<FontStyleConfig> fontList1 = fontStyleConfigMapper.selectList(queryWrapper);
            if (!JavaUtils.isEmtryOrNull(fontList1) && fontList1.size()>0)
            for (FontStyleConfig fonttem : fontList){
                for (FontStyleConfig fonttem1 : fontList1){
                    if (fonttem.getCreateTime() != null && fonttem1.getCreateTime() != null){
                        if (fonttem.getPkid().equals(fonttem1.getPkid()) && fonttem.getCreateTime().after(fonttem1.getCreateTime())){
                            fontStyleConfigMapper.update(fonttem,queryWrapper);
                            break;
                        }
                    }

                }
            }
        }


        //线型样式
        String linesql = "select * from drawing_line_style_config where template_id='"+templateId+"' and project_id='"+projectId+"'";
        List<LineStyleConfig> lineList = SqliteUtils.executeQuery(filePath, linesql, LineStyleConfig.class);
        if (!JavaUtils.isEmtryOrNull(lineList) && lineList.size()>0){
            List<LineStyleConfig> lineList1 = lineStyleConfigMapper.selectList(queryWrapper);
            if (!JavaUtils.isEmtryOrNull(lineList1) && lineList1.size()>0){
                for (LineStyleConfig linetem : lineList){
                    for (LineStyleConfig linetem1 : lineList1){
                        if (linetem.getCreateTime() != null && linetem1.getCreateTime() != null){
                            if (linetem.getPkid().equals(linetem1.getPkid()) && linetem.getCreateTime().after(linetem1.getCreateTime())){
                                lineStyleConfigMapper.update(linetem,queryWrapper);
                                break;
                            }
                        }

                    }
                }
            }
        }

        //查询编辑的dwg文件
        String tempSql = "select * from pld_templatefiles where template_id='"+templateId+"' " +
                " and project_id='"+projectId+"' and file_path != '' ";
        List<Templatefiles> templatefileList = SqliteUtils.executeQuery(filePath, tempSql, Templatefiles.class);
        if (!JavaUtils.isEmtryOrNull(templatefileList) && templatefileList.size()>0){
            for (Templatefiles files : templatefileList){
                map.put("0-"+files.getPkid(),files.getFilePath());
            }
        }

        //查询构件库
        String compSql = "select * from drawing_components ";

        List<Components> comList = SqliteUtils.executeQuery(filePath, compSql, Components.class);
        if (!JavaUtils.isEmtryOrNull(comList) && comList.size()>0){
            List<Components> componentsTems = componentsMapper.selectList(new QueryWrapper<>());
            if (!JavaUtils.isEmtryOrNull(componentsTems) && componentsTems.size()>0){
                for (Components comtem : comList){
                    for (Components comtem1 : componentsTems){
                        if (comtem.getUpdateTime() != null && comtem1.getUpdateTime() != null)
                            if(comtem.getPkid().equals(comtem1.getPkid()) && comtem.getUpdateTime().after(comtem1.getUpdateTime())){
                                map.put("1-"+comtem1.getPkid(), comtem.getFilePath());
                                break;
                            }
                    }
                }
            }
        }


        return map;
    }
    public Map<String, Object> drawUpdateBy(String filePath,String templateId) throws SQLException, InstantiationException, IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("template_id",templateId);
        //模板定位
        String localsql = "select * from drawing_template_locate_tem where template_id='"+templateId+"'";
        List<TemplateLocateTem> locateList = SqliteUtils.executeQuery(filePath, localsql, TemplateLocateTem.class);
        if (!JavaUtils.isEmtryOrNull(locateList) && locateList.size()>0){
            TemplateLocateTem locateTem = locateList.get(0);
            TemplateLocateTem locateTem1 = templateLocateTemMapper.selectOne(queryWrapper);
            if (locateTem.getCreateTime() != null && locateTem1.getCreateTime() != null){
                if(locateTem.getCreateTime().after(locateTem1.getCreateTime())){
                    templateLocateTemMapper.updateById(locateTem1);
                }
            }

        }
        //子便宜距离
        String offsql = "select * from sub_offset_distance_tem where template_id='"+templateId+"'";
        List<OffsetDistanceTem> offList = SqliteUtils.executeQuery(filePath, offsql, OffsetDistanceTem.class);
        if (!JavaUtils.isEmtryOrNull(offList) && offList.size()>0){
            OffsetDistanceTem offTem = offList.get(0);
            OffsetDistanceTem offTem1 = offsetDistanceTemMapper.selectOne(queryWrapper);
            if (offTem.getCreateTime() != null && offTem1.getCreateTime() != null){
                if(offTem.getCreateTime().after(offTem1.getCreateTime())){
                    offsetDistanceTemMapper.updateById(offTem);
                }
            }

        }

        //拉盲目定义
        String colsql = "select * from drawing_column_config_tem where template_id='"+templateId+"'";
        List<ColumnConfigTem> colList = SqliteUtils.executeQuery(filePath, colsql, ColumnConfigTem.class);
        if (!JavaUtils.isEmtryOrNull(colList) && colList.size()>0){
            List<ColumnConfigTem> colList1 = columnConfigTemMapper.selectList(queryWrapper);
            if (!JavaUtils.isEmtryOrNull(colList1) && colList1.size()>0){
                for (ColumnConfigTem coltem : colList){
                    for (ColumnConfigTem coltem1 : colList1){
                        if (coltem.getCreateTime() != null && coltem1.getCreateTime() != null){
                            if (coltem.getPkid().equals(coltem1.getPkid()) && coltem.getCreateTime().after(coltem1.getCreateTime())){
                                columnConfigTemMapper.updateById(coltem);
                                break;
                            }
                        }

                    }
                }
            }
        }

//            文字样式
        String fontsql = "select * from drawing_font_style_config_tem where template_id='"+templateId+"'";
        List<FontStyleConfigTem> fontList = SqliteUtils.executeQuery(filePath, fontsql, FontStyleConfigTem.class);
        if (!JavaUtils.isEmtryOrNull(fontList) && fontList.size()>0){
            List<FontStyleConfigTem> fontList1 = fontStyleConfigTemMapper.selectList(queryWrapper);
            if (!JavaUtils.isEmtryOrNull(fontList1) && fontList1.size()>0){
                for (FontStyleConfigTem fonttem : fontList){
                    for (FontStyleConfigTem fonttem1 : fontList1){
                        if (fonttem.getCreateTime() != null && fonttem1.getCreateTime() != null){
                            if (fonttem.getPkid().equals(fonttem1.getPkid()) && fonttem.getCreateTime().after(fonttem1.getCreateTime())){
                                fontStyleConfigTemMapper.updateById(fonttem);
                                break;
                            }
                        }

                    }
                }
            }

        }


        //线型样式
        String linesql = "select * from drawing_line_style_config_tem where template_id='"+templateId+"'";
        List<LineStyleConfigTem> lineList = SqliteUtils.executeQuery(filePath, linesql, LineStyleConfigTem.class);
        if (!JavaUtils.isEmtryOrNull(lineList) && lineList.size()>0){
            List<LineStyleConfigTem> lineList1 = lineStyleConfigTemMapper.selectList(queryWrapper);
            if (!JavaUtils.isEmtryOrNull(lineList1) && lineList1.size()>0){
                for (LineStyleConfigTem linetem : lineList){
                    for (LineStyleConfigTem linetem1 : lineList1){
                        if (linetem.getCreateTime() != null && linetem1.getCreateTime() != null){
                            if (linetem.getPkid().equals(linetem1.getPkid()) && linetem.getCreateTime().after(linetem1.getCreateTime())){
                                lineStyleConfigTemMapper.updateById(linetem);
                                break;
                            }
                        }

                    }
                }
            }

        }

        //查询编辑的dwg文件
        String tempSql = "select * from pld_templatefiles_tem where template_id='"+templateId+"' and file_path !=''";
        List<TemplatefilesTem> templatefileList = SqliteUtils.executeQuery(filePath, tempSql, TemplatefilesTem.class);
        if (!JavaUtils.isEmtryOrNull(templatefileList) && templatefileList.size()>0){
            for (TemplatefilesTem files : templatefileList){
                map.put("0-"+files.getPkid(),files.getFilePath());
            }
        }

        String comSql = "select * from drawing_components_tem ";
        List<ComponentsTem> comList = SqliteUtils.executeQuery(filePath, comSql, ComponentsTem.class);
        if (!JavaUtils.isEmtryOrNull(comList) && comList.size() >0){
            List<ComponentsTem> componentsTems = componentsTemMapper.selectList(new QueryWrapper<>());
            if (!JavaUtils.isEmtryOrNull(componentsTems) && componentsTems.size()>0){
                for (ComponentsTem comtem : comList){
                    for (ComponentsTem comtem1 : componentsTems){
                        if (comtem.getUpdateTime() != null && comtem1.getUpdateTime() != null){
                            if(comtem.getPkid().equals(comtem1.getPkid()) && comtem.getUpdateTime().after(comtem1.getUpdateTime())){
                                map.put("1-"+comtem1.getPkid(), comtem.getFilePath());
                                break;
                            }
                        }
                    }
                }
            }
        }


        return map;
    }

    @Override
    public int uploadDrawDwg(MultipartFile file, String projectId, String templateId, String valueKey, User user) {
        TemplatefilesComVo vo = new TemplatefilesComVo();

        String templateFilesId = valueKey.substring(2);
        if (valueKey.startsWith("0")){
            QueryWrapper queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("template_id",templateId);
            queryWrapper.eq("pkid",templateFilesId);
            if (JavaUtils.isEmtryOrNull(projectId)){
                TemplatefilesTem templatefilesTem = templatefilesTemMapper.selectOne(queryWrapper);
                if (templatefilesTem != null){
                    vo = GsonUtil.ObjectToEntity(templatefilesTem,TemplatefilesComVo.class);
                }
            }else {
                queryWrapper.eq("project_id",projectId);
                Templatefiles templatefiles = templatefilesMapper.selectOne(queryWrapper);
                if (templatefiles != null){
                    vo = GsonUtil.ObjectToEntity(templatefiles,TemplatefilesComVo.class);
                }
            }
            if (vo != null && JavaUtils.isNotEmtryOrNull(vo.getPkid())) {
                String fileToPath = PathUtils.getDbUploadPath()+File.separator+JavaUtils.getNowDate2()+File.separator+"drawtemplate";
                //添加项目路径 项目id
                if (JavaUtils.isNotEmtryOrNull(projectId)){
                    fileToPath = fileToPath+File.separator+projectId;
                }
                fileToPath = fileToPath+File.separator+templateId;
                //文件上传
                File fil = fileUpLoadUtils.uploadRetrunFile(file, fileToPath);

                String newName = fil.getName();
                String filPath = fil.getPath();
//        //添加到文件清单中
                String fileId = workbagFileImpl.dwgToWorkBag(projectId, templateId, vo.getTemplateBlockCode(),vo.getFileName(), filPath, user);
                vo.setFileId(fileId);
                if (JavaUtils.isEmtryOrNull(projectId)){
                    TemplatefilesTem templatefilesTem = GsonUtil.ObjectToEntity(vo,TemplatefilesTem.class);
                    templatefilesTemMapper.update(templatefilesTem,queryWrapper);
                }else{
                    Templatefiles templatefiles = GsonUtil.ObjectToEntity(vo,Templatefiles.class);
                    templatefilesMapper.update(templatefiles,queryWrapper);
                }
            }
        } else if (valueKey.startsWith("1")) {
            if (JavaUtils.isEmtryOrNull(projectId)) {
                //构建库路径
//                String compDwgPath = PathUtils.getDbUploadPath()+File.separator+"template"+File.separator+"components";
                //模板下构件库文件路径
                String tempDwgPath = PathUtils.getDbUploadPath()+File.separator+"template";
                if (JavaUtils.isNotEmtryOrNull(projectId)){
                    tempDwgPath = tempDwgPath + File.separator + projectId;
                }
                tempDwgPath = tempDwgPath + File.separator + templateId;
                String filee = tempDwgPath+File.separator + file.getOriginalFilename();
                File file1 = new File(filee);
                if (file1.exists()){
                    file1.delete();
                }

//                String fileToPath = PathUtils.getDbUploadPath()+File.separator+JavaUtils.getNowDate2()+File.separator+"drawtemplate";
//                //添加项目路径 项目id
//                if (JavaUtils.isNotEmtryOrNull(projectId)){
//                    fileToPath = fileToPath+File.separator+projectId;
//                }
//                fileToPath = fileToPath+File.separator+templateId;
                //文件上传
                File fil = fileUpLoadUtils.uploadRetrunFile(file, tempDwgPath);
            }
        }





        return 1;
    }

    @Resource(name = "WorkbagFileImpl")
    private IWorkbagFileService workbagFileImpl;
}
