package com.supermap.pipedesign.pipechina.file.impl;

import cn.hutool.core.util.StrUtil;
import com.supermap.pipedesign.pipechina.file.service.IDesignFileService;
import com.supermap.tools.sqlite.SqliteUtils;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title DesignFileImpl
 * @date 2023/03/23 17:22
 * @description 设计成果版本管理
 */
public class DesignFileImpl implements IDesignFileService {

    @Override
    public void createVersion(String dbPath){

        try {
            // 连接db
            Connection conn = SqliteUtils.getConnection(dbPath);
            String sql = " SELECT name  FROM sqlite_master  WHERE type ='table' ";
            List<Map<String,Object>> tables = SqliteUtils.executeQueryMap(conn, sql, Arrays.asList(new String[]{"name"}));
            // 表循环
            for (Map<String, Object> item : tables) {
                Object name = item.get("name");
                sql = StrUtil.format(" PRAGMA table_info({}) ",name);
                //字段循环
                List<String> heads = new ArrayList<>();
                List<Map<String,Object>> fields = SqliteUtils.executeQueryMap(conn, sql, Arrays.asList(new String[]{"name"}));
                for (Map<String, Object> field : fields) {
                    Object fieldName = field.get("name");
                    heads.add(fieldName+"");
                }
                sql = StrUtil.format(" select * from {}  ",name);
                List<Map<String,Object>> dataList = SqliteUtils.executeQueryMap(conn,sql,heads);
                // 插入数据 todo 这里 要确认是连接数据库还是 数据集

            }


        } catch (SQLException e) {
            e.printStackTrace();
        }


    }
}

