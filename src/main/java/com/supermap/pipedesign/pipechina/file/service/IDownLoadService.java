package com.supermap.pipedesign.pipechina.file.service;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Repository;

/** @Name IDownLoadService @Desc 文件下载、导出 <AUTHOR> @Date 2022/12/29 10:11 */
@Repository
public interface IDownLoadService {
  /**
   * 创建sheet 并赋值
   * @param workbook
   * @param headerStyle
   * @param contentStyle
   * @param id
   * @return void
   * @Date 2022/12/29 10:12
   * @auther eomer
   */
  void createSheetList(
      HSSFWorkbook workbook, HSSFCellStyle headerStyle, HSSFCellStyle contentStyle, String sheetName, String id);
}
