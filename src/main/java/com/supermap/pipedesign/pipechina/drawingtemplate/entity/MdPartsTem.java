package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * $部件表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_parts_tem")
@ApiModel(value="PartsTem对象", description="$部件表")
public class MdPartsTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "项目编号(项目编号为0时，为平台级实体定义)")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "更新人id")
    @TableField("update_user_id")
    private String updateuserid;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Timestamp updatetime;

    @ApiModelProperty(value = "部件类型")
    @TableField("parts_type")
    private String partstype;

    @ApiModelProperty(value = "部件名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "部件分类名称")
    @TableField("first_name")
    private String firstname;

    @ApiModelProperty(value = "部件开发类名称")
    @TableField("second_name")
    private String secondname;

    @ApiModelProperty(value = "部件信息")
    @TableField("param")
    private String param;

    @ApiModelProperty(value = "部件信息")
    @TableField("code_list")
    private String codeList;










}
