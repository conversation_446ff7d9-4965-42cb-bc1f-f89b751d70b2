package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.LineStyleConfigTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.LineStyleConfigTemVo;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 线型样式配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Repository
public interface ILineStyleConfigTemService extends IService<LineStyleConfigTem> {

 /**
 * 添加线型样式配置表信息
 *
 * @param lineStyleConfigTem
 * @return int
 * @Date 2023-02-18
 * @auther eomer
 */
 int insert(LineStyleConfigTemVo lineStyleConfigTem, User userId);

 /*
  * <AUTHOR>
  * @Description
  * @Date 2023/2/9 16:29
  * @param templateId
  * 查询模板 子模块偏移距离 信息
  **/
 List<LineStyleConfigTem> getInfo(String templateId);


 }
