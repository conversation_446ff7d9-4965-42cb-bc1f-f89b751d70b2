package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.OffsetDistanceMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.OffsetDistanceTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.OffsetDistance;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.OffsetDistanceTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IOffsetDistanceService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 子模块偏移距离 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service("OffsetDistanceImpl")
public class OffsetDistanceImpl extends ServiceImpl<OffsetDistanceMapper, OffsetDistance> implements IOffsetDistanceService {

    @Autowired
    private OffsetDistanceMapper offsetDistanceMapper;

    /**
     * 添加子模块偏移距离信息
     *
     * @param offsetDistance
     * @return int
     * @Date 2023-02-17
     * @auther eomer
     */
    @Override
    public int insert(OffsetDistance offsetDistance, User user) {
        offsetDistance.setCreateTime(JavaUtils.getTimestamp());
        offsetDistance.setCreateUserId(user.getUserid());
        if (JavaUtils.isNotEmtryOrNull(offsetDistance.getPkid())){
            OffsetDistance tem = offsetDistanceMapper.selectById(offsetDistance.getPkid());
            if (tem != null){
                offsetDistance.setCreateTime(tem.getCreateTime());
                offsetDistance.setCreateUserId(tem.getCreateUserId());
                return offsetDistanceMapper.updateById(offsetDistance);
            }
        }
        offsetDistance.setCreateTime(JavaUtils.getTimestamp());
        return offsetDistanceMapper.insert(offsetDistance);
    }

    /**
    * 删除子模块偏移距离信息
    *
    * @param offsetDistanceId
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int delete(String offsetDistanceId) {
        return offsetDistanceMapper.deleteById(offsetDistanceId);
    }

    /**
    * 更新子模块偏移距离信息
    *
    * @param offsetDistance
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int update(OffsetDistance offsetDistance) {
        return offsetDistanceMapper.updateById(offsetDistance);
    }

    /**
    * 全部查询
    *
    * @param offsetDistance
    * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.OffsetDistance>
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public List<OffsetDistance> list(OffsetDistance offsetDistance) {

        QueryWrapper<OffsetDistance> queryWrapper = new QueryWrapper<>();

        return offsetDistanceMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<OffsetDistance> offsetDistanceIPage = new Page<>();
        offsetDistanceIPage.setCurrent(current);
        offsetDistanceIPage.setSize(size);

        QueryWrapper<OffsetDistance> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return offsetDistanceMapper.selectPage(offsetDistanceIPage, queryWrapper);
    }
    @Autowired
    private OffsetDistanceTemMapper offsetDistanceTemMapper;
    @Override
    public OffsetDistance getInfo(String templateId, String projectId) {
        QueryWrapper<OffsetDistance> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id",templateId);
        queryWrapper.eq("project_id",projectId);
        OffsetDistance offsetDistanceTem = offsetDistanceMapper.selectOne(queryWrapper);
        if (offsetDistanceTem == null){
            QueryWrapper<OffsetDistanceTem> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("template_id","000-000");
            OffsetDistanceTem offsetDistanceTem1 = offsetDistanceTemMapper.selectOne(queryWrapper1);
            offsetDistanceTem = GsonUtil.ObjectToEntity(offsetDistanceTem1,OffsetDistance.class);
            offsetDistanceTem.setPkid(null);
        }
        return offsetDistanceTem;
    }
}
