package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.TemplateLocateTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplateLocateTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ITemplateLocateTemService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.tools.base.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 图纸模板定位模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service("TemplateLocateTemImpl")
public class TemplateLocateTemImpl extends ServiceImpl<TemplateLocateTemMapper, TemplateLocateTem> implements ITemplateLocateTemService {

    @Autowired
    private TemplateLocateTemMapper templateLocateTemMapper;


    /**
    * 添加图纸模板定位模板信息
    *
    * @param templateLocateTem
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int insert(TemplateLocateTem templateLocateTem, User user) {

        templateLocateTem.setCreateUserId(user.getUserid());
        templateLocateTem.setCreateTime(JavaUtils.getTimestamp());
        if (JavaUtils.isNotEmtryOrNull(templateLocateTem.getPkid())){
            TemplateLocateTem templateLocateTem1 = templateLocateTemMapper.selectById(templateLocateTem.getPkid());
            if (templateLocateTem1 != null){
                return templateLocateTemMapper.updateById(templateLocateTem);
            }
        }
        return templateLocateTemMapper.insert(templateLocateTem);
    }

    @Override
    public TemplateLocateTem getInfo(String templateId) {
        QueryWrapper<TemplateLocateTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id",templateId);
        TemplateLocateTem templateLocateTem = templateLocateTemMapper.selectOne(queryWrapper);
        if (templateLocateTem == null){
            QueryWrapper<TemplateLocateTem> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("template_id","000-000");
            templateLocateTem = templateLocateTemMapper.selectOne(queryWrapper1);
            templateLocateTem.setPkid(null);
        }
        return templateLocateTem;
    }
}
