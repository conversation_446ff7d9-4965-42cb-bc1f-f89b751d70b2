package com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * $部件表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
public class MdPartsComVo  {



    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "项目编号(项目编号为0时，为平台级实体定义)")
    private String projectid;

    @ApiModelProperty(value = "更新人id")
    private String updateuserid;

    @ApiModelProperty(value = "更新时间")
    private String updatetime;

    @ApiModelProperty(value = "部件类型")
    private String partstype;

    @ApiModelProperty(value = "部件名称")
    private String name;

    @ApiModelProperty(value = "部件分类名称")
    private String firstname;

    @ApiModelProperty(value = "部件开发类名称")
    private String secondname;

    @ApiModelProperty(value = "部件信息")
    private String param;

    @ApiModelProperty(value = "部件参数")
    private String codeList;










}
