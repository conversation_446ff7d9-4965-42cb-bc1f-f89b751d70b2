package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.sql.Date;
import java.io.Serializable;
import java.sql.Timestamp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 基本块模板 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("basic_block_tem")
@ApiModel(value="BlockTem对象", description="基本块模板")
public class BlockTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "指北针块名称")
    @TableField("compass_name")
    private String compassName;

    @ApiModelProperty(value = "索引图块名称	索引图块名称	索引图块名称")
    @TableField("index_chart_name")
    private String indexChartName;

    @ApiModelProperty(value = "图例块名称")
    @TableField("legend_chart_name")
    private String legendChartName;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_user_id")
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Timestamp createTime;

    @ApiModelProperty(value = "指北针块名称路径")
    @TableField("compass_file_path")
    private String compassFilePath;

    @ApiModelProperty(value = "索引图块名称路径")
    @TableField("index_file_path")
    private String indexFilePath;

    @ApiModelProperty(value = "图例块名称路径")
    @TableField("legend_file_path")
    private String legendFilePath;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "模拟板id")
    @TableField("template_id")
    private String templateId;


}
