package com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo;

import com.supermap.pipedesign.pipechina.drawingtemplate.entity.PropertyBlock;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.PropertyBlockTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.PropertyDef;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.PropertyDefTem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *  实体类
 * </p>
 * 属性快
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
public class PropertyBlockProVo {
    @ApiModelProperty(value = "属性快")
    private List<PropertyBlock> propertyBlock;

    @ApiModelProperty(value = "块属性定义")
    private List<PropertyDef> propertyDef;


}
