package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.Multilingual;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 多语言管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Repository
public interface IMultilingualService extends IService<Multilingual> {

 /**
 * 添加多语言管理表信息
 *
 * @param multilingual
 * @return int
 * @Date 2023-02-10
 * @auther eomer
 */
 int insert(Multilingual multilingual, User userId);

 /**
  * 删除多语言管理表信息
  *
  * @param multilingualId
  * @param projectId
  * @return int
  * @Date 2023-02-10
  * @auther eomer
  */
 int delete(String multilingualId, String projectId);

 /**
 * 更新多语言管理表信息
 *
 * @param multilingual
 * @return int
 * @Date 2023-02-10
 * @auther eomer
 */
 int update(Multilingual multilingual);

 /**
  * 全部查询
  *
  * @param multilingual
  * @param projectId
  * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Multilingual>
  * @Date 2023-02-10
  * @auther eomer
  */
 List<Multilingual> list(Multilingual multilingual, String projectId);

 /**
  * 分页查询
  *
  * @param current
  * @param size
  * @param projectId
  * @return com.baomidou.mybatisplus.core.metadata.IPage
  * @Date 2023-02-10
  * @auther eomer
  */
 IPage pageList(long current, long size, String projectId);

 /*
  * <AUTHOR>
  * @Description
  * @Date 2023/2/11 9:19
  * @param pkid
  * 根据id查详情
  **/
 Multilingual getInfoById(String pkid, String projectId);

 /**
  * @param file      文件
  * @param pkid      语言id
  * @param projectId
  * <AUTHOR>
  * @Description
  * @Date 2023/2/13 11:45
  */
 int mulitImport(MultipartFile file, String pkid, String projectId);


 }
