package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.MdPartsMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.MdPartsTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.MdParts;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.MdPartsTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.MdPartsComVo;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IMdPartsService;
import com.supermap.pipedesign.pipechina.engineering.dao.RefMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.UserMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignDb;
import com.supermap.pipedesign.pipechina.engineering.entity.Ref;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.service.IDesignDbService;
import com.supermap.pipedesign.pipechina.file.impl.FileUpLoadUtils;
import com.supermap.pipedesign.pipechina.sqlite.service.IProjectToSqliteService;
import com.supermap.tools.base.FileGenerateUtils;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.file.FileUtils;
import com.supermap.tools.file.PathUtils;
import com.supermap.tools.gson.GsonUtil;
import com.supermap.tools.sqlite.SqliteUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 部件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Service("MdPartsImpl")
public class MdPartsImpl extends ServiceImpl<MdPartsMapper, MdParts> implements IMdPartsService {

    @Autowired
    private MdPartsMapper mdPartsMapper;

    @Autowired
    private MdPartsTemMapper mdPartstemMapper;

    @Autowired
    private UserMapper userMapper;

    @Resource(name = "DesignDbImpl")
    private IDesignDbService designDbService;

    @Resource(name = "FileUpLoadUtils")
    private FileUpLoadUtils fileUpLoadUtils;

    @Resource(name = "ProjectToSqliteImpl")
    private IProjectToSqliteService projectToSqliteService;
    @Autowired
    private RefMapper refMapper;

    /**
     * 添加部件表信息
     *
     * @param mdParts
     * @return int
     * @Date 2023-02-18
     * @auther eomer
     */
    @Override
    public MdParts insert(MdParts mdParts, User user) {
//        mdParts.setUpdatetime(JavaUtils.getTimestamp());
//        mdParts.setUpdateuserid(userId);
//        mdPartsMapper.insert(mdParts);
        //锁定此项目权限
        DesignDb designDbs = designDbService.getVersion("auth_parts", mdParts.getProjectid());
        designDbs.setUserId(user.getUserid());
        designDbs.setVersionTime(JavaUtils.getTimestamp());
        designDbService.update(designDbs);
        return mdParts;
    }

    /**
     * 跟据id查详情
     *
     * <AUTHOR>
     * @Description
     * @Date 2023/3/20 20:38
     **/
    @Override
    public MdParts selInfoByPkid(String pkid) {
        return mdPartsMapper.selectById(pkid);
    }

    /**
     * 删除部件表信息
     *
     * @param mdPartsId
     * @return int
     * @Date 2023-02-18
     * @auther eomer
     */
    @Override
    public int delete(String mdPartsId) {
        return mdPartsMapper.deleteById(mdPartsId);
    }

    /**
     * 更新部件表信息
     *
     * @param mdParts
     * @return int
     * @Date 2023-02-18
     * @auther eomer
     */
    @Override
    public MdParts update(MdParts mdParts, User userId) {
        mdParts.setUpdateuserid(userId.getUserid());
        mdParts.setUpdatetime(JavaUtils.getTimestamp());
        mdPartsMapper.updateById(mdParts);
        //锁定此项目权限
        DesignDb designDbs = designDbService.getVersion("auth_parts", mdParts.getProjectid());
        designDbs.setUserId(userId.getUserid());
        designDbs.setVersionTime(JavaUtils.getTimestamp());
        designDbService.update(designDbs);

        return mdParts;
    }

    /**
     * 全部查询
     *
     * @param mdParts
     * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.MdParts>
     * @Date 2023-02-18
     * @auther eomer
     */
    @Override
    public List<MdParts> list(MdParts mdParts) {

        QueryWrapper<MdParts> queryWrapper = new QueryWrapper<>();

        return mdPartsMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * @Date 2023-02-18
     * @auther eomer
     */
    @Override
    public IPage pageList(long current, long size, String searchName, String partstype, String projectid) {

        IPage<MdParts> mdPartsIPage = new Page<>();
        mdPartsIPage.setCurrent(current);
        mdPartsIPage.setSize(size);

        QueryWrapper<MdParts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectid);
        if (JavaUtils.isNotEmtryOrNull(partstype)) {
            queryWrapper.eq("parts_type", partstype);
        }
        if (JavaUtils.isNotEmtryOrNull(searchName)) {
            queryWrapper.like("name", searchName);
        }
        IPage<MdParts> list = mdPartsMapper.selectPage(mdPartsIPage, queryWrapper);
        List<MdParts> records = list.getRecords();
        for (MdParts parts : records) {
            if (JavaUtils.isNotEmtryOrNull(parts.getUpdateuserid())) {
                List<User> users = userMapper.selByUserId(parts.getUpdateuserid());
                if (!users.isEmpty()){
                    parts.setUpdateuserid(users.get(0).getUsername());
                }else {
                    List<Ref> refs = refMapper.selectByUserId(parts.getUpdateuserid());
                    if (!refs.isEmpty()){
                        parts.setUpdateuserid(refs.get(0).getUserName());
                    }

                }

            }

        }
        return list;
    }

    /**
     * 上传db
     *
     * <AUTHOR>
     * @Description
     * @Date 2023/3/18 14:08
     **/
    @Override
    public Map<String, Object> uploadParts(MultipartFile file, String projectId, User userId) {
        Map<String, Object> map = new HashMap<>();
        DesignDb designDbs = designDbService.getVersion("auth_parts", projectId);
        if (designDbs != null && !userId.getUserid().equals(designDbs.getUserId())) {
            map.put("error", "非法操作，缺少操作权限");
            List<User> userList = userMapper.selByUserId(userId.getUserid());
            if (userList.get(0).getUsername() != null) {
                map.put("userName", userList.get(0).getUsername());
            }else{
                List<Ref> refs = refMapper.selectByUserId(userId.getUserid());
                if (!refs.isEmpty()){
                    map.put("userName", refs.get(0).getUserName());
                }
            }
            map.put("success", false);
            return map;
        }

        //上传db---------------
        String savePath = PathUtils.getDbUploadPath();
        String filePath = fileUpLoadUtils.uploadFile(file, savePath);
        //更新数据库
        try {
            String sql = "select * from pld_md_parts";
            List<MdPartsComVo> list = SqliteUtils.executeQuery(filePath, sql, MdPartsComVo.class);
            for (MdPartsComVo vo : list) {
                QueryWrapper<MdParts> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("pkid", vo.getPkid());
                queryWrapper.eq("project_id", projectId);
                MdParts mdPartsTem = mdPartsMapper.selectOne(queryWrapper);
                String updatetime = vo.getUpdatetime();
                Timestamp timestamp = JavaUtils.stringToTimestamp(updatetime);
                if (mdPartsTem == null) {
                    MdParts partsTem = new MdParts();
                    partsTem.setPkid(vo.getPkid());
                    partsTem.setUpdateuserid(vo.getUpdateuserid());
                    partsTem.setUpdatetime(timestamp);
                    partsTem.setPartstype(vo.getPartstype());
                    partsTem.setName(vo.getName());
                    partsTem.setFirstname(vo.getFirstname());
                    partsTem.setSecondname(vo.getSecondname());
                    partsTem.setProjectid(vo.getProjectid());
                    partsTem.setParam(vo.getParam());
                    partsTem.setCodeList(vo.getCodeList());
                    mdPartsMapper.insert(partsTem);
                } else {
                    Timestamp tem = mdPartsTem.getUpdatetime();
                    mdPartsTem.setUpdateuserid(vo.getUpdateuserid());
                    mdPartsTem.setUpdatetime(timestamp);
                    mdPartsTem.setPartstype(vo.getPartstype());
                    mdPartsTem.setName(vo.getName());
                    mdPartsTem.setFirstname(vo.getFirstname());
                    mdPartsTem.setSecondname(vo.getSecondname());
                    mdPartsTem.setParam(vo.getParam());
                    mdPartsTem.setProjectid(vo.getProjectid());
                    mdPartsTem.setCodeList(vo.getCodeList());
                    QueryWrapper<MdParts> queryWrapper1 = new QueryWrapper<>();
                    queryWrapper1.eq("pkid", mdPartsTem.getPkid());
                    queryWrapper1.eq("project_id", mdPartsTem.getProjectid());
                    mdPartsMapper.update(mdPartsTem, queryWrapper1);
                }
            }
        } catch (Exception throwables) {
            throwables.printStackTrace();
        }
        // 解除占用
        designDbs.setUserId("");
        //designDbs.setVersionTime(-1);
        Timestamp timestamp = JavaUtils.getTimestamp();
        designDbs.setDbVersion(timestamp);
        int updateInt = designDbService.update(designDbs);
        System.out.println("解除占用 -> " + updateInt);

        map.put("success", true);
        map.put("version", timestamp);

        return map;
    }


    /**
     * 下载db
     *
     * <AUTHOR>
     * @Description
     * @Date 2023/3/18 11:23
     **/
    @Override
    public String checkOut(String projectId) {
        try {
            //创建数据并复制文件
            String filePath = PathUtils.initDirectoryPart();
            //查询版本
            DesignDb designDb = designDbService.getVersion("auth_parts", projectId);
            // 添加版本信息
            if (designDb != null) {
                FileUtils.setVersion(filePath + File.separator+"version.json", designDb.getDbVersion().toString());
            }
            System.out.println("项目数据复制开始");
            projectToSqliteService.copyTablePart(filePath, projectId);
            System.out.println("项目数据复制完成");
            System.out.println("开始压缩文件");
            String zipFile = PathUtils.getPackagePath() +File.separator+"parts-" + JavaUtils.getDateTimeSSS() + ".zip";
            OutputStream fileOutputStream = new FileOutputStream(zipFile);
            FileGenerateUtils.toZip(filePath, fileOutputStream);
            System.out.println("压缩完成，开始下载");
            return zipFile;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";

    }


    @Override
    public int insertForEntity(MdPartsTem mdParts, User user, String projectId) {
        MdParts mdParts1 = new MdParts();
        mdParts1.setProjectid(projectId);
        mdParts1.setPartstype(mdParts.getPartstype());
        mdParts1.setName(mdParts.getName());
        mdParts1.setFirstname(mdParts.getFirstname());
        mdParts1.setSecondname(mdParts.getSecondname());
        mdParts1.setParam(mdParts.getParam());
        mdParts1.setUpdateuserid(user.getUserid());
        return mdPartsMapper.insert(mdParts1);
    }

    @Override
    public List<MdPartsTem> selForEntity(String projectId) {
        QueryWrapper<MdParts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        List<MdParts> mdParts = mdPartsMapper.selectList(queryWrapper);
        List<MdPartsTem> mdPartsTems = mdPartstemMapper.selectList(new QueryWrapper<>());
        for (MdParts mdPart : mdParts) {
            for (MdPartsTem mdPartsTem : mdPartsTems) {
                if (mdPart.getPkid().equals(mdPartsTem.getPkid())) {
                    mdPartsTems.remove(mdPartsTem);
                }
            }
        }
        return mdPartsTems;
    }


    @Override
    public Map<String, List<String>> selInfoById(String partsId, String projectId) {
        QueryWrapper<MdParts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        queryWrapper.eq("parts_id", partsId);
        MdParts partsTem = mdPartsMapper.selectOne(queryWrapper);
        String codeList = partsTem.getCodeList();
        Map<String, List<String>> stringListMap = GsonUtil.GsonToMapString(codeList);
        return stringListMap;
    }
}
