package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_templates_tem")
@ApiModel(value="TemplatesTem对象", description="模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)")
public class TemplatesTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "模板名称")
    @TableField("template_name")
    private String templatename;

    @ApiModelProperty(value = "模板类型(枚举值。1:图纸模板;2:文档模板)")
    @TableField("template_type")
    private String templatetype;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user_id")
    private String createuserid;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Timestamp createtime;

    @ApiModelProperty(value = "审核状态(枚举值)")
    @TableField("audit_state")
    private Integer auditstate;

    @ApiModelProperty(value = "模板描述信息")
    @TableField("template_desc")
    private String templatedesc;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "模板具体类型id")
    @TableField("type")
    private String type;

    @ApiModelProperty(value = "工程阶段(0预可研1可研2初设3施工4竣工)")
    @TableField("stage")
    private String stage;

    @ApiModelProperty(value = "语言")
    @TableField("tem_language_id")
    private String temlanguageid;


}
