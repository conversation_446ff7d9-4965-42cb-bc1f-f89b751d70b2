package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 多语言模板管 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_multilingual_tem")
@ApiModel(value="MultilingualTem对象", description="多语言模板管")
public class MultilingualTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "多语言名称")
    @TableField("mult_name")
    private String multname;

    @ApiModelProperty(value = "说明")
    @TableField("mult_desc")
    private String multdesc;

    @ApiModelProperty(value = "路径")
    @TableField("file_path")
    private String filepath;

    @ApiModelProperty(value = "创建人")
    @TableField("create_user_id")
    private String createuserid;

    @ApiModelProperty(value = "创建日期")
    @TableField("create_time")
    private Timestamp createtime;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "是否删除(t 启用 f 删除)")
    @TableField("enable")
    private Boolean enable;


}
