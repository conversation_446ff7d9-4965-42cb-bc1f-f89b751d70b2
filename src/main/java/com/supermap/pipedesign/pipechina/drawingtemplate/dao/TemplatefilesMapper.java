package com.supermap.pipedesign.pipechina.drawingtemplate.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.Templatefiles;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 模板关联文件表(存储模板关联的文件。考虑到用户有可能上传压缩包，所以如果用户上传的是压缩包的话，除了将压缩包本身需要与模板进行关联以外，还会对解压后的每个文件建立起与模板的关联关系。这样做主要为了提高系统的适配性。) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Mapper
public interface TemplatefilesMapper extends BaseMapper<Templatefiles> {

    @Select("select file_id from pld_templatefiles where project_id = #{projectId} and template_id = #{templateId}")
    List<String> selFileIdByTemplateId(@Param("projectId") String projectId ,@Param("templateId") String templateId);


    @Select("select file_id from pld_templatefiles ${ew.customSqlSegment}")
    List<String> selFileIdByTemplate(@Param(Constants.WRAPPER) Wrapper<Templatefiles> queryWrapper);

}
