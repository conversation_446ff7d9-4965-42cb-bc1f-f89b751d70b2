package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 线型样式模板表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("drawing_line_style_template_tem")
@ApiModel(value="LineStyleTemplateTem对象", description="线型样式模板表")
public class LineStyleTemplateTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编码")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "线型样式名称")
    @TableField("style_name")
    private String styleName;

    @ApiModelProperty(value = "线型")
    @TableField("line_style")
    private String lineStyle;

    @ApiModelProperty(value = "线宽")
    @TableField("line_width")
    private Double lineWidth;

    @ApiModelProperty(value = "线色")
    @TableField("line_color")
    private String lineColor;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_user_id")
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Timestamp createTime;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;


}
