package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.sql.Timestamp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * GOUJIAN构件库中所有构件 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("drawing_components")
@ApiModel(value="Components对象", description="GOUJIAN构件库中所有构件")
public class Components implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "构件类型id")
    @TableField("components_id")
    private String componentsId;

    @ApiModelProperty(value = "构件名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "构件对应dwg图块名称")
    @TableField("tk_name")
    private String tkName;

    @ApiModelProperty(value = "dwg图块对应的幻灯片名称")
    @TableField("sld_name")
    private String sldName;

    @ApiModelProperty(value = "是否是系统图块")
    @TableField("is_sym")
    private Integer isSym;

    @ApiModelProperty(value = "构件类型在界面显示顺序")
    @TableField("seq_num")
    private Integer seqNum;

    @ApiModelProperty(value = "扩展字段1")
    @TableField("reserve1")
    private String reserve1;

    @ApiModelProperty(value = "扩展字段2")
    @TableField("reserve2")
    private String reserve2;

    @TableField("project_id")
    private String projectId;

    @TableField("file_path")
    private String filePath;

    @TableField("update_time")
    private Timestamp updateTime;


}
