package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.PropertyBlockMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.PropertyDefMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.PropertyBlock;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.PropertyDef;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.PropertyBlockProVo;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IPropertyBlockService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 属性块 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service("PropertyBlockImpl")
public class PropertyBlockImpl extends ServiceImpl<PropertyBlockMapper, PropertyBlock> implements IPropertyBlockService {

    @Autowired
    private PropertyBlockMapper propertyBlockMapper;

    @Autowired
    private PropertyDefMapper propertyDefMapper;


    /**
     * 添加属性块信息
     *
     * @param vo
     * @param user
     * @return int
     * @Date 2023-02-17
     * @auther eomer
     */
    @Override
    public int insert(@Valid PropertyBlockProVo vo, User user) {
        int result = 0;
        List<PropertyBlock> propertyBlock = vo.getPropertyBlock();
        for (PropertyBlock tem : propertyBlock){
            tem.setCreateTime(JavaUtils.getTimestamp());
            tem.setCreateUserId(user.getUserid());
            PropertyBlock block = GsonUtil.ObjectToEntity(tem,PropertyBlock.class);
            if (JavaUtils.isNotEmtryOrNull(block.getPkid())){
                QueryWrapper queryWrapper=new QueryWrapper();
                queryWrapper.eq("pkid",block.getPkid());
                queryWrapper.eq("project_id",block.getProjectId());
                PropertyBlock propertyBlock1 = propertyBlockMapper.selectOne(queryWrapper);
                if (propertyBlock1 != null){
                    block.setCreateTime(propertyBlock1.getCreateTime());
                    block.setCreateUserId(propertyBlock1.getCreateUserId());
                    result = propertyBlockMapper.update(block,queryWrapper);
                    continue;
                }
            }
            block.setCreateTime(JavaUtils.getTimestamp());
            result = propertyBlockMapper.insert(block);
        }

        List<PropertyDef> propertyDef = vo.getPropertyDef();
        for (PropertyDef def : propertyDef){
            PropertyDef tdef = GsonUtil.ObjectToEntity(def,PropertyDef.class);
            if (JavaUtils.isNotEmtryOrNull(tdef.getPkid())){
                QueryWrapper queryWrapper=new QueryWrapper();
                queryWrapper.eq("pkid",def.getPkid());
                queryWrapper.eq("project_id",def.getProjectId());
                PropertyDef propertyDef1 = propertyDefMapper.selectOne(queryWrapper);
                if (propertyDef != null){
                    tdef.setCreateTime(propertyDef1.getCreateTime());
                    tdef.setCreateUserId(propertyDef1.getCreateUserId());
                    result = propertyDefMapper.update(tdef,queryWrapper);
                    continue;
                }
            }
            tdef.setCreateTime(JavaUtils.getTimestamp());
            result = propertyDefMapper.insert(tdef);
        }

        return result;
    }

    /**
    * 删除属性块信息
    *
    * @param propertyBlockId
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int delete(String propertyBlockId) {
        return propertyBlockMapper.deleteById(propertyBlockId);
    }

    /**
    * 更新属性块信息
    *
    * @param propertyBlock
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int update(PropertyBlock propertyBlock) {
        return propertyBlockMapper.updateById(propertyBlock);
    }

    /**
    * 全部查询
    *
    * @param propertyBlock
    * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.PropertyBlock>
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public List<PropertyBlock> list(PropertyBlock propertyBlock) {

        QueryWrapper<PropertyBlock> queryWrapper = new QueryWrapper<>();

        return propertyBlockMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<PropertyBlock> propertyBlockIPage = new Page<>();
        propertyBlockIPage.setCurrent(current);
        propertyBlockIPage.setSize(size);

        QueryWrapper<PropertyBlock> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return propertyBlockMapper.selectPage(propertyBlockIPage, queryWrapper);
    }

    @Override
    public PropertyBlockProVo getInfo(String templateId, String belongBlockDefType, String projectId) {
        PropertyBlockProVo vo = new PropertyBlockProVo();
        QueryWrapper<PropertyBlock> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("belong_block_def_type", belongBlockDefType);
        queryWrapper.eq("template_id", templateId);
        queryWrapper.eq("project_id", projectId);
        List<PropertyBlock> blockList = new ArrayList<>();
        List<PropertyBlock> blockLists = propertyBlockMapper.selectList(queryWrapper);
        for (PropertyBlock propertyBlockTem : blockLists) {
            if ("块名称".equals(propertyBlockTem.getPropertyBlock())) {
                blockList.add(0,propertyBlockTem);
            } else {
                blockList.add(propertyBlockTem);
            }

        }
        if (JavaUtils.isEmtryOrNull(blockList)) {
            QueryWrapper<PropertyBlock> qbt = new QueryWrapper<>();
            qbt.eq("belong_block_def_type", belongBlockDefType);
            qbt.eq("template_id", "000-000");
            List<PropertyBlock> blockTemplatesList = propertyBlockMapper.selectList(qbt);

            for (PropertyBlock blockTemplate : blockTemplatesList) {
                PropertyBlock block = GsonUtil.ObjectToEntity(blockTemplate,PropertyBlock.class);
                block.setPkid(null);
                blockList.add(block);
            }
        }
        QueryWrapper<PropertyDef> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("belong_block_def_type", belongBlockDefType);
        queryWrapper1.eq("template_id", templateId);
        queryWrapper1.eq("project_id", projectId);
        List<PropertyDef> defList = propertyDefMapper.selectList(queryWrapper1);
        if (JavaUtils.isEmtryOrNull(defList)) {
            QueryWrapper<PropertyDef> qd = new QueryWrapper<>();
            qd.eq("belong_block_def_type", belongBlockDefType);
            qd.eq("project_id", projectId);
            qd.eq("template_id", "000-000");
            List<PropertyDef> defTemplateList = propertyDefMapper.selectList(qd);
            for (PropertyDef defTemplate : defTemplateList) {
                PropertyDef def = GsonUtil.ObjectToEntity(defTemplate, PropertyDef.class);
                def.setPkid(null);
                def.setProjectId(projectId);
                defList.add(def);
            }
        }

        vo.setPropertyDef(defList);
        vo.setPropertyBlock(blockList);
        return vo;
    }
}
