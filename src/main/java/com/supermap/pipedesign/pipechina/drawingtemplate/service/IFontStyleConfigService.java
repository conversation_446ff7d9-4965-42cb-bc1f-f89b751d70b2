package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.FontStyleConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.FontStyleConfigVo;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 文字样式配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Repository
public interface IFontStyleConfigService extends IService<FontStyleConfig> {

 /**
  * 添加文字样式配置表信息
  *
  * @param vo
  * @param userId
  * @return int
  * @Date 2023-02-18
  * @auther eomer
  */
 int insert(FontStyleConfigVo vo, User userId);

 /**
 * 删除文字样式配置表信息
 *
 * @param fontStyleConfigId
 * @return int
 * @Date 2023-02-18
 * @auther eomer
 */
 int delete(String fontStyleConfigId);

 /**
 * 更新文字样式配置表信息
 *
 * @param fontStyleConfig
 * @return int
 * @Date 2023-02-18
 * @auther eomer
 */
 int update(FontStyleConfig fontStyleConfig);

 /**
 * 全部查询
 *
 * @param fontStyleConfig
 * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.FontStyleConfig>
 * @Date 2023-02-18
 * @auther eomer
 */
 List<FontStyleConfig> list(FontStyleConfig fontStyleConfig);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-02-18
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);

 /*
  * <AUTHOR>
  * @Description
  * @Date 2023/2/9 16:29
  * @param templateId
  * 查询模板 子模块偏移距离 信息
  **/
 List<FontStyleConfig> getInfo(String templateId,String projectId);


 }
