package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.LineStyleTemplateTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.LineStyleTemplateTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ILineStyleTemplateTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 线型样式模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Service("LineStyleTemplateTemImpl")
public class LineStyleTemplateTemImpl extends ServiceImpl<LineStyleTemplateTemMapper, LineStyleTemplateTem> implements ILineStyleTemplateTemService {

    @Autowired
    private LineStyleTemplateTemMapper lineStyleTemplateTemMapper;

    /**
    * 添加线型样式模板表信息
    *
    * @param lineStyleTemplateTem
    * @return int
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public int insert(LineStyleTemplateTem lineStyleTemplateTem) {

        //lineStyleTemplateTem.setUserId(JavaUtils.getUUID36());
        //lineStyleTemplateTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return lineStyleTemplateTemMapper.insert(lineStyleTemplateTem);
    }

    /**
    * 删除线型样式模板表信息
    *
    * @param lineStyleTemplateTemId
    * @return int
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public int delete(String lineStyleTemplateTemId) {
        return lineStyleTemplateTemMapper.deleteById(lineStyleTemplateTemId);
    }

    /**
    * 更新线型样式模板表信息
    *
    * @param lineStyleTemplateTem
    * @return int
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public int update(LineStyleTemplateTem lineStyleTemplateTem) {
        return lineStyleTemplateTemMapper.updateById(lineStyleTemplateTem);
    }

    /**
    * 全部查询
    *
    * @param lineStyleTemplateTem
    * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.LineStyleTemplateTem>
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public List<LineStyleTemplateTem> list(LineStyleTemplateTem lineStyleTemplateTem) {

        QueryWrapper<LineStyleTemplateTem> queryWrapper = new QueryWrapper<>();

        return lineStyleTemplateTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<LineStyleTemplateTem> lineStyleTemplateTemIPage = new Page<>();
        lineStyleTemplateTemIPage.setCurrent(current);
        lineStyleTemplateTemIPage.setSize(size);

        QueryWrapper<LineStyleTemplateTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return lineStyleTemplateTemMapper.selectPage(lineStyleTemplateTemIPage, queryWrapper);
    }


}
