package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.TemplateDirectoryMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.TemplateDirectoryTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.TemplatefilesMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.TemplatefilesTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplateDirectory;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplateDirectoryTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.Templatefiles;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplatefilesTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ITemplatefilesTemService;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;


/**
 * <p>
 * 模板关联文件表(存储模板关联的文件。考虑到用户有可能上传压缩包，所以如果用户上传的是压缩包的话，除了将压缩包本身需要与模板进行关联以外，还会对解压后的每个文件建立起与模板的关联关系。这样做主要为了提高系统的适配性。) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service("TemplatefilesTemImpl")
public class TemplatefilesTemImpl extends ServiceImpl<TemplatefilesTemMapper, TemplatefilesTem> implements ITemplatefilesTemService {

    @Autowired
    private TemplatefilesTemMapper templatefilesTemMapper;

    @Autowired
    private TemplatefilesMapper templatefilesMapper;

    @Autowired
    private TemplateDirectoryMapper templateDirectoryMapper;

    @Autowired
    private TemplateDirectoryTemMapper templateDirectoryTemMapper;

    /**
    * 添加模板关联文件表(存储模板关联的文件。考虑到用户有可能上传压缩包，所以如果用户上传的是压缩包的话，除了将压缩包本身需要与模板进行关联以外，还会对解压后的每个文件建立起与模板的关联关系。这样做主要为了提高系统的适配性。)信息
    *
    * @param templatefilesTem
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int insert(TemplatefilesTem templatefilesTem) {

        //templatefilesTem.setUserId(JavaUtils.getUUID36());
        //templatefilesTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return templatefilesTemMapper.insert(templatefilesTem);
    }

    /**
    * 删除模板关联文件表(存储模板关联的文件。考虑到用户有可能上传压缩包，所以如果用户上传的是压缩包的话，除了将压缩包本身需要与模板进行关联以外，还会对解压后的每个文件建立起与模板的关联关系。这样做主要为了提高系统的适配性。)信息
    *
    * @param templatefilesTemId
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int delete(String templatefilesTemId) {
        return templatefilesTemMapper.deleteById(templatefilesTemId);
    }

    /**
    * 更新模板关联文件表(存储模板关联的文件。考虑到用户有可能上传压缩包，所以如果用户上传的是压缩包的话，除了将压缩包本身需要与模板进行关联以外，还会对解压后的每个文件建立起与模板的关联关系。这样做主要为了提高系统的适配性。)信息
    *
    * @param templatefilesTem
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int update(TemplatefilesTem templatefilesTem) {
        return templatefilesTemMapper.updateById(templatefilesTem);
    }

    /**
    * 全部查询
    *
    * @param templatefilesTem
    * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplatefilesTem>
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public List<TemplatefilesTem> list(TemplatefilesTem templatefilesTem) {

        QueryWrapper<TemplatefilesTem> queryWrapper = new QueryWrapper<>();

        return templatefilesTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<TemplatefilesTem> templatefilesTemIPage = new Page<>();
        templatefilesTemIPage.setCurrent(current);
        templatefilesTemIPage.setSize(size);

        QueryWrapper<TemplatefilesTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return templatefilesTemMapper.selectPage(templatefilesTemIPage, queryWrapper);
    }

    @Override
    public void addTemplateFiles(TemplatefilesTem templatefilesTem) {
        String temCode = templatefilesTem.getTemplateBlockCode();
        if (temCode.contains("-")){
            temCode = temCode.split("-")[0];
        }
        if (JavaUtils.isNotEmtryOrNull(templatefilesTem.getProjectId())){
            Templatefiles templatefiles = GsonUtil.ObjectToEntity(templatefilesTem, Templatefiles.class);
            QueryWrapper<TemplateDirectory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_id",templatefilesTem.getProjectId());
            queryWrapper.eq("tem_code",temCode);
            queryWrapper.eq("template_id",templatefilesTem.getTemplateId());
            TemplateDirectory templateDirectory = templateDirectoryMapper.selectOne(queryWrapper);
            if (templateDirectory != null){
                templatefiles.setTemplateBlockName(templateDirectory.getTemName());
            }
            QueryWrapper<Templatefiles> fileQuery = new QueryWrapper<>();
            fileQuery.eq("project_id",templatefiles.getProjectId());
            fileQuery.eq("template_id",templatefiles.getTemplateId());
            fileQuery.eq("template_block_code", templatefiles.getTemplateBlockCode());
            Templatefiles templatefiles1 = templatefilesMapper.selectOne(fileQuery);
            if (templatefiles1 == null){
                templatefiles.setPkid(UUID.randomUUID().toString());
                templatefilesMapper.insert(templatefiles);
            }else {
                templatefiles1.setFileId(templatefiles.getFileId());
                templatefiles1.setUpdataTime(JavaUtils.getTimestamp());
                templatefiles1.setUpdataUserId(templatefiles.getUpdataUserId());
                templatefilesMapper.update(templatefiles1,fileQuery);
            }
        }else {
            QueryWrapper<TemplateDirectoryTem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tem_code",temCode);
            queryWrapper.eq("template_id",templatefilesTem.getTemplateId());
            TemplateDirectoryTem templateDirectory = templateDirectoryTemMapper.selectOne(queryWrapper);
            if (templateDirectory != null){
                templatefilesTem.setTemplateBlockName(templateDirectory.getTemName());
            }
            QueryWrapper<TemplatefilesTem> fileTemQuery = new QueryWrapper<>();
            fileTemQuery.eq("template_id",templatefilesTem.getTemplateId());
            fileTemQuery.eq("template_block_code", templatefilesTem.getTemplateBlockCode());
            TemplatefilesTem templatefilesTem1 = templatefilesTemMapper.selectOne(fileTemQuery);
            if (templatefilesTem1 == null){
                templatefilesTem.setPkid(UUID.randomUUID().toString());
                templatefilesTem.setUpdataTime(JavaUtils.getTimestamp());
                templatefilesTemMapper.insert(templatefilesTem);
            }else {
                templatefilesTem.setPkid(templatefilesTem1.getPkid());
                templatefilesTem.setUpdataTime(JavaUtils.getTimestamp());
                templatefilesTemMapper.update(templatefilesTem,fileTemQuery);
            }
        }
    }
}
