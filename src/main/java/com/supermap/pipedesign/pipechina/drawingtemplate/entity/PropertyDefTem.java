package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * <p>
 * 块属性定义模板 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("block_property_def_tem")
@ApiModel(value="PropertyDefTem对象", description="块属性定义模板")
public class PropertyDefTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "块属性")
    @TableField("block_property")
    private String blockProperty;

    @ApiModelProperty(value = "属性名称")
    @TableField("property_name")
    private String propertyName;

    @ApiModelProperty(value = "属块标记")
    @TableField("property_mark")
    private String propertyMark;

    @ApiModelProperty(value = "属性默认值")
    @TableField("default_value")
    private String defaultValue;

    @ApiModelProperty(value = "所属块定义")
    @TableField("belong_block_def_type")
    private String belongBlockDefType;

    @TableField("create_user_id")
    private String createUserId;

    @TableField("create_time")
    private Timestamp createTime;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "模板id")
    @TableField("template_id")
    private String templateId;

    @ApiModelProperty(value = "所在表格中行数")
    @TableField("table_row")
    private Double tableRow;

    @ApiModelProperty(value = "所在表格中列数")
    @TableField("table_column")
    private Double tableColumn;


}
