package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.ComponentsType;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * GJTYPE构件库包含构件类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Repository
public interface IComponentsTypeService extends IService<ComponentsType> {

 /**
 * 添加GJTYPE构件库包含构件类型信息
 *
 * @param componentsType
 * @return int
 * @Date 2023-03-22
 * @auther eomer
 */
 int insert(ComponentsType componentsType);

 /**
 * 删除GJTYPE构件库包含构件类型信息
 *
 * @param componentsTypeId
 * @return int
 * @Date 2023-03-22
 * @auther eomer
 */
 int delete(String componentsTypeId);

 /**
 * 更新GJTYPE构件库包含构件类型信息
 *
 * @param componentsType
 * @return int
 * @Date 2023-03-22
 * @auther eomer
 */
 int update(ComponentsType componentsType);

 /**
 * 全部查询
 *
 * @param componentsType
 * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.ComponentsType>
 * @Date 2023-03-22
 * @auther eomer
 */
 List<ComponentsType> list(ComponentsType componentsType);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-22
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
