package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplateDirectory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.TemplatesTreeVo;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 模板树-系统 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@Repository
public interface ITemplateDirectoryService extends IService<TemplateDirectory> {



    List<TemplatesTreeVo> selTemTree(String templateId, String projectid);

 int updateEnable(String nodeId, boolean flage, String projectid);
}
