package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.MultilingualMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.MultilingualTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.Multilingual;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.MultilingualTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IMultilingualTemService;
import com.supermap.pipedesign.pipechina.file.impl.FileUpLoadUtils;
import com.supermap.tools.file.PathUtils;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 多语言管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Service("MultilingualTemImpl")
public class MultilingualTemImpl extends ServiceImpl<MultilingualTemMapper, MultilingualTem> implements IMultilingualTemService {

    @Autowired
    private MultilingualTemMapper multilingualtemMapper;

    @Resource(name = "FileUpLoadUtils")
    private FileUpLoadUtils fileUpLoadUtils;


    private final String filePath= PathUtils.getDbUploadPath();

    @Autowired
    private MultilingualMapper multilingualMapper;

    /**
    * 添加多语言管理表信息
    *
    * @param multilingual
    * @return int
    * @Date 2023-02-10
    * @auther eomer
    */
    @Override
    public int insert(MultilingualTem multilingual, String userId) {

        multilingual.setCreatetime(new Timestamp(System.currentTimeMillis()));
        multilingual.setCreateuserid(userId);
        return multilingualtemMapper.insert(multilingual);
    }

    /**
    * 删除多语言管理表信息
    *
    * @param multilingualId
    * @return int
    * @Date 2023-02-10
    * @auther eomer
    */
    @Override
    public int delete(String multilingualId) {
        MultilingualTem multilingual = multilingualtemMapper.selectById(multilingualId);
        multilingual.setEnable(false);
        return multilingualtemMapper.updateById(multilingual);
    }

    /**
    * 更新多语言管理表信息
    *
    * @param multilingual
    * @return int
    * @Date 2023-02-10
    * @auther eomer
    */
    public int update(MultilingualTem multilingual) {
        return multilingualtemMapper.updateById(multilingual);
    }

    /**
    * 全部查询
    *
    * @param multilingual
    * @return java.util.List<com.supermap.pipedesign.pipechina.engineering.entity.Multilingual>
    * @Date 2023-02-10
    * @auther eomer
    */
    @Override
    public List<MultilingualTem> list(MultilingualTem multilingual) {

        QueryWrapper<MultilingualTem> queryWrapper = new QueryWrapper<>();

        return multilingualtemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-10
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size) {

        IPage<MultilingualTem> multilingualIPage = new Page<>();
        multilingualIPage.setCurrent(current);
        multilingualIPage.setSize(size);

        QueryWrapper<MultilingualTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable",true);

        return multilingualtemMapper.selectPage(multilingualIPage, queryWrapper);
    }

    @Override
    public MultilingualTem getInfoById(String pkid) {
        return multilingualtemMapper.selectById(pkid);
    }

    /**
     * @param file 文件
     * @param pkid 语言id
     * 导入多语言
     * <AUTHOR>
     * @Description
     * @Date 2023/2/13 11:45
     */
    @Override
    public int mulitImport(MultipartFile file, String pkid) {
        File fil = fileUpLoadUtils.uploadRetrunPath(file, filePath);
        MultilingualTem multilingual = multilingualtemMapper.selectById(pkid);
        multilingual.setMultname(fil.getName());
        multilingual.setFilepath(fil.getPath());

        return multilingualtemMapper.updateById(multilingual);
    }

    /**
     * 复制多语言到项目
     * <AUTHOR>
     * @Description
     * @Date 2023/2/22 15:48
     */
    @Override
    public void copyMulToPro(String projectId) {
        QueryWrapper<MultilingualTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable",true);
        List<MultilingualTem> temList = multilingualtemMapper.selectList(queryWrapper);
        for (MultilingualTem tem : temList){
            Multilingual mul = GsonUtil.ObjectToEntity(tem, Multilingual.class);
            mul.setProjectid(projectId);
            mul.setPkid(null);
            multilingualMapper.insert(mul);
        }
    }


    @Override
    public Multilingual downloadMulit(String pkid, String projectId) {
        QueryWrapper<Multilingual> multilingualQueryWrapper = new QueryWrapper<>();
        multilingualQueryWrapper.eq("pkid",pkid);
        multilingualQueryWrapper.eq("project_id",projectId);
        return multilingualMapper.selectOne(multilingualQueryWrapper);
    }

    @Override
    public MultilingualTem downloadMulitTem(String pkid) {
        return multilingualtemMapper.selectById(pkid);
    }
}
