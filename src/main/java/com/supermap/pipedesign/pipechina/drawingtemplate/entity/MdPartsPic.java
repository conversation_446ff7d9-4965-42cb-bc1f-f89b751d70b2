package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 部件表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_parts_pic")
@ApiModel(value="MdPartsPic对象", description="部件表")
public class MdPartsPic implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "贴图id")
    @TableField(value = "chartlet_id")
    private String chartletId;

    @ApiModelProperty(value = "项目编号(项目编号为0时，为平台级实体定义)")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "部件id")
    @TableField("parts_id")
    private String partsId;

    @ApiModelProperty(value = "目录")
    @TableField("pic_url")
    private String picUrl;

    @ApiModelProperty(value = "文件名")
    @TableField("file_name")
    private String fileName;

    @ApiModelProperty(value = "贴图名称")
    @TableField("pic_name")
    private String picName;

    @ApiModelProperty(value = "位置:1上(外),2下(内),3左,4右,5前,6后")
    @TableField("pic_seat")
    private Integer picSeat;


}
