package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.BlockTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.BlockTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplatefilesTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IBlockTemService;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ITemplatefilesTemService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.service.IWorkbagFileService;
import com.supermap.pipedesign.pipechina.file.impl.FileUpLoadUtils;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.file.PathUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.UUID;


/**
 * <p>
 * 基本块模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service("BlockTemImpl")
public class BlockTemImpl extends ServiceImpl<BlockTemMapper, BlockTem> implements IBlockTemService {

    @Autowired
    private BlockTemMapper blockTemMapper;

    @Resource(name = "WorkbagFileImpl")
    private IWorkbagFileService workbagFileImpl;

    @Resource(name = "TemplatefilesTemImpl")
    private ITemplatefilesTemService templatefilesTemImpl;

    /**
     * 更新基本块信息
     * @param blockTem
     * @return int
     * @Date 2023-02-17
     * @auther yxt
     */
    @Override
    public int insert(BlockTem blockTem, User user) {
        blockTem.setCreateUserId(user.getUserid());
        blockTem.setCreateTime(JavaUtils.getTimestamp());
        if (JavaUtils.isNotEmtryOrNull(blockTem.getPkid())) {
            updatPath(blockTem);
            BlockTem block1 = blockTemMapper.selectById(blockTem.getPkid());
            if (block1 != null) {
                return blockTemMapper.updateById(blockTem);
            }
        }
        blockTem.setPkid(UUID.randomUUID().toString());
        updatPath(blockTem);
        return blockTemMapper.insert(blockTem);
    }

    public BlockTem updatPath(BlockTem tem){
        String newPath = "";
        if (JavaUtils.isNotEmtryOrNull(tem.getCompassFilePath())){
            File file = new File(tem.getCompassFilePath());
            newPath = tem.getCompassFilePath().replace(file.getName(),tem.getPkid()+"-compass.dwg");
            File newFile = new File(newPath);
            file.renameTo(newFile);
            file.delete();
            tem.setCompassFilePath(newPath);
        }
        if (JavaUtils.isNotEmtryOrNull(tem.getIndexFilePath())){
            File file = new File(tem.getIndexFilePath());
            newPath = tem.getIndexFilePath().replace(file.getName(),tem.getPkid()+"-index.dwg");
            File newFile = new File(newPath);
            file.renameTo(newFile);
            file.delete();
            tem.setIndexFilePath(newPath);
        }
        if (JavaUtils.isNotEmtryOrNull(tem.getLegendFilePath())){
            File file = new File(tem.getLegendFilePath());
            newPath = tem.getLegendFilePath().replace(file.getName(),tem.getPkid()+"-legend.dwg");
            File newFile = new File(newPath);
            file.renameTo(newFile);
            file.delete();
            tem.setLegendFilePath(newPath);
        }


        return tem;
    }

    @Override
    public BlockTem getInfo(String templateId) {
        QueryWrapper<BlockTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id",templateId);

        BlockTem blockTem = blockTemMapper.selectOne(queryWrapper);
        if (blockTem == null){
            QueryWrapper<BlockTem> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.eq("template_id","000-000");
            blockTem = blockTemMapper.selectOne(queryWrapper1);
            blockTem.setPkid(null);
        }

        return blockTem;
    }


    private final String filePath=PathUtils.getDbUploadPath();

    @Resource(name = "FileUpLoadUtils")
    private FileUpLoadUtils fileUpLoadUtils;

    @Override
    public int uploadTem(MultipartFile file, User user, String pkid, String name, String temCode) {
        BlockTem blockTem = blockTemMapper.selectById(pkid);
        blockTem.setCreateUserId(user.getUserid());
        blockTem.setCreateTime(JavaUtils.getTimestamp());
        BlockTem blockTem1 = blockTemMapper.selectById(pkid);
        //路径
        String fileToPath = PathUtils.getDbUploadPath()+File.separator+JavaUtils.getNowDate2()+File.separator+"drawtemplate";
        fileToPath = fileToPath+File.separator+pkid;
        String oldName = file.getOriginalFilename();
        File fil = fileUpLoadUtils.uploadRetrunFile(file, fileToPath);
        String newName = fil.getName();
        String filPath = fil.getPath();

        if ("指北针".equals(name)){
            temCode = temCode+"-compass";
            blockTem.setCompassName(fil.getName());
            blockTem.setCompassFilePath(fil.getPath());
        } else if ("索引图".equals(name)){
            temCode = temCode+"-index";
            blockTem.setIndexChartName(fil.getName());
            blockTem.setIndexFilePath(fil.getPath());
        } else if ("图例".equals(name)){
            temCode = temCode+"-legend";
            blockTem.setLegendChartName(fil.getName());
            blockTem.setLegendFilePath(fil.getPath());
        }

        //添加到文件清单中
        String fileId = workbagFileImpl.dwgToWorkBag(null, blockTem.getTemplateId(), temCode, oldName, filPath, user);
        //添加到模板关联文件表
        TemplatefilesTem tf = new TemplatefilesTem();
        tf.setFileName(oldName);
        tf.setFileUuidName(newName);
        tf.setTemplateId(blockTem.getTemplateId());
        tf.setTemplateBlockCode(temCode);
        tf.setFileId(fileId);
        tf.setUpdataUserId(user.getPkid());
        templatefilesTemImpl.addTemplateFiles(tf);
        return blockTemMapper.updateById(blockTem);
    }

    @Override
    public String downloadTem(String pkid, String name) {
        BlockTem blockTem = blockTemMapper.selectById(pkid);
        if (name.equals("指北针")){
            return blockTem.getCompassFilePath();
        }
        if (name.equals("索引图")){
            return blockTem.getIndexFilePath();
        }
        if (name.equals("图例")){
            return blockTem.getLegendFilePath();
        }
        return null;
    }
}
