package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.ComponentsTypeTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * GJTYPE构件库包含构件类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Repository
public interface IComponentsTypeTemService extends IService<ComponentsTypeTem> {

 /**
 * 添加GJTYPE构件库包含构件类型信息
 *
 * @param componentsTypeTem
 * @return int
 * @Date 2023-03-22
 * @auther eomer
 */
 int insert(ComponentsTypeTem componentsTypeTem);

 /**
 * 删除GJTYPE构件库包含构件类型信息
 *
 * @param componentsTypeTemId
 * @return int
 * @Date 2023-03-22
 * @auther eomer
 */
 int delete(String componentsTypeTemId);

 /**
 * 更新GJTYPE构件库包含构件类型信息
 *
 * @param componentsTypeTem
 * @return int
 * @Date 2023-03-22
 * @auther eomer
 */
 int update(ComponentsTypeTem componentsTypeTem);

 /**
 * 全部查询
 *
 * @param componentsTypeTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.ComponentsTypeTem>
 * @Date 2023-03-22
 * @auther eomer
 */
 List<ComponentsTypeTem> list(ComponentsTypeTem componentsTypeTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-22
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
