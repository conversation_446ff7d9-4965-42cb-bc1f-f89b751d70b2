package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.MdPartsTem;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 部件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Repository
public interface IMdPartsTemService extends IService<MdPartsTem> {

 /**
 * 添加部件表信息
 *
 * @param mdPartsTem
 * @return int
 * @Date 2023-02-18
 * @auther eomer
 */
 MdPartsTem insert(MdPartsTem mdPartsTem, User userId);

 /**
  * 删除部件表信息
  *
  * @param mdPartsTemId
  * @return int
  * @Date 2023-02-18
  * @auther eomer
  */
 int delete(String mdPartsTemId);

 /**
 * 更新部件表信息
 *
 * @param
 * @return int
 * @Date 2023-02-18
 * @auther eomer
 */
 MdPartsTem update(MdPartsTem mdPartsTem, User userId);

 /**
  * 分页查询
  *
  * @param current
  * @param size
  * @param searchName
  * @param partstype
  * @return com.baomidou.mybatisplus.core.metadata.IPage
  * @Date 2023-02-18
  * @auther eomer
  */
 IPage pageList(long current, long size, String searchName, String partstype);

 /*
  * <AUTHOR>
  * @Description
  * @Date 2023/2/8 11:21
  * 部件表查询
  **/
 List<MdPartsTem> selectParts(String partsType);


 /**下载db
  * <AUTHOR>
  * @Description
  * @Date 2023/3/18 11:23
  **/
 String checkOut();

 /**
  * 查看
  * @param userId
  * @return
  */
 Map<String, Object> checrDbVersion(String userId);

 /**
  * 上传db
  * <AUTHOR>
  * @Description
  * @Date 2023/3/18 14:08
  **/
 Map<String,Object> uploadPartsTem(MultipartFile file, User userId);


/**
 * 部件查看详情
 * <AUTHOR>
 * @Description
 * @Date 2023/3/20 16:51
 **/
 MdPartsTem selInfo(String pkid);


 Map<String, List<String>> selInfoById(String partsId);
}
