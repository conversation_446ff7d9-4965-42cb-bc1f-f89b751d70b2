package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.ColumnConfigTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.ColumnDefineTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.ColumnConfigTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.ColumnConfigTemVo;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IColumnConfigTemService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.tools.base.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 图纸栏目配置表(该表中的数据来自于column_define(栏目定义)，存储具体每个项目的图纸栏目展示信息。) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Service("ColumnConfigTemImpl")
public class ColumnConfigTemImpl extends ServiceImpl<ColumnConfigTemMapper, ColumnConfigTem> implements IColumnConfigTemService {

    @Autowired
    private ColumnConfigTemMapper columnConfigTemMapper;

    @Autowired
    private ColumnDefineTemMapper columnDefineTemMapper;

    /**
    * 添加图纸栏目配置表(该表中的数据来自于column_define(栏目定义)，存储具体每个项目的图纸栏目展示信息。)信息
    *
    * @param vo
    * @return int
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public int insert(ColumnConfigTemVo vo, User user) {

        int result = 0;
        List<ColumnConfigTem> columList = vo.getColumList();
        for (ColumnConfigTem tem : columList) {
            tem.setIsDraw(true);
            tem.setCreateTime(JavaUtils.getTimestamp());
            tem.setCreateUserId(user.getUserid());
            columnConfigTemMapper.updateById(tem);
        }

        List<ColumnConfigTem> definesList = vo.getDefinesList();
        for (ColumnConfigTem tem1 : definesList) {
            tem1.setIsDraw(false);
            tem1.setCreateTime(JavaUtils.getTimestamp());
            tem1.setCreateUserId(user.getUserid());
            columnConfigTemMapper.updateById(tem1);

        }
        return result;
    }

    @Override
    public ColumnConfigTemVo getInfo(String templateId) {
        ColumnConfigTemVo vo = new ColumnConfigTemVo();
        QueryWrapper<ColumnConfigTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        queryWrapper.eq("is_draw",true);
        queryWrapper.orderByAsc("sort");
        List<ColumnConfigTem> columnConfigTems = columnConfigTemMapper.selectList(queryWrapper);

        QueryWrapper<ColumnConfigTem> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("template_id", templateId);
        queryWrapper1.eq("is_draw",false);
        queryWrapper1.orderByAsc("sort");
        List<ColumnConfigTem> columnConfigTemss = columnConfigTemMapper.selectList(queryWrapper1);

        vo.setColumList(columnConfigTems);
        vo.setDefinesList(columnConfigTemss);
        return vo;
    }
}
