package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.LineStyleConfigTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.LineStyleTemplateTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.LineStyleConfigTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.LineStyleTemplateTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.LineStyleConfigTemVo;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ILineStyleConfigTemService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 线型样式配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Service("LineStyleConfigTemImpl")
public class LineStyleConfigTemImpl extends ServiceImpl<LineStyleConfigTemMapper, LineStyleConfigTem> implements ILineStyleConfigTemService {

    @Autowired
    private LineStyleConfigTemMapper lineStyleConfigTemMapper;

    @Autowired
    private LineStyleTemplateTemMapper lineStyleTemplateTemMapper;

    /**
    * 添加线型样式配置表信息
    *
    * @param lineStyleConfigTem
    * @return int
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public int insert(LineStyleConfigTemVo lineStyleConfigTem, User user) {

        int result = 0;
        List<LineStyleConfigTem> temList = lineStyleConfigTem.getTemList();
        for (LineStyleConfigTem tem : temList){
            tem.setCreateTime(JavaUtils.getTimestamp());
            tem.setCreateUserId(user.getUserid());
            if (JavaUtils.isNotEmtryOrNull(tem.getPkid())){
                LineStyleConfigTem lineStyleConfigTem1 = lineStyleConfigTemMapper.selectById(tem.getPkid());
                if (lineStyleConfigTem1 != null){
                    result = lineStyleConfigTemMapper.updateById(tem);
                    continue;
                }
            }
            result = lineStyleConfigTemMapper.insert(tem);
        }
        return result;
    }

    @Override
    public List<LineStyleConfigTem> getInfo(String templateId) {
        QueryWrapper<LineStyleConfigTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id",templateId);
        List<LineStyleConfigTem> lineStyleConfigTems = lineStyleConfigTemMapper.selectList(queryWrapper);
        if (JavaUtils.isEmtryOrNull(lineStyleConfigTems)){
            QueryWrapper<LineStyleTemplateTem> queryWrapper1 = new QueryWrapper<>();
            List<LineStyleTemplateTem> lineStyleTemplateTems = lineStyleTemplateTemMapper.selectList(queryWrapper1);
            for (LineStyleTemplateTem tem : lineStyleTemplateTems){
                LineStyleConfigTem linec = GsonUtil.ObjectToEntity(tem,LineStyleConfigTem.class);
                linec.setPkid(null);
                lineStyleConfigTems.add(linec);
            }
        }
        return lineStyleConfigTems;
    }
}
