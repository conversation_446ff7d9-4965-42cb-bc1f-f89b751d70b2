package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * <p>
 * 图纸模板定位配置表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("drawing_template_locate")
@ApiModel(value="TemplateLocate对象", description="图纸模板定位配置表")
public class TemplateLocate implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "图幅高")
    @TableField("frame_height")
    private Double frameHeight;

    @ApiModelProperty(value = "图幅宽")
    @TableField("frame_width")
    private Double frameWidth;

    @ApiModelProperty(value = "图幅名称")
    @TableField("frame_name")
    private String frameName;

    @ApiModelProperty(value = "图幅加长长度")
    @TableField("frame_ext_length")
    private String frameExtLength;

    @ApiModelProperty(value = "左侧装订边框宽度")
    @TableField("left_bbb")
    private Double leftBbb;

    @ApiModelProperty(value = "其他三边边框宽度")
    @TableField("other_tbw")
    private Double otherTbw;

    @ApiModelProperty(value = "模板基点X坐标")
    @TableField("template_base_xcoord")
    private Double templateBaseXcoord;

    @ApiModelProperty(value = "模板基点Y坐标")
    @TableField("template_base_ycoord")
    private Double templateBaseYcoord;

    @ApiModelProperty(value = "模板基点Z坐标")
    @TableField("template_base_zcoord")
    private Double templateBaseZcoord;

    @ApiModelProperty(value = "图框外框左侧边框到绘图区左侧边长度")
    @TableField("length_bobald")
    private Double lengthBobald;

    @ApiModelProperty(value = "图框外框左侧边框到绘图区右侧边长度")
    @TableField("length_bobard")
    private Double lengthBobard;

    @ApiModelProperty(value = "左纵线X偏移（想对于图纸左下角）")
    @TableField("left_vloox")
    private Double leftVloox;

    @ApiModelProperty(value = "右纵线X偏移（想对于图纸右下角）")
    @TableField("right_vloox")
    private Double rightVloox;

    @ApiModelProperty(value = "平面区域高")
    @TableField("plane_area_height")
    private Double planeAreaHeight;

    @ApiModelProperty(value = "平面区域Y偏移（相对于图纸左下角）")
    @TableField("plane_aooy")
    private Double planeAooy;

    @ApiModelProperty(value = "纵断面区域高")
    @TableField("vertical_sah")
    private Double verticalSah;

    @ApiModelProperty(value = "纵断面区域Y偏移（相对于图纸左下角）")
    @TableField("vertical_saooy")
    private Double verticalSaooy;

    @ApiModelProperty(value = "纵断面区域里程刻度线高")
    @TableField("vertical_samtm")
    private Double verticalSamtm;

    @ApiModelProperty(value = "纵断面区域里程刻度线高")
    @TableField("column_area_height")
    private Double columnAreaHeight;

    @ApiModelProperty(value = "栏目区域Y偏移（想对于图纸左下角）")
    @TableField("column_aooy")
    private Double columnAooy;

    @ApiModelProperty(value = "栏目区域栏目序号宽度（0不绘制序号）")
    @TableField("column_asw")
    private Double columnAsw;

    @ApiModelProperty(value = "平面区域左接图栏宽度")
    @TableField("column_albw")
    private Double columnAlbw;

    @ApiModelProperty(value = "平面区域右接图栏宽度")
    @TableField("column_arbw")
    private Double columnArbw;

    @ApiModelProperty(value = "纵断面区域左接图栏宽度")
    @TableField("vertical_slbw")
    private Double verticalSlbw;

    @ApiModelProperty(value = "纵断面区域右接图栏宽度")
    @TableField("vertical_srbw")
    private Double verticalSrbw;

    @ApiModelProperty(value = "里程刻度（米）")
    @TableField("mileage_scale")
    private Double mileageScale;

    @ApiModelProperty(value = "里程刻度2（米），要求是里程刻度的整数倍")
    @TableField("mileage_scale2")
    private Double mileageScale2;

    @ApiModelProperty(value = "高程刻度（米）")
    @TableField("elevation_scale")
    private Double elevationScale;

    @ApiModelProperty(value = "高程刻度2（米），要求是里程刻度的整数倍")
    @TableField("elevation_scale2")
    private Double elevationScale2;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_user_id")
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Timestamp createTime;

    @ApiModelProperty(value = "模板id")
    @TableField("template_id")
    private String templateId;

    @ApiModelProperty(value = "所属项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mId;

    @ApiModelProperty(value = "里程刻度尺是否存在")
    @TableField("mileage_mark")
    private Double mileageMark;

    @ApiModelProperty(value = "高程刻度尺是否存在")
    @TableField("elevation_mark")
    private Double elevationMark;

    @ApiModelProperty(value = "横断面区域高度")
    @TableField("crosssection_sah")
    private Double crosssectionSah;

    @ApiModelProperty(value = "横断面区域Y偏移（相对于图纸左下角）")
    @TableField("crosssection_saooy")
    private Double crosssectionSaooy;

    @ApiModelProperty(value = "专题图图例宽度")
    @TableField("legend_otaw")
    private Double legendOtaw;

    @ApiModelProperty(value = "专题图图例高度")
    @TableField("legend_otah")
    private Double legendOtah;

    @ApiModelProperty(value = "专题图图例X偏移（相对于图纸左下角）")
    @TableField("legend_otaoox")
    private Double legendOtaoox;

    @ApiModelProperty(value = "专题图图例Y偏移（相对于图纸左下角）")
    @TableField("legend_otaooy")
    private Double legendOtaooy;

    @ApiModelProperty(value = "专题图比例尺单位")
    @TableField("scale_otunit")
    private Double scaleOtunit;

    @ApiModelProperty(value = "专题图比例尺第一段长度")
    @TableField("scale_otfl")
    private Double scaleOtfl;

    @ApiModelProperty(value = "专题图比例尺X偏移（相对于图纸左下角）")
    @TableField("scale_otoox")
    private Double scaleOtoox;

    @ApiModelProperty(value = "专题图比例尺Y偏移（相对于图纸左下角）")
    @TableField("scale_otooy")
    private Double scaleOtooy;


}
