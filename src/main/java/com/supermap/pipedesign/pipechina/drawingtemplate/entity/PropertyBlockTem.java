package com.supermap.pipedesign.pipechina.drawingtemplate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * <p>
 * 属性块模板 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("property_block_tem")
@ApiModel(value = "PropertyBlockTem对象", description = "属性块模板")
public class PropertyBlockTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid", type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "属性块")
    @TableField("property_block_template")
    private String propertyBlock;

    @ApiModelProperty(value = "参考值")
    @TableField("reference_value")
    private String referenceValue;

    @ApiModelProperty(value = "所属块定义")
    @TableField("belong_block_def_type")
    private String belongBlockDefType;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_user_id")
    private String createUserId;

    @ApiModelProperty(value = "路径")
    @TableField("file_path")
    private String filePath;

    @ApiModelProperty(value = "名称")
    @TableField("file_name")
    private String fileName;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Timestamp createTime;

    @ApiModelProperty(value = "所属项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "模板id")
    @TableField("template_id")
    private String templateId;


}
