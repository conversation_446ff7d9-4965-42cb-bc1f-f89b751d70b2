package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.PropertyDef;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.PropertyDefMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IPropertyDefService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 块属性定义 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service("PropertyDefImpl")
public class PropertyDefImpl extends ServiceImpl<PropertyDefMapper, PropertyDef> implements IPropertyDefService {

    @Autowired
    private PropertyDefMapper propertyDefMapper;

    /**
    * 添加块属性定义信息
    *
    * @param propertyDef
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int insert(PropertyDef propertyDef) {

        //propertyDef.setUserId(JavaUtils.getUUID36());
        //propertyDef.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return propertyDefMapper.insert(propertyDef);
    }

    /**
    * 删除块属性定义信息
    *
    * @param propertyDefId
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int delete(String propertyDefId) {
        return propertyDefMapper.deleteById(propertyDefId);
    }

    /**
    * 更新块属性定义信息
    *
    * @param propertyDef
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int update(PropertyDef propertyDef) {
        return propertyDefMapper.updateById(propertyDef);
    }

    /**
    * 全部查询
    *
    * @param propertyDef
    * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.PropertyDef>
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public List<PropertyDef> list(PropertyDef propertyDef) {

        QueryWrapper<PropertyDef> queryWrapper = new QueryWrapper<>();

        return propertyDefMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<PropertyDef> propertyDefIPage = new Page<>();
        propertyDefIPage.setCurrent(current);
        propertyDefIPage.setSize(size);

        QueryWrapper<PropertyDef> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return propertyDefMapper.selectPage(propertyDefIPage, queryWrapper);
    }


}
