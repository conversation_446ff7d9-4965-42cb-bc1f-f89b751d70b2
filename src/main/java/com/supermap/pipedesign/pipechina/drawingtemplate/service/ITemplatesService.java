package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.Templates;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.TemplatesTrees;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFileuploadrecord;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Repository
public interface ITemplatesService extends IService<Templates> {

 /**
  * 添加模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)信息
  *
  * @param templates
  * @param userid
  * @return int
  * @Date 2023-02-17
  * @auther eomer
  */
 int insert(Templates templates, String userid);

 /**
  * 删除模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)信息
  *
  * @param templatesId
  * @param projectid
  * @return int
  * @Date 2023-02-17
  * @auther eomer
  */
 int delete(String templatesId, String projectid);

 /**
 * 更新模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)信息
 *
 * @param templates
 * @return int
 * @Date 2023-02-17
 * @auther eomer
 */
 int update(Templates templates);

 /**
 * 全部查询
 *
 * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.Templates>
 * @Date 2023-02-17
 * @auther eomer
 */
 List<Templates> selListBy(String searchName, String type, String templatetype, String modelType, String stage, String projectid);




 List<TemplatesTrees> listTree();

 TemplatesTrees getTemplatesTree(String templatesId, String type);

 List<Templates> getTimestampCombox(String projectid);

 int addTemplate(MultipartFile file, String templatetype, String type, String createuserid, String projectid);

 int upLoadDrawings(MultipartFile file, String pkid, String temCode, String projectid, User userId);



 Map<String, List<Templates>> seleByStageType(String stage, String type, String projectid);

 int addStageById(String ids, String stage, String projectid);

    WorkbagFileuploadrecord downloadModel(String pkid, String projectid);
}
