package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.ComponentsTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * GOUJIAN构件库中所有构件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Repository
public interface IComponentsTemService extends IService<ComponentsTem> {

 /**
 * 添加GOUJIAN构件库中所有构件信息
 *
 * @param componentsTem
 * @return int
 * @Date 2023-03-22
 * @auther eomer
 */
 int insert(ComponentsTem componentsTem);

 /**
 * 删除GOUJIAN构件库中所有构件信息
 *
 * @param componentsTemId
 * @return int
 * @Date 2023-03-22
 * @auther eomer
 */
 int delete(String componentsTemId);

 /**
 * 更新GOUJIAN构件库中所有构件信息
 *
 * @param componentsTem
 * @return int
 * @Date 2023-03-22
 * @auther eomer
 */
 int update(ComponentsTem componentsTem);

 /**
 * 全部查询
 *
 * @param componentsTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.ComponentsTem>
 * @Date 2023-03-22
 * @auther eomer
 */
 List<ComponentsTem> list(ComponentsTem componentsTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-22
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
