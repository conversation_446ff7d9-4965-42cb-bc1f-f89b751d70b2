package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.TemplateDirectoryMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplateDirectory;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.TemplatesTreeVo;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ITemplateDirectoryService;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 模板树-系统 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@Service("TemplateDirectoryImpl")
public class TemplateDirectoryImpl extends ServiceImpl<TemplateDirectoryMapper, TemplateDirectory> implements ITemplateDirectoryService {

    @Autowired
    private TemplateDirectoryMapper templateDirectoryMapper;

    @Override
    public List<TemplatesTreeVo> selTemTree(String templateId, String projectid) {
        QueryWrapper queryWrapper =new QueryWrapper();
        queryWrapper.eq("template_id", templateId);
        queryWrapper.eq("project_id", projectid);
        List <TemplateDirectory>list = templateDirectoryMapper.selectList(queryWrapper);
        TemplatesTreeVo templatesTreeVoss=new TemplatesTreeVo();
        List<TemplatesTreeVo> templatesTreeVos = new ArrayList();
        for (TemplateDirectory templatesTree : list) {
            templatesTreeVoss= GsonUtil.ObjectToEntity(templatesTree,TemplatesTreeVo.class);
            templatesTreeVos.add(templatesTreeVoss);
        }
        List<TemplatesTreeVo> templatesTreeVoTree=new ArrayList<TemplatesTreeVo>();
        List<TemplatesTreeVo> templatesOneTreeVo=new ArrayList<TemplatesTreeVo>();
        List<TemplatesTreeVo> templatestwoTreeVo=new ArrayList<TemplatesTreeVo>();
        List<TemplatesTreeVo> templatesthreeTreeVo=new ArrayList<TemplatesTreeVo>();
        List<TemplatesTreeVo> templatesforTreeVo=new ArrayList<TemplatesTreeVo>();
        for (TemplatesTreeVo templatesTreeVo : templatesTreeVos) {
            if ("0d189233f9cc2993359eee59daa952e0".equals(templatesTreeVo.getPid())) {
                templatesTreeVoTree.add(templatesTreeVo);
            }
        }
        for (TemplatesTreeVo templatesTreeVo : templatesTreeVos) {
            if(templatesTreeVoTree.get(0).getPkid().equals(templatesTreeVo.getPid())){
                templatesOneTreeVo.add(templatesTreeVo);
                templatesTreeVoTree.get(0).setChild(templatesOneTreeVo);

            }
        }
        for (TemplatesTreeVo templatesTreeVo : templatesTreeVos){
            for (TemplatesTreeVo treeVo : templatesOneTreeVo) {
                if (treeVo.getPkid().equals(templatesTreeVo.getPid())){
                    templatestwoTreeVo.add(templatesTreeVo);
                    treeVo.setChild(templatestwoTreeVo);
                }
            }
        }
        for (TemplatesTreeVo templatesTreeVo : templatesTreeVos) {
            for (TemplatesTreeVo treeVos : templatestwoTreeVo) {
                if (treeVos.getPkid().equals(templatesTreeVo.getPid())){
                    templatesthreeTreeVo.add(templatesTreeVo);
                    treeVos.setChild(templatesthreeTreeVo);
                }
            }
        }
        for (TemplatesTreeVo templatesTreeVo : templatesTreeVos) {
            for (TemplatesTreeVo treeVos : templatesthreeTreeVo) {
                if (treeVos.getPkid().equals(templatesTreeVo.getPid())){
                    templatesforTreeVo.add(templatesTreeVo);
                    treeVos.setChild(templatesforTreeVo);
                }
            }
        }
        return templatesTreeVoTree;
    }

    @Override
    public int updateEnable(String nodeId, boolean flage, String projectid) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("pkid",nodeId);
        queryWrapper.eq("project_id",projectid);
        TemplateDirectory templateDirectoryTem = templateDirectoryMapper.selectOne(queryWrapper);
        templateDirectoryTem.setTemEnable(flage);
        return templateDirectoryMapper.update(templateDirectoryTem,queryWrapper);
    }
}
