package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.MdPartsPicMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.MdPartsPic;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.MdPartsPicVo;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IMdPartsPicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;


/**
 * <p>
 * 部件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-26
 */
@Service("MdPartsPicImpl")
public class MdPartsPicImpl extends ServiceImpl<MdPartsPicMapper, MdPartsPic> implements IMdPartsPicService {

    @Autowired
    private MdPartsPicMapper mdPartsPicMapper;

    /**
    * 添加部件表信息
    *
    * @param mdPartsPic
    * @return int
    * @Date 2023-03-26
    * @auther eomer
    */
    @Override
    public int insert(MdPartsPicVo mdPartsPic) {
        List<MdPartsPic> mdPartsPicList = mdPartsPic.getMdPartsPicList();
        QueryWrapper<MdPartsPic> wrapper = new QueryWrapper<>();
        wrapper.eq("parts_id", mdPartsPicList.get(0).getPartsId());
        wrapper.eq("project_id", mdPartsPicList.get(0).getProjectId());
        mdPartsPicMapper.delete(wrapper);
        for (MdPartsPic partsPic : mdPartsPicList) {
            partsPic.setPkid(UUID.randomUUID().toString());
            mdPartsPicMapper.insert(partsPic);
        }
      return  1;
    }

    /**
    * 删除部件表信息
    *
    * @param mdPartsPicId
    * @return int
    * @Date 2023-03-26
    * @auther eomer
    */
    @Override
    public int delete(String mdPartsPicId) {
        return mdPartsPicMapper.deleteById(mdPartsPicId);
    }

    /**
    * 更新部件表信息
    *
    * @param mdPartsPic
    * @return int
    * @Date 2023-03-26
    * @auther eomer
    */
    @Override
    public int update(MdPartsPic mdPartsPic) {
        return mdPartsPicMapper.updateById(mdPartsPic);
    }

    /**
    * 全部查询
    *
    * @param mdPartsPic
    * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.MdPartsPic>
    * @Date 2023-03-26
    * @auther eomer
    */
    @Override
    public List<MdPartsPic> list(MdPartsPic mdPartsPic) {

        QueryWrapper<MdPartsPic> queryWrapper = new QueryWrapper<>();

        return mdPartsPicMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-26
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<MdPartsPic> mdPartsPicIPage = new Page<>();
        mdPartsPicIPage.setCurrent(current);
        mdPartsPicIPage.setSize(size);

        QueryWrapper<MdPartsPic> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return mdPartsPicMapper.selectPage(mdPartsPicIPage, queryWrapper);
    }


    @Override
    public List<MdPartsPic> selectById(String partsId, String projectId) {
        QueryWrapper<MdPartsPic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parts_id", partsId).eq("project_id", projectId);
        return mdPartsPicMapper.selectList(queryWrapper);
    }
}
