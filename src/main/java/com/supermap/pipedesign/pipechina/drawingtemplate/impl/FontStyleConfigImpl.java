package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.FontStyleConfigMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.FontStyleTemplateTemMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.FontStyleConfig;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.FontStyleTemplateTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.FontStyleConfigVo;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.IFontStyleConfigService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 文字样式配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Service("FontStyleConfigImpl")
public class FontStyleConfigImpl extends ServiceImpl<FontStyleConfigMapper, FontStyleConfig> implements IFontStyleConfigService {

    @Autowired
    private FontStyleConfigMapper fontStyleConfigMapper;

    @Autowired
    private FontStyleTemplateTemMapper fontStyleTemplateTemMapper;

    /**
     * 添加文字样式配置表信息
     *
     * @param vo
     * @param user
     * @return int
     * @Date 2023-02-18
     * @auther eomer
     */
    @Override
    public int insert(FontStyleConfigVo vo, User user) {
        int reslut = 0;
        List<FontStyleConfig> fontList = vo.getFontList();
        for (FontStyleConfig fonts : fontList){
            fonts.setCreateTime(JavaUtils.getTimestamp());
            fonts.setCreateUserId(user.getUserid());
            if (JavaUtils.isNotEmtryOrNull(fonts.getPkid())){
                QueryWrapper query = new QueryWrapper();
                query.eq("pkid",fonts.getPkid());
                query.eq("project_id",fonts.getProjectId());
                FontStyleConfig fontStyleConfig = fontStyleConfigMapper.selectOne(query);
                if (fontStyleConfig != null){
                    fonts.setCreateTime(fontStyleConfig.getCreateTime());
                    fonts.setCreateUserId(fontStyleConfig.getCreateUserId());
                    QueryWrapper query1 = new QueryWrapper();
                    query1.eq("pkid",fontStyleConfig.getPkid());
                    query1.eq("project_id",fontStyleConfig.getProjectId());
                    reslut = fontStyleConfigMapper.update(fonts,query1);
                    continue;
                }
            }
            fonts.setCreateTime(JavaUtils.getTimestamp());
            reslut = fontStyleConfigMapper.insert(fonts);
        }
        return reslut;
    }

    /**
    * 删除文字样式配置表信息
    *
    * @param fontStyleConfigId
    * @return int
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public int delete(String fontStyleConfigId) {
        return fontStyleConfigMapper.deleteById(fontStyleConfigId);
    }

    /**
    * 更新文字样式配置表信息
    *
    * @param fontStyleConfig
    * @return int
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public int update(FontStyleConfig fontStyleConfig) {
        return fontStyleConfigMapper.updateById(fontStyleConfig);
    }

    /**
    * 全部查询
    *
    * @param fontStyleConfig
    * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.FontStyleConfig>
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public List<FontStyleConfig> list(FontStyleConfig fontStyleConfig) {

        QueryWrapper<FontStyleConfig> queryWrapper = new QueryWrapper<>();

        return fontStyleConfigMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-18
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<FontStyleConfig> fontStyleConfigIPage = new Page<>();
        fontStyleConfigIPage.setCurrent(current);
        fontStyleConfigIPage.setSize(size);

        QueryWrapper<FontStyleConfig> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return fontStyleConfigMapper.selectPage(fontStyleConfigIPage, queryWrapper);
    }


    @Override
    public List<FontStyleConfig> getInfo(String templateId,String projectId) {
        QueryWrapper<FontStyleConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id",templateId);
        queryWrapper.eq("project_id",projectId);
        List<FontStyleConfig> fontStyleConfigs = fontStyleConfigMapper.selectList(queryWrapper);
        if (JavaUtils.isEmtryOrNull(fontStyleConfigs)){
            QueryWrapper<FontStyleTemplateTem> queryWrapper1 = new QueryWrapper<>();
            List<FontStyleTemplateTem> fontStyleTemplateTems = fontStyleTemplateTemMapper.selectList(queryWrapper1);
            for (FontStyleTemplateTem tem : fontStyleTemplateTems){
                FontStyleConfig conf = GsonUtil.ObjectToEntity(tem,FontStyleConfig.class);
                conf.setPkid(null);
                fontStyleConfigs.add(conf);
            }
        }
        return fontStyleConfigs;
    }
}
