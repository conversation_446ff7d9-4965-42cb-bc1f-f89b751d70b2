package com.supermap.pipedesign.pipechina.drawingtemplate.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplateDirectoryTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.TemplatesTreeVo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 * 模板树-系统 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Mapper
public interface TemplateDirectoryTemMapper extends BaseMapper<TemplateDirectoryTem> {
    @Results(id="TemplatesTreeVo",value = {
            @Result(id = true,column = "pkid",property = "pkid"),
            @Result(column = "pid",property = "pid"),
            @Result(column = "tem_name",property = "temName"),
            @Result(column = "tem_code",property = "temCode"),
            @Result(column = "tem_enable",property = "temEnable"),
            @Result(column = "template_id",property = "templateId"),
            @Result(column = "project_id",property = "projectId"),
            @Result(column = "pid",property = "child",javaType = List.class,
                    many=@Many(select="com.supermap.pipedesign.pipechina.drawingtemplate.dao.TemplateDirectoryTemMapper.selTreeList"))})
    @Select("select * from pld_template_directory_tem  order by sort")
    List<TemplatesTreeVo> selTreeList();

}
