package com.supermap.pipedesign.pipechina.drawingtemplate.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.Block;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 基本块 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Mapper
public interface BlockMapper extends BaseMapper<Block> {

    @Select({
            "call sp_copy_project0304(#{projectId,mode=IN,jdbcType=VARCHAR},#{stage,mode=IN,jdbcType=VARCHAR})"
    })
    void cpDrawingTemplate(@Param("projectId") String projectId, @Param("stage") String stage);


}
