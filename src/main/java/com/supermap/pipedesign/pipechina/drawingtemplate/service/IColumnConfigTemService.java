package com.supermap.pipedesign.pipechina.drawingtemplate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.ColumnConfigTem;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.ColumnConfigTemVo;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 图纸栏目配置表(该表中的数据来自于column_define(栏目定义)，存储具体每个项目的图纸栏目展示信息。) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Repository
public interface IColumnConfigTemService extends IService<ColumnConfigTem> {

 /**
 * 添加图纸栏目配置表(该表中的数据来自于column_define(栏目定义)，存储具体每个项目的图纸栏目展示信息。)信息
 *
 * @param vo
 * @return int
 * @Date 2023-02-18
 * @auther eomer
 */
 int insert(ColumnConfigTemVo vo, User userId);

 /*
  * @param templateId 模板id
  * <AUTHOR>
  * @Description
  * @Date 2023/2/9 18:17
  * 获取栏目定义信息
  **/
 ColumnConfigTemVo getInfo(String templateId);
 }
