package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.*;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.*;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.TemplatesTrees;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ITemplatefilesTemService;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ITemplatesService;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.RefMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.UserMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.WorkbagFileuploadrecordMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.engineering.entity.Ref;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFileuploadrecord;
import com.supermap.pipedesign.pipechina.engineering.service.IUserService;
import com.supermap.pipedesign.pipechina.engineering.service.IWorkbagFileService;
import com.supermap.pipedesign.pipechina.file.impl.FileUpLoadUtils;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.file.PathUtils;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.sql.Timestamp;
import java.util.*;


/**
 * <p>
 * 模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service("TemplatesImpl")
public class TemplatesImpl extends ServiceImpl<TemplatesMapper, Templates> implements ITemplatesService {

    @Autowired
    private TemplatesMapper templatesMapper;

    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private TemplateLocateMapper templateLocateMapper;
    @Autowired
    private BlockMapper blockMapper;
    @Autowired
    private PropertyBlockMapper propertyBlockMapper;
    @Autowired
    private PropertyDefMapper propertyDefMapper;
    @Autowired
    private OffsetDistanceMapper offsetDistanceMapper;
    @Autowired
    private ColumnConfigMapper columnConfigMapper;
    @Autowired
    private FontStyleConfigMapper fontStyleConfigMapper;
    @Autowired
    private LineStyleConfigMapper lineStyleConfigMapper;

    @Resource(name = "WorkbagFileImpl")
    private IWorkbagFileService workbagFileImpl;

    @Resource(name = "TemplatefilesTemImpl")
    private ITemplatefilesTemService templatefilesTemImpl;

    @Resource(name = "UserImpl")
    private IUserService userImpl;

    @Autowired
    private TemplateLocateTemMapper templateLocateTemMapper;

    @Autowired
    private BlockTemMapper blockTemMapper;

    @Autowired
    private PropertyBlockTemMapper propertyBlockTemMapper;

    @Autowired
    private PropertyDefTemMapper propertyDefTemMapper;

    @Autowired
    private OffsetDistanceTemMapper offsetDistanceTemMapper;

    /**
     * 添加模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)信息
     *
     * @param templates
     * @param userid
     * @return int
     * @Date 2023-02-17
     * @auther eomer
     */
    @Override
    public int insert(Templates templates, String userid) {

        templates.setPkid(UUID.randomUUID().toString());
        //templatesTem.setCreateuserid(user.getPkid());
        templates.setCreatetime(new Timestamp(System.currentTimeMillis()));
        templates.setCreateuserid(userid);
        templates.setTemplatetype("1");
        templatesMapper.insert(templates);
        String pkid = templates.getPkid();
        String projectid = templates.getProjectid();
        copyTree(pkid,templates.getType(),projectid);
        copyTemplateFiles(pkid,projectid);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("template_id", "000-000");
        //新增模板定位
        TemplateLocateTem tem = templateLocateTemMapper.selectOne(queryWrapper);
        if (tem != null){
            TemplateLocate templateLocateTem = GsonUtil.ObjectToEntity(tem,TemplateLocate.class);
            templateLocateTem.setTemplateId(pkid);
            templateLocateTem.setCreateUserId(userid);
            templateLocateTem.setPkid(UUID.randomUUID().toString());
            templateLocateTem.setProjectId(projectid);
            templateLocateMapper.insert(templateLocateTem);
        }

        //新增基本块
        BlockTem blockTem = blockTemMapper.selectOne(queryWrapper);
        if (blockTem != null){
            Block block = GsonUtil.ObjectToEntity(blockTem,Block.class);
            block.setTemplateId(pkid);
            block.setCreateUserId(userid);
            block.setPkid(UUID.randomUUID().toString());
            block.setProjectId(projectid);
            blockMapper.insert(block);
        }
        List<PropertyBlockTem> list = propertyBlockTemMapper.selectList(queryWrapper);
        if (!JavaUtils.isEmtryOrNull(list)){
            for (PropertyBlockTem pblt : list){
                PropertyBlock pbtem = GsonUtil.ObjectToEntity(pblt,PropertyBlock.class);
                pbtem.setPkid(UUID.randomUUID().toString());
                pbtem.setTemplateId(pkid);
                pbtem.setCreateUserId(userid);
                pbtem.setProjectId(projectid);
                propertyBlockMapper.insert(pbtem);
            }
        }

        List<PropertyDefTem> defTemList = propertyDefTemMapper.selectList(queryWrapper);
        if (!JavaUtils.isEmtryOrNull(defTemList)){
            for (PropertyDefTem def : defTemList){
                PropertyDef ptem = GsonUtil.ObjectToEntity(def,PropertyDef.class);
                ptem.setPkid(UUID.randomUUID().toString());
                ptem.setTemplateId(pkid);
                ptem.setProjectId(projectid);
                ptem.setCreateUserId(userid);
                propertyDefMapper.insert(ptem);
            }
        }

        //新增子块偏移距离
        OffsetDistanceTem offsetDistanceTem = offsetDistanceTemMapper.selectOne(queryWrapper);
        if (offsetDistanceTem != null){
            OffsetDistance offsetd = GsonUtil.ObjectToEntity(offsetDistanceTem,OffsetDistance.class);
            offsetd.setTemplateId(pkid);
            offsetd.setPkid(UUID.randomUUID().toString());
            offsetd.setProjectId(projectid);
            offsetd.setCreateUserId(userid);
            offsetDistanceMapper.insert(offsetd);
        }


        QueryWrapper queryWrapper1=new  QueryWrapper();

        //新增栏目定义
        List<ColumnDefineTem> columnDefineTemlist = columnDefineTemMapper.selectList(queryWrapper1);
        for (Object fig : columnDefineTemlist) {
            ColumnConfig figTem = GsonUtil.ObjectToEntity(fig, ColumnConfig.class);
            figTem.setPkid(UUID.randomUUID().toString());
            figTem.setTemplateId(pkid);
            figTem.setIsDraw(false);
            figTem.setProjectId(projectid);
            figTem.setCreateUserId(userid);
            figTem.setEditenable(0);
            columnConfigMapper.insert(figTem);
        }
        //新增文字样式
        List<FontStyleTemplateTem> fontStyleTemplateTemlist = fontStyleTemplateTemMapper.selectList(queryWrapper1);
        for (Object fonts : fontStyleTemplateTemlist) {
            FontStyleConfig fontTem = GsonUtil.ObjectToEntity(fonts, FontStyleConfig.class);
            fontTem.setPkid(UUID.randomUUID().toString());
            fontTem.setTemplateId(pkid);
            fontTem.setCreateUserId(userid);
            fontStyleConfigMapper.insert(fontTem);
        }
        //新增线型样式
        List<LineStyleTemplateTem> lineStyleTemplateTemlist = lineStyleTemplateTemMapper.selectList(queryWrapper1);
        for (Object line : lineStyleTemplateTemlist) {
            LineStyleConfig lineTem = GsonUtil.ObjectToEntity(line,LineStyleConfig.class);
            lineTem.setPkid(UUID.randomUUID().toString());
            lineTem.setTemplateId(pkid);
            lineTem.setCreateUserId(userid);
            lineStyleConfigMapper.insert(lineTem);
        }
        return 1;
    }

    @Autowired
    private TemplateDirectoryTemMapper tempDirectoryTemMapper;

    public void copyTree(String templateId, String node, String projectid){
        QueryWrapper<TemplateDirectoryTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id","000-000");
        List<TemplateDirectoryTem> temList = tempDirectoryTemMapper.selectList(queryWrapper);
        if (!JavaUtils.isEmtryOrNull(temList)){
            String[] src = {"ZhuanYeQianShuLan","ZhuanJiaoDianBiao","GaoCheng","PingMianZuo","DuanMianZuo","DuanMianYou"};
            List<String> conList = new ArrayList<>(Arrays.asList(src));
            for (TemplateDirectoryTem tem : temList){
                TemplateDirectory templateDirectory = GsonUtil.ObjectToEntity(tem,TemplateDirectory.class);
                templateDirectory.setTemplateId(templateId);
                templateDirectory.setProjectId(projectid);
                if (node.equals("4") || node.equals("5")){
                    if (conList.contains(templateDirectory.getTemCode())){
                        tem.setTemEnable(true);
                    }
                }
                templateDirectoryTemMapper.insert(templateDirectory);
            }
        }

//        List<TemplateDirectory> temList = templateDirectoryTemMapper.selectList(queryWrapper);
//        String[] src = {"ZhuanYeQianShuLan","ZhuanJiaoDianBiao","GaoCheng","PingMianZuo","DuanMianZuo","DuanMianYou"};
//        List<String> conList = new ArrayList<>(Arrays.asList(src));
//        for (TemplateDirectory tem : temList){
//            tem.setTemplateId(templateId);
//            tem.setProjectId(projectid);
//            if (node.equals("SGT") || node.equals("JGT")){
//                if (conList.contains(tem.getTemCode())){
//                    tem.setTemEnable(true);
//                }
//            }
//            templateDirectoryTemMapper.insert(tem);
//        }
    }

    @Autowired
    private TemplateDirectoryMapper templateDirectoryTemMapper;
    @Autowired
    private FontStyleTemplateTemMapper fontStyleTemplateTemMapper;
    @Autowired
    private LineStyleTemplateTemMapper lineStyleTemplateTemMapper;

    @Autowired
    private ColumnDefineTemMapper columnDefineTemMapper;
    /**
     * 删除模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)信息
     *
     * @param templatesId
     * @param projectid
     * @return int
     * @Date 2023-02-17
     * @auther eomer
     */
    @Override
    public int delete(String templatesId, String projectid) {
        //删除 模板定位
        QueryWrapper<TemplateLocate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id",templatesId);
        queryWrapper.eq("project_id",projectid);
        TemplateLocate templateLocate = templateLocateMapper.selectOne(queryWrapper);
        if (templateLocate != null){
            templateLocateMapper.deleteById(templateLocate.getPkid());
        }
        //删除基本快
        QueryWrapper<Block> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("template_id",templatesId);
        queryWrapper1.eq("project_id",projectid);
        Block block = blockMapper.selectOne(queryWrapper1);
        if (block != null){
            blockMapper.deleteById(block.getPkid());
        }
        //删除属性快
        QueryWrapper<PropertyBlock> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("template_id",templatesId);
        queryWrapper2.eq("project_id",projectid);
        List<PropertyBlock> propertyBlock = propertyBlockMapper.selectList(queryWrapper2);
        for (PropertyBlock btem : propertyBlock){
            propertyBlockMapper.deleteById(btem.getPkid());
        }
        //删除块属性定义
        QueryWrapper<PropertyDef> queryWrapper3 =new QueryWrapper<>();
        queryWrapper3.eq("template_id",templatesId);
        queryWrapper3.eq("project_id",projectid);
        List<PropertyDef> propertyDefTems = propertyDefMapper.selectList(queryWrapper3);
        for (PropertyDef defTem : propertyDefTems){
            propertyDefMapper.deleteById(defTem.getPkid());
        }
        //删除子偏移距离
        QueryWrapper<OffsetDistance> queryWrapper4 = new QueryWrapper<>();
        queryWrapper4.eq("template_id",templatesId);
        queryWrapper4.eq("project_id",projectid);
        OffsetDistance offsetDistanceTem = offsetDistanceMapper.selectOne(queryWrapper4);
        if (offsetDistanceTem != null){
            offsetDistanceMapper.deleteById(offsetDistanceTem.getPkid());
        }
        //删除栏目定义
        QueryWrapper<ColumnConfig> queryWrapper5 = new QueryWrapper<>();
        queryWrapper5.eq("template_id",templatesId);
        queryWrapper5.eq("project_id",projectid);
        List<ColumnConfig> columnConfigTems = columnConfigMapper.selectList(queryWrapper5);
        for (ColumnConfig figTem : columnConfigTems){
            columnConfigMapper.deleteById(figTem.getPkid());
        }
        //删除文字样式
        QueryWrapper<FontStyleConfig> queryWrapper6 = new QueryWrapper<>();
        queryWrapper6.eq("template_id",templatesId);
        queryWrapper6.eq("project_id",projectid);
        List<FontStyleConfig> fontTems = fontStyleConfigMapper.selectList(queryWrapper6);
        for (FontStyleConfig fontt : fontTems){
            fontStyleConfigMapper.deleteById(fontt.getPkid());
        }
        //删除线型样式
        QueryWrapper<LineStyleConfig> queryWrapper7 = new QueryWrapper<>();
        queryWrapper7.eq("template_id",templatesId);
        queryWrapper7.eq("project_id",projectid);
        List<LineStyleConfig> lineTems = lineStyleConfigMapper.selectList(queryWrapper7);
        for (LineStyleConfig lin : lineTems){
            lineStyleConfigMapper.deleteById(lin.getPkid());
        }
        QueryWrapper<TemplateLocate> queryWrapper8 = new QueryWrapper<>();

        return templatesMapper.deleteById(templatesId);
    }


    /**
    * 更新模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)信息
    *
    * @param templates
    * @return int
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public int update(Templates templates) {
        return templatesMapper.updateById(templates);
    }


    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RefMapper refMapper;
    /**
    * 全部查询
    *
    * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.Templates>
    * @Date 2023-02-17
    * @auther eomer
    */
    @Override
    public List<Templates> selListBy(String searchName, String type, String templatetype, String modelType, String stage, String projectid) {
        QueryWrapper<Templates> queryWrapper = new QueryWrapper<>();
        if (JavaUtils.isNotEmtryOrNull(searchName)){
            queryWrapper.like("template_name",searchName);
        }
        if (JavaUtils.isNotEmtryOrNull(type)){
            queryWrapper.eq("type",type);
        }
        if (JavaUtils.isNotEmtryOrNull(templatetype)){
            queryWrapper.eq("template_type",templatetype);
        }
        if (modelType.equals("1")){
            if (JavaUtils.isNotEmtryOrNull(stage)){
                queryWrapper.like("stage",stage);
            }
        }
        queryWrapper.eq("project_id",projectid);
        List<Templates> list = templatesMapper.selectList(queryWrapper);
        for (Templates tem : list){
            List<User> users = userMapper.selByUserId(tem.getCreateuserid());
            if (!users.isEmpty()) {
                tem.setCreateuserid(users.get(0).getUsername());

            }else {
                List<Ref> refs = refMapper.selectByUserId(tem.getCreateuserid());
                if (!refs.isEmpty()){
                    tem.setCreateuserid(refs.get(0).getUserName());
                }

            }

//            QueryWrapper<Dictionary> query = new QueryWrapper<>();
//            query.eq("pkid",tem.getType());
//            Dictionary dictionary = dictionaryMapper.selectOne(query);
//            tem.setType(dictionary.getDictionaryname());
            if(JavaUtils.isNotEmtryOrNull(tem.getTemlanguageid())){
                Dictionary dictionary1 = dictionaryMapper.selectById(tem.getTemlanguageid());
                tem.setTemlanguageid(dictionary1.getDictionaryname());
            }
        }
        return list;
    }


    @Override
    public List<TemplatesTrees> listTree() {
        QueryWrapper<Dictionary> queryOne = new QueryWrapper<>();
        queryOne.eq("pid","60056310002");
        List<Dictionary> oneList = dictionaryMapper.selectList(queryOne);
        List<TemplatesTrees> treeList = new ArrayList<>();
        for (Dictionary dic : oneList) {
            TemplatesTrees onet = new TemplatesTrees();
            onet.setName(dic.getDictionaryname());
            onet.setId(dic.getPkid());
            onet.setCode(dic.getDictionarycode());
            QueryWrapper<Dictionary> queryTwo = new QueryWrapper<>();
            queryTwo.eq("pid",dic.getPkid());
            List<Dictionary> twoList = dictionaryMapper.selectList(queryTwo);
            List<TemplatesTrees> twoTree = new ArrayList<>();
            for (Dictionary dict : twoList) {
                TemplatesTrees twot = new TemplatesTrees();
                twot.setName(dict.getDictionaryname());
                twot.setId(dict.getPkid());
                twot.setPid(dic.getPkid());
                twot.setCode(dict.getDictionarycode());
                twoTree.add(twot);
            }
            onet.setChiled(twoTree);
            treeList.add(onet);
        }
        return treeList;
    }


    @Override
    public TemplatesTrees getTemplatesTree(String templatesId, String type) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("templates_id",templatesId);
        Dictionary dictionary = dictionaryMapper.selectById(type);
        TemplatesTrees tree = new TemplatesTrees();
        tree.setId(dictionary.getPkid());
        tree.setPid(templatesId);
        tree.setName(dictionary.getDictionaryname());
        QueryWrapper<Dictionary> queryOne = new QueryWrapper<>();
        queryOne.eq("pid","60056290000");
        List<Dictionary> oneList = dictionaryMapper.selectList(queryOne);

        List<TemplatesTrees> oneChild = new ArrayList<>();
        for (Dictionary dictionaryo : oneList) {
            TemplatesTrees onet = new TemplatesTrees();
            onet.setId(dictionaryo.getPkid());
            onet.setPid(dictionary.getPkid());
            onet.setName(dictionaryo.getDictionaryname());
            onet.setCode(dictionaryo.getDictionarycode());

            QueryWrapper<Dictionary> queryTwo = new QueryWrapper<>();
            queryTwo.eq("pid",dictionaryo.getPkid());
            List<Dictionary> twoList = dictionaryMapper.selectList(queryTwo);
            List<TemplatesTrees> twoTree = new ArrayList<>();
            for (Dictionary dictionaryt : twoList) {
                TemplatesTrees twot = new TemplatesTrees();
                twot.setId(dictionaryt.getPkid());
                twot.setPid(dictionaryo.getPid());
                twot.setName(dictionaryt.getDictionaryname());
                twot.setCode(dictionaryt.getDictionarycode());

                QueryWrapper<Dictionary> queryThree = new QueryWrapper<>();
                queryThree.eq("pid",dictionaryt.getPkid());
                List<Dictionary> threeList = dictionaryMapper.selectList(queryThree);
                List<TemplatesTrees> threeTree = new ArrayList<>();
                for (Dictionary dictionarytth : threeList) {
                    TemplatesTrees twoth = new TemplatesTrees();
                    twoth.setId(dictionarytth.getPkid());
                    twoth.setPid(dictionaryt.getPid());
                    twoth.setName(dictionarytth.getDictionaryname());
                    twoth.setCode(dictionarytth.getDictionarycode());
                    threeTree.add(twoth);
                }
                twot.setChiled(threeTree);
                twoTree.add(twot);
            }
            onet.setChiled(twoTree);
            oneChild.add(onet);
        }
        tree.setChiled(oneChild);
        return tree;
    }


    @Override
    public List<Templates> getTimestampCombox(String projectid) {
        QueryWrapper<Templates> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("pkid","templatename");
        queryWrapper.eq("project_id", projectid);
        return templatesMapper.selectList(queryWrapper);
    }

    @Resource(name ="FileUpLoadUtils")
    private FileUpLoadUtils fileUpLoadUtils;

    private final String filePath= PathUtils.getDbUploadPath();

    @Autowired
    private WorkbagFileuploadrecordMapper workbagFileuploadrecordMapper;

    @Autowired
    private TemplatefilesMapper templatefilesMapper;

    @Override
    public int addTemplate(MultipartFile file, String templatetype, String type, String createuserid, String projectid) {
//        String destPath = "D:\\file\\target";
        File fil = fileUpLoadUtils.uploadRetrunPath(file, filePath);
        Templates temp = new Templates();
        temp.setProjectid(projectid);
        temp.setCreatetime(new Timestamp(System.currentTimeMillis()));
        temp.setCreateuserid(createuserid);
        temp.setTemplatename(fil.getName());
        temp.setType(type);
        temp.setTemplatetype(templatetype);
        templatesMapper.insert(temp);

        WorkbagFileuploadrecord fileuploadrecord = new WorkbagFileuploadrecord();
        fileuploadrecord.setUploaduserid(createuserid);
        fileuploadrecord.setUploaddate(new Timestamp(System.currentTimeMillis()));
        fileuploadrecord.setFileurl(fil.getPath());
        workbagFileuploadrecordMapper.insert(fileuploadrecord);

        Templatefiles tfiles = new Templatefiles();
        tfiles.setProjectId(projectid);
        tfiles.setTemplateId(temp.getPkid());
        tfiles.setFileId(fileuploadrecord.getPkid());
        int insert = templatefilesMapper.insert(tfiles);
        return insert;
    }

    @Override
    public WorkbagFileuploadrecord downloadModel(String pkid, String projectid) {
        QueryWrapper<Templatefiles> templatefilesTemQueryWrapper = new QueryWrapper<>();
        templatefilesTemQueryWrapper.eq("template_id",pkid);
        templatefilesTemQueryWrapper.eq("project_id",projectid);
        templatefilesTemQueryWrapper.isNull("template_block_name");
        Templatefiles templatefilesTem = templatefilesMapper.selectOne(templatefilesTemQueryWrapper);
        return workbagFileuploadrecordMapper.selectById(templatefilesTem.getFileId());
    }

    @Override
    public int upLoadDrawings(MultipartFile file, String pkid, String temCode, String projectid, User user) {
//        File fil = fileUpLoadUtils.uploadRetrunFile(file, filePath);
        QueryWrapper<PropertyBlock> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("template_id",pkid);
        queryWrapper1.eq("belong_block_def_type",temCode);
        queryWrapper1.eq("project_id",projectid);
        queryWrapper1.ne("file_path","");
        queryWrapper1.isNotNull("file_path");
        PropertyBlock propertyBlockTem = propertyBlockMapper.selectOne(queryWrapper1);

        //路径
        String fileToPath = PathUtils.getDbUploadPath()+File.separator+JavaUtils.getNowDate2()+File.separator+"drawtemplate";
        fileToPath = fileToPath+File.separator+projectid+File.separator+pkid;
        String oldName = file.getOriginalFilename();
        File fil = fileUpLoadUtils.uploadRetrunFile(file, fileToPath);
        String newName = fil.getName();
        String filPath = fil.getPath();
        //添加到文件清单中
        String fileId = workbagFileImpl.dwgToWorkBag(null, pkid, temCode, oldName, filPath, user);
        //添加到模板关联文件表
        TemplatefilesTem tf = new TemplatefilesTem();
        tf.setFileName(oldName);
        tf.setFileUuidName(newName);
        tf.setTemplateId(pkid);
        tf.setTemplateBlockCode(temCode);
        tf.setFileId(fileId);
        tf.setUpdataUserId(user.getUserid());
        templatefilesTemImpl.addTemplateFiles(tf);
//        if (propertyBlockTem==null){
//            QueryWrapper<PropertyBlock> queryWrapper = new QueryWrapper<PropertyBlock>();
//            queryWrapper.eq("template_id","000-000");
//            PropertyBlock propertyBlockTem1= propertyBlockMapper.selectOne(queryWrapper);
//            propertyBlockTem1.setPkid(UUID.randomUUID().toString());
//            propertyBlockTem1.setTemplateId(pkid);
//            propertyBlockTem1.setBelongBlockDefType(pid);
//            propertyBlockTem1.setFilePath(fil.getPath());
//            propertyBlockTem1.setReferenceValue(fil.getName());
//            propertyBlockTem1.setProjectId(projectid);
//            propertyBlockMapper.insert(propertyBlockTem1);
//        }
        QueryWrapper<PropertyBlock> queryWrapper = new QueryWrapper<PropertyBlock>();
        queryWrapper.eq("pkid", propertyBlockTem.getPkid());
        queryWrapper.eq("project_id",projectid);
        propertyBlockTem.setFilePath(fil.getPath());
        propertyBlockTem.setReferenceValue(fil.getName());
        propertyBlockMapper.update(propertyBlockTem,queryWrapper);
        return 0;
    }



    @Override
    public Map<String, List<Templates>> seleByStageType(String stage, String type, String projectid) {
        Map<String, List<Templates>> map = new HashMap<>();
        QueryWrapper<Templates> queryWrapper = new QueryWrapper<>();
        queryWrapper.notLike("stage", stage);
        queryWrapper.eq("type", type);
        queryWrapper.eq("project_id",projectid);
        List<Templates> list = templatesMapper.selectList(queryWrapper);
        map.put("left", list);
        QueryWrapper<Templates> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.like("stage", stage);
        queryWrapper1.eq("type", type);
        queryWrapper1.eq("project_id",projectid);
        List<Templates> list1 = templatesMapper.selectList(queryWrapper);
        map.put("right", list1);
        return map;
    }


    @Override
    public int addStageById(String ids, String stage, String projectid) {
        int result = 0;
        String[] split = ids.split(",");
        for (int i = 0; i < split.length; i++){
            QueryWrapper<Templates> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("pkid",split[i]);
            queryWrapper.eq("project_id",projectid);
            Templates tem = templatesMapper.selectOne(queryWrapper);
            if (JavaUtils.isEmtryOrNull(tem.getStage())){
                tem.setStage(stage);
            } else {
                if (!tem.getStage().contains(stage)){
                    tem.setStage(tem.getStage()+","+stage);
                }
            }
            QueryWrapper queryWrapper1 = new QueryWrapper();
            queryWrapper1.eq("pkid",tem.getPkid());
            queryWrapper1.eq("project_id",projectid);
            result = templatesMapper.updateById(tem);
        }
        return result;
    }

    @Autowired
    private TemplatefilesTemMapper templatefilesTemMapper;
    //新建模板复制文件关联
    public void copyTemplateFiles(String pkid, String projectid){
        QueryWrapper<TemplatefilesTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id","000-000");
        List<TemplatefilesTem> templatefilesTems = templatefilesTemMapper.selectList(queryWrapper);
        if (!JavaUtils.isEmtryOrNull(templatefilesTems)){
            for (TemplatefilesTem tem : templatefilesTems){
                Templatefiles templatefiles = GsonUtil.ObjectToEntity(tem, Templatefiles.class);
                templatefiles.setPkid(UUID.randomUUID().toString());
                templatefiles.setTemplateId(pkid);
                templatefiles.setProjectId(projectid);
                templatefilesMapper.insert(templatefiles);
            }
        }
    }
}
