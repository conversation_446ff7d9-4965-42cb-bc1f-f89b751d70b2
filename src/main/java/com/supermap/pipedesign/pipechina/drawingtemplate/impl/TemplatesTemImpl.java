package com.supermap.pipedesign.pipechina.drawingtemplate.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.*;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.*;
import com.supermap.pipedesign.pipechina.drawingtemplate.entity.vo.TemplatesTrees;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ITemplatefilesTemService;
import com.supermap.pipedesign.pipechina.drawingtemplate.service.ITemplatesTemService;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.RefMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.UserMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.WorkbagFileuploadrecordMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.engineering.entity.Ref;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.entity.WorkbagFileuploadrecord;
import com.supermap.pipedesign.pipechina.engineering.service.IWorkbagFileService;
import com.supermap.pipedesign.pipechina.file.impl.FileUpLoadUtils;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.file.PathUtils;
import com.supermap.tools.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.sql.Timestamp;
import java.util.*;


/**
 * <p>
 * 模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service("TemplatesTemImpl")
public class TemplatesTemImpl extends ServiceImpl<TemplatesTemMapper, TemplatesTem> implements ITemplatesTemService {

    @Autowired
    private TemplatesTemMapper templatesTemMapper;

    @Autowired
    private DictionaryMapper dictionaryMapper;

    private final String filePath= PathUtils.getDbUploadPath();

    @Resource(name = "FileUpLoadUtils")
    private FileUpLoadUtils fileUpLoadUtils;

    @Autowired
    private TemplatefilesTemMapper templatefilestemMapper;

    @Autowired
    private WorkbagFileuploadrecordMapper workbagFileuploadrecordMapper;

    @Autowired
    private TemplateLocateTemMapper templateLocateTemMapper;
    @Autowired
    private BlockTemMapper blockTemMapper;
    @Autowired
    private PropertyBlockTemMapper propertyBlockTemMapper;
    @Autowired
    private PropertyDefTemMapper propertyDefTemMapper;
    @Autowired
    private OffsetDistanceTemMapper offsetDistanceTemMapper;
    @Autowired
    private ColumnConfigTemMapper columnConfigTemMapper;
    @Autowired
    private FontStyleConfigTemMapper fontStyleConfigTemMapper;
    @Autowired
    private LineStyleConfigTemMapper lineStyleConfigTemMapper;

    @Autowired
    private ColumnDefineTemMapper columnDefineTemMapper;
    @Autowired
    private TemplateDirectoryTemMapper templateDirectoryTemMapper;

    @Resource(name = "WorkbagFileImpl")
    private IWorkbagFileService workbagFileImpl;

    @Resource(name = "TemplatefilesTemImpl")
    private ITemplatefilesTemService templatefilesTemImpl;


    /**
     * 添加模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)信息
     *
     * @param templatesTem
     * @return int
     * @Date 2023-02-17
     * @auther eomer
     */
    @Override
    public int insert(TemplatesTem templatesTem, User user) {
        //新增模板
        templatesTem.setCreateuserid(user.getPkid());
        templatesTem.setPkid(UUID.randomUUID().toString());
        templatesTem.setTemplatetype("1");
        //templatesTem.setCreateuserid(user.getPkid());
        templatesTem.setCreatetime(new Timestamp(System.currentTimeMillis()));
        templatesTemMapper.insert(templatesTem);
        String pkid = templatesTem.getPkid();
        copyTree(pkid, templatesTem.getType());
        copyTemplateFiles(pkid);

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("template_id", "000-000");
        //新增模板定位
        TemplateLocateTem templateLocateTem = templateLocateTemMapper.selectOne(queryWrapper);
        templateLocateTem.setTemplateId(pkid);
        templateLocateTem.setPkid(UUID.randomUUID().toString());
        templateLocateTem.setCreateUserId(user.getUserid());
        templateLocateTemMapper.insert(templateLocateTem);
        //新增基本块
        BlockTem blockTem = blockTemMapper.selectOne(queryWrapper);
        blockTem.setTemplateId(pkid);
        blockTem.setPkid(UUID.randomUUID().toString());
        blockTem.setCreateUserId(user.getUserid());
        blockTemMapper.insert(blockTem);

        List<PropertyBlockTem> propertyBlockTemlist = propertyBlockTemMapper.selectList(queryWrapper);
        for (Object object : propertyBlockTemlist) {
            PropertyBlockTem tem = GsonUtil.ObjectToEntity(object, PropertyBlockTem.class);
            tem.setPkid(UUID.randomUUID().toString());
            tem.setTemplateId(pkid);
            tem.setCreateUserId(user.getUserid());
            propertyBlockTemMapper.insert(tem);
        }
        List<PropertyDefTem> list1 = propertyDefTemMapper.selectList(queryWrapper);
        for (Object obj : list1) {
            PropertyDefTem ptem = GsonUtil.ObjectToEntity(obj, PropertyDefTem.class);
            ptem.setPkid(UUID.randomUUID().toString());
            ptem.setTemplateId(pkid);
            ptem.setCreateUserId(user.getUserid());
            propertyDefTemMapper.insert(ptem);
        }

        //新增子块偏移距离
        OffsetDistanceTem offsetDistanceTem = offsetDistanceTemMapper.selectOne(queryWrapper);
        offsetDistanceTem.setTemplateId(pkid);
        offsetDistanceTem.setPkid(UUID.randomUUID().toString());
        offsetDistanceTem.setCreateUserId(user.getUserid());
        offsetDistanceTemMapper.insert(offsetDistanceTem);

        QueryWrapper queryWrapper1 = new QueryWrapper();

        //新增栏目定义
        List<ColumnDefineTem> columnDefineTemlist = columnDefineTemMapper.selectList(queryWrapper1);
        for (Object fig : columnDefineTemlist) {
            ColumnConfigTem figTem = GsonUtil.ObjectToEntity(fig, ColumnConfigTem.class);
            figTem.setPkid(UUID.randomUUID().toString());
            figTem.setTemplateId(pkid);
            figTem.setIsDraw(false);
            figTem.setCreateUserId(user.getUserid());
            figTem.setEditenable(0);
            columnConfigTemMapper.insert(figTem);
        }
        //新增文字样式
        List<FontStyleTemplateTem> fontStyleTemplateTemlist = fontStyleTemplateTemMapper.selectList(queryWrapper1);
        for (Object fonts : fontStyleTemplateTemlist) {
            FontStyleConfigTem fontTem = GsonUtil.ObjectToEntity(fonts, FontStyleConfigTem.class);
            fontTem.setPkid(UUID.randomUUID().toString());
            fontTem.setTemplateId(pkid);
            fontTem.setCreateUserId(user.getUserid());
            fontStyleConfigTemMapper.insert(fontTem);
        }
        //新增线型样式
        List<LineStyleTemplateTem> lineStyleTemplateTemlist = lineStyleTemplateTemMapper.selectList(queryWrapper1);
        for (Object line : lineStyleTemplateTemlist) {
            LineStyleConfigTem lineTem = GsonUtil.ObjectToEntity(line, LineStyleConfigTem.class);
            lineTem.setPkid(UUID.randomUUID().toString());
            lineTem.setTemplateId(pkid);
            lineTem.setCreateUserId(user.getUserid());
            lineStyleConfigTemMapper.insert(lineTem);
        }
        return 1;
    }

    private void extracted(String pkid, QueryWrapper queryWrapper, TemplateDirectoryTem templateDirectoryTem) {
        String nodeId = templateDirectoryTem.getPkid();
        List<PropertyBlockTem> propertyBlockTemlist = propertyBlockTemMapper.selectList(queryWrapper);
        for (Object object : propertyBlockTemlist) {
            PropertyBlockTem tem = GsonUtil.ObjectToEntity(object, PropertyBlockTem.class);
            tem.setPkid(UUID.randomUUID().toString());
            tem.setTemplateId(pkid);
            tem.setBelongBlockDefType(nodeId);
            propertyBlockTemMapper.insert(tem);
        }
        List<PropertyDefTem> list1 = propertyDefTemMapper.selectList(queryWrapper);
        for (Object obj : list1) {
            PropertyDefTem ptem = GsonUtil.ObjectToEntity(obj, PropertyDefTem.class);
            ptem.setPkid(UUID.randomUUID().toString());
            ptem.setTemplateId(pkid);
            ptem.setBelongBlockDefType(nodeId);
            propertyDefTemMapper.insert(ptem);
        }
    }

    @Autowired
    private FontStyleTemplateTemMapper fontStyleTemplateTemMapper;
    @Autowired
    private LineStyleTemplateTemMapper lineStyleTemplateTemMapper;


    /**
     * 删除模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)信息
     *
     * @param templatesTemId
     * @return int
     * @Date 2023-02-17
     * @auther eomer
     */
    @Override
    public int delete(String templatesTemId) {
        //删除 模板定位
        QueryWrapper<TemplateLocateTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templatesTemId);
        TemplateLocateTem templateLocateTem = templateLocateTemMapper.selectOne(queryWrapper);
        if (templateLocateTem != null) {
            templateLocateTemMapper.deleteById(templateLocateTem.getPkid());
        }
        //删除基本快
        QueryWrapper<BlockTem> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("template_id", templatesTemId);
        BlockTem blockTem = blockTemMapper.selectOne(queryWrapper1);
        if (blockTem != null) {
            blockTemMapper.deleteById(blockTem.getPkid());
        }
        //删除属性快
        QueryWrapper<PropertyBlockTem> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("template_id", templatesTemId);
        List<PropertyBlockTem> propertyBlockTems = propertyBlockTemMapper.selectList(queryWrapper2);
        for (PropertyBlockTem btem : propertyBlockTems) {
            propertyBlockTemMapper.deleteById(btem.getPkid());
        }
        //删除块属性定义
        QueryWrapper<PropertyDefTem> queryWrapper3 = new QueryWrapper<>();
        queryWrapper3.eq("template_id", templatesTemId);
        List<PropertyDefTem> propertyDefTems = propertyDefTemMapper.selectList(queryWrapper3);
        for (PropertyDefTem defTem : propertyDefTems) {
            propertyDefTemMapper.deleteById(defTem.getPkid());
        }
        //删除子偏移距离
        QueryWrapper<OffsetDistanceTem> queryWrapper4 = new QueryWrapper<>();
        queryWrapper4.eq("template_id", templatesTemId);
        OffsetDistanceTem offsetDistanceTem = offsetDistanceTemMapper.selectOne(queryWrapper4);
        if (offsetDistanceTem != null) {
            offsetDistanceTemMapper.deleteById(offsetDistanceTem.getPkid());
        }
        //删除栏目定义
        QueryWrapper<ColumnConfigTem> queryWrapper5 = new QueryWrapper<>();
        queryWrapper5.eq("template_id", templatesTemId);
        List<ColumnConfigTem> columnConfigTems = columnConfigTemMapper.selectList(queryWrapper5);
        for (ColumnConfigTem figTem : columnConfigTems) {
            columnConfigTemMapper.deleteById(figTem.getPkid());
        }
        //删除文字样式
        QueryWrapper<FontStyleConfigTem> queryWrapper6 = new QueryWrapper<>();
        queryWrapper6.eq("template_id", templatesTemId);
        List<FontStyleConfigTem> fontTems = fontStyleConfigTemMapper.selectList(queryWrapper6);
        for (FontStyleConfigTem fontt : fontTems) {
            fontStyleConfigTemMapper.deleteById(fontt.getPkid());
        }
        //删除线型样式
        QueryWrapper<LineStyleConfigTem> queryWrapper7 = new QueryWrapper<>();
        queryWrapper7.eq("template_id", templatesTemId);
        List<LineStyleConfigTem> lineTems = lineStyleConfigTemMapper.selectList(queryWrapper7);
        for (LineStyleConfigTem lin : lineTems) {
            lineStyleConfigTemMapper.deleteById(lin.getPkid());
        }
        return templatesTemMapper.deleteById(templatesTemId);
    }

    /**
     * 更新模板表(存储各类模板的上传信息。	需要与CAD团队交流，让他们设计。)信息
     *
     * @param templatesTem
     * @return int
     * @Date 2023-02-17
     * @auther eomer
     */
    @Override
    public int update(TemplatesTem templatesTem) {
        return templatesTemMapper.updateById(templatesTem);
    }

    /**
     * 全部查询
     *
     * @param templatesTem
     * @return java.util.List<com.supermap.pipedesign.pipechina.drawingtemplate.entity.TemplatesTem>
     * @Date 2023-02-17
     * @auther eomer
     */
    @Override
    public List<TemplatesTem> list(TemplatesTem templatesTem) {

        QueryWrapper<TemplatesTem> queryWrapper = new QueryWrapper<>();

        return templatesTemMapper.selectList(queryWrapper);
    }

    /**
     * 模板列表类型tree
     *
     * <AUTHOR>
     * @Description
     * @Date 2023/2/13 14:01
     */
    @Override
    public List<TemplatesTrees> listTree() {
        QueryWrapper<Dictionary> queryOne = new QueryWrapper<>();
        queryOne.eq("pid", "60056310002");
        List<Dictionary> oneList = dictionaryMapper.selectList(queryOne);
        List<TemplatesTrees> treeList = new ArrayList<>();
        for (Dictionary dic : oneList) {
            TemplatesTrees onet = new TemplatesTrees();
            onet.setName(dic.getDictionaryname());
            onet.setId(dic.getPkid());
            onet.setCode(dic.getDictionarycode());
            QueryWrapper<Dictionary> queryTwo = new QueryWrapper<>();
            queryTwo.eq("pid", dic.getPkid());
            List<Dictionary> twoList = dictionaryMapper.selectList(queryTwo);
            List<TemplatesTrees> twoTree = new ArrayList<>();
            for (Dictionary dict : twoList) {
                TemplatesTrees twot = new TemplatesTrees();
                twot.setName(dict.getDictionaryname());
                twot.setId(dict.getPkid());
                twot.setPid(dic.getPkid());
                twot.setCode(dict.getDictionarycode());
                twoTree.add(twot);
            }
            onet.setChiled(twoTree);
            treeList.add(onet);
        }
        return treeList;
    }

    /*
     * <AUTHOR>
     * @Description
     * @Date 2023/2/6 17:39
     * @param templatesId 模板id
     * @param type 模板类型
     * 模型管理树
     **/
    @Override
    public TemplatesTrees getTemplatesTree(String templatesId, String type) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("templates_id", templatesId);
        Dictionary dictionary = dictionaryMapper.selectById(type);
        TemplatesTrees tree = new TemplatesTrees();
        tree.setId(dictionary.getPkid());
        tree.setPid(templatesId);
        tree.setName(dictionary.getDictionaryname());
        QueryWrapper<Dictionary> queryOne = new QueryWrapper<>();
        queryOne.eq("pid", "60056290000");
        List<Dictionary> oneList = dictionaryMapper.selectList(queryOne);

        List<TemplatesTrees> oneChild = new ArrayList<>();
        for (Dictionary dictionaryo : oneList) {
            TemplatesTrees onet = new TemplatesTrees();
            onet.setId(dictionaryo.getPkid());
            onet.setPid(dictionary.getPkid());
            onet.setName(dictionaryo.getDictionaryname());
            onet.setCode(dictionaryo.getDictionarycode());

            QueryWrapper<Dictionary> queryTwo = new QueryWrapper<>();
            queryTwo.eq("pid", dictionaryo.getPkid());
            List<Dictionary> twoList = dictionaryMapper.selectList(queryTwo);
            List<TemplatesTrees> twoTree = new ArrayList<>();
            for (Dictionary dictionaryt : twoList) {
                TemplatesTrees twot = new TemplatesTrees();
                twot.setId(dictionaryt.getPkid());
                twot.setPid(dictionaryo.getPid());
                twot.setName(dictionaryt.getDictionaryname());
                twot.setCode(dictionaryt.getDictionarycode());

                QueryWrapper<Dictionary> queryThree = new QueryWrapper<>();
                queryThree.eq("pid", dictionaryt.getPkid());
                List<Dictionary> threeList = dictionaryMapper.selectList(queryThree);
                List<TemplatesTrees> threeTree = new ArrayList<>();
                for (Dictionary dictionarytth : threeList) {
                    TemplatesTrees twoth = new TemplatesTrees();
                    twoth.setId(dictionarytth.getPkid());
                    twoth.setPid(dictionaryt.getPid());
                    twoth.setName(dictionarytth.getDictionaryname());
                    twoth.setCode(dictionarytth.getDictionarycode());
                    threeTree.add(twoth);
                }
                twot.setChiled(threeTree);
                twoTree.add(twot);
            }
            onet.setChiled(twoTree);
            oneChild.add(onet);
        }
        tree.setChiled(oneChild);
        return tree;
    }

    /**
     * 获取模板下拉框
     *
     * @return
     */
    public List<TemplatesTem> getTimestampCombox() {
        QueryWrapper<TemplatesTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("pkid", "templatename");
        return templatesTemMapper.selectList(queryWrapper);
    }

    @Override
    public int addTemplate(MultipartFile file, String templatetype, String type, String createuserid) {
//        String destPath = "D:\\file\\target";
        File fil = fileUpLoadUtils.uploadRetrunPath(file, filePath);
        TemplatesTem temp = new TemplatesTem();
        temp.setCreatetime(new Timestamp(System.currentTimeMillis()));
        temp.setCreateuserid(createuserid);
        temp.setTemplatename(fil.getName());
        temp.setType(type);
        temp.setTemplatetype(templatetype);
        templatesTemMapper.insert(temp);

        WorkbagFileuploadrecord fileuploadrecord = new WorkbagFileuploadrecord();
        fileuploadrecord.setPkid(UUID.randomUUID().toString());
        fileuploadrecord.setUploaduserid(createuserid);
        fileuploadrecord.setUploaddate(new Timestamp(System.currentTimeMillis()));
        fileuploadrecord.setFileurl(fil.getPath());
        workbagFileuploadrecordMapper.insert(fileuploadrecord);

        TemplatefilesTem tfiles = new TemplatefilesTem();
        tfiles.setTemplateId(temp.getPkid());
        tfiles.setFileId(fileuploadrecord.getPkid());
        int insert = templatefilestemMapper.insert(tfiles);
        return insert;
    }

    //模板下载
    @Override
    public WorkbagFileuploadrecord downloadModel(String pkid) {
        QueryWrapper<TemplatefilesTem> templatefilesTemQueryWrapper = new QueryWrapper<>();
        templatefilesTemQueryWrapper.eq("template_id",pkid);
        templatefilesTemQueryWrapper.isNull("template_block_name");
        TemplatefilesTem templatefilesTem = templatefilestemMapper.selectOne(templatefilesTemQueryWrapper);
        return workbagFileuploadrecordMapper.selectById(templatefilesTem.getFileId());

    }

    @Override
    public int upLoadDrawings(MultipartFile file, String pkid, String temCode, User user) {
//        File fil = fileUpLoadUtils.uploadRetrunFile(file, filePath);
        QueryWrapper queryWrapper1 = new QueryWrapper();
        queryWrapper1.eq("template_id", pkid);
        queryWrapper1.eq("belong_block_def_type", temCode);//模块code
//        queryWrapper1.eq("property_block_template", "块名称");
        queryWrapper1.ne("file_path","");
        queryWrapper1.isNotNull("file_path");
        PropertyBlockTem propertyBlockTem = propertyBlockTemMapper.selectOne(queryWrapper1);

        //路径
        String fileToPath = PathUtils.getDbUploadPath()+File.separator+JavaUtils.getNowDate2()+File.separator+"drawtemplate";
        fileToPath = fileToPath+File.separator+pkid;
        String oldName = file.getOriginalFilename();
        File fil = fileUpLoadUtils.uploadRetrunFile(file, fileToPath);
        String newName = fil.getName();
        String filPath = fil.getPath();
        //添加到文件清单中
        String fileId = workbagFileImpl.dwgToWorkBag(null, pkid, temCode, oldName, filPath, user);
        //添加到模板关联文件表
        TemplatefilesTem tf = new TemplatefilesTem();
        tf.setFileName(oldName);
        tf.setFileUuidName(newName);
        tf.setTemplateId(pkid);
        tf.setTemplateBlockCode(temCode);
        tf.setFileId(fileId);
        tf.setUpdataUserId(user.getUserid());
        templatefilesTemImpl.addTemplateFiles(tf);
        propertyBlockTem.setFilePath(fil.getPath());
        propertyBlockTem.setReferenceValue(fil.getName());

//        if (propertyBlockTem == null) {
//            QueryWrapper queryWrapper = new QueryWrapper();
//            queryWrapper.eq("template_id", "000-000");
//            PropertyBlockTem propertyBlockTem1 = propertyBlockTemMapper.selectOne(queryWrapper);
//            propertyBlockTem1.setPkid(UUID.randomUUID().toString());
//            propertyBlockTem1.setTemplateId(pkid);
//            propertyBlockTem1.setBelongBlockDefType(temCode);
//            propertyBlockTem1.setFilePath(fil.getPath());
//            propertyBlockTem1.setReferenceValue(fil.getName());
//            propertyBlockTemMapper.insert(propertyBlockTem1);
//        }
        propertyBlockTemMapper.updateById(propertyBlockTem);

        return 0;
    }


    @Override
    public PropertyBlockTem downloadDrawings(String pkid) {
        PropertyBlockTem propertyBlockTem = propertyBlockTemMapper.selectById(pkid);
        // , downloadPath+propertyBlockTem.getFileName()
        return propertyBlockTem;
    }


    @Autowired
    private TemplateDirectoryMapper templateDirectoryMapper;
//    @Override
//    public int copyModelToPro(String projectId) {
//        QueryWrapper queryWrapper = new QueryWrapper<>();
// //      qtemptem.eq("pkid",templateId);
//        queryWrapper.ne("template_id","000-000");
//        List<TemplateDirectoryTem> list = templateDirectoryTemMapper.selectList(queryWrapper);
//        for (TemplateDirectoryTem templateDirectoryTem : list) {
//            TemplateDirectory tem = GsonUtil.ObjectToEntity(templateDirectoryTem, TemplateDirectory.class);
//            tem.setPkid(UUID.randomUUID().toString());
//            tem.setProjectId(projectId);
//            // templateDirectoryTemMapper.inserttemplateDirectory(templateDirectoryTem);
//            templateDirectoryMapper.insert(tem);
//        }
//        //查询模板
//        QueryWrapper queryWrapper1 = new QueryWrapper();
//        List<TemplatesTem> templatesTems = templatesTemMapper.selectList(queryWrapper1);
//        for (TemplatesTem templatesTem : templatesTems) {
//            if (templatesTem != null){
//                QueryWrapper queryWrapper2 = new QueryWrapper();
//                queryWrapper2.eq("template_id",templatesTem.getPkid());
//                String templateTemId = UUID.randomUUID().toString();
//                //模板文件关联表
//                TemplatefilesTem templatefilesTem = templatefilestemMapper.selectOne(queryWrapper1);
//              /*  if (templatefilesTem != null){
//                    //复制文件
//                    WorkbagFileuploadrecord fileuploadrecord = workbagFileuploadrecordMapper.selectById(templatefilesTem.getFileid());
//                    if (fileuploadrecord != null){
//                        fileuploadrecord.setPkid(UUID.randomUUID().toString());
//                        workbagFileuploadrecordMapper.insert(fileuploadrecord);
//                        templatefilesTem.setFileid(fileuploadrecord.getPkid());
//                        templatefilesTem.setTemplateid(templateTemId);
//                        templatefilesTem.setPkid(UUID.randomUUID().toString());
//                        templatefilestemMapper.insert(templatefilesTem);
//                    }
//                }*/
//                //复制模板定位
//
//                TemplateLocateTem templateLocateTem = templateLocateTemMapper.selectOne(queryWrapper2);
//                if (templateLocateTem != null){
//                    TemplateLocate tll = GsonUtil.ObjectToEntity(templateLocateTem, TemplateLocate.class);
//                    tll.setProjectId(projectId);
//                    tll.setTemplateId(templateTemId);
//                    tll.setPkid(UUID.randomUUID().toString());
//                    templateLocateMapper.insert(tll);
//                }
//                //复制基本快  basic_block
//
//                BlockTem blockTem = blockTemMapper.selectOne(queryWrapper2);
//                if (blockTem != null){
//                    Block block = GsonUtil.ObjectToEntity(blockTem, Block.class);
//                    block.setProjectId(projectId);
//                    block.setTemplateId(templateTemId);
//                    block.setPkid(UUID.randomUUID().toString());
//                    blockMapper.insert(block);
//                }
//                //复制属性快
//
//                List<PropertyBlockTem> propertyBlockTems = propertyBlockTemMapper.selectList(queryWrapper);
//                for (PropertyBlockTem tem : propertyBlockTems){
//                    PropertyBlock pbl = GsonUtil.ObjectToEntity(tem, PropertyBlock.class);
//                    pbl.setProjectId(projectId);
//                    pbl.setTemplateId(templateTemId);
//                    pbl.setPkid(UUID.randomUUID().toString());
//                    propertyBlockMapper.insert(pbl);
//                }
//                //复制块属性定义
//
//                List<PropertyDefTem> propertyDefTems = propertyDefTemMapper.selectList(queryWrapper);
//                for (PropertyDefTem tem : propertyDefTems){
//                    PropertyDef pdef = GsonUtil.ObjectToEntity(tem, PropertyDef.class);
//                    pdef.setTemplateId(templateTemId);
//                    pdef.setProjectId(projectId);
//                    pdef.setPkid(UUID.randomUUID().toString());
//                    propertyDefMapper.insert(pdef);
//                }
//                //复制子偏移距离
//
//                OffsetDistanceTem offsetDistanceTem = offsetDistanceTemMapper.selectOne(queryWrapper2);
//                if (offsetDistanceTem != null){
//                    OffsetDistance offsetDistance = GsonUtil.ObjectToEntity(offsetDistanceTem, OffsetDistance.class);
//                    offsetDistance.setTemplateId(templateTemId);
//                    offsetDistance.setProjectId(projectId);
//                    offsetDistance.setPkid(UUID.randomUUID().toString());
//                    offsetDistanceMapper.insert(offsetDistance);
//                }
//                //复制栏目定义表
//
//                List<ColumnConfigTem> columnConfigTems = columnConfigTemMapper.selectList(queryWrapper);
//                for (ColumnConfigTem tem : columnConfigTems){
//                    ColumnConfig ccf = GsonUtil.ObjectToEntity(tem,ColumnConfig.class);
//                    ccf.setTemplateId(templateTemId);
//                    ccf.setProjectId(projectId);
//                    ccf.setPkid(UUID.randomUUID().toString());
//                    columnConfigMapper.insert(ccf);
//                }
//                //复制文字样式表
//
//                List<FontStyleConfigTem> fontStyleConfigTem = fontStyleConfigTemMapper.selectList(queryWrapper);
//                for (FontStyleConfigTem tem : fontStyleConfigTem){
//                    FontStyleConfig fstc = GsonUtil.ObjectToEntity(tem,FontStyleConfig.class);
//                    fstc.setTemplateId(templateTemId);
//                    fstc.setProjectId(projectId);
//                    fstc.setPkid(UUID.randomUUID().toString());
//                    fontStyleConfigMapper.insert(fstc);
//                }
//                //复制线型样式
//
//                List<LineStyleConfigTem> lineList = lineStyleConfigTemMapper.selectList(queryWrapper);
//                for (LineStyleConfigTem tem : lineList){
//                    LineStyleConfig fstc = GsonUtil.ObjectToEntity(tem,LineStyleConfig.class);
//                    fstc.setTemplateId(templateTemId);
//                    fstc.setProjectId(projectId);
//                    fstc.setPkid(UUID.randomUUID().toString());
//                    lineStyleConfigMapper.insert(fstc);
//                }
//                Templates templates = GsonUtil.ObjectToEntity(templatesTem, Templates.class);
//                templates.setProjectid(projectId);
//                templates.setPkid(templateTemId);
//                return templatesMapper.insert(templates);
//        }else {
//                try {
//                    throw new Exception("模板不存在，请检查");
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//
//        }
//        return 0;
//    }

    /**
     * @param stage 阶段
     * @param type  模板具体类型
     *              查询非该阶段下模板类型数据
     * <AUTHOR>
     * @Description
     * @Date 2023/2/22 21:23
     */
    @Override
    public Map<String, List<TemplatesTem>> seleByStageType(String stage, String type) {
        Map<String, List<TemplatesTem>> map = new HashMap<>();
        QueryWrapper<TemplatesTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.notLike("stage", stage);
        queryWrapper.eq("type", type);
        List<TemplatesTem> list = templatesTemMapper.selectList(queryWrapper);
        map.put("left", list);
        QueryWrapper<TemplatesTem> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.like("stage", stage);
        queryWrapper1.eq("type", type);
        List<TemplatesTem> list1 = templatesTemMapper.selectList(queryWrapper);
        map.put("right", list1);
        return map;
    }

    @Override
    public int addStageById(String ids, String stage) {
        int result = 0;
        String[] split = ids.split(",");
        for (int i = 0; i < split.length; i++) {
            TemplatesTem tem = templatesTemMapper.selectById(split[i]);
            if (JavaUtils.isEmtryOrNull(tem.getStage())) {
                tem.setStage(stage);
            } else {
                if (!tem.getStage().contains(stage)) {
                    tem.setStage(tem.getStage() + "," + stage);
                }
            }
            result = templatesTemMapper.updateById(tem);
        }
        return result;
    }

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RefMapper refMapper;

    @Override
    public List<TemplatesTem> selListBy(String searchName, String type, String templatetype, String modelType, String stage) {
        QueryWrapper<TemplatesTem> queryWrapper = new QueryWrapper<>();
        if (JavaUtils.isNotEmtryOrNull(searchName)) {
            queryWrapper.like("template_name", searchName);
        }
        if (JavaUtils.isNotEmtryOrNull(type)) {
            queryWrapper.eq("type", type);
        }
        if (JavaUtils.isNotEmtryOrNull(templatetype)) {
            queryWrapper.eq("template_type", templatetype);
        }
        if (modelType.equals("1")) {
            if (JavaUtils.isNotEmtryOrNull(stage)) {
                queryWrapper.like("stage", stage);
            }
        }
        List<TemplatesTem> list = templatesTemMapper.selectList(queryWrapper);
        for (TemplatesTem tem : list) {
            List<User> users = userMapper.selByUserId(tem.getCreateuserid());
            if (!JavaUtils.isEmtryOrNull(users)){
                tem.setCreateuserid(users.get(0).getUsername());

            }else {
                List<Ref> refs = refMapper.selectByUserId(tem.getCreateuserid());
                if (!JavaUtils.isEmtryOrNull(refs)){
                    Ref ref = refs.get(0);
                    tem.setCreateuserid(ref.getUserName());
                }
            }
//            QueryWrapper<Dictionary> query = new QueryWrapper<>();
//            query.eq("pkid", tem.getType());
//            Dictionary dictionary = dictionaryMapper.selectOne(query);
//            tem.setType(dictionary.getDictionaryname());
            if(JavaUtils.isNotEmtryOrNull(tem.getTemlanguageid())){
                Dictionary dictionary1 = dictionaryMapper.selectById(tem.getTemlanguageid());
                tem.setTemlanguageid(dictionary1.getDictionaryname());
            }
        }


        return list;
    }


    public void copyTree(String templateId, String node) {
        QueryWrapper<TemplateDirectoryTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", "000-000");
        List<TemplateDirectoryTem> temList = templateDirectoryTemMapper.selectList(queryWrapper);
        String[] src = {"ZhuanYeQianShuLan", "ZhuanJiaoDianBiao", "GaoCheng", "PingMianZuo", "DuanMianZuo", "DuanMianYou"};
        List<String> conList = new ArrayList<>(Arrays.asList(src));
        for (TemplateDirectoryTem tem : temList) {
            tem.setTemplateId(templateId);
            if (node.equals("4") || node.equals("5")) {
                if (conList.contains(tem.getTemCode())) {
                    tem.setTemEnable(true);
                }
            }
            templateDirectoryTemMapper.insert(tem);
        }
    }
    //添加模板复制文件关联
    public void copyTemplateFiles(String pkid){
        QueryWrapper<TemplatefilesTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id","000-000");
        List<TemplatefilesTem> templatefilesTems = templatefilestemMapper.selectList(queryWrapper);
        if (!JavaUtils.isEmtryOrNull(templatefilesTems)){
            for (TemplatefilesTem fileTem : templatefilesTems){
                fileTem.setPkid(UUID.randomUUID().toString());
                fileTem.setTemplateId(pkid);
                templatefilestemMapper.insert(fileTem);
            }
        }
    }
}
