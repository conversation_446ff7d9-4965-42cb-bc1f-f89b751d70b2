//package com.supermap.pipedesign.pipechina.demo.sys;
//
//import com.supermap.base.entity.LoginToken;
//import com.supermap.base.entity.Result;
//import com.supermap.base.service.impl.BaseServiceImpl;
//import com.supermap.config.exception.BusinessException;
//import com.supermap.tools.base.JavaUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.File;
//
///**
// * UploadService
// *
// * <AUTHOR>
// * @date 21/7/5 17:07
// */
//@Slf4j
//@Service("UploadService")
//public class UploadService extends BaseServiceImpl<FileMapper, FileEntity> {
//
////    private static Logger log = LoggerFactory.getLogger("ApiLogger");
//
//    @Autowired
//    private FileMapper fileMapper;
//
//    /**
//     * 正式路径
//     *
//     * @auther eomer
//     */
//    @Value("${upload.path.online}")
//    private String onlinePath;
//
//    /**
//     * 文件上传
//     *
//     * @param file
//     * @param loginToken
//     * @param basePath
//     * @return com.micrant.education.base.entity.Result
//     * @Date 21/7/7 11:28
//     * @auther eomer
//     */
//
//    public Result uploadFile(MultipartFile file, LoginToken loginToken, String basePath) {
//        FileEntity fileResult = new FileEntity();
//        try {
//            //List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
//
//            String uuid = JavaUtils.getUUID36();
//            String fileName = file.getOriginalFilename();//上传的文件名
//            String fileSize = JavaUtils.getFileSize(file.getSize());//上传的文件大小
//            String fileSuffix = uuid + fileName.substring(fileName.lastIndexOf("."));
//            //上传到指定目录
//            String uploadDir = onlinePath + "\\online\\"+basePath;
//
//            //Linux路径
//                /*if ("linux".equals(systemType)) {
//                    uploadDir = onlinePath + "/" + basePath + "/" + DateTimeUtil.getNowYear() + "/" + DateTimeUtil.getNowMonth() + "/" + DateTimeUtil.getNowDay();//Linux路径
//                } else {
//                    uploadDir = onlinePath + "\\" + basePath + "\\" + DateTimeUtil.getNowYear() + "\\" + DateTimeUtil.getNowMonth() + "\\" + DateTimeUtil.getNowDay();//Linux路径
//                }*/
//
//            String fileDir = "";//basePath + "/" + DateTimeUtil.getNowYear() + "/" + DateTimeUtil.getNowMonth() + "/" + DateTimeUtil.getNowDay() + "/";//Linux路径
//
////                String uploadDir = "/work/upload";//Linux路径
//
//            //上传到相对路径 request.getSession().getServletContext().getRealPath("/")+"upload/";
//
//            //如果目录不存在，自动创建文件夹
//            File dir = new File(uploadDir);
//            if (!dir.exists()) {
//                dir.mkdirs();//多层目录
//            }
//            //保存文件对象 加上uuid是为了防止文件重名
//            String filePath = uploadDir + File.separator + fileSuffix;
//            String showUrl = uploadDir + File.separator + fileSuffix;
//
//            File serverFile = new File(filePath);
//            serverFile.setReadable(true);//设置可读权限
//            serverFile.setWritable(true);//设置可写权限
//            file.transferTo(serverFile);
////                if ("linux".equals(systemType)) {
////                    //linux 设置权限
////                    Runtime.getRuntime().exec("chmod 644 " + uploadDir + File.separator + fileSuffix);
////                    logger.info("chmod 644 " + uploadDir + File.separator + fileSuffix);
////                }
//            //存储
//            FileEntity fileEntity = new FileEntity();
//            fileEntity.setFileId(uuid);
//            fileEntity.setFileName(fileName);
//            fileEntity.setFileSuffix(fileName.substring(fileName.lastIndexOf(".") + 1));
//            fileEntity.setPath(filePath);//
//            fileEntity.setUrl(showUrl);//
//            fileEntity.setFileSize(fileSize);
//            fileEntity.setUserId(loginToken.getUserId());
//            fileEntity.setUserName(loginToken.getUserName());
//            int result = fileMapper.insert(fileEntity);
//            log.info("上传文件 5");
//            if (result > 0) {
//                fileResult = fileEntity;
//            } else {
//                return error("添加失败");
//            }
//
//        } catch (Exception e) {
//            log.error("上传文件异常：" + e.getMessage());
//            throw new BusinessException("上传失败，稍后请重试");
//        }
//
//        return Result.success(fileResult);
//    }
//
//}
