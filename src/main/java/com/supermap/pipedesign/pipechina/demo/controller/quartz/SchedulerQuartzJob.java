//package com.supermap.pipedesign.pipechina.demo.controller.quartz;
//
//import org.quartz.Job;
//import org.quartz.JobExecutionContext;
//import org.quartz.JobExecutionException;
//
//import javax.annotation.Resource;
//
///**
// * SchedulerQuartzJob
// *
// * <AUTHOR>
// * @date 21/7/12 16:40
// */
//public class SchedulerQuartzJob implements Job {
//
////    @Resource(name = "WpsService")
////    private WpsService wpsService;
//
//    private void before() {
//        System.out.println("任务开始执行");
//    }
//
//    @Override
//    public void execute(JobExecutionContext context) throws JobExecutionException {
//        before();
//        System.out.println("开始：" + System.currentTimeMillis());
//        //JobDataMap jdMap = context.getJobDetail().getJobDataMap();  //获得传递过来的参数
//        //System.out.println(jdMap.get("user"));
//        //刷新token
////        wpsService.updateToken();
//
//        System.out.println("结束：" + System.currentTimeMillis());
//        after();
//    }
//
//    private void after() {
//        System.out.println("任务执行结束");
//    }
//
//}