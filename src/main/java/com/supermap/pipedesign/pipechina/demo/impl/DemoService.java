package com.supermap.pipedesign.pipechina.demo.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.base.service.impl.BaseServiceImpl;
import com.supermap.pipedesign.pipechina.demo.dao.DemoMapper;
import com.supermap.pipedesign.pipechina.demo.entity.Demo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * a-用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-09
 */
@Service("DemoService")
public class DemoService extends BaseServiceImpl<DemoMapper, Demo> {

    @Autowired
    private DemoMapper demoMapper;

    /**
    * 添加a-用户表信息
    *
    * @param demo
    * @return int
    * @Date 2021-07-09
    * @auther eomer
    */
    public int insert(Demo demo) {

        //user.setUserId(JavaUtils.getUUID36());
        //user.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return demoMapper.insert(demo);
    }

    /**
    * 删除a-用户表信息
    *
    * @param userId
    * @return int
    * @Date 2021-07-09
    * @auther eomer
    */
    public int delete(String userId) {
        return demoMapper.deleteById(userId);
    }

    /**
    * 更新a-用户表信息
    *
    * @param demo
    * @return int
    * @Date 2021-07-09
    * @auther eomer
    */
    public int update(Demo demo) {
        return demoMapper.updateById(demo);
    }

    /**
    * 全部查询
    *
    * @param demo
    * @return java.util.List<com.micrant.education.entity.sys.User>
    * @Date 2021-07-09
    * @auther eomer
    */
    public List<Demo> list(Demo demo) {

        QueryWrapper<Demo> queryWrapper = new QueryWrapper<>();

        return demoMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2021-07-09
    * @auther eomer
    */
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<Demo> userIPage = new Page<>();
        userIPage.setCurrent(current);
        userIPage.setSize(size);

        QueryWrapper<Demo> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        queryWrapper.eq("t.date",endDate);

        demoMapper.selectList(queryWrapper);


        demoMapper.getEggPage(userIPage,queryWrapper);

        demoMapper.getSubjectTeacherPage(userIPage,"");

        demoMapper.getList(queryWrapper);

        demoMapper.getList2("");


        return demoMapper.selectPage(userIPage, queryWrapper);
    }


}
