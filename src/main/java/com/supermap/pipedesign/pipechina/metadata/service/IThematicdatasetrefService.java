package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.Thematicdatasetref;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 数据资源数据集关联表(定义数据对应的数据集或关联文件信息。) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IThematicdatasetrefService extends IService<Thematicdatasetref> {

 /**
 * 添加数据资源数据集关联表(定义数据对应的数据集或关联文件信息。)信息
 *
 * @param thematicdatasetref
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Thematicdatasetref thematicdatasetref);

 /**
 * 删除数据资源数据集关联表(定义数据对应的数据集或关联文件信息。)信息
 *
 * @param thematicdatasetrefId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String thematicdatasetrefId);

 /**
 * 更新数据资源数据集关联表(定义数据对应的数据集或关联文件信息。)信息
 *
 * @param thematicdatasetref
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Thematicdatasetref thematicdatasetref);

 /**
 * 全部查询
 *
 * @param thematicdatasetref
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Thematicdatasetref>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Thematicdatasetref> list(Thematicdatasetref thematicdatasetref);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
