package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesPartsPic;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 默认风格部件贴图关联表-项目级 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Repository
public interface IMdEntitytypeStylesPartsPicService extends IService<MdEntitytypeStylesPartsPic> {

 /**
 * 添加默认风格部件贴图关联表-项目级信息
 *
 * @param mdEntitytypeStylesPartsPic
 * @return int
 * @Date 2023-07-17
 * @auther eomer
 */
 int insert(MdEntitytypeStylesPartsPic mdEntitytypeStylesPartsPic);

 /**
 * 删除默认风格部件贴图关联表-项目级信息
 *
 * @param mdEntitytypeStylesPartsPicId
 * @return int
 * @Date 2023-07-17
 * @auther eomer
 */
 int delete(String mdEntitytypeStylesPartsPicId);

 /**
 * 更新默认风格部件贴图关联表-项目级信息
 *
 * @param mdEntitytypeStylesPartsPic
 * @return int
 * @Date 2023-07-17
 * @auther eomer
 */
 int update(MdEntitytypeStylesPartsPic mdEntitytypeStylesPartsPic);

 /**
 * 全部查询
 *
 * @param mdEntitytypeStylesPartsPic
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesPartsPic>
 * @Date 2023-07-17
 * @auther eomer
 */
 List<MdEntitytypeStylesPartsPic> list(MdEntitytypeStylesPartsPic mdEntitytypeStylesPartsPic);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-07-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
