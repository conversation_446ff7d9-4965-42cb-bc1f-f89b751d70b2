package com.supermap.pipedesign.pipechina.metadata.entity.expand;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.supermap.pipedesign.pipechina.metadata.entity.Entitytypestyles;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesParts;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesPartsTem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 定义每类实体的默认显示风格。每个用户可以自定义同一类实体的展示风格。 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
public class EntitytypestylesEx extends Entitytypestyles {
    @ApiModelProperty(value = "部件信息")
    private List<MdEntitytypeStylesParts> stylesParts;
}
