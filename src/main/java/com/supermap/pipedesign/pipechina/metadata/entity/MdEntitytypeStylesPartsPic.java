package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 默认风格部件贴图关联表-项目级 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_entitytype_styles_parts_pic")
@ApiModel(value="MdEntitytypeStylesPartsPic对象", description="默认风格部件贴图关联表-项目级")
public class MdEntitytypeStylesPartsPic implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "默认部件表id")
    @TableField("style_parts_id")
    private String stylePartsId;

    @ApiModelProperty(value = "贴图id")
    @TableField("chartlet_id")
    private String chartletId;

    @ApiModelProperty(value = "位置:1上(外),2下(内),3左,4右,5前,6后")
    @TableField("pic_seat")
    private Integer picSeat;

    @ApiModelProperty(value = "路径")
    @TableField("pic_url")
    private String picUrl;

    @ApiModelProperty(value = "贴图名称")
    @TableField("pic_name")
    private String picName;

    @ApiModelProperty(value = "贴图图片名称")
    @TableField("cad_pic_name")
    private String cadPicName;

    @ApiModelProperty(value = "实体类型PKID")
    @TableField("entity_type_id")
    private String entityTypeId;

    @ApiModelProperty(value = "所属项目ID")
    @TableField("project_id")
    private String projectId;


}
