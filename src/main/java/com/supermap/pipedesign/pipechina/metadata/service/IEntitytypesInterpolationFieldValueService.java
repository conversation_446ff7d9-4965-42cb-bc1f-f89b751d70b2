package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationFieldValue;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 实体-插值表-字段表-存储值 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@Repository
public interface IEntitytypesInterpolationFieldValueService extends IService<EntitytypesInterpolationFieldValue> {

 /**
 * 添加实体-插值表-字段表-存储值信息
 *
 * @param entitytypesInterpolationFieldValue
 * @return int
 * @Date 2023-03-25
 * @auther eomer
 */
 int insert(EntitytypesInterpolationFieldValue entitytypesInterpolationFieldValue);

 /**
 * 删除实体-插值表-字段表-存储值信息
 *
 * @param entitytypesInterpolationFieldValueId
 * @return int
 * @Date 2023-03-25
 * @auther eomer
 */
 int delete(String entitytypesInterpolationFieldValueId);

 /**
 * 更新实体-插值表-字段表-存储值信息
 *
 * @param entitytypesInterpolationFieldValue
 * @return int
 * @Date 2023-03-25
 * @auther eomer
 */
 int update(EntitytypesInterpolationFieldValue entitytypesInterpolationFieldValue);

 /**
 * 全部查询
 *
 * @param entitytypesInterpolationFieldValue
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationFieldValue>
 * @Date 2023-03-25
 * @auther eomer
 */
 List<EntitytypesInterpolationFieldValue> list(EntitytypesInterpolationFieldValue entitytypesInterpolationFieldValue);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-25
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
