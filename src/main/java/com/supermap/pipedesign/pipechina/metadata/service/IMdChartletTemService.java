package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.metadata.entity.MdChartletTem;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 贴图管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Repository
public interface IMdChartletTemService extends IService<MdChartletTem> {

 /**
  * 添加贴图管理表信息
  *
  * @param file
  * @param user
  * @return int
  * @Date 2023-02-18
  * @auther eomer
  */
 int insert(MultipartFile file, String filenName, Integer type, String id, User user);

 /**
  * 删除贴图管理表信息
  *
  * @param mdChartletTemId
  * @return int
  * @Date 2023-02-18
  * @auther eomer
  */
 int delete(String mdChartletTemId);

 /**
 * 更新贴图管理表信息
 *
 * @param mdChartletTem
 * @return int
 * @Date 2023-02-18
 * @auther eomer
 */
 int update(MdChartletTem mdChartletTem);

 /**
 * 全部查询
 *
 * @param mdChartletTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdChartletTem>
 * @Date 2023-02-18
 * @auther eomer
 */
 List<MdChartletTem> list(String mdChartletTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param filenName
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-02-18
 * @auther eomer
 */
 IPage pageList(long current, long size, String filenName);


 MdChartletTem selectById(String id);
}
