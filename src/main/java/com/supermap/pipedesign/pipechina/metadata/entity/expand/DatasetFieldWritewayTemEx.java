package com.supermap.pipedesign.pipechina.metadata.entity.expand;

import com.supermap.pipedesign.pipechina.metadata.entity.DatasetFieldWritewayValueTem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
public class DatasetFieldWritewayTemEx  {


    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "实体id")
    private String entityTypeId;

    @ApiModelProperty(value = "实体属性id")
    private String datasetFieldId;

    @ApiModelProperty(value = "写值类型")
    private String writeType;

    @ApiModelProperty(value = "内置函数、值域属性")
    private String writeTypeAtt;

    @ApiModelProperty(value = "运算公式(1加2减3乘4除)")
    private String operationFormula;

    @ApiModelProperty(value = "运算数字")
    private BigDecimal operationNum;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "写值方式关联表")
    List<DatasetFieldWritewayValueTem> writewayValueTemsList;


}
