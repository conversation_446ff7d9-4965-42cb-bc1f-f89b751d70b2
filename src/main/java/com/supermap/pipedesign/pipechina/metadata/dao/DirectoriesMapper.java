package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.ThirdPartyData;
import com.supermap.pipedesign.pipechina.metadata.entity.Directories;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 * 目录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface DirectoriesMapper extends BaseMapper<Directories> {
   @Select("select ps.pkid ,ps.node_name,ps.node_alias,ps.parent_id,ps.m_id,pf.pkid,pf.directory_node_id,pf.resource_type,pf.resource_id from pld_md_directories ps left join pld_md_directoryresref pf on ps.pkid = pf.directory_node_id where  ps.project_id =#{projectid}")
    List<ThirdPartyData> getThirdTree(@Param("projectid") String projectid);

//    @Results(id="directoriesList",value = {
//            @Result(id = true,column = "pkid",property = "pkid"),
//            @Result(column = "parent_id",property = "pid"),
//            @Result(column = "name",property = "name"),
//            @Result(column = "pkid",property = "children",javaType = List.class,
//                    many=@Many(select="com.supermap.pipedesign.pipechina.rules.dao.GeneralTreeTemMapper.listTrees"))})
//    @Select("select * from qi_general_tree_tem where pid=#{pkid} order by sort asc")
//    List<ThirdPartyData> getThirdTree(String projectid);
}
