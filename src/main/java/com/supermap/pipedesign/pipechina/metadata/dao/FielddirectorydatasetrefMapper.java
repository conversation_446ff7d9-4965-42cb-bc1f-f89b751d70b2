package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.Fielddirectorydatasetref;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 踏勘目录-数据集关联表(存储外业踏勘目录与数据集的关联关系。如果关联的是文件，则存储文件ID或路径信息。) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface FielddirectorydatasetrefMapper extends BaseMapper<Fielddirectorydatasetref> {

}
