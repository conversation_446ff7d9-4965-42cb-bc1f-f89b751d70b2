package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 实体专题风格部件表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_entitytype_theme_styles_parts_tem")
@ApiModel(value="MdEntitytypeThemeStylesPartsTem对象", description="实体专题风格部件表")
public class MdEntitytypeThemeStylesPartsTem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "实体类型PKID")
    @TableField("entity_type_id")
    private String entityTypeId;

    @ApiModelProperty(value = "所属项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "样式id")
    @TableField("styles_id")
    private String stylesId;

    @ApiModelProperty(value = "部件id")
    @TableField("parts_id")
    private String partsId;

    @ApiModelProperty(value = "实体属性keys集合(字段1，字段2，字段3)")
    @TableField("filed_keys")
    private String filedKeys;

    @ApiModelProperty(value = "部件顺序")
    @TableField("sort_num")
    private Integer sortNum;


}
