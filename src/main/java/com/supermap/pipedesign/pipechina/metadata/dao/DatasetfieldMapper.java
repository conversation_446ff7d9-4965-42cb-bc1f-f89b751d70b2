package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.DatasetfieldtemVo;
import com.supermap.pipedesign.pipechina.metadata.entity.Datasetfield;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字段表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface DatasetfieldMapper extends BaseMapper<Datasetfield> {

    // @Select("select * from pld_md_dataset_field where data_set_id=#{datasetid}" )
    // List<DatasetfieldtemVo> selByDataSetId(String datasetid);

    @Select("select * from pld_md_dataset_field where entity_type_id=#{entityTypeId} and project_id=#{projectId}" )
    List<DatasetfieldtemVo> selByDataSetId(@Param("entityTypeId") String entityTypeId, @Param("projectId")  String projectId);


    @Select("select pkid, field_name as fieldname from pld_md_dataset_field where entity_type_id=#{entityTypeId} and project_id=#{projectId}")
    List<Map> selFielByEntityTypeId(@Param("entityTypeId") String entityTypeId, @Param("projectId") String projectId);

}
