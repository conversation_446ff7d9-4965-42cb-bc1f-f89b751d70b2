package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.Entitytypethemestyles;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 定义每类实体的专题图显示风格。每类实体根据实体类型字段来定义不同的展示风格。 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface EntitytypethemestylesMapper extends BaseMapper<Entitytypethemestyles> {

    @Select("select * from pld_md_entitytype_theme_styles order by entity_type_id,data_set_id,theme_field_id")
    List<Entitytypethemestyles> fetchEntitytypethemestylesByOrder();
}
