package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Name PersonalRules
 * @Desc 个人定制算法规则
 * <AUTHOR>
 * @Date 2022/2/13 11:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pur_cus_alg_ rules")
@ApiModel(value="PersonalRules对象", description="自动创建实体个人定制数据保存")
public class PersonalRules implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;
//用户id
    @TableField("userid")
    private String userid;
//项目id
    @TableField("projectid")
    private String projectid;
//实体id
    @TableField("entityid")
    private String entityid;
//个人定制数据规则（json字符串）
    @TableField("peralgorithm")
    private String peralgorithm;





}
