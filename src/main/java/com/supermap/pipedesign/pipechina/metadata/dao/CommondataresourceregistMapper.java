package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.Commondataresourceregist;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 公共数据资源注册表(这里的公共数据资源指的是可以叠加到地图或场景上的空间数据。如DOM、DEM、DLG数据，以及居民地信息、道路信息、滑坡泥石流等地质信息等。	公共数据为不可再拆分。	对应数据目录中的第六级。) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface CommondataresourceregistMapper extends BaseMapper<Commondataresourceregist> {

}
