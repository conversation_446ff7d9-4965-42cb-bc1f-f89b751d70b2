package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesParts;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 实体普通风格部件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-18
 */
@Repository
public interface IMdEntitytypeStylesPartsService extends IService<MdEntitytypeStylesParts> {

 /**
 * 添加实体普通风格部件表信息
 *
 * @param mdEntitytypeStylesParts
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int insert(MdEntitytypeStylesParts mdEntitytypeStylesParts);

 /**
 * 删除实体普通风格部件表信息
 *
 * @param mdEntitytypeStylesPartsId
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int delete(String mdEntitytypeStylesPartsId);

 /**
 * 更新实体普通风格部件表信息
 *
 * @param mdEntitytypeStylesParts
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int update(MdEntitytypeStylesParts mdEntitytypeStylesParts);

 /**
 * 全部查询
 *
 * @param mdEntitytypeStylesParts
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesParts>
 * @Date 2023-03-18
 * @auther eomer
 */
 List<MdEntitytypeStylesParts> list(MdEntitytypeStylesParts mdEntitytypeStylesParts);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-18
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
