package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.Directoryresref;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 目录资源关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IDirectoryresrefService extends IService<Directoryresref> {

 /**
 * 添加目录资源关联表信息
 *
 * @param directoryresref
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Directoryresref directoryresref);

 /**
 * 删除目录资源关联表信息
 *
 * @param directoryresrefId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String directoryresrefId);

 /**
 * 更新目录资源关联表信息
 *
 * @param directoryresref
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Directoryresref directoryresref);

 /**
 * 全部查询
 *
 * @param directoryresref
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Directoryresref>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Directoryresref> list(Directoryresref directoryresref);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
