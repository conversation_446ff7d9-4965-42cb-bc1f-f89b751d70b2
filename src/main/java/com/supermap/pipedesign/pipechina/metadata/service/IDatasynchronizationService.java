package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.metadata.entity.Datasynchronization;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据同步目录配置
 * <AUTHOR>
 * @date 2023/2/17 11:35
 */
@Repository
public interface IDatasynchronizationService extends IService<Datasynchronization> {

    List<Datasynchronization> selectOne(String userId , String machinecode);

    int create(String userId , Datasynchronization datasynchronization);
}
