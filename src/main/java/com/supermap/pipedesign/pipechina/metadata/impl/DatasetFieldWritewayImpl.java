package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.DatasetFieldWriteway;
import com.supermap.pipedesign.pipechina.metadata.dao.DatasetFieldWritewayMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IDatasetFieldWritewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service("DatasetFieldWritewayImpl")
public class DatasetFieldWritewayImpl extends ServiceImpl<DatasetFieldWritewayMapper, DatasetFieldWriteway> implements IDatasetFieldWritewayService {

    @Autowired
    private DatasetFieldWritewayMapper datasetFieldWritewayMapper;

    /**
    * 添加信息
    *
    * @param datasetFieldWriteway
    * @return int
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public int insert(DatasetFieldWriteway datasetFieldWriteway) {

        //datasetFieldWriteway.setUserId(JavaUtils.getUUID36());
        //datasetFieldWriteway.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return datasetFieldWritewayMapper.insert(datasetFieldWriteway);
    }

    /**
    * 删除信息
    *
    * @param datasetFieldWritewayId
    * @return int
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public int delete(String datasetFieldWritewayId) {
        return datasetFieldWritewayMapper.deleteById(datasetFieldWritewayId);
    }

    /**
    * 更新信息
    *
    * @param datasetFieldWriteway
    * @return int
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public int update(DatasetFieldWriteway datasetFieldWriteway) {
        return datasetFieldWritewayMapper.updateById(datasetFieldWriteway);
    }

    /**
    * 全部查询
    *
    * @param datasetFieldWriteway
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.DatasetFieldWriteway>
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public List<DatasetFieldWriteway> list(DatasetFieldWriteway datasetFieldWriteway) {

        QueryWrapper<DatasetFieldWriteway> queryWrapper = new QueryWrapper<>();

        return datasetFieldWritewayMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<DatasetFieldWriteway> datasetFieldWritewayIPage = new Page<>();
        datasetFieldWritewayIPage.setCurrent(current);
        datasetFieldWritewayIPage.setSize(size);

        QueryWrapper<DatasetFieldWriteway> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return datasetFieldWritewayMapper.selectPage(datasetFieldWritewayIPage, queryWrapper);
    }


}
