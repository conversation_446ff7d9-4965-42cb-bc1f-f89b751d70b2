package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.MaterialVo;
import com.supermap.pipedesign.pipechina.metadata.entity.TypeRegisterTem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Mapper
public interface TypeRegisterTemMapper extends BaseMapper<TypeRegisterTem> {

    @Select("select large_class_name as name,large_class_code as code from material_type_register_tem GROUP BY large_class_name,large_class_code")
    List<MaterialVo> getLargeList();

    @Select("select middle_class_name as name,middle_class_code as code from material_type_register_tem where material_type_code like concat(#{code},'%') GROUP BY middle_class_name,middle_class_code")
    List<MaterialVo> getMiddleList(@Param("code") String code);

    @Select("select small_class_name as name,small_class_code as code from material_type_register_tem where material_type_code like concat(#{code},'%') GROUP BY small_class_name,small_class_code")
    List<MaterialVo> getSmallList(@Param("code") String code);

    @Select(" ${selectSql} ")
    List<Map> getList(@Param("selectSql") String selectSql);
}
