package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import com.supermap.pipedesign.pipechina.metadata.dao.*;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.EntityTypeRulesVo;
import com.supermap.pipedesign.pipechina.metadata.entity.*;
import com.supermap.pipedesign.pipechina.metadata.service.IEntitytypesRuleAlgoritService;
import com.supermap.pipedesign.pipechina.rules.dao.*;
import com.supermap.pipedesign.pipechina.rules.entity.*;
import com.supermap.pipedesign.pipechina.rules.entity.vo.*;
import com.supermap.tools.gson.GsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;


/**
 * <p>
 * 规则和算法的关联信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("EntitytypesRuleAlgoritImpl")
public class EntitytypesRuleAlgoritImpl
        extends ServiceImpl<EntitytypesRuleAlgoritMapper, EntitytypesRuleAlgorit>
        implements IEntitytypesRuleAlgoritService {

    @Autowired
    private EntitytypestemMapper entitytypestemMapper;

    @Autowired
    private EntitydatasrefMapper entitydatasrefMapper;

    @Autowired
    private EntitytypesMapper entitytypesMapper;

    @Autowired
    private AlgorithmRegisterMapper algorithmRegisterMapper;

    @Autowired
    private LineIntersectLayerTemMapper lineIntersectLayerTemMapper;

    @Autowired
    private OnLineTemMapper onLineTemMapper;

    @Autowired
    private EntitytypesRuleTemMapper entitytypesRuleTemMapper;

    @Autowired
    private EntitytypesRuleMapper entitytypesRuleMapper;

    @Autowired
    private EntitytypesRuleAlgoritTemMapper entitytypesRuleAlgoritTemMapper;

    @Autowired
    private EntitytypesRuleAlgoritMapper entitytypesRuleAlgoritMapper;

    @Autowired
    private LineReferenceObjectTemMapper lineReferenceObjectTemMapper;

    @Autowired
    private LineIntersectLineTemMapper lineIntersectLineTemMapper;

    @Autowired
    private LinesAngleTemMapper linesAngleTemMapper;

    @Autowired
    private LineIntersectLayerMapper lineIntersectLayerMapper;

    @Autowired
    private OnLineMapper onLineMapper;

    @Autowired
    private LineReferenceObjectMapper lineReferenceObjectMapper;

    @Autowired
    private LinesAngleMapper linesAngleMapper;

    @Autowired
    private LineIntersectLineMapper lineIntersectLineMapper;

    @Autowired
    private LineRegionCreateLineTemMapper lineRegionCreateLineTemMapper;

    @Autowired
    private LineRegionCreateLineMapper lineRegionCreateLineMapper;

    @Autowired
    private DatasetinfotemMapper datasetinfotemMapper;

    @Autowired
    private DatasetinfoMapper datasetinfoMapper;

    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private AlgorithmRegisterParamsMapper algorithmRegisterParamsMapper;

    @Override
    public EntityTypeRulesVo selRulesByETId(String entityTypesId) {

        EntityTypeRulesVo entityTypeRulesVo = new EntityTypeRulesVo();
        entityTypeRulesVo.setEntityTypeId(entityTypesId);
        entityTypeRulesVo.setEntityTypeName(entitytypestemMapper.selectById(entityTypesId).getEntitytypealias());
        List<EntityTypeRulesVo.SpacingEntity> beanSE = new ArrayList<>();//沿管道中线创
        /**-----------------------------------------------------------------------------------**/
        QueryWrapper<EntitytypesRuleAlgoritTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("entity_types_id", entityTypesId);
        List<EntitytypesRuleAlgoritTem> entitytypesRuleAlgorits = entitytypesRuleAlgoritTemMapper.selectList(queryWrapper);
        entityTypesRule(entityTypeRulesVo);
        if (entitytypesRuleAlgorits != null
                && entitytypesRuleAlgorits.size() > 0) {
            for (EntitytypesRuleAlgoritTem entitytypesRuleAlgoritTem : entitytypesRuleAlgorits) {
                String etRAPkId = entitytypesRuleAlgoritTem.getPkid();
                String algoritid = entitytypesRuleAlgoritTem.getAlgoritid();
                AlgorithmRegister algorithmRegister = algorithmRegisterMapper.selectById(algoritid);
                if (algorithmRegister != null
                        && StringUtils.isNotBlank(algorithmRegister.getAlgorithmtype())) {
                    String algorithmtype = algorithmRegister.getAlgorithmtype();
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("rule_algorit_id", etRAPkId);
                    if (algorithmtype.equals("line_intersect_layer")) {//管线与图层相交算法
                        List<LineIntersectLayerVo> lilList = lineIntersectLayerTemMapper.selByRulealgoritid(etRAPkId);
                        if (lilList != null && lilList.size() > 0) {
                            for (LineIntersectLayerVo lineIntersectLayerVo : lilList) {
                                if (StringUtils.isNotEmpty(lineIntersectLayerVo.getDatesetname())) {
                                    String datasetName = lineIntersectLayerVo.getDatesetname();
                                    Datasetinfotem datasetinfotem
                                            = datasetinfotemMapper.selectById(datasetName);
                                    lineIntersectLayerVo.setDatesetname(datasetinfotem.getDatasetalias());
                                    lineIntersectLayerVo.setDatesetid(datasetName);
                                }
                                lineIntersectLayerVo.setRulealgorittype("line_intersect_layer");
                            }
                            entityTypeRulesVo.setBeanLLIL(lilList);
                        }
                    }
                    if (algorithmtype.equals("on_line")) {//沿管道中线创
                        List<OnLineTem> onLines = onLineTemMapper.selectByMap(paramMap);
                        if (onLines != null && onLines.size() > 0) {
                            OnLineTem onLine = onLines.get(0);
                            EntityTypeRulesVo.SpacingEntity spacingEntity = entityTypeRulesVo.new SpacingEntity();
                            spacingEntity.setDistance(String.valueOf(onLine.getDistance()));
                            spacingEntity.setSpacing(String.valueOf(onLine.getSpacing()));
                            spacingEntity.setRulealgorittype("on_line");
                            beanSE.add(spacingEntity);
                        }
                    }
                    if (algorithmtype.equals("line_reference_object")) {//管线与参照物相交算法
                        List<LineReferenceObjectVo> lineReferenceObjects
                                = lineReferenceObjectTemMapper.selByRulealgoritid(etRAPkId);
                        if (lineReferenceObjects != null
                                && lineReferenceObjects.size() > 0) {
                            for (LineReferenceObjectVo lineReferenceObjectVo : lineReferenceObjects) {
                                if (StringUtils.isNotEmpty(lineReferenceObjectVo.getDatesetname())) {
                                    String datasetName = lineReferenceObjectVo.getDatesetname();
                                    Datasetinfotem datasetinfotem
                                            = datasetinfotemMapper.selectById(datasetName);
                                    lineReferenceObjectVo.setDatesetname(datasetinfotem.getDatasetalias());
                                    lineReferenceObjectVo.setDatesetid(datasetName);
                                }
                                lineReferenceObjectVo.setRulealgorittype("line_reference_object");
                            }
                            entityTypeRulesVo.setBeanLLRO(lineReferenceObjects);
                        }
                    }
                    if (algorithmtype.equals("lines_angle")) {//线图层与与管道交叉角度规定
                        List<LinesAngleVo> linesAngles = linesAngleTemMapper.selByRulealgoritid(etRAPkId);
                        if (linesAngles != null
                                && linesAngles.size() > 0) {
                            for (LinesAngleVo linesAngleVo : linesAngles) {
                                if (StringUtils.isNotEmpty(linesAngleVo.getDatesetname())) {
                                    String datasetName = linesAngleVo.getDatesetname();
                                    Datasetinfotem datasetinfotem
                                            = datasetinfotemMapper.selectById(datasetName);
                                    linesAngleVo.setDatesetname(datasetinfotem.getDatasetalias());
                                    linesAngleVo.setDatesetid(datasetName);
                                }
                                linesAngleVo.setRulealgorittype("lines_angle");
                            }
                            entityTypeRulesVo.setBeanLLA(linesAngles);
                        }
                    }
                    if (algorithmtype.equals("line_intersect_line")) {//管道转折角度规定
                        List<LineIntersectLineVo> lineIntersectLines = lineIntersectLineTemMapper.selByRulealgoritid(etRAPkId);
                        if (lineIntersectLines != null
                                && lineIntersectLines.size() > 0) {
                            for (LineIntersectLineVo lineIntersectLineVo : lineIntersectLines) {
                                lineIntersectLineVo.setRulealgorittype("line_intersect_line");
                            }
                            entityTypeRulesVo.setBeanLIL(lineIntersectLines);
                        }
                    }
                    if (algorithmtype.equals("line_region_create_line")) {//基于中线与面图层交叉线创建线实体
                        List<LineRegionCreateLineVo> lineRegionCreateLineTems = lineRegionCreateLineTemMapper.selByRulealgoritid(etRAPkId);
                        if (lineRegionCreateLineTems != null && lineRegionCreateLineTems.size() > 0) {
                            for (LineRegionCreateLineVo lineRegionCreateLineVo : lineRegionCreateLineTems) {
                                if (StringUtils.isNotEmpty(lineRegionCreateLineVo.getDatesetname())) {
                                    String datasetName = lineRegionCreateLineVo.getDatesetname();
                                    Datasetinfotem datasetinfotem
                                            = datasetinfotemMapper.selectById(datasetName);
                                    lineRegionCreateLineVo.setDatesetname(datasetinfotem.getDatasetalias());
                                    lineRegionCreateLineVo.setDatesetid(datasetName);
                                }
                                lineRegionCreateLineVo.setRulealgorittype("line_region_create_line");
                            }
                            entityTypeRulesVo.setBeanLRCL(lineRegionCreateLineTems);
                        }
                    }
                }
            }
        }
        entityTypeRulesVo.setBeanSE(beanSE);
        return entityTypeRulesVo;
    }

    private void entityTypesRule(EntityTypeRulesVo entityTypeRulesVo) {
        QueryWrapper<AlgorithmRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("algorithm_group", "auto_reate");
        List<AlgorithmRegister> algorithmRegisters = algorithmRegisterMapper.selectList(queryWrapper);
        if (algorithmRegisters != null && algorithmRegisters.size() > 0) {
            for (AlgorithmRegister algorithmRegister : algorithmRegisters) {
                AlgorithmDataVo algorithmEx = new AlgorithmDataVo();
                if (algorithmRegister.getAlgorithmtype().equals("line_intersect_line")) {
                    algorithmEx = getAlgorithmEx(algorithmEx, algorithmRegister);
                    entityTypeRulesVo.setLineIntersectLine(algorithmEx);
                }
                if (algorithmRegister.getAlgorithmtype().equals("line_reference_object")) {
                    algorithmEx = getAlgorithmEx(algorithmEx, algorithmRegister);
                    entityTypeRulesVo.setLineReferenceObject(algorithmEx);
                }
                if (algorithmRegister.getAlgorithmtype().equals("on_line")) {
                    algorithmEx = getAlgorithmEx(algorithmEx, algorithmRegister);
                    entityTypeRulesVo.setOnLine(algorithmEx);
                }
                if (algorithmRegister.getAlgorithmtype().equals("lines_angle")) {
                    algorithmEx = getAlgorithmEx(algorithmEx, algorithmRegister);
                    entityTypeRulesVo.setLinesAngle(algorithmEx);
                }
                if (algorithmRegister.getAlgorithmtype().equals("line_intersect_layer")) {
                    algorithmEx = getAlgorithmEx(algorithmEx, algorithmRegister);
                    entityTypeRulesVo.setLineIntersectLayer(algorithmEx);
                }
                if (algorithmRegister.getAlgorithmtype().equals("line_region_create_line")) {
                    algorithmEx = getAlgorithmEx(algorithmEx, algorithmRegister);
                    entityTypeRulesVo.setLineRegionCreateLine(algorithmEx);
                }
            }
        }
    }

    private AlgorithmDataVo getAlgorithmEx(AlgorithmDataVo algorithmEx, AlgorithmRegister algorithmRegister) {
        algorithmEx.setAlgorithmTitle(algorithmRegister.getAlgorithmname());
        algorithmEx.setAlgorithmType(algorithmRegister.getAlgorithmtype());
        algorithmEx.setLayerDictPid(algorithmRegister.getShowtypes());
        algorithmEx.setShowType(algorithmRegister.getShowtypes());
        List<AlgorithmRegisterParams> algorithmRegisterParams
                = algorithmRegisterParamsMapper.selByRuleId(algorithmRegister.getPkid());
        List<AlgorithmFieldsData> fieldsEntities = new ArrayList<>();
        if (algorithmRegisterParams != null
                && algorithmRegisterParams.size() > 0) {
            String algorithmParam = "";
            for (AlgorithmRegisterParams algorithmRegisterParams1 : algorithmRegisterParams) {
                AlgorithmFieldsData fieldsEntity = new AlgorithmFieldsData();
                fieldsEntity.setCode(algorithmRegisterParams1.getFieldName());
                fieldsEntity.setName(algorithmRegisterParams1.getFieldAlias());
                fieldsEntity.setInputType(algorithmRegisterParams1.getFieldType());
                algorithmParam = algorithmParam + algorithmRegisterParams1.getFieldAlias() + ",";
                if (algorithmRegisterParams1.getFieldType().equals("select")) {
                    fieldsEntity.setDataType(algorithmRegisterParams1.getFieldName() + algorithmRegisterParams1.getFieldType());
                }
                algorithmParam = algorithmParam.substring(0, (algorithmParam.length() - 1));
                fieldsEntity.setDescription(algorithmRegisterParams1.getRemark());
                fieldsEntities.add(fieldsEntity);
            }
            algorithmParam = algorithmParam.substring(0, (algorithmParam.length() - 1));
            algorithmEx.setAlgorithmParam(algorithmParam);
            algorithmEx.setFieldsData(fieldsEntities);
        }
        return algorithmEx;
    }

    @Override
    public EntityTypeRulesVo selProRulesByETId(String entityTypesId, String projectid) {
        EntityTypeRulesVo entityTypeRulesVo = new EntityTypeRulesVo();
        entityTypeRulesVo.setEntityTypeId(entityTypesId);
        entityTypeRulesVo.setProjectId(projectid);
        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid", entityTypesId);
        queryWrapper.eq("project_id", projectid);
        Entitytypes entitytypes = entitytypesMapper.selectOne(queryWrapper);
        entityTypeRulesVo.setEntityTypeName(entitytypes.getEntitytypealias());

        List<EntityTypeRulesVo.SpacingEntity> beanSE = new ArrayList<>();//沿管道中线创
        entityTypesRule(entityTypeRulesVo);
        /**-----------------------------------------------------------------------------------**/
        List<EntitytypesRuleAlgorit> entitytypesRuleAlgorits
                = entitytypesRuleAlgoritMapper.selectByProIdEntityId(projectid, entityTypesId);
        if (entitytypesRuleAlgorits != null
                && entitytypesRuleAlgorits.size() > 0) {
            for (EntitytypesRuleAlgorit entitytypesRuleAlgoritTem : entitytypesRuleAlgorits) {
                String etRAPkId = entitytypesRuleAlgoritTem.getPkid();
                String algoritid = entitytypesRuleAlgoritTem.getAlgoritid();
                AlgorithmRegister algorithmRegister = algorithmRegisterMapper.selectById(algoritid);
                if (algorithmRegister != null
                        && StringUtils.isNotBlank(algorithmRegister.getAlgorithmtype())) {
                    String algorithmtype = algorithmRegister.getAlgorithmtype();
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("rule_algorit_id", etRAPkId);
                    paramMap.put("project_id", projectid);
                    if (algorithmtype.equals("line_intersect_layer")) {//管线与图层相交算法
                        List<LineIntersectLayer> lilList = lineIntersectLayerMapper.selectByMap(paramMap);
                        if (lilList != null && lilList.size() > 0) {
                            List<LineIntersectLayerVo> voLLILlist = new ArrayList<>();
                            for (LineIntersectLayer lineIntersectLayer : lilList) {
                                LineIntersectLayerVo lineIntersectLayerTem = GsonUtil.ObjectToEntity(lineIntersectLayer, LineIntersectLayerVo.class);
                                if (StringUtils.isNotEmpty(lineIntersectLayerTem.getDatesetname())) {
                                    String datasetName = lineIntersectLayerTem.getDatesetname();
                                    Datasetinfotem datasetinfotem
                                            = datasetinfotemMapper.selectById(datasetName);
                                    lineIntersectLayerTem.setDatesetname(datasetinfotem.getDatasetalias());
                                    lineIntersectLayerTem.setDatesetid(datasetName);
                                }
                                voLLILlist.add(lineIntersectLayerTem);
                            }
                            entityTypeRulesVo.setBeanLLIL(voLLILlist);
                        }
                    }
                    if (algorithmtype.equals("on_line")) {//沿管道中线创
                        List<OnLine> onLines = onLineMapper.selectByMap(paramMap);
                        if (onLines != null && onLines.size() > 0) {
                            OnLine onLine = onLines.get(0);
                            EntityTypeRulesVo.SpacingEntity spacingEntity = entityTypeRulesVo.new SpacingEntity();
                            spacingEntity.setDistance(String.valueOf(onLine.getDistance()));
                            spacingEntity.setSpacing(String.valueOf(onLine.getSpacing()));
                            beanSE.add(spacingEntity);
                        }
                    }
                    if (algorithmtype.equals("line_reference_object")) {//管线与参照物相交算法
                        List<LineReferenceObject> lineReferenceObjects
                                = lineReferenceObjectMapper.selectByMap(paramMap);
                        if (lineReferenceObjects != null
                                && lineReferenceObjects.size() > 0) {
                            List<LineReferenceObjectVo> voLROTlist = new ArrayList<>();
                            for (LineReferenceObject lineReferenceObject : lineReferenceObjects) {
                                LineReferenceObjectVo lineReferenceObjectTem = GsonUtil.ObjectToEntity(lineReferenceObject, LineReferenceObjectVo.class);
                                if (StringUtils.isNotEmpty(lineReferenceObjectTem.getDatesetname())) {
                                    String datasetName = lineReferenceObjectTem.getDatesetname();
                                    Datasetinfotem datasetinfotem
                                            = datasetinfotemMapper.selectById(datasetName);
                                    lineReferenceObjectTem.setDatesetname(datasetinfotem.getDatasetalias());
                                    lineReferenceObjectTem.setDatesetid(datasetName);
                                }
                                voLROTlist.add(lineReferenceObjectTem);
                            }
                            entityTypeRulesVo.setBeanLLRO(voLROTlist);
                        }
                    }
                    if (algorithmtype.equals("lines_angle")) {//线图层与与管道交叉角度规定
                        List<LinesAngle> linesAngles = linesAngleMapper.selectByMap(paramMap);
                        if (linesAngles != null
                                && linesAngles.size() > 0) {
                            List<LinesAngleVo> voLATlist = new ArrayList<>();
                            for (LinesAngle linesAngle : linesAngles) {
                                LinesAngleVo linesAngleTem = GsonUtil.ObjectToEntity(linesAngle, LinesAngleVo.class);
                                if (StringUtils.isNotEmpty(linesAngleTem.getDatesetname())) {
                                    String datasetName = linesAngleTem.getDatesetname();
                                    Datasetinfotem datasetinfotem
                                            = datasetinfotemMapper.selectById(datasetName);
                                    linesAngleTem.setDatesetname(datasetinfotem.getDatasetalias());
                                    linesAngleTem.setDatesetid(datasetName);
                                }
                                voLATlist.add(linesAngleTem);
                            }
                            entityTypeRulesVo.setBeanLLA(voLATlist);
                        }
                    }
                    if (algorithmtype.equals("line_intersect_line")) {//管道转折角度规定
                        List<LineIntersectLine> lineIntersectLines = lineIntersectLineMapper.selectByMap(paramMap);
                        if (lineIntersectLines != null
                                && lineIntersectLines.size() > 0) {
                            List<LineIntersectLineVo> voLITTlist = new ArrayList<>();
                            for (LineIntersectLine lineIntersectLine : lineIntersectLines) {
                                LineIntersectLineVo lineIntersectLineTem = GsonUtil.ObjectToEntity(lineIntersectLine, LineIntersectLineVo.class);
                                lineIntersectLineTem.setAngleMax(lineIntersectLine.getAnglemax());
                                lineIntersectLineTem.setAngleMin(lineIntersectLine.getAnglemin());
                                voLITTlist.add(lineIntersectLineTem);
                            }
                            entityTypeRulesVo.setBeanLIL(voLITTlist);
                        }
                    }
                    if (algorithmtype.equals("line_region_create_line")) {//基于中线与面图层交叉线创建线实体
                        List<LineRegionCreateLineVo> lineRegionCreateLineTems = lineRegionCreateLineMapper.selByRulealgoritid(etRAPkId, projectid);
                        if (lineRegionCreateLineTems != null && lineRegionCreateLineTems.size() > 0) {
                            for (LineRegionCreateLineVo lineRegionCreateLineVo : lineRegionCreateLineTems) {
                                if (StringUtils.isNotEmpty(lineRegionCreateLineVo.getDatesetname())) {
                                    String datasetName = lineRegionCreateLineVo.getDatesetname();
                                    Datasetinfotem datasetinfotem
                                            = datasetinfotemMapper.selectById(datasetName);
                                    lineRegionCreateLineVo.setDatesetname(datasetinfotem.getDatasetalias());
                                    lineRegionCreateLineVo.setDatesetid(datasetName);
                                }
                                lineRegionCreateLineVo.setRulealgorittype("line_region_create_line");
                            }
                            entityTypeRulesVo.setBeanLRCL(lineRegionCreateLineTems);
                        }
                    }
                }
            }
        }
        entityTypeRulesVo.setBeanSE(beanSE);
        return entityTypeRulesVo;
    }


    private DictionaryVo getDictionary(String dicCode) {
        List<DictionaryVo> dictionaryVos
                = dictionaryMapper.selCodeByKey("60056301003");
        if (dictionaryVos != null && dictionaryVos.size() > 0) {
            for (DictionaryVo dictionaryVo : dictionaryVos) {
                List<DictionaryVo> dictionaryChil = dictionaryMapper.selCodeByKey(dictionaryVo.getPkid());
                if (dictionaryChil != null && dictionaryChil.size() > 0) {
                    for (DictionaryVo childDiction : dictionaryChil) {
                        if (childDiction.getKey().equals(dicCode)) {
                            return childDiction;
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    public int createEntityTypeRules(EntityTypeRulesVo entityTypeRulesVo, User user) {
        int insertVal = 0;
        EntitytypesRuleTem entitytypesRule = new EntitytypesRuleTem();

        String entityTypeRuleId = UUID.randomUUID().toString();
        Timestamp curTimestamp = new Timestamp(Calendar.getInstance().getTimeInMillis());
        entitytypesRule.setPkid(entityTypeRuleId);
        entitytypesRule.setCreateuserid(user.getUserid());
        entitytypesRule.setUpdateuserid(user.getUserid());
        entitytypesRule.setCreatetime(curTimestamp);
        entitytypesRule.setUpdatetime(curTimestamp);
        entitytypesRule.setEntitytypesid(entityTypeRulesVo.getEntityTypeId());
        entityTypesRules(entityTypeRulesVo);
        insertVal = insertVal + entitytypesRuleTemMapper.insert(entitytypesRule);
        return insertVal;
    }

    private void entityTypesRules(EntityTypeRulesVo entityTypeRulesVo) {
        if (entityTypeRulesVo.getBeanLLIL() != null
                && entityTypeRulesVo.getBeanLLIL().size() > 0) {//基于中线与面图层交叉线创建点实体
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgorit(entityTypeRulesVo.getEntityTypeId(), "line_intersect_layer", ruleAlgoritId);
            for (LineIntersectLayerVo lineIntersectLayer : entityTypeRulesVo.getBeanLLIL()) {
                LineIntersectLayerTem lineIntersectLayerTem = GsonUtil.ObjectToEntity(lineIntersectLayer, LineIntersectLayerTem.class);
                String pkId = UUID.randomUUID().toString();
                lineIntersectLayerTem.setPkid(pkId);
                lineIntersectLayerTem.setRuleAlgoritId(ruleAlgoritId);
                lineIntersectLayerTemMapper.insert(lineIntersectLayerTem);
            }
        }

        if (entityTypeRulesVo.getBeanSE() != null
                && entityTypeRulesVo.getBeanSE().size() > 0) {//沿中线创建点实体算法
            for (EntityTypeRulesVo.SpacingEntity spacingEntity : entityTypeRulesVo.getBeanSE()) {
                String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
                OnLineTem onLine = new OnLineTem();
                if (StringUtils.isNotBlank(spacingEntity.getDistance())) {
                    onLine.setDistance(Integer.parseInt(spacingEntity.getDistance()));
                }
                if (StringUtils.isNotBlank(spacingEntity.getSpacing())) {
                    onLine.setSpacing(Integer.parseInt(spacingEntity.getSpacing()));
                }
                if (StringUtils.isNotBlank(spacingEntity.getDirection())) {
                    onLine.setDirection(Integer.parseInt(spacingEntity.getDirection()));
                }
                onLine.setRuleAlgoritId(ruleAlgoritId);
                onLineTemMapper.insert(onLine);
                insertETRuleAlgorit(entityTypeRulesVo.getEntityTypeId(), "on_line", ruleAlgoritId);
            }
        }

        if (entityTypeRulesVo.getBeanLLRO() != null
                && entityTypeRulesVo.getBeanLLRO().size() > 0) {//基于点图层在中线上的垂足创建实体
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgorit(entityTypeRulesVo.getEntityTypeId(), "line_reference_object", ruleAlgoritId);
            for (LineReferenceObjectVo lineReferenceObject : entityTypeRulesVo.getBeanLLRO()) {
                String lroPkid = UUID.randomUUID().toString();
                LineReferenceObjectTem lineReferenceObjectTem = GsonUtil.ObjectToEntity(lineReferenceObject, LineReferenceObjectTem.class);
                lineReferenceObjectTem.setPkid(lroPkid);
                lineReferenceObjectTem.setRuleAlgoritId(ruleAlgoritId);
                lineReferenceObjectTemMapper.insert(lineReferenceObjectTem);
            }
        }

        if (entityTypeRulesVo.getBeanLLA() != null
                && entityTypeRulesVo.getBeanLLA().size() > 0) {//基于中线转角创建点实体算法
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgorit(entityTypeRulesVo.getEntityTypeId(), "lines_angle", ruleAlgoritId);
            for (LinesAngleVo linesAngle : entityTypeRulesVo.getBeanLLA()) {
                LinesAngleTem linesAngleTem = GsonUtil.ObjectToEntity(linesAngle, LinesAngleTem.class);
                String linesAnglePkid = UUID.randomUUID().toString();
                linesAngleTem.setPkid(linesAnglePkid);
                linesAngleTem.setRuleAlgoritId(ruleAlgoritId);
                linesAngleTemMapper.insert(linesAngleTem);
            }
        }

        if (entityTypeRulesVo.getBeanLIL() != null
                && entityTypeRulesVo.getBeanLIL().size() > 0) {//基于中线与线图层交点创建点实体
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgorit(entityTypeRulesVo.getEntityTypeId(), "line_intersect_line", ruleAlgoritId);
            for (LineIntersectLineVo lineIntersectLine : entityTypeRulesVo.getBeanLIL()) {
                String lilPkid = UUID.randomUUID().toString();
                LineIntersectLineTem lineIntersectLineTem = GsonUtil.ObjectToEntity(lineIntersectLine, LineIntersectLineTem.class);
                lineIntersectLineTem.setPkid(lilPkid);
                lineIntersectLineTem.setRuleAlgoritId(ruleAlgoritId);
                lineIntersectLineTemMapper.insert(lineIntersectLineTem);
            }
        }
        if (entityTypeRulesVo.getBeanLRCL() != null
                && entityTypeRulesVo.getBeanLRCL().size() > 0) {
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgorit(entityTypeRulesVo.getEntityTypeId(), "line_region_create_line", ruleAlgoritId);
            for (LineRegionCreateLineVo lineRegionCreateLineVo : entityTypeRulesVo.getBeanLRCL()) {
                LineRegionCreateLineTem lineRegionCreateLineTem = GsonUtil.ObjectToEntity(lineRegionCreateLineVo, LineRegionCreateLineTem.class);
                String pkid = UUID.randomUUID().toString();
                lineRegionCreateLineTem.setPkid(pkid);
                lineRegionCreateLineTem.setRuleAlgoritId(ruleAlgoritId);
                lineRegionCreateLineTemMapper.insert(lineRegionCreateLineTem);
            }
        }
    }

    private void insertETRuleAlgorit(String entityTypeId, String algorithmtype, String pkId) {
        //--规则和算法的关联信息
        EntitytypesRuleAlgoritTem entitytypesRuleAlgorit = new EntitytypesRuleAlgoritTem();
        entitytypesRuleAlgorit.setPkid(pkId);
        entitytypesRuleAlgorit.setEntitytypesid(entityTypeId);
        QueryWrapper<AlgorithmRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("algorithm_type", algorithmtype);
        AlgorithmRegister algorithmRegister = algorithmRegisterMapper.selectOne(queryWrapper);
        entitytypesRuleAlgorit.setAlgoritid(algorithmRegister.getPkid());
        entitytypesRuleAlgoritTemMapper.insert(entitytypesRuleAlgorit);
        //--规则和算法的关联信息
    }

    @Override
    public int updateRulesByETId(EntityTypeRulesVo entityTypeRulesVo) {
        int insertVal = 0;
        /**先删除lineIntersectLayerTemMapper,onLineTemMapper等五个算法表，然后删除entitytypesRuleAlgoritTemMapper表**/
        QueryWrapper<EntitytypesRuleAlgoritTem> entitytypesRuleAlgoritTemQueryWrapper = new QueryWrapper<>();
        entitytypesRuleAlgoritTemQueryWrapper.eq("entity_types_id", entityTypeRulesVo.getEntityTypeId());
        List<EntitytypesRuleAlgoritTem> entitytypesRuleAlgoritTems
                = entitytypesRuleAlgoritTemMapper.selectList(entitytypesRuleAlgoritTemQueryWrapper);
        if (entitytypesRuleAlgoritTems != null
                && entitytypesRuleAlgoritTems.size() > 0) {
            for (EntitytypesRuleAlgoritTem entitytypesRuleAlgoritTem : entitytypesRuleAlgoritTems) {
                if (StringUtils.isNotEmpty(entitytypesRuleAlgoritTem.getAlgoritid())) {
                    AlgorithmRegister algorithmRegister = algorithmRegisterMapper.selectById(entitytypesRuleAlgoritTem.getAlgoritid());
                    if (algorithmRegister != null) {
                        if (StringUtils.isNotEmpty(algorithmRegister.getAlgorithmtype())) {
                            String algorithmType = algorithmRegister.getAlgorithmtype();
                            if (algorithmType.equals("line_intersect_line")) {////管道转折角度规定
                                QueryWrapper<LineIntersectLineTem> lineIntersectLineTemQueryWrapper = new QueryWrapper<>();
                                lineIntersectLineTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineIntersectLineTemMapper.delete(lineIntersectLineTemQueryWrapper);
                            }
                            if (algorithmType.equals("lines_angle")) {////线图层与与管道交叉角度规定
                                QueryWrapper<LinesAngleTem> linesAngleTemQueryWrapper = new QueryWrapper<>();
                                linesAngleTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                linesAngleTemMapper.delete(linesAngleTemQueryWrapper);
                            }
                            if (algorithmType.equals("line_reference_object")) {////管线与参照物相交算法
                                QueryWrapper<LineReferenceObjectTem> lineReferenceObjectTemQueryWrapper = new QueryWrapper<>();
                                lineReferenceObjectTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineReferenceObjectTemMapper.delete(lineReferenceObjectTemQueryWrapper);
                            }
                            if (algorithmType.equals("on_line")) {//////沿管道中线创建
                                QueryWrapper<OnLineTem> onLineTemQueryWrapper = new QueryWrapper<>();
                                onLineTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                onLineTemMapper.delete(onLineTemQueryWrapper);
                            }
                            if (algorithmType.equals("line_intersect_layer")) {////面图层与管道交叉
                                QueryWrapper<LineIntersectLayerTem> lineIntersectLayerQueryWrapper = new QueryWrapper<>();
                                lineIntersectLayerQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineIntersectLayerTemMapper.delete(lineIntersectLayerQueryWrapper);
                            }
                            if (algorithmType.equals("line_region_create_line")) {//线与图层

                                QueryWrapper<LineRegionCreateLineTem> lineRegionCreateLineQueryWrapper = new QueryWrapper<>();
                                lineRegionCreateLineQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineRegionCreateLineTemMapper.delete(lineRegionCreateLineQueryWrapper);
                            }
                        }
                    }
                    entitytypesRuleAlgoritTemMapper.deleteById(entitytypesRuleAlgoritTem.getPkid());
                }
            }
        }

        EntitytypesRuleTem entitytypesRule = new EntitytypesRuleTem();
        Timestamp curTimestamp = new Timestamp(Calendar.getInstance().getTimeInMillis());
        entitytypesRule.setCreateuserid("1002");
        entitytypesRule.setUpdateuserid("1002");
        entitytypesRule.setCreatetime(curTimestamp);
        entitytypesRule.setUpdatetime(curTimestamp);
        entitytypesRule.setEntitytypesid(entityTypeRulesVo.getEntityTypeId());
        entityTypesRules(entityTypeRulesVo);
        insertVal = insertVal + entitytypesRuleTemMapper.updateById(entitytypesRule);
        return insertVal;
    }


    /**
     * 自动创建规则-删除
     */
    @Override
    public int delRulesByETId(String entitytypeid) {
        QueryWrapper<EntitytypesRuleAlgoritTem> entitytypesRuleAlgoritTemQueryWrapper = new QueryWrapper<>();
        entitytypesRuleAlgoritTemQueryWrapper.eq("entity_types_id", entitytypeid);
        List<EntitytypesRuleAlgoritTem> entitytypesRuleAlgoritTems
                = entitytypesRuleAlgoritTemMapper.selectList(entitytypesRuleAlgoritTemQueryWrapper);
        if (entitytypesRuleAlgoritTems != null
                && entitytypesRuleAlgoritTems.size() > 0) {
            for (EntitytypesRuleAlgoritTem entitytypesRuleAlgoritTem : entitytypesRuleAlgoritTems) {
                if (StringUtils.isNotEmpty(entitytypesRuleAlgoritTem.getAlgoritid())) {
                    AlgorithmRegister algorithmRegister = algorithmRegisterMapper.selectById(entitytypesRuleAlgoritTem.getAlgoritid());
                    if (algorithmRegister != null) {
                        if (StringUtils.isNotEmpty(algorithmRegister.getAlgorithmtype())) {
                            String algorithmType = algorithmRegister.getAlgorithmtype();
                            if (algorithmType.equals("line_intersect_line")) {////管道转折角度规定
                                QueryWrapper<LineIntersectLineTem> lineIntersectLineTemQueryWrapper = new QueryWrapper<>();
                                lineIntersectLineTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineIntersectLineTemMapper.delete(lineIntersectLineTemQueryWrapper);
                            }
                            if (algorithmType.equals("lines_angle")) {////线图层与与管道交叉角度规定
                                QueryWrapper<LinesAngleTem> linesAngleTemQueryWrapper = new QueryWrapper<>();
                                linesAngleTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                linesAngleTemMapper.delete(linesAngleTemQueryWrapper);
                            }
                            if (algorithmType.equals("line_reference_object")) {////管线与参照物相交算法
                                QueryWrapper<LineReferenceObjectTem> lineReferenceObjectTemQueryWrapper = new QueryWrapper<>();
                                lineReferenceObjectTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineReferenceObjectTemMapper.delete(lineReferenceObjectTemQueryWrapper);
                            }
                            if (algorithmType.equals("on_line")) {//////沿管道中线创建
                                QueryWrapper<OnLineTem> onLineTemQueryWrapper = new QueryWrapper<>();
                                onLineTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                onLineTemMapper.delete(onLineTemQueryWrapper);
                            }
                            if (algorithmType.equals("line_intersect_layer")) {////面图层与管道交叉
                                QueryWrapper<LineIntersectLayerTem> lineIntersectLayerQueryWrapper = new QueryWrapper<>();
                                lineIntersectLayerQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineIntersectLayerTemMapper.delete(lineIntersectLayerQueryWrapper);
                            }
                        }
                    }
                    entitytypesRuleAlgoritTemMapper.deleteById(entitytypesRuleAlgoritTem.getPkid());
                }

            }
        }
        QueryWrapper<EntitytypesRuleTem> entityTypesId = new QueryWrapper<>();
        entityTypesId.eq("entity_types_id", entitytypeid);
        entitytypesRuleTemMapper.delete(entityTypesId);
        return entitytypestemMapper.deleteById(entitytypeid);
    }

    @Override
    public int delProRulesByETId(String entityTypesId, String projectid) {
        QueryWrapper<EntitytypesRuleAlgoritTem> entitytypesRuleAlgoritTemQueryWrapper = new QueryWrapper<>();
        entitytypesRuleAlgoritTemQueryWrapper.eq("entity_types_id", entityTypesId);
        entitytypesRuleAlgoritTemQueryWrapper.eq("project_id", projectid);
        List<EntitytypesRuleAlgoritTem> entitytypesRuleAlgoritTems
                = entitytypesRuleAlgoritTemMapper.selectList(entitytypesRuleAlgoritTemQueryWrapper);
        if (entitytypesRuleAlgoritTems != null
                && entitytypesRuleAlgoritTems.size() > 0) {
            for (EntitytypesRuleAlgoritTem entitytypesRuleAlgoritTem : entitytypesRuleAlgoritTems) {
                if (StringUtils.isNotEmpty(entitytypesRuleAlgoritTem.getAlgoritid())) {
                    QueryWrapper<AlgorithmRegister> algorithmRegisterQueryWrapper = new QueryWrapper<>();
                    algorithmRegisterQueryWrapper.eq("pkid", entitytypesRuleAlgoritTem.getAlgoritid());
                    algorithmRegisterQueryWrapper.eq("project_id",projectid);
                    AlgorithmRegister algorithmRegister = algorithmRegisterMapper.selectOne(algorithmRegisterQueryWrapper);
                    if (algorithmRegister != null) {
                        if (StringUtils.isNotEmpty(algorithmRegister.getAlgorithmtype())) {
                            String algorithmType = algorithmRegister.getAlgorithmtype();
                            if (algorithmType.equals("line_intersect_line")) {////管道转折角度规定
                                QueryWrapper<LineIntersectLineTem> lineIntersectLineTemQueryWrapper = new QueryWrapper<>();
                                lineIntersectLineTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineIntersectLineTemQueryWrapper.eq("project_id", projectid);
                                lineIntersectLineTemMapper.delete(lineIntersectLineTemQueryWrapper);
                            }
                            if (algorithmType.equals("lines_angle")) {////线图层与与管道交叉角度规定
                                QueryWrapper<LinesAngleTem> linesAngleTemQueryWrapper = new QueryWrapper<>();
                                linesAngleTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                linesAngleTemQueryWrapper.eq("project_id", projectid);
                                linesAngleTemMapper.delete(linesAngleTemQueryWrapper);
                            }
                            if (algorithmType.equals("line_reference_object")) {////管线与参照物相交算法
                                QueryWrapper<LineReferenceObjectTem> lineReferenceObjectTemQueryWrapper = new QueryWrapper<>();
                                lineReferenceObjectTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineReferenceObjectTemQueryWrapper.eq("project_id", projectid);
                                lineReferenceObjectTemMapper.delete(lineReferenceObjectTemQueryWrapper);
                            }
                            if (algorithmType.equals("on_line")) {//////沿管道中线创建
                                QueryWrapper<OnLineTem> onLineTemQueryWrapper = new QueryWrapper<>();
                                onLineTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                onLineTemQueryWrapper.eq("project_id", projectid);
                                onLineTemMapper.delete(onLineTemQueryWrapper);
                            }
                            if (algorithmType.equals("line_intersect_layer")) {////面图层与管道交叉
                                QueryWrapper<LineIntersectLayerTem> lineIntersectLayerQueryWrapper = new QueryWrapper<>();
                                lineIntersectLayerQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineIntersectLayerQueryWrapper.eq("project_id", projectid);
                                lineIntersectLayerTemMapper.delete(lineIntersectLayerQueryWrapper);
                            }
                        }
                    }
                    QueryWrapper<EntitytypesRuleAlgoritTem> entitytypesRuleAlgoritTemQueryWrapper1 = new QueryWrapper<>();
                    entitytypesRuleAlgoritTemQueryWrapper1.eq("pkid", entitytypesRuleAlgoritTem.getPkid());
                    entitytypesRuleAlgoritTemQueryWrapper1.eq("project_id", projectid);
                    entitytypesRuleAlgoritTemMapper.delete(entitytypesRuleAlgoritTemQueryWrapper1);
                }

            }
        }
        QueryWrapper<EntitytypesRule> entityTypesQueryQueryWrapper = new QueryWrapper<>();
        entityTypesQueryQueryWrapper.eq("entity_types_id", entityTypesId);
        entityTypesQueryQueryWrapper.eq("project_id", projectid);
        entitytypesRuleMapper.delete(entityTypesQueryQueryWrapper);
        QueryWrapper<Entitytypes> entitytypesQueryWrapper = new QueryWrapper<>();
        entitytypesQueryWrapper.eq("pkid",entityTypesId);
        entitytypesQueryWrapper.eq("project_id", projectid);
        return entitytypesMapper.delete(entitytypesQueryWrapper);
    }

    @Override
    public int updateProRulesByETId(EntityTypeRulesVo entityTypeRulesVo) {

        String projectId = entityTypeRulesVo.getProjectId();
        int insertVal = 0;
        /**先删除lineIntersectLayerTemMapper,onLineTemMapper等五个算法表，然后删除entitytypesRuleAlgoritTemMapper表**/
        QueryWrapper<EntitytypesRuleAlgorit> entitytypesRuleAlgoritTemQueryWrapper = new QueryWrapper<>();
        entitytypesRuleAlgoritTemQueryWrapper.eq("entity_types_id", entityTypeRulesVo.getEntityTypeId());
        entitytypesRuleAlgoritTemQueryWrapper.eq("project_id",projectId);
        List<EntitytypesRuleAlgorit> entitytypesRuleAlgoritTems
                = entitytypesRuleAlgoritMapper.selectList(entitytypesRuleAlgoritTemQueryWrapper);
        if (entitytypesRuleAlgoritTems != null
                && entitytypesRuleAlgoritTems.size() > 0) {
            for (EntitytypesRuleAlgorit entitytypesRuleAlgoritTem : entitytypesRuleAlgoritTems) {
                if (StringUtils.isNotEmpty(entitytypesRuleAlgoritTem.getAlgoritid())) {
                    AlgorithmRegister algorithmRegister = algorithmRegisterMapper.selectById(entitytypesRuleAlgoritTem.getAlgoritid());
                    if (algorithmRegister != null) {
                        if (StringUtils.isNotEmpty(algorithmRegister.getAlgorithmtype())) {
                            String algorithmType = algorithmRegister.getAlgorithmtype();
                            if (algorithmType.equals("line_intersect_line")) {////管道转折角度规定
                                QueryWrapper<LineIntersectLine> lineIntersectLineTemQueryWrapper = new QueryWrapper<>();
                                lineIntersectLineTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineIntersectLineTemQueryWrapper.eq("project_id",projectId);
                                lineIntersectLineMapper.delete(lineIntersectLineTemQueryWrapper);
                            }
                            if (algorithmType.equals("lines_angle")) {////线图层与与管道交叉角度规定
                                QueryWrapper<LinesAngle> linesAngleTemQueryWrapper = new QueryWrapper<>();
                                linesAngleTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                linesAngleTemQueryWrapper.eq("project_id",projectId);
                                linesAngleMapper.delete(linesAngleTemQueryWrapper);
                            }
                            if (algorithmType.equals("line_reference_object")) {////管线与参照物相交算法
                                QueryWrapper<LineReferenceObject> lineReferenceObjectTemQueryWrapper = new QueryWrapper<>();
                                lineReferenceObjectTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineReferenceObjectTemQueryWrapper.eq("project_id",projectId);
                                lineReferenceObjectMapper.delete(lineReferenceObjectTemQueryWrapper);
                            }
                            if (algorithmType.equals("on_line")) {//////沿管道中线创建
                                QueryWrapper<OnLine> onLineTemQueryWrapper = new QueryWrapper<>();
                                onLineTemQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                onLineTemQueryWrapper.eq("project_id",projectId);
                                onLineMapper.delete(onLineTemQueryWrapper);
                            }
                            if (algorithmType.equals("line_intersect_layer")) {////面图层与管道交叉
                                QueryWrapper<LineIntersectLayer> lineIntersectLayerQueryWrapper = new QueryWrapper<>();
                                lineIntersectLayerQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineIntersectLayerQueryWrapper.eq("project_id",projectId);
                                lineIntersectLayerMapper.delete(lineIntersectLayerQueryWrapper);
                            }
                            if (algorithmType.equals("line_region_create_line")) {//线与图层
                                QueryWrapper<LineRegionCreateLine> lineRegionCreateLineQueryWrapper = new QueryWrapper<>();
                                lineRegionCreateLineQueryWrapper.eq("rule_algorit_id", entitytypesRuleAlgoritTem.getPkid());
                                lineRegionCreateLineQueryWrapper.eq("project_id",projectId);
                                lineRegionCreateLineMapper.delete(lineRegionCreateLineQueryWrapper);
                            }
                        }
                    }
                    entitytypesRuleAlgoritMapper.delete(entitytypesRuleAlgoritTemQueryWrapper);
                }
            }
        }

        EntitytypesRule entitytypesRule = new EntitytypesRule();
        Timestamp curTimestamp = new Timestamp(Calendar.getInstance().getTimeInMillis());
        entitytypesRule.setCreateuserid("1002");
        entitytypesRule.setUpdateuserid("1002");
        entitytypesRule.setCreatetime(curTimestamp);
        entitytypesRule.setUpdatetime(curTimestamp);
        entitytypesRule.setEntitytypesid(entityTypeRulesVo.getEntityTypeId());
        entityTypesRulesPro(entityTypeRulesVo);
        QueryWrapper<EntitytypesRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("entity_types_id",entityTypeRulesVo.getEntityTypeId());
        queryWrapper.eq("project_id",projectId);
        insertVal = insertVal + entitytypesRuleMapper.update(entitytypesRule,queryWrapper);
        return insertVal;

    }

    private void entityTypesRulesPro(EntityTypeRulesVo entityTypeRulesVo) {
        String projefctId = entityTypeRulesVo.getProjectId();
        if (entityTypeRulesVo.getBeanLLIL() != null
                && entityTypeRulesVo.getBeanLLIL().size() > 0) {//面图层与管道交叉
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgoritPro(entityTypeRulesVo.getEntityTypeId(), "line_intersect_layer", ruleAlgoritId, entityTypeRulesVo.getProjectId());
            for (LineIntersectLayerVo linel : entityTypeRulesVo.getBeanLLIL()) {
                LineIntersectLayer lineIntersectLayer = GsonUtil.ObjectToEntity(linel, LineIntersectLayer.class);
                String pkId = UUID.randomUUID().toString();
                lineIntersectLayer.setPkid(pkId);
                lineIntersectLayer.setRuleAlgoritId(ruleAlgoritId);
                lineIntersectLayer.setProjectId(projefctId);
                lineIntersectLayerMapper.insert(lineIntersectLayer);
            }
        }

        if (entityTypeRulesVo.getBeanSE() != null
                && entityTypeRulesVo.getBeanSE().size() > 0) {//沿管道中线创建
            for (EntityTypeRulesVo.SpacingEntity spacingEntity : entityTypeRulesVo.getBeanSE()) {
                String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
                OnLine onLine = new OnLine();
                onLine.setPkid(UUID.randomUUID().toString());
                if (StringUtils.isNotBlank(spacingEntity.getDistance())) {
                    onLine.setDistance(Integer.parseInt(spacingEntity.getDistance()));
                }
                if (StringUtils.isNotBlank(spacingEntity.getSpacing())) {
                    onLine.setSpacing(Integer.parseInt(spacingEntity.getSpacing()));
                }
                onLine.setRulealgoritid(ruleAlgoritId);
                onLine.setProjectid(projefctId);
                onLineMapper.insert(onLine);
                insertETRuleAlgoritPro(entityTypeRulesVo.getEntityTypeId(), "on_line", ruleAlgoritId, entityTypeRulesVo.getProjectId());
            }
        }

        if (entityTypeRulesVo.getBeanLLRO() != null
                && entityTypeRulesVo.getBeanLLRO().size() > 0) {//管线与参照物相交算法
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgoritPro(entityTypeRulesVo.getEntityTypeId(), "line_reference_object", ruleAlgoritId, entityTypeRulesVo.getProjectId());
            for (LineReferenceObjectVo tem : entityTypeRulesVo.getBeanLLRO()) {
                LineReferenceObject lineReferenceObject = GsonUtil.ObjectToEntity(tem, LineReferenceObject.class);
                String lroPkid = UUID.randomUUID().toString();
                lineReferenceObject.setPkid(lroPkid);
                lineReferenceObject.setRulealgoritid(ruleAlgoritId);
                lineReferenceObject.setProjectid(projefctId);
                lineReferenceObjectMapper.insert(lineReferenceObject);
            }
        }

        if (entityTypeRulesVo.getBeanLLA() != null
                && entityTypeRulesVo.getBeanLLA().size() > 0) {//线图层与与管道交叉角度规定
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgoritPro(entityTypeRulesVo.getEntityTypeId(), "lines_angle", ruleAlgoritId, entityTypeRulesVo.getProjectId());
            for (LinesAngleVo temAngle : entityTypeRulesVo.getBeanLLA()) {
                LinesAngle linesAngle = GsonUtil.ObjectToEntity(temAngle, LinesAngle.class);
                String linesAnglePkid = UUID.randomUUID().toString();
                linesAngle.setPkid(linesAnglePkid);
                linesAngle.setRulealgoritid(ruleAlgoritId);
                linesAngle.setProjectid(projefctId);
                linesAngleMapper.insert(linesAngle);
            }
        }

        if (entityTypeRulesVo.getBeanLIL() != null
                && entityTypeRulesVo.getBeanLIL().size() > 0) {//管道转折角度规定
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgoritPro(entityTypeRulesVo.getEntityTypeId(), "line_intersect_line", ruleAlgoritId, entityTypeRulesVo.getProjectId());
            for (LineIntersectLineVo lineITemLine : entityTypeRulesVo.getBeanLIL()) {
                LineIntersectLine lineIntersectLine = GsonUtil.ObjectToEntity(lineITemLine, LineIntersectLine.class);
                String lilPkid = UUID.randomUUID().toString();
                lineIntersectLine.setPkid(lilPkid);
                lineIntersectLine.setRulealgoritid(ruleAlgoritId);
                lineIntersectLine.setProjectid(projefctId);
                lineIntersectLine.setAnglemax(lineITemLine.getAngleMax());
                lineIntersectLine.setAnglemin(lineITemLine.getAngleMin());
                lineIntersectLineMapper.insert(lineIntersectLine);
            }
        }
        if (entityTypeRulesVo.getBeanLRCL() != null
                && entityTypeRulesVo.getBeanLRCL().size() > 0) {
            String ruleAlgoritId = UUID.randomUUID().toString();//中间表id
            insertETRuleAlgoritPro(entityTypeRulesVo.getEntityTypeId(), "line_region_create_line", ruleAlgoritId ,entityTypeRulesVo.getProjectId());
            for (LineRegionCreateLineVo lineRegionCreateLineVo : entityTypeRulesVo.getBeanLRCL()) {
                LineRegionCreateLine lineRegionCreateLineTem = GsonUtil.ObjectToEntity(lineRegionCreateLineVo, LineRegionCreateLine.class);
                String pkid = UUID.randomUUID().toString();
                lineRegionCreateLineTem.setPkid(pkid);
                lineRegionCreateLineTem.setRuleAlgoritId(ruleAlgoritId);
                lineRegionCreateLineTem.setProjectId(projefctId);
                lineRegionCreateLineMapper.insert(lineRegionCreateLineTem);
            }
        }

    }

    private void insertETRuleAlgoritPro(String entityTypeId, String algorithmtype, String pkId, String projectid) {
        //--规则和算法的关联信息
        EntitytypesRuleAlgorit entitytypesRuleAlgorit = new EntitytypesRuleAlgorit();
        entitytypesRuleAlgorit.setPkid(pkId);
        entitytypesRuleAlgorit.setEntitytypesid(entityTypeId);
        QueryWrapper<AlgorithmRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("algorithm_type", algorithmtype);
        AlgorithmRegister algorithmRegister = algorithmRegisterMapper.selectOne(queryWrapper);
        entitytypesRuleAlgorit.setAlgoritid(algorithmRegister.getPkid());
        entitytypesRuleAlgorit.setProjectid(projectid);
        entitytypesRuleAlgoritMapper.insert(entitytypesRuleAlgorit);
        //--规则和算法的关联信息
    }

}
