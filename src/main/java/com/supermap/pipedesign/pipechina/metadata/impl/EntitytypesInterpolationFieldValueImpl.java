package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationFieldValue;
import com.supermap.pipedesign.pipechina.metadata.dao.EntitytypesInterpolationFieldValueMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IEntitytypesInterpolationFieldValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 实体-插值表-字段表-存储值 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@Service("EntitytypesInterpolationFieldValueImpl")
public class EntitytypesInterpolationFieldValueImpl extends ServiceImpl<EntitytypesInterpolationFieldValueMapper, EntitytypesInterpolationFieldValue> implements IEntitytypesInterpolationFieldValueService {

    @Autowired
    private EntitytypesInterpolationFieldValueMapper entitytypesInterpolationFieldValueMapper;

    /**
    * 添加实体-插值表-字段表-存储值信息
    *
    * @param entitytypesInterpolationFieldValue
    * @return int
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public int insert(EntitytypesInterpolationFieldValue entitytypesInterpolationFieldValue) {

        //entitytypesInterpolationFieldValue.setUserId(JavaUtils.getUUID36());
        //entitytypesInterpolationFieldValue.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return entitytypesInterpolationFieldValueMapper.insert(entitytypesInterpolationFieldValue);
    }

    /**
    * 删除实体-插值表-字段表-存储值信息
    *
    * @param entitytypesInterpolationFieldValueId
    * @return int
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public int delete(String entitytypesInterpolationFieldValueId) {
        return entitytypesInterpolationFieldValueMapper.deleteById(entitytypesInterpolationFieldValueId);
    }

    /**
    * 更新实体-插值表-字段表-存储值信息
    *
    * @param entitytypesInterpolationFieldValue
    * @return int
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public int update(EntitytypesInterpolationFieldValue entitytypesInterpolationFieldValue) {
        return entitytypesInterpolationFieldValueMapper.updateById(entitytypesInterpolationFieldValue);
    }

    /**
    * 全部查询
    *
    * @param entitytypesInterpolationFieldValue
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationFieldValue>
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public List<EntitytypesInterpolationFieldValue> list(EntitytypesInterpolationFieldValue entitytypesInterpolationFieldValue) {

        QueryWrapper<EntitytypesInterpolationFieldValue> queryWrapper = new QueryWrapper<>();

        return entitytypesInterpolationFieldValueMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<EntitytypesInterpolationFieldValue> entitytypesInterpolationFieldValueIPage = new Page<>();
        entitytypesInterpolationFieldValueIPage.setCurrent(current);
        entitytypesInterpolationFieldValueIPage.setSize(size);

        QueryWrapper<EntitytypesInterpolationFieldValue> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return entitytypesInterpolationFieldValueMapper.selectPage(entitytypesInterpolationFieldValueIPage, queryWrapper);
    }


}
