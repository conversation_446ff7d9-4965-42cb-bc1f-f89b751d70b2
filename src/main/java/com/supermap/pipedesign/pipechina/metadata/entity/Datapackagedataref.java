package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 数据包-数据关联表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_data_package_dataref")
@ApiModel(value="Datapackagedataref对象", description="数据包-数据关联表")
public class Datapackagedataref implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "数据包编号")
    @TableField("data_type_id")
    private String datatypeid;

    @ApiModelProperty(value = "数据类型(1:关系型数据;2:非关系型数据)")
    @TableField("data_type")
    private String datatype;

    @ApiModelProperty(value = "数据集编号(如果DataType为1时，存储表PL_MD_DATASETINFO的PKID；	如果DataType为2时，存储文件编码或文档服务中心的URL)")
    @TableField("data_set_id")
    private String datasetid;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
