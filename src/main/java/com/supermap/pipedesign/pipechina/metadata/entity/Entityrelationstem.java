package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 实体关联表模板(存储实体和实体之间的关联关系。主要用于存储衍生实体。) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_entityrelations_tem")
@ApiModel(value="EntityrelationsTem对象", description="实体关联表模板(存储实体和实体之间的关联关系。主要用于存储衍生实体。)")
public class Entityrelationstem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "主实体编号")
    @TableField("main_entity_id")
    private String mainentityid;

    @ApiModelProperty(value = "衍生实体编号")
    @TableField("derivative_entity_id")
    private String derivativeentityid;


}
