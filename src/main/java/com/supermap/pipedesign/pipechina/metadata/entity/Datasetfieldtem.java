package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * 字段表模板 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_dataset_field_tem")
@ApiModel(value="DatasetfieldTem对象", description="字段表模板")
public class Datasetfieldtem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "字段名")
    @TableField("field_name")
    private String fieldname;

    @ApiModelProperty(value = "字段别名")
    @TableField("field_alias")
    private String fieldalias;

    @ApiModelProperty(value = "字段类型枚举值。各个枚举值代表的含义如下：	INTEGER(\"整型\", 1), 	FLOAT(\"单精度型\", 2),	DECIMAL(\"数值型\", 3), 	STRING(\"字符串型\", 4), 	CLOB(\"块型\", 5), 	DATE(\"日期型\", 6),	DOUBLE(\"双精度型\", 7), 	BOOLEAN(\"布尔型\", 8)")
    @TableField("field_type")
    private String fieldtype;

    @ApiModelProperty(value = "字段大小")
    @TableField("field_size")
    private Integer fieldsize;

    @ApiModelProperty(value = "是否主键字段")
    @TableField("is_mainkey_field")
    private Boolean ismainkeyfield;

    @ApiModelProperty(value = "长度可否为0")
    @TableField("can_zero_length")
    private Boolean canzerolength;

    @ApiModelProperty(value = "是否自增字段")
    @TableField("is_auto_increment")
    private Boolean isautoincrement;

    @ApiModelProperty(value = "默认值")
    @TableField("default_value")
    private String defaultvalue;

    @ApiModelProperty(value = "值域(0是取值范围 存最大、最小、存正则校验，1是数据字典，id存储在正则，2是材料库，id存储在正则)")
    @TableField("field_range")
    private String fieldrange;

    @ApiModelProperty(value = "是否必填字段")
    @TableField("is_required")
    private Boolean isrequired;

    @ApiModelProperty(value = "是否只读字段")
    @TableField("is_read_only")
    private Boolean isreadonly;

    @ApiModelProperty(value = "该字段是否参与实体设计及构造")
    @TableField("is_design_field")
    private Boolean isdesignfield;

    @ApiModelProperty(value = "可变长度")
    @TableField("variable_length")
    private Integer variablelength;

    @ApiModelProperty(value = "最大值")
    @TableField("max_values")
    private String maxvalues;

    @ApiModelProperty(value = "最小值")
    @TableField("min_values")
    private String minvalues;

    @ApiModelProperty(value = "正则表达式")
    @TableField("regular_expression")
    private String regularexpression;

    @ApiModelProperty(value = "是否为查询字段")
    @TableField("is_queryable")
    private Boolean isqueryable;

    @ApiModelProperty(value = "数据集ID")
    @TableField("data_set_id")
    private String datasetid;

    @ApiModelProperty(value = "顺序号")
    @TableField("order_index")
    private Integer orderindex;

    @ApiModelProperty(value = "字段描述")
    @TableField("desc_info")
    private String descinfo;

    @ApiModelProperty(value = "序列启用时间")
    @TableField("sequence_start")
    private Timestamp sequencestart;

    @ApiModelProperty(value = "序列失效时间")
    @TableField("sequence_end")
    private Timestamp sequenceend;

    @ApiModelProperty(value = "序列状态(1:生效;-1:失效;0:未被初始化)")
    @TableField("sequence_state")
    private Integer sequencestate;

    @ApiModelProperty(value = "序列上一版本ID")
    @TableField("sequence_org_id")
    private String sequenceorgid;

    @ApiModelProperty(value = "是否为唯一字段")
    @TableField("is_uniquefld")
    private Boolean isuniquefld;

    @ApiModelProperty(value = "取值方式")
    @TableField("value_type")
    private String valuetype;

    @ApiModelProperty(value = "分组(0为系统字段,1为业务字段,2为汇总属性)")
    @TableField("is_extend")
    private Integer isextend;

    @ApiModelProperty(value = "字段英文名")
    @TableField("field_enname")
    private String fieldenname;

    @ApiModelProperty(value = "所属实体类型定义模板UUID。将所属的实体类型ID放到这里，而不是单独建关联表去存储，这样才能有效提升系统性能。")
    @TableField("entity_type_id")
    private String entitytypeid;

    @ApiModelProperty(value = "单位（1、m  2 km ）")
    @TableField("units")
    private String units;

    @ApiModelProperty(value = "精度")
    @TableField("precision")
    private Double precision;

    @ApiModelProperty(value = "是否显示阶段（多个逗号分隔  0,1,2,3,4 预可研0、可研1、初设2、施工3、竣工4）")
    @TableField("stage_code")
    private String stagecode;


}
