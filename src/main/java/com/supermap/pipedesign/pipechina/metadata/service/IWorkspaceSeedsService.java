package com.supermap.pipedesign.pipechina.metadata.service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipOutputStream;

/**
 * 种子工作空间服务,生成公共文件，不允许并发
 * <AUTHOR>
 * @Date   2023-2-26
 */
public interface IWorkspaceSeedsService {

    /**
     * 从服务端获取压缩后的种子工作空间文件下载到本地
     * @param response
     */
    void fetchNewWorkspaceFiles(HttpServletResponse response);

    /**
     * @描述 拉取最新工作空间生成种子
     * @param projectId 项目ID
     * @param unitId    工程单元或设计段ID
     * @param filePath  种子模板文件夹路径
     * @param strExecuteTime 开始执行时间
     * @日期 2023/03/06 21:51
     * @作者 eomer
     **/
    Boolean loadNewWorkspace(String projectId, String unitId, String filePath, String strExecuteTime);

    /**
     * 为项目预创建对应的种子工作空间，包括工作空间文件和UDBX文件，并创建对应的数据集
     * @param strTaskId 分布式任务ID
     * @return
     */
    String reCreateWorkspaceSeedsUDBX(String entityTypeId, String strTaskId);
    String createProjectDB(String projectid, String desginId, String strTaskKey,String filePath);
    /**
     * 为项目预创建对应的种子工作空间，包括工作空间文件和UDBX文件，并创建对应的数据集(从项目级实体定义里生成)
     * @param strTaskId 分布式任务ID
     * @return
     */
    String reCreateWorkspaceSeedsUDBXFromProject(String entityTypeId, String strTaskId, String projectId);
    /**
     * 为项目预创建对应的种子工作空间，包括工作空间文件和UDBX文件，并创建对应的数据集
     * @return
     */
    void reCreateEntitytemDB(String entityTypeId);

    List<String> checkDbTablesFields(String dbPath, String projectId);
    List<String> createTabularTables(String projectId);
    /**
     * 为项目预创建对应的种子工作空间，包括工作空间文件和UDBX文件，并创建对应的数据集
     * @return
     */
    List<String> reCreateEntityDB(String entityTypeId, String projectId);

    /**
     * 为项目重新创建对应的种子工作空间
     * @param projectId 项目ID
     * @return
     */
    Map<String, Object> rebuildProjectWorkspaceSeed(String projectId);

    /**
     * 通过分布式的方式为项目重新创建对应的种子工作空间
     * @param projectId 项目ID
     * @param strTaskId 分布式任务ID
     * @return
     */
    Map<String, Object> rebuildProjectWorkspaceSeedDistributed(String projectId, String strTaskId);
}
