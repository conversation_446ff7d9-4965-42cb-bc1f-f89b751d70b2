package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 实体类型-数据分类关联表(存储实体类型与数据类型的关联关系。对于指定的实体类型，当在该表中找到了对应的数据类型时，就在对应的目录节点下展示关联的数据类型及下属数据。) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_entitytype_datatyperef")
@ApiModel(value="Entitytypedatatyperef对象", description="实体类型-数据分类关联表(存储实体类型与数据类型的关联关系。对于指定的实体类型，当在该表中找到了对应的数据类型时，就在对应的目录节点下展示关联的数据类型及下属数据。)")
public class Entitytypedatatyperef implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "实体类型编号")
    @TableField("entity_type_id")
    private String entitytypeid;

    @ApiModelProperty(value = "数据类型编号")
    @TableField("data_type_id")
    private String datatypeid;

    @ApiModelProperty(value = "顺序号(用来排序)")
    @TableField("order_index")
    private Integer orderindex;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
