package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * $数据源注册表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_datasource_info")
@ApiModel(value="Datasourceinfo对象", description="$数据源注册表")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class MdDatasourceinfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "数据源名称")
    @TableField("data_source_name")
    private String datasourcename;

    @ApiModelProperty(value = "数据源别名")
    @TableField("data_source_alias")
    private String datasourcealias;

    @ApiModelProperty(value = "引擎类型枚举值。枚举值的定义参考com.supermap.data.EngineType中的对应关系。")
    @TableField("engine_type")
    private Integer enginetype;

    @ApiModelProperty(value = "服务器地址")
    @TableField("data_base_server")
    private String databaseserver;

    @ApiModelProperty(value = "服务器端口号")
    @TableField("data_base_port")
    private Integer databaseport;

    @ApiModelProperty(value = "数据库名称")
    @TableField("data_base_name")
    private String databasename;

    @ApiModelProperty(value = "用户名")
    @TableField("data_base_user")
    private String databaseuser;

    @ApiModelProperty(value = "密码")
    @TableField("data_base_pwd")
    private String databasepwd;

    @ApiModelProperty(value = "数据比例尺")
    @TableField("datas_ource_scale")
    private Integer datasourcescale;

    @ApiModelProperty(value = "数据类型 0 成果数据 1 公共基础数据")
    @TableField("data_type")
    private Integer datatype;

    @ApiModelProperty(value = "所属数据源目录节点ID。如果需要对数据源进行编目的话使用该字段存储所属的上级目录节点ID。")
    @TableField("directory_id")
    private String directoryid;

    @ApiModelProperty(value = "创建日期")
    @TableField("create_date")
    private Timestamp createdate;

    @ApiModelProperty(value = "创建用户ID")
    @TableField("create_userid")
    private String createuserid;

    @ApiModelProperty(value = "坐标单位")
    @TableField("coord_inate_unit")
    private String coordinateunit;

    @ApiModelProperty(value = "投影坐标系名称")
    @TableField("prj_coord_sys_name")
    private String prjcoordsysname;

    @ApiModelProperty(value = "投影坐标系描述")
    @TableField("prj_coord_sys_desc")
    private String prjcoordsysdesc;

    @ApiModelProperty(value = "数据源描述")
    @TableField("data_source_desc")
    private String datasourcedesc;

    @ApiModelProperty(value = "顺序号")
    @TableField("order_index")
    private Integer orderindex;

    @ApiModelProperty(value = "是否为系统数据源(预留字段)")
    @TableField("is_sysdb")
    private Boolean issysdb;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
