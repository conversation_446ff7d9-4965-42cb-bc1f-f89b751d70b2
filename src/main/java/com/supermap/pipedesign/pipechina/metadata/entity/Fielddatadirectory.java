package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 外业踏勘数据目录表(存储外业踏勘相关的目录结构) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_field_data_directory")
@ApiModel(value="Fielddatadirectory对象", description="外业踏勘数据目录表(存储外业踏勘相关的目录结构)")
public class Fielddatadirectory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "上级编号")
    @TableField("parent_id")
    private String parentid;

    @ApiModelProperty(value = "目录名称")
    @TableField("directory_name")
    private String directoryname;

    @ApiModelProperty(value = "目录别名")
    @TableField("directory_alias")
    private String directoryalias;

    @ApiModelProperty(value = "关联任务段编号")
    @TableField("task_id")
    private String taskid;

    @ApiModelProperty(value = "目录类型(1:固定目录;2:自定义目录)")
    @TableField("directory_type")
    private Integer directorytype;

    @ApiModelProperty(value = "创建人")
    @TableField("creator_id")
    private String creatorid;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Timestamp createtime;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
