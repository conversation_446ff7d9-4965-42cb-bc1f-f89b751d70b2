package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesPartsTem;
import com.supermap.pipedesign.pipechina.metadata.dao.MdEntitytypeStylesPartsTemMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IMdEntitytypeStylesPartsTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 实体普通风格部件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-18
 */
@Service("MdEntitytypeStylesPartsTemImpl")
public class MdEntitytypeStylesPartsTemImpl extends ServiceImpl<MdEntitytypeStylesPartsTemMapper, MdEntitytypeStylesPartsTem> implements IMdEntitytypeStylesPartsTemService {

    @Autowired
    private MdEntitytypeStylesPartsTemMapper mdEntitytypeStylesPartsTemMapper;

    /**
    * 添加实体普通风格部件表信息
    *
    * @param mdEntitytypeStylesPartsTem
    * @return int
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public int insert(MdEntitytypeStylesPartsTem mdEntitytypeStylesPartsTem) {

        //mdEntitytypeStylesPartsTem.setUserId(JavaUtils.getUUID36());
        //mdEntitytypeStylesPartsTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return mdEntitytypeStylesPartsTemMapper.insert(mdEntitytypeStylesPartsTem);
    }

    /**
    * 删除实体普通风格部件表信息
    *
    * @param mdEntitytypeStylesPartsTemId
    * @return int
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public int delete(String mdEntitytypeStylesPartsTemId) {
        return mdEntitytypeStylesPartsTemMapper.deleteById(mdEntitytypeStylesPartsTemId);
    }

    /**
    * 更新实体普通风格部件表信息
    *
    * @param mdEntitytypeStylesPartsTem
    * @return int
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public int update(MdEntitytypeStylesPartsTem mdEntitytypeStylesPartsTem) {
        return mdEntitytypeStylesPartsTemMapper.updateById(mdEntitytypeStylesPartsTem);
    }

    /**
    * 全部查询
    *
    * @param mdEntitytypeStylesPartsTem
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesPartsTem>
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public List<MdEntitytypeStylesPartsTem> list(MdEntitytypeStylesPartsTem mdEntitytypeStylesPartsTem) {

        QueryWrapper<MdEntitytypeStylesPartsTem> queryWrapper = new QueryWrapper<>();

        return mdEntitytypeStylesPartsTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<MdEntitytypeStylesPartsTem> mdEntitytypeStylesPartsTemIPage = new Page<>();
        mdEntitytypeStylesPartsTemIPage.setCurrent(current);
        mdEntitytypeStylesPartsTemIPage.setSize(size);

        QueryWrapper<MdEntitytypeStylesPartsTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return mdEntitytypeStylesPartsTemMapper.selectPage(mdEntitytypeStylesPartsTemIPage, queryWrapper);
    }


}
