package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.EntityTypeRulesVo;
import com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesRuleAlgorit;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 规则和算法的关联信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IGisEntitytypesRuleAlgoritService extends IService<EntitytypesRuleAlgorit> {

 List<EntityTypeRulesVo> gisEntityTypeRules(String projectid,String entityTypesIds);
}
