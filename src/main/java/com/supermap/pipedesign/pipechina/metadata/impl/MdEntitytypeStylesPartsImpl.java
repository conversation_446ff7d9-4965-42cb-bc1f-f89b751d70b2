package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesParts;
import com.supermap.pipedesign.pipechina.metadata.dao.MdEntitytypeStylesPartsMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IMdEntitytypeStylesPartsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 实体普通风格部件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-18
 */
@Service("MdEntitytypeStylesPartsImpl")
public class MdEntitytypeStylesPartsImpl extends ServiceImpl<MdEntitytypeStylesPartsMapper, MdEntitytypeStylesParts> implements IMdEntitytypeStylesPartsService {

    @Autowired
    private MdEntitytypeStylesPartsMapper mdEntitytypeStylesPartsMapper;

    /**
    * 添加实体普通风格部件表信息
    *
    * @param mdEntitytypeStylesParts
    * @return int
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public int insert(MdEntitytypeStylesParts mdEntitytypeStylesParts) {

        //mdEntitytypeStylesParts.setUserId(JavaUtils.getUUID36());
        //mdEntitytypeStylesParts.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return mdEntitytypeStylesPartsMapper.insert(mdEntitytypeStylesParts);
    }

    /**
    * 删除实体普通风格部件表信息
    *
    * @param mdEntitytypeStylesPartsId
    * @return int
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public int delete(String mdEntitytypeStylesPartsId) {
        return mdEntitytypeStylesPartsMapper.deleteById(mdEntitytypeStylesPartsId);
    }

    /**
    * 更新实体普通风格部件表信息
    *
    * @param mdEntitytypeStylesParts
    * @return int
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public int update(MdEntitytypeStylesParts mdEntitytypeStylesParts) {
        return mdEntitytypeStylesPartsMapper.updateById(mdEntitytypeStylesParts);
    }

    /**
    * 全部查询
    *
    * @param mdEntitytypeStylesParts
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesParts>
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public List<MdEntitytypeStylesParts> list(MdEntitytypeStylesParts mdEntitytypeStylesParts) {

        QueryWrapper<MdEntitytypeStylesParts> queryWrapper = new QueryWrapper<>();

        return mdEntitytypeStylesPartsMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-18
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<MdEntitytypeStylesParts> mdEntitytypeStylesPartsIPage = new Page<>();
        mdEntitytypeStylesPartsIPage.setCurrent(current);
        mdEntitytypeStylesPartsIPage.setSize(size);

        QueryWrapper<MdEntitytypeStylesParts> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return mdEntitytypeStylesPartsMapper.selectPage(mdEntitytypeStylesPartsIPage, queryWrapper);
    }


}
