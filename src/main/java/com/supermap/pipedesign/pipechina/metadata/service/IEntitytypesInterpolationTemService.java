package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 实体-插值表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@Repository
public interface IEntitytypesInterpolationTemService extends IService<EntitytypesInterpolationTem> {

 /**
 * 添加实体-插值表信息
 *
 * @param entitytypesInterpolationTem
 * @return int
 * @Date 2023-03-25
 * @auther eomer
 */
 int insert(EntitytypesInterpolationTem entitytypesInterpolationTem);

 /**
 * 删除实体-插值表信息
 *
 * @param entitytypesInterpolationTemId
 * @return int
 * @Date 2023-03-25
 * @auther eomer
 */
 int delete(String entitytypesInterpolationTemId);

 /**
 * 更新实体-插值表信息
 *
 * @param entitytypesInterpolationTem
 * @return int
 * @Date 2023-03-25
 * @auther eomer
 */
 int update(EntitytypesInterpolationTem entitytypesInterpolationTem);

 /**
 * 全部查询
 *
 * @param entitytypesInterpolationTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationTem>
 * @Date 2023-03-25
 * @auther eomer
 */
 List<EntitytypesInterpolationTem> list(EntitytypesInterpolationTem entitytypesInterpolationTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-25
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
