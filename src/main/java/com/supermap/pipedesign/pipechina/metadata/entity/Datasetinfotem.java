package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 * $数据集注册表模板 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_dataset_info_tem")
@ApiModel(value="DatasetinfoTem对象", description="$数据集注册表模板")
public class Datasetinfotem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "数据集名称")
    @TableField("data_set_name")
    private String datasetname;

    @ApiModelProperty(value = "数据集类型编码(0:属性数据集;1:点数据集;2:线数据集;3:面数据集;4:模型数据集)")
    @TableField("data_set_type")
    private Integer datasettype;

    @ApiModelProperty(value = "数据集别名")
    @TableField("data_set_alias")
    private String datasetalias;

    @ApiModelProperty(value = "所属的数据源PKID")
    @TableField("data_source_id")
    private String datasourceid;

    @ApiModelProperty(value = "数据集表名")
    @TableField("table_name")
    private String tablename;

    @ApiModelProperty(value = "数据集所属目录ID(如果需要对数据集编目的话才需要使用这个字段)")
    @TableField("data_set_directory_id")
    private String datasetdirectoryid;

    @ApiModelProperty(value = "数据集状态(1:启用;-1:禁用;0:未配置)")
    @TableField("state")
    private Integer state;

    @ApiModelProperty(value = "创建用户ID")
    @TableField("create_user_id")
    private String createuserid;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_date")
    private Timestamp createdate;

    @ApiModelProperty(value = "顺序号")
    @TableField("order_index")
    private Integer orderindex;

    @ApiModelProperty(value = "数据集描述")
    @TableField("data_set_desc")
    private String datasetdesc;

    @ApiModelProperty(value = "模型精度级别编号。如果需要对实体的模型数据集按精度进行区分的话可以使用该字段。从1到4分别对应白膜、简模、精模、BIM模型。")
    @TableField("accuracy_class")
    private Integer accuracyclass;

    @ApiModelProperty(value = "交互方式(来源字典)")
    @TableField("interactive_type")
    private String interactivetype;

    @ApiModelProperty(value = "数据集种类  common_layer公共图层")
    @TableField("data_set_category")
    private String datasetcategory;

    @ApiModelProperty(value = "所属实体类型定义模板UUID。将所属的实体类型ID放到这里，而不是单独建关联表去存储，这样才能有效提升系统性能。")
    @TableField("entity_type_id")
    private String entitytypeid;

    @ApiModelProperty(value = "坐标系名称")
    @TableField("prj_coord_sys")
    private String prjcoordsys;

    @ApiModelProperty(value = "坐标系度带号")
    @TableField("degree_projection")
    private String degreeprojection;


}
