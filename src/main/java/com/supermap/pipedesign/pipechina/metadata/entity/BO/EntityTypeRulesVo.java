package com.supermap.pipedesign.pipechina.metadata.entity.BO;

import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import com.supermap.pipedesign.pipechina.rules.entity.vo.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EntityTypeRulesVo {

    @ApiModelProperty(value = "实体类型id")
    private String entityTypeId;

    @ApiModelProperty(value = "实体类型名称")
    private String entityTypeName;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "管线与图层相交算法")
    public List<LineIntersectLayerVo>  beanLLIL;

     public AlgorithmDataVo lineIntersectLayer;

    @ApiModelProperty(value = "沿管道中线创建")
    List<SpacingEntity> beanSE;

    public AlgorithmDataVo spacingEntity;

    @ApiModelProperty(value = "管线与参照物相交算法")
    public List<LineReferenceObjectVo> beanLLRO;

    private AlgorithmDataVo  lineReferenceObject;

    @ApiModelProperty(value = "线图层与与管道交叉角度规定")
    List<LinesAngleVo>  beanLLA;

    @ApiModelProperty(value = "线图层与与管道交叉角度规定")
    List<LineRegionCreateLineVo>  beanLRCL;

    private AlgorithmDataVo  linesAngle;

    private AlgorithmDataVo  lineRegionCreateLine;

    @ApiModelProperty(value = "管道转折角度规定")
    public List<LineIntersectLineVo>  beanLIL;

    private AlgorithmDataVo  lineIntersectLine;

    private AlgorithmDataVo onLine;

    @ApiModelProperty(value = "交互方式字典值")
    private DictionaryVo interactiveDic;

    public  class SpacingEntity implements Serializable{

        @ApiModelProperty(value = "垂直于管线的距离")
        private String spacing;

        @ApiModelProperty(value = "间距")
        private String distance;

        @ApiModelProperty(value = "方向 0是上方1是下方")
        private String direction;

        @ApiModelProperty(value = "规则类型")
        private String rulealgorittype;

        public String getSpacing() {
            return spacing;
        }

        public void setSpacing(String spacing) {
            this.spacing = spacing;
        }

        public String getDistance() {
            return distance;
        }

        public void setDistance(String distance) {
            this.distance = distance;
        }

        public String getDirection() {
            return direction;
        }

        public void setDirection(String direction) {
            this.direction = direction;
        }

        public String getRulealgorittype() {
            return rulealgorittype;
        }

        public void setRulealgorittype(String rulealgorittype) {
            this.rulealgorittype = rulealgorittype;
        }
    }

}
