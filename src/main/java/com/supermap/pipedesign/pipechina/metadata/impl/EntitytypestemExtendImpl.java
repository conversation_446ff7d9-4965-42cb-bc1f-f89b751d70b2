package com.supermap.pipedesign.pipechina.metadata.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.engineering.dao.DictionaryMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.engineering.entity.User;
import com.supermap.pipedesign.pipechina.metadata.dao.*;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.DatasetfieldtemVo;
import com.supermap.pipedesign.pipechina.metadata.entity.*;
import com.supermap.pipedesign.pipechina.metadata.entity.expand.*;
import com.supermap.pipedesign.pipechina.rules.dao.EntitytypestemRulMapper;
import com.supermap.pipedesign.pipechina.rules.dao.GeneralDrawingTemMapper;
import com.supermap.pipedesign.pipechina.rules.entity.EntitytypestemRul;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawingTem;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.file.PathUtils;
import com.supermap.tools.gson.GsonUtil;
import com.supermap.tools.sqlite.SqliteUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

@Service("EntitytypestemExtendImpl")
public class EntitytypestemExtendImpl {

    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private EntitytypestylestemMapper entitytypestylestemMapper;

    @Autowired
    private DatasetinfotemMapper datasetinfotemMapper;

    @Autowired
    private DatasetfieldtemMapper datasetfieldtemMapper;

    @Autowired
    private EntitytypestemMapper entitytypestemMapper;


    @Autowired
    private MdDatasetfieldTemintMapper mdDatasetfieldTemintMapper;

    // 符号表
    @Autowired
    private MdEntitytypethemestylesTemMapper mdEntitytypethemestylesTemMapper;

    @Autowired
    private EntitytypestemRulMapper entitytypestemRulMapper;

    @Autowired
    private GeneralDrawingTemMapper generalDrawingTemMapper;

    @Autowired
    private  MdEntitytypeThemeStylesPartsTemMapper mdEntitytypeThemeStylesPartsTemMapper;

    @Autowired
    private  MdEntitytypeStylesPartsTemMapper mdEntitytypeStylesPartsTemMapper;

    @Autowired
    private DatasetFieldWritewayTemMapper datasetFieldWritewayTemMapper;

    @Autowired
    private DatasetFieldWritewayValueTemMapper datasetFieldWritewayValueTemMapper;

    /**
     * @作者 eomer
     * @描述 创建实体类型
     * @日期 2023/03/06 20:45
     * 参数 entityTypesVo
     * 参数 userId
     * @返回值 void
     **/
    public void createEntityType(EntitytypesTemConfig entityTypesVo, User user) {
        //String s = JSONUtil.toJsonStr(entityTypesVo);
        /**实体保存逻辑
         1、 实体表保存
         2、 数据集 原来有中间表， 现在（2023/03/06 17:20）直接关联实体
         3、 实体样式 -  点-样式，面-样式  - 关联实体ID + 数据集类型
         4、 专题 - 这里进行了专题字段添加（与字段循环结合实现）
         5、 实体字段 - 原关联数据集 ，2023/03/06 17:18 改为 关联实体ID
         （ 字段分
         特殊实体【中线表、地面线、纵管线】、
         点、线、面  公共属性 及 基类继承
         ）
         **/


        // 1、实体类型保存
        String entityTypeId = UUID.randomUUID().toString();//实体类型id
        //判断属性是否与公共属性重复
        Entitytypestem entitytypestem = entityTypesVo.getEntitytypestem();
        //
        String filedKeyId = null;
        String themeValue = null;

        String  dataSetId = UUID.randomUUID().toString();
        int indexNext = 0;
        List<DatasetfieldTemint> datasetfieldTemints = new ArrayList<>();
        if (entitytypestem.getIsbasics() != 0) {
            //---公共实体类型属性表 insert
            datasetfieldTemints = getPublicProperty(entityTypesVo);
            //非基类配置公共属性
            if (datasetfieldTemints != null && datasetfieldTemints.size() > 0) {

                //5、 保存 实体类型属性表 -字段
                if( entityTypesVo.getDatasetfieldsVo() !=null  && entityTypesVo.getDatasetfieldsVo().size() > 0 ){
                    for (DatasetfieldtemVo datasetfieldtem : entityTypesVo.getDatasetfieldsVo() ){
                        for (DatasetfieldTemint temint : datasetfieldTemints){
                            if (datasetfieldtem.getFieldname().equals(temint.getFieldname())) {
                                throw new BusinessException("属性"+datasetfieldtem.getFieldname()+"与公共属性重复，请检查");
                            }
                        }
                    }
                }

                for (DatasetfieldTemint datasetfieldTemint : datasetfieldTemints) {
                    Datasetfieldtem datasetfieldtem = GsonUtil.ObjectToEntity(datasetfieldTemint, Datasetfieldtem.class);
                    indexNext = datasetfieldtem.getOrderindex();
                    datasetfieldtem.setIsrequired(false);
                    datasetfieldtem.setPkid(UUID.randomUUID().toString());
                    datasetfieldtem.setEntitytypeid(entityTypeId);
                    datasetfieldtem.setDatasetid(dataSetId);
                    datasetfieldtemMapper.insert(datasetfieldtem);
                }
            }
        }

        saveEntity(entityTypesVo, entityTypeId, user.getUserid());


        //5、 保存 实体类型属性表 -字段
        if( entityTypesVo.getDatasetfieldsVo() !=null
                && entityTypesVo.getDatasetfieldsVo().size() > 0 ){
            for (DatasetfieldtemVo datasetfieldtem : entityTypesVo.getDatasetfieldsVo() ){
                Datasetfieldtem datasetfieldtemInsert = GsonUtil.ObjectToEntity(datasetfieldtem,Datasetfieldtem.class);
                String dataFieldPkid = UUID.randomUUID().toString();
                if(JavaUtils.isNotEmtryOrNull(entitytypestem.getThemeId()) &&
                        entitytypestem.getThemeId().equals(datasetfieldtem.getFieldname()))
                {
                    //获取专题属性的id
                    filedKeyId=dataFieldPkid;
                    themeValue = datasetfieldtem.getDefaultvalue();
                }
                if (indexNext != 0){
                    indexNext++;
                    datasetfieldtemInsert.setOrderindex(indexNext);
                }

                datasetfieldtemInsert.setPkid( dataFieldPkid );
                datasetfieldtemInsert.setEntitytypeid(entityTypeId);
                datasetfieldtemInsert.setDatasetid(dataSetId);
                datasetfieldtemMapper.insert(datasetfieldtemInsert);
                DatasetFieldWritewayTemEx writeway = datasetfieldtem.getWriteway();
                if (writeway != null){
                    List<DatasetFieldWritewayValueTem> writewayValueTemsList = writeway.getWritewayValueTemsList();
                        DatasetFieldWritewayTem wayTem = GsonUtil.ObjectToEntity(writeway,DatasetFieldWritewayTem.class);
                        String wayId = UUID.randomUUID().toString();
                        wayTem.setPkid(wayId);
                        wayTem.setEntityTypeId(entityTypeId);
                        wayTem.setDatasetFieldId(datasetfieldtemInsert.getPkid());
                        datasetFieldWritewayTemMapper.insert(wayTem);
                        if (!JavaUtils.isEmtryOrNull(writewayValueTemsList)){
                            for (DatasetFieldWritewayValueTem valueTem : writewayValueTemsList){
                                valueTem.setPkid(UUID.randomUUID().toString());
                                valueTem.setEntityTypeId(entityTypeId);
                                valueTem.setWriteWayId(wayId);
                                datasetFieldWritewayValueTemMapper.insert(valueTem);
                            }
                        }
                }
            }
        }
        //6添加插值表
//        saveInterpolation(entityTypeId,entityTypesVo);

        if (entitytypestem.getIsbasics() != 0){

            // 2、 添加数据集 //3、添加风格
            DatasetTypeClass displayType = entityTypesVo.getDatasetType();
            // 特殊实体 -固定展示样式
            if( isSpecialEntity(entitytypestem.getEntitytypecode()) ){
                displayType.setP("CenterLineAboveP");
                displayType.setL("CenterLineAboveL");
            }
            //添加字段数据集
            saveDatasetInfo(entityTypesVo, entityTypeId,"",0,"",dataSetId);
            saveDatasetInfo(entityTypesVo, entityTypeId,"",4,"",dataSetId);
            if(JavaUtils.isNotEmtryOrNull(displayType.getP()))
            {
                saveDatasetInfo(entityTypesVo, entityTypeId,displayType.getP(),1,filedKeyId,themeValue);
            }
            if(JavaUtils.isNotEmtryOrNull(displayType.getL()))
            {
                saveDatasetInfo(entityTypesVo, entityTypeId,displayType.getL(),2,filedKeyId,themeValue);
            }
            if(JavaUtils.isNotEmtryOrNull(displayType.getR()))
            {
                saveDatasetInfo(entityTypesVo, entityTypeId,displayType.getR(),3,filedKeyId,themeValue);
            }
            //---实体类型insert
            //  生成成果表  sqq -- 这里只生成属性表
            //addEntitiesAchivments(entityTypesVo);entitytypestem.getEntitytypecode()

            /**
             * @Description 不包含此类实体编码时才创建实体类型同时生成sqllite表
             * <AUTHOR>
             */
            createSqlLiteDbFile(entityTypesVo, datasetfieldTemints);
        }
    }

    /**
     * @描述 是否特殊实体
     * @日期 2023/03/06 17:38
     * @作者 eomer
     **/
    private boolean isSpecialEntity(String entitytypecode){
        Set<String> entityCodeSet = new HashSet<>();
        entityCodeSet.add("center_line");
        entityCodeSet.add("ground_line");
        entityCodeSet.add("longitudinal_pipeline");
        return entityCodeSet.contains( entitytypecode);
    }

    /**
     * @描述 实体保存
     **/
    private void saveEntity(EntitytypesTemConfig entityTypesVo, String entityTypeId, String userId){

        Entitytypestem entitytypestem = entityTypesVo.getEntitytypestem();

        if (JavaUtils.isNotEmtryOrNull(userId)){
            entitytypestem.setCreateuserid(userId);
        }
        entitytypestem.setCreatetime(new Timestamp(System.currentTimeMillis()));

        if( entitytypestem.getIsbasics() == 1 ) {//1实体0基类
            entitytypestem.setParententityid( entitytypestem.getParententityid() );
        }else{
            entitytypestem.setIsbasics(0);
        }

        entitytypestem.setPkid( entityTypeId );
        entitytypestem.setSequencestate(1);//当前的可用状态(1:可用;-1:不可用;0:未初始化)
//        entitytypestem.setEntitydesigntype(0);//设计种类(0:设计实体;1:地理实体;2:其他实体;)
        entitytypestem.setIsshow(0);//是否显示(0:显示;1:不显示;)
        entitytypestemMapper.insert(entitytypestem);//实体类型
        EntitytypestemRul entitytypestemRul = GsonUtil.ObjectToEntity(entitytypestem,EntitytypestemRul.class);
        entitytypestemRulMapper.insert(entitytypestemRul);

    }

    // 保存数据集
    private void saveDatasetInfo(EntitytypesTemConfig entityTypesVo, String entityTypeId, String displayTypeId,Integer dataSetType, String filedId, String themeValue){

        Entitytypestem entitytypestem = entityTypesVo.getEntitytypestem();
            //---数据集insert
            Datasetinfotem datasetinfotem = new Datasetinfotem();
            String datasetinfoId = UUID.fastUUID().toString();//数据集id
        if( dataSetType == 0 ){
            //数据集id从外部传进来
            datasetinfoId=themeValue;
        }
            datasetinfotem.setPkid( datasetinfoId );
            datasetinfotem.setDatasetalias( entitytypestem.getEntitytypealias() );
            if( dataSetType == 0 ){
                datasetinfotem.setDatasetname(entitytypestem.getEntitytypecode());
                datasetinfotem.setDatasetalias( entitytypestem.getEntitytypealias()+"属性表" );
                datasetinfotem.setTablename(entitytypestem.getEntitytypecode());
            }else if(dataSetType == 1 ){
                datasetinfotem.setDatasetname( entitytypestem.getEntitytypecode()+"_P" );
                datasetinfotem.setDatasetalias( entitytypestem.getEntitytypealias()+"-"+"点" );
                datasetinfotem.setTablename( entitytypestem.getEntitytypecode()+"_P" );
            }else if( dataSetType == 2){

                datasetinfotem.setDatasetname( entitytypestem.getEntitytypecode()+"_L" );
                datasetinfotem.setDatasetalias( entitytypestem.getEntitytypealias()+"-"+"线" );
                datasetinfotem.setTablename( entitytypestem.getEntitytypecode()+"_L" );
            }else if( dataSetType  == 3 ){

                datasetinfotem.setDatasetname( entitytypestem.getEntitytypecode()+"_R" );
                datasetinfotem.setDatasetalias( entitytypestem.getEntitytypealias()+"-"+"面" );
                datasetinfotem.setTablename( entitytypestem.getEntitytypecode()+"_R" );
            }else if( dataSetType  == 4 ){

                datasetinfotem.setDatasetname( entitytypestem.getEntitytypecode()+"_M" );
                datasetinfotem.setDatasetalias( entitytypestem.getEntitytypealias()+"-"+"三维模型" );
                datasetinfotem.setTablename( entitytypestem.getEntitytypecode()+"_M" );
            }
            datasetinfotem.setDatasettype(  dataSetType);
            datasetinfotem.setState(1);
            if( dataSetType > 0 && dataSetType < 4 ) {
                datasetinfotem.setInteractivetype(displayTypeId);
            }
            datasetinfotem.setEntitytypeid(entityTypeId);
            datasetinfotemMapper.insert( datasetinfotem );//数据集
        if( dataSetType > 0 && dataSetType < 4) {
            saveStylesInfo(entityTypesVo, entityTypeId, datasetinfoId, dataSetType, filedId, themeValue);
        }
    }

    // 保存样式
    private void saveStylesInfo(EntitytypesTemConfig entityTypesVo, String entityTypeId, String datasetinfoId,Integer dataSetType,String filedId, String themeValue){
        {
            StylesClassTem stylesClass = new StylesClassTem();
            //点
            if(dataSetType==1)
            {
                stylesClass = entityTypesVo.getP();
            }
            if(dataSetType==2)
            {
                stylesClass = entityTypesVo.getL();
            }
            if(dataSetType==3)
            {
                stylesClass = entityTypesVo.getR();
            }
            //普通风格
            EntitytypestylestemEx entitytypestylestem = stylesClass.getEntityTypeStyles();
            if(entitytypestylestem!=null)
            {
                Entitytypestylestem typles = GsonUtil.ObjectToEntity(entitytypestylestem, Entitytypestylestem.class);
                String typlesId = UUID.randomUUID().toString();
                typles.setPkid(typlesId);
                typles.setDatasetid(datasetinfoId);
                typles.setEntitytypeid(entityTypeId);
                entitytypestylestemMapper.insert(typles);//实体类型风格

                //关联部件
                //插入parts表
                List<MdEntitytypeStylesPartsTem> listMdEntitytypeStylesPartsTem = entitytypestylestem.getStylesParts();

                if(listMdEntitytypeStylesPartsTem!=null) {
                    for (MdEntitytypeStylesPartsTem entitytypeStylesPartsTem : listMdEntitytypeStylesPartsTem) {
                        entitytypeStylesPartsTem.setPkid(UUID.randomUUID().toString());
                        entitytypeStylesPartsTem.setEntityTypeId(entityTypeId);
                        entitytypeStylesPartsTem.setStylesId(typlesId);
                        mdEntitytypeStylesPartsTemMapper.insert(entitytypeStylesPartsTem);
                    }
                }
            }

            //专题图风格
            List<MdEntitytypethemestylesTemEx> listEntitytypethemestyles = stylesClass.getEntityTypeThemeStyles();
            if(listEntitytypethemestyles!=null)
            {
                for( MdEntitytypethemestylesTemEx entitytypethemestyles : listEntitytypethemestyles) {
                        MdEntitytypethemestylesTem symbols = GsonUtil.ObjectToEntity(entitytypethemestyles,MdEntitytypethemestylesTem.class);
                        String symbolsId =  UUID.randomUUID().toString();
                        symbols.setPkid(symbolsId );
                        symbols.setEntitytypeid( entityTypeId );
                        symbols.setDatasetid(datasetinfoId);
                        symbols.setThemefieldid(filedId);
                        symbols.setThemevalue(entitytypethemestyles.getThemevalue());
                        //插入模板表
                        mdEntitytypethemestylesTemMapper.insert(symbols);

                        //插入parts表
                        List<MdEntitytypeThemeStylesPartsTem> listMdEntitytypeThemeStylesPartsTem = entitytypethemestyles.getStylesParts();
                        if(listMdEntitytypeThemeStylesPartsTem!=null){
                            for (MdEntitytypeThemeStylesPartsTem entitytypeThemeStylesPartsTem:listMdEntitytypeThemeStylesPartsTem)
                            {
                                entitytypeThemeStylesPartsTem.setPkid( UUID.randomUUID().toString() );
                                entitytypeThemeStylesPartsTem.setEntityTypeId(entityTypeId);
                                entitytypeThemeStylesPartsTem.setStylesId(symbolsId);
                                mdEntitytypeThemeStylesPartsTemMapper.insert(entitytypeThemeStylesPartsTem);
                            }
                        }


                }
            }
        }

    }


    // 获取公共属性
    private List<DatasetfieldTemint> getPublicProperty(EntitytypesTemConfig entitytypesTemConfig){
        String interactivetype = "";
        DatasetTypeClass displayType = entitytypesTemConfig.getDatasetType();
        if(JavaUtils.isNotEmtryOrNull(displayType.getP()))
        {
            interactivetype = displayType.getP();
        }
        if(JavaUtils.isNotEmtryOrNull(displayType.getL()))
        {
            interactivetype = displayType.getL();
        }
        Set<String> interactivetypeSet = new HashSet<>();
        Set<String> entityCodeSet = new HashSet<>();
        entityCodeSet.add("center_line");
        entityCodeSet.add("ground_line");
        entityCodeSet.add("longitudinal_pipeline");
        interactivetypeSet.add("CenterLineAboveL"); //中线上
        interactivetypeSet.add("DynamicPart");//动态分段
        interactivetypeSet.add("DispersedLine");//离散线
        List<DatasetfieldTemint> datasetfieldTemints = new ArrayList<>();
        if( !entityCodeSet.contains( entitytypesTemConfig.getEntitytypestem().getEntitytypecode())) {
            if (!interactivetypeSet.contains(interactivetype)) {//复制1
                datasetfieldTemints = mdDatasetfieldTemintMapper.selByTypeVal(1);
            } else {//复制2
                datasetfieldTemints = mdDatasetfieldTemintMapper.selByTypeVal(2);
            }
        }
        return datasetfieldTemints;
    }

    private void createSqlLiteDbFile( EntitytypesTemConfig entitytypesTemConfig, List<DatasetfieldTemint> datasetfieldTemints) {
        List<DatasetfieldtemVo> datasetfieldtemVos = entitytypesTemConfig.getDatasetfieldsVo();
        if( datasetfieldtemVos != null
                && datasetfieldtemVos.size()>0 ) {
            if (datasetfieldTemints != null
                    && datasetfieldTemints.size() > 0) {
                for (DatasetfieldTemint datasetfieldTemint : datasetfieldTemints) {
                    DatasetfieldtemVo datasetfieldtemVo = GsonUtil.ObjectToEntity(datasetfieldTemint, DatasetfieldtemVo.class);
                    datasetfieldtemVos.add(datasetfieldtemVo);
                }
            }
        }else{
            datasetfieldtemVos =  new ArrayList<>();
            if (datasetfieldTemints != null
                    && datasetfieldTemints.size() > 0) {
                for (DatasetfieldTemint datasetfieldTemint : datasetfieldTemints) {
                    DatasetfieldtemVo datasetfieldtemVo = GsonUtil.ObjectToEntity(datasetfieldTemint, DatasetfieldtemVo.class);
                    datasetfieldtemVos.add(datasetfieldtemVo);
                }
            }
        }

            String createSql = "CREATE TABLE "+entitytypesTemConfig.getEntitytypestem().getEntitytypecode()+" (pkid TEXT,";
            StringBuilder stringBuilder = new StringBuilder(1024);

            Map<String,Object> hashSet = new HashMap<>();
            for( DatasetfieldtemVo datasetfieldtemVo : datasetfieldtemVos ){
                String column = datasetfieldtemVo.getFieldname();
                if (hashSet.get(column)!=null){
                    continue;
                }
                hashSet.put(column,column);
                stringBuilder.append(column);
                if( datasetfieldtemVo.getFieldtype().equals("1")){
                    stringBuilder.append(" INTEGER");
                }else if( datasetfieldtemVo.getFieldtype().equals("2")){
                    stringBuilder.append(" REAL");
                }else if( datasetfieldtemVo.getFieldtype().equals("4")){
                    stringBuilder.append(" TEXT");
                }else {
                    stringBuilder.append(" TEXT");
                }
                stringBuilder.append(" , ");
            }
            stringBuilder = new StringBuilder( stringBuilder.substring(0,(stringBuilder.length()-2)));
            createSql =  createSql + stringBuilder + " )";
            try {
                System.out.println(createSql);
                String path = PathUtils.getDesignPath()+File.separator+"entitiesachivments.db";
                System.out.println("db --> "+path);
                SqliteUtils.createTable(path, createSql);
            }catch (SQLException exp ){
                exp.printStackTrace();
            }
    }



    //根据实体类型id获取实体类弄详情
    public EntitytypesTemConfig entityTypeConfigTem(String entitytypeid) {
        // 根据专业id查询数据
        Entitytypestem entitytypes = entitytypestemMapper.selectById(entitytypeid);
        if (JavaUtils.isEmtryOrNull(entitytypes.getTagtext())){
            entitytypes.setTagtext(null);
        }
        if (entitytypes == null) {
            throw new BusinessException("未找到该实体类型，请核查！");
        }
        //数据字典
        List<Dictionary> list = new ArrayList<>();
        if (JavaUtils.isNotEmtryOrNull(entitytypes.getThemeId())){
            QueryWrapper<Datasetfieldtem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("entity_type_id",entitytypeid);
            queryWrapper.eq("field_name",entitytypes.getThemeId());
            Datasetfieldtem fieldt = datasetfieldtemMapper.selectOne(queryWrapper);
            if (fieldt != null){
                if (JavaUtils.isNotEmtryOrNull(fieldt.getFieldrange()) && fieldt.getFieldrange().equals("1") &&
                        JavaUtils.isNotEmtryOrNull(fieldt.getRegularexpression())){
                    QueryWrapper<Dictionary> dque = new QueryWrapper<>();
                    dque.eq("pid",fieldt.getRegularexpression());
                    list = dictionaryMapper.selectList(dque);
                }
            }
        }
        //通用图
        String reusediagram="";
        if (JavaUtils.isNotEmtryOrNull(entitytypes.getReusediagramid())){
            String[] splits = entitytypes.getReusediagramid().split(",");
//            for (int i =0; i < 2; i++){
             /*   RuledefDirecotriesTem ruledefDirecotriesTem = ruledefDirecotriesTemMapper.selectById(splits[i]);
               if (ruledefDirecotriesTem != null){
                   reusediagram += "/"+ruledefDirecotriesTem.getDirectoryname();
               }

            }
            if(splits.length == 3){*/
            for (String split : splits) {
                GeneralDrawingTem generalDrawingTem = generalDrawingTemMapper.selectById(split);
                if (generalDrawingTem != null){
                    reusediagram += "/"+generalDrawingTem.getGdName();
                }
            }
            reusediagram=reusediagram.substring(1);
            }

//        }

//        //查询专题图设置的字段对应的英文名
//        QueryWrapper<MdEntitytypethemestylesTem> entitytypethemestylesWrapper = new QueryWrapper<>();
//        entitytypethemestylesWrapper.eq("entity_type_id", entitytypeid);
//        List<MdEntitytypethemestylesTem> MdEntitytypethemestylesTemList = mdEntitytypethemestylesTemMapper.selectList(entitytypethemestylesWrapper);
//        if(MdEntitytypethemestylesTemList.size()>0)
//        {
//            String themefieldid = MdEntitytypethemestylesTemList.get(0).getThemefieldid();
//            QueryWrapper<Datasetfieldtem> datasetfieldQueryWrapper = new QueryWrapper<>();
//            datasetfieldQueryWrapper.eq("pkid", themefieldid);
//            List<Datasetfieldtem> datasetfieldtemList = datasetfieldtemMapper.selectList(datasetfieldQueryWrapper);
//            if(datasetfieldtemList.size()>0)
//            {
//                entitytypes.setThemeId(datasetfieldtemList.get(0).getFieldname());
//            }
//        }
        EntitytypesTemConfig config = getEntitytypesTemConfig(entitytypeid);

        //查询 插值表  List<EntitytypesInterpolationTemEx>
        List<EntitytypesInterpolationTemEx> exList = selInterpolation(entitytypeid);
        config.setTableTemList(exList);
        config.setEntitytypestem(entitytypes);
        config.setDictionaryList(list);
        config.setReusediagram(reusediagram);
        return config;
    }

    /**
     * 获取实体配置信息
     * @param entitytypesId
     * @return com.supermap.pipedesign.pipechina.metadata.entity.expand.EntitytypesConfig @Date
     *     2022/12/28 15:03
     * @auther eomer
     */
    private EntitytypesTemConfig getEntitytypesTemConfig(String entitytypesId) {
        EntitytypesTemConfig config = new EntitytypesTemConfig();

        //1、查询字段
        QueryWrapper<Datasetfieldtem> datasetfieldQueryWrapper = new QueryWrapper<>();
        datasetfieldQueryWrapper.eq("entity_type_id", entitytypesId);//orderindex
        datasetfieldQueryWrapper.ne("is_extend",-1);
        datasetfieldQueryWrapper.orderByAsc("order_index");
        List<Datasetfieldtem> datasetfields = datasetfieldtemMapper.selectList(datasetfieldQueryWrapper);
        if(!datasetfields.isEmpty()){
            List<DatasetfieldtemVo> datasetfieldtemVos =  new ArrayList<>();
            for( Datasetfieldtem datasetfieldtem : datasetfields ){
                DatasetfieldtemVo  datasetfieldtemVo = new DatasetfieldtemVo();
                BeanUtils.copyProperties(datasetfieldtem,datasetfieldtemVo);
                if (JavaUtils.isNotEmtryOrNull(datasetfieldtem.getFieldrange())){
                    if (datasetfieldtem.getFieldrange().equals("1")){
                        if (JavaUtils.isNotEmtryOrNull(datasetfieldtem.getRegularexpression())){
                            List<String> list = dictionaryMapper.selByRangeId(datasetfieldtem.getRegularexpression());
                            datasetfieldtemVo.setRangevalue(StringUtils.join(list,"、"));
                        }
                    }
                }
                //查询写值
                QueryWrapper<DatasetFieldWritewayTem> writeQuery = new QueryWrapper<>();
                writeQuery.eq("dataset_field_id",datasetfieldtem.getPkid());
                writeQuery.eq("entity_type_id",datasetfieldtem.getEntitytypeid());
                DatasetFieldWritewayTem datasetFieldWritewayTem = datasetFieldWritewayTemMapper.selectOne(writeQuery);
                DatasetFieldWritewayTemEx datasetFieldWritewayTemEx = null;
                if (datasetFieldWritewayTem != null) {
                    datasetFieldWritewayTemEx = GsonUtil.ObjectToEntity(datasetFieldWritewayTem,DatasetFieldWritewayTemEx.class);
                    QueryWrapper<DatasetFieldWritewayValueTem> valueQuery = new QueryWrapper<>();
                    valueQuery.eq("write_way_id",datasetFieldWritewayTem.getPkid());
                    valueQuery.eq("entity_type_id",datasetFieldWritewayTem.getEntityTypeId());
                    List<DatasetFieldWritewayValueTem> datasetFieldWritewayValueTems = datasetFieldWritewayValueTemMapper.selectList(valueQuery);
                    datasetFieldWritewayTemEx.setWritewayValueTemsList(datasetFieldWritewayValueTems);
                    if (datasetFieldWritewayValueTems.isEmpty()){
                        datasetFieldWritewayTemEx.setWritewayValueTemsList(new ArrayList<>());
                    }
                }
                datasetfieldtemVo.setWriteway(datasetFieldWritewayTemEx);
                datasetfieldtemVos.add( datasetfieldtemVo );

            }

            config.setDatasetfieldsVo(datasetfieldtemVos);
        }else{
            config.setDatasetfieldsVo(new ArrayList<>());
        }
        //2、查询数据集
        QueryWrapper<Datasetinfotem> datasetQueryWrapper = new QueryWrapper<>();
        datasetQueryWrapper.eq("entity_type_id", entitytypesId);
        List<Datasetinfotem> datasetinfoList = datasetinfotemMapper.selectList(datasetQueryWrapper);
        DatasetTypeClass datasetTypeClass = new DatasetTypeClass();
        for (Datasetinfotem datasetinfotem : datasetinfoList) {
            if (datasetinfotem.getDatasettype() == 1) {
                datasetTypeClass.setP(datasetinfotem.getInteractivetype());
                //设置点样式
                StylesClassTem stylesClass = getStylesClass(entitytypesId, datasetinfotem.getPkid());
                config.setP(stylesClass);
            }
            if (datasetinfotem.getDatasettype() == 2) {
                datasetTypeClass.setL(datasetinfotem.getInteractivetype());
                //设置线样式
                StylesClassTem stylesClass = getStylesClass(entitytypesId, datasetinfotem.getPkid());
                config.setL(stylesClass);
            }
            if (datasetinfotem.getDatasettype() == 3) {
                datasetTypeClass.setR(datasetinfotem.getInteractivetype());
                //设置面样式
                StylesClassTem stylesClass = getStylesClass(entitytypesId, datasetinfotem.getPkid());
                config.setR(stylesClass);
            }
            //设置交互方式
            config.setDatasetType(datasetTypeClass);
        }
        return config;
    }

    //获取实体样式
    private StylesClassTem getStylesClass(String entitytypesId, String dataSetId)
    {
        StylesClassTem stylesClass = new StylesClassTem();
        // 默认风格
        QueryWrapper<Entitytypestylestem> stylesQueryWrapper = new QueryWrapper<>();
        stylesQueryWrapper.eq("entity_type_id", entitytypesId);
        stylesQueryWrapper.eq("data_set_id", dataSetId);
        List<Entitytypestylestem> EntitytypestylestemList = entitytypestylestemMapper.selectList(stylesQueryWrapper);
        if(EntitytypestylestemList.size()>0)
        {
            EntitytypestylestemEx entitytypestylestemEx = GsonUtil.ObjectToEntity(EntitytypestylestemList.get(0),EntitytypestylestemEx.class);
            QueryWrapper<MdEntitytypeStylesPartsTem> entitytypeStylesPartsTemWrapper = new QueryWrapper<>();
            entitytypeStylesPartsTemWrapper.eq("styles_id", EntitytypestylestemList.get(0).getPkid());
            List<MdEntitytypeStylesPartsTem> listMdEntitytypeStylesPartsTem = mdEntitytypeStylesPartsTemMapper.selectList(entitytypeStylesPartsTemWrapper);
            entitytypestylestemEx.setStylesParts(new ArrayList<>());
            if (!JavaUtils.isEmtryOrNull(listMdEntitytypeStylesPartsTem)){
                entitytypestylestemEx.setStylesParts(listMdEntitytypeStylesPartsTem);
            }

            stylesClass.setEntityTypeStyles(entitytypestylestemEx);
        }
        // 默认风格
        QueryWrapper<MdEntitytypethemestylesTem> entitytypethemestylesWrapper = new QueryWrapper<>();
        entitytypethemestylesWrapper.eq("entity_type_id", entitytypesId);
        entitytypethemestylesWrapper.eq("data_set_id", dataSetId);
        List<MdEntitytypethemestylesTem> MdEntitytypethemestylesTemList = mdEntitytypethemestylesTemMapper.selectList(entitytypethemestylesWrapper);
        List<MdEntitytypethemestylesTemEx> list = new ArrayList<>();
        for (int i = 0; i <MdEntitytypethemestylesTemList.size() ; i++) {

            MdEntitytypethemestylesTem entitytypethemestylesTem = MdEntitytypethemestylesTemList.get(i);
            MdEntitytypethemestylesTemEx entitytypethemestylesTemEx = GsonUtil.ObjectToEntity(entitytypethemestylesTem,MdEntitytypethemestylesTemEx.class);

            QueryWrapper<MdEntitytypeThemeStylesPartsTem> entitytypeThemeStylesPartsTemWrapper = new QueryWrapper<>();
            entitytypeThemeStylesPartsTemWrapper.eq("styles_id", entitytypethemestylesTem.getPkid());
            List<MdEntitytypeThemeStylesPartsTem> listMdEntitytypeThemeStylesPartsTem = mdEntitytypeThemeStylesPartsTemMapper.selectList(entitytypeThemeStylesPartsTemWrapper);
            entitytypethemestylesTemEx.setStylesParts(new ArrayList<>());
            if (!JavaUtils.isEmtryOrNull(listMdEntitytypeThemeStylesPartsTem)){
                entitytypethemestylesTemEx.setStylesParts(listMdEntitytypeThemeStylesPartsTem);
            }
            list.add(entitytypethemestylesTemEx);
        }
        stylesClass.setEntityTypeThemeStyles(list);
        return  stylesClass;

    }



    //更新实体类型
    public int updateConfigTem(EntitytypesTemConfig entitytypesTemConfig, User user){
        int resultInt = 0 ;
        if( StringUtils.isNotBlank( entitytypesTemConfig.getEntitytypestem().getPkid() ) ){
            //删除Entitytypestylestem,MdEntitytypethemestylesTem表里的数据
            String entityId = entitytypesTemConfig.getEntitytypestem().getPkid();
            //定义每类实体模板的默认显示风格  pld_md_entitytype_styles_tem
            QueryWrapper<Entitytypestylestem> entitytypestylestemQueryWrapper = new QueryWrapper<>();
            entitytypestylestemQueryWrapper.eq("entity_type_id",entityId);
            entitytypestylestemMapper.delete(entitytypestylestemQueryWrapper);//删除entitytypestyles
            //删除写值表
            QueryWrapper wayWrapper = new QueryWrapper();
            wayWrapper.eq("entity_type_id",entityId);
            datasetFieldWritewayTemMapper.delete(wayWrapper);
            datasetFieldWritewayValueTemMapper.delete(wayWrapper);
            // 专题图 pld_md_entitytype_theme_styles_tem
            QueryWrapper<MdEntitytypethemestylesTem> themqwer = new QueryWrapper<>();
            themqwer.eq("entity_type_id",entityId);
            mdEntitytypethemestylesTemMapper.delete(themqwer);
            /**----------------------------------------------------------------------------**/
            //更新实体 pld_md_entitytypes_tem
            Entitytypestem entitytypestem = entitytypesTemConfig.getEntitytypestem();
            if (entitytypestem.getIsbasics() == 0){
                if (JavaUtils.isNotEmtryOrNull(entitytypestem.getStageinnercode())){
                    QueryWrapper<Entitytypestem> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("parent_entity_id",entitytypestem.getPkid());
                    List<Entitytypestem> entitytypestems = entitytypestemMapper.selectList(queryWrapper);
                    if (!JavaUtils.isEmtryOrNull(entitytypestems)){
                        for (Entitytypestem etem : entitytypestems){
                            etem.setStageinnercode(entitytypestem.getStageinnercode());
                            entitytypestemMapper.updateById(etem);
                        }
                    }
                }
            }
            entitytypestem.setCreatetime(new Timestamp(Calendar.getInstance().getTimeInMillis()));
            entitytypestem.setCreateuserid(user.getUserid());
            resultInt = resultInt + entitytypestemMapper.updateById(entitytypestem);
            //更新实体同时更新规则库中实体，用于复制时保持一致
            EntitytypestemRul tem = GsonUtil.ObjectToEntity(entitytypestem,EntitytypestemRul.class);
            entitytypestemRulMapper.updateById(tem);

            String filedKeyId = null;
            String themeValue = null;

            QueryWrapper<Datasetinfotem> dataquery = new QueryWrapper<>();
            dataquery.eq("entity_type_id", entityId);
            dataquery.eq("data_set_type", 0);
            Datasetinfotem datasetinfotem = datasetinfotemMapper.selectOne(dataquery);
            String datasetId = datasetinfotem.getPkid();
            //查询实体属性
            QueryWrapper<Datasetfieldtem> fieldQuery = new QueryWrapper<>();
            fieldQuery.eq("entity_type_id",entityId);
            List<Datasetfieldtem> temList = datasetfieldtemMapper.selectList(fieldQuery);
            //5、 保存 实体类型属性表 -字段
            if( entitytypesTemConfig.getDatasetfieldsVo() !=null
                    && entitytypesTemConfig.getDatasetfieldsVo().size() > 0 ){
                for (DatasetfieldtemVo datasetfieldtem : entitytypesTemConfig.getDatasetfieldsVo() ){
                    if(!JavaUtils.isNotEmtryOrNull(datasetfieldtem.getPkid()))
                    {
                        for (Datasetfieldtem fieldTem : temList){
                            if (datasetfieldtem.getFieldname().equals(fieldTem.getFieldname())){
                                throw new BusinessException("属性"+datasetfieldtem.getFieldname()+"已存在，请检查");
                            }
                        }
                        if( datasetfieldtem.getFieldtype().equals(1)){
                            datasetfieldtem.setFieldsize( Integer.parseInt( datasetfieldtem.getFieldrange() ));
                            datasetfieldtem.setFieldrange("");
                        }
                        Datasetfieldtem datasetfieldtemInsert = GsonUtil.ObjectToEntity(datasetfieldtem,Datasetfieldtem.class);
                        String dataFieldPkid = UUID.randomUUID().toString();
                        if(JavaUtils.isNotEmtryOrNull(entitytypestem.getThemeId()) &&
                                entitytypestem.getThemeId().equals(datasetfieldtem.getFieldname()))
                        {
                            //获取专题属性的id
                            filedKeyId=dataFieldPkid;
                            themeValue= datasetfieldtem.getDefaultvalue();
                        }
                        datasetfieldtemInsert.setPkid(dataFieldPkid);
//                        datasetfieldtemInsert.setIsrequired(false);
//                        if(StringUtils.isNotBlank( datasetfieldtem.getIsrequired() )){
//                            if( datasetfieldtem.getIsrequired().equals("1")){
//                                datasetfieldtemInsert.setIsrequired(true);
//                            }
//                        }
                        datasetfieldtemInsert.setEntitytypeid(entityId);
                        datasetfieldtemInsert.setDatasetid(datasetId);
                        datasetfieldtemMapper.insert(datasetfieldtemInsert);
                        //写值方式
                        DatasetFieldWritewayTemEx writeway = datasetfieldtem.getWriteway();
                        if (writeway != null && JavaUtils.isNotEmtryOrNull(writeway.getWriteType())){
                            List<DatasetFieldWritewayValueTem> writewayValueTemsList = writeway.getWritewayValueTemsList();
                                DatasetFieldWritewayTem wayTem = GsonUtil.ObjectToEntity(writeway,DatasetFieldWritewayTem.class);
                                String wayId = UUID.randomUUID().toString();
                                wayTem.setPkid(wayId);
                                wayTem.setEntityTypeId(entityId);
                                wayTem.setDatasetFieldId(dataFieldPkid);
                                datasetFieldWritewayTemMapper.insert(wayTem);
                                if (!JavaUtils.isEmtryOrNull(writewayValueTemsList)){
                                    for (DatasetFieldWritewayValueTem valueTem : writewayValueTemsList){
                                        valueTem.setPkid(UUID.randomUUID().toString());
                                        valueTem.setEntityTypeId(entityId);
                                        valueTem.setWriteWayId(wayId);
                                        datasetFieldWritewayValueTemMapper.insert(valueTem);
                                    }
                                }
                        }
                    }else
                    {
                        if(JavaUtils.isNotEmtryOrNull(entitytypestem.getThemeId()) &&
                                entitytypestem.getThemeId().equals(datasetfieldtem.getFieldname()))
                        {
                            //获取专题属性的id
                            filedKeyId=datasetfieldtem.getPkid();
                            themeValue = datasetfieldtem.getDefaultvalue();
                        }
                        //写值方式
                        DatasetFieldWritewayTemEx writeway = datasetfieldtem.getWriteway();
                        if (writeway != null && JavaUtils.isNotEmtryOrNull(writeway.getWriteType())){
                            List<DatasetFieldWritewayValueTem> writewayValueTemsList = writeway.getWritewayValueTemsList();
                            DatasetFieldWritewayTem wayTem = GsonUtil.ObjectToEntity(writeway,DatasetFieldWritewayTem.class);
                            String wayId = UUID.randomUUID().toString();
                            wayTem.setPkid(wayId);
                            wayTem.setEntityTypeId(entityId);
                            wayTem.setDatasetFieldId(datasetfieldtem.getPkid());
                            datasetFieldWritewayTemMapper.insert(wayTem);
                            if (!JavaUtils.isEmtryOrNull(writewayValueTemsList)){
                                for (DatasetFieldWritewayValueTem valueTem : writewayValueTemsList){
                                    valueTem.setPkid(UUID.randomUUID().toString());
                                    valueTem.setEntityTypeId(entityId);
                                    valueTem.setWriteWayId(wayId);
                                    datasetFieldWritewayValueTemMapper.insert(valueTem);
                                }
                            }
                        }
                    }
                }
            }
            if (entitytypestem.getIsbasics() != 0){
                // 2、 添加数据集 //3、添加风格
                DatasetTypeClass displayType = entitytypesTemConfig.getDatasetType();

                if(JavaUtils.isNotEmtryOrNull(displayType.getP()))
                {
                    String datasetinfoId = null;
                    QueryWrapper<Datasetinfotem> datasetfieldQueryWrapper = new QueryWrapper<>();
                    datasetfieldQueryWrapper.eq("entity_type_id", entityId);
                    datasetfieldQueryWrapper.eq("data_set_type", 1);
                    List<Datasetinfotem> datasetfieldtemList = datasetinfotemMapper.selectList(datasetfieldQueryWrapper);
                    if(datasetfieldtemList.size()>0)
                    {
                        datasetinfoId = datasetfieldtemList.get(0).getPkid();
                    }
                    saveStylesInfo(entitytypesTemConfig, entityId, datasetinfoId, 1, filedKeyId, themeValue);
                }
                if(JavaUtils.isNotEmtryOrNull(displayType.getL()))
                {
                    String datasetinfoId = null;
                    QueryWrapper<Datasetinfotem> datasetfieldQueryWrapper = new QueryWrapper<>();
                    datasetfieldQueryWrapper.eq("entity_type_id", entityId);
                    datasetfieldQueryWrapper.eq("data_set_type", 2);
                    List<Datasetinfotem> datasetfieldtemList = datasetinfotemMapper.selectList(datasetfieldQueryWrapper);
                    if(datasetfieldtemList.size()>0)
                    {
                        datasetinfoId = datasetfieldtemList.get(0).getPkid();
                    }
                    saveStylesInfo(entitytypesTemConfig, entityId, datasetinfoId, 2, filedKeyId, themeValue);
                }
                if(JavaUtils.isNotEmtryOrNull(displayType.getR()))
                {
                    String datasetinfoId = null;
                    QueryWrapper<Datasetinfotem> datasetfieldQueryWrapper = new QueryWrapper<>();
                    datasetfieldQueryWrapper.eq("entity_type_id", entityId);
                    datasetfieldQueryWrapper.eq("data_set_type", 3);
                    List<Datasetinfotem> datasetfieldtemList = datasetinfotemMapper.selectList(datasetfieldQueryWrapper);
                    if(datasetfieldtemList.size()>0)
                    {
                        datasetinfoId = datasetfieldtemList.get(0).getPkid();
                    }
                    saveStylesInfo(entitytypesTemConfig, entityId, datasetinfoId, 3, filedKeyId, themeValue);
                }
            }

            //更新插值表
            updateInterpolation(entitytypestem.getPkid(),entitytypesTemConfig);

            /**
             * @Description 遍历字段,校验sqllite表是否存在该字段
             * <AUTHOR>
             */
            try {
                sqlliteAddColumn( entitytypesTemConfig );
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return  resultInt;
    }


    private void sqlliteAddColumn(EntitytypesTemConfig entitytypesTemConfig) throws SQLException {
        String path = PathUtils.getDesignPath()+ File.separator+"entitiesachivments.db";
        String tableName
                = entitytypesTemConfig.getEntitytypestem().getEntitytypecode();
        List<DatasetfieldtemVo> datasetfieldtems
                = entitytypesTemConfig.getDatasetfieldsVo();
        if( datasetfieldtems != null  && datasetfieldtems.size() > 0 ) {
            for( DatasetfieldtemVo datasetfieldtemVo : datasetfieldtems ) {
                String col = datasetfieldtemVo.getFieldname();
                String columnSql = "select * from sqlite_master where name = '" + tableName + "' and sql like '%" + col + "%'";
                //Object tableData = sqlliteHelper.executeQuery(columnSql, new RowCallbackHandlerResultSetExtractor(new RowCountCallbackHandler()));
                ResultSet resultSet = SqliteUtils.executeQuery(path, columnSql);
                if (resultSet == null || resultSet.getMetaData()==null || resultSet.getMetaData().getColumnCount()==0) { //插入字段
                    String addColumnSql = "ALTER TABLE " + tableName + " ADD COLUMN " + col + " TEXT NULL";
                    //sqlliteHelper.executeUpdate(addColumnSql);
                    SqliteUtils.createTable(path, addColumnSql);
                }
            }
        }
    }

    public List<DatasetfieldTemint> selFieldTemint(String interactivetype){
        List<DatasetfieldTemint> list = new ArrayList<>();
        if (interactivetype.contains("CenterLineAboveL") || interactivetype.contains("DynamicPart")){
            list = mdDatasetfieldTemintMapper.selByTypeVal(1);
            return list;

        }
        list = mdDatasetfieldTemintMapper.selByTypeVal(2);

        return list;
    }
    //插值表
    @Autowired
    private EntitytypesInterpolationTemMapper entitytypesInterpolationTemMapper;
    //插值字段表
    @Autowired
    private EntitytypesInterpolationFieldTemMapper entitytypesInterpolationFieldTemMapper;
    //插值存储值表
    @Autowired
    private EntitytypesInterpolationFieldValueTemMapper entitytypesInterpolationFieldValueTemMapper;
    public void saveInterpolation(String entityTypeId,EntitytypesTemConfig entityTypesVo){
        List<EntitytypesInterpolationTemEx> tableTemList = entityTypesVo.getTableTemList();
        if (!JavaUtils.isEmtryOrNull(tableTemList)){
            //插值表
            for (EntitytypesInterpolationTemEx temex : tableTemList){
                EntitytypesInterpolationTem tem = GsonUtil.ObjectToEntity(temex,EntitytypesInterpolationTem.class);
                String temId = UUID.randomUUID().toString();
                tem.setPkid(temId);
                tem.setEntityTypeId(entityTypeId);
                entitytypesInterpolationTemMapper.insert(tem);
                //插值字段表
                List<EntitytypesInterpolationFieldTemEx> fieldTemList = temex.getFieldTemList();
                if (!JavaUtils.isEmtryOrNull(fieldTemList)){
                    for (EntitytypesInterpolationFieldTemEx fieldTemEx : fieldTemList){
                        EntitytypesInterpolationFieldTem fieldTem = GsonUtil.ObjectToEntity(fieldTemEx,EntitytypesInterpolationFieldTem.class);
                        String fieldTemId = UUID.randomUUID().toString();
//                        if (fieldTem.getAttType() == 1){
//                            QueryWrapper<Datasetfieldtem> queryWrapper = new QueryWrapper<>();
//                            queryWrapper.eq("entity_type_id",entityTypeId);
//                            queryWrapper.eq("field_name",fieldTem.getFieldId());
//                            Datasetfieldtem datasetfieldtem = datasetfieldtemMapper.selectOne(queryWrapper);
//                            fieldTem.setFieldId(datasetfieldtem.getPkid());
//                            fieldTem.setEntityTypeIdAtt(entityTypeId);
//                        }
                        fieldTem.setPkid(fieldTemId);
                        fieldTem.setEntityTypeId(entityTypeId);
                        fieldTem.setInterpolationId(temId);
                        entitytypesInterpolationFieldTemMapper.insert(fieldTem);

                        //插值字段存储值表
                        List<EntitytypesInterpolationFieldValueTem> fieldValueTemList = fieldTemEx.getFieldValueTemList();
                        if (!JavaUtils.isEmtryOrNull(fieldValueTemList)){
                            for (EntitytypesInterpolationFieldValueTem fieldValueTem : fieldValueTemList){
                                if (fieldValueTem == null){
                                    fieldValueTem = new EntitytypesInterpolationFieldValueTem();
                                }
                                fieldValueTem.setPkid(UUID.randomUUID().toString());
                                fieldValueTem.setEntityTypeId(entityTypeId);
                                fieldValueTem.setInterpolationId(temId);
                                fieldValueTem.setInterpolationFiledId(fieldTemId);
                                entitytypesInterpolationFieldValueTemMapper.insert(fieldValueTem);
                            }
                        }
                    }
                }

            }
        }
    }

    //插值表
//    @Autowired
//    private EntitytypesInterpolationTemMapper entitytypesInterpolationTemMapper;
//    //插值字段表
//    @Autowired
//    private EntitytypesInterpolationFieldTemMapper entitytypesInterpolationFieldTemMapper;
//    //插值存储值表
//    @Autowired
//    private EntitytypesInterpolationFieldValueTemMapper entitytypesInterpolationFieldValueTemMapper;
    public List<EntitytypesInterpolationTemEx> selInterpolation(String entitytypeid){
        List<EntitytypesInterpolationTemEx> temExList = new ArrayList<>();
        QueryWrapper<EntitytypesInterpolationTem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("entity_type_id",entitytypeid);
        queryWrapper.orderByAsc("sort_num");
        //查询插值表
        List<EntitytypesInterpolationTem> temList = entitytypesInterpolationTemMapper.selectList(queryWrapper);
        if (!JavaUtils.isEmtryOrNull(temList)){
            for (EntitytypesInterpolationTem tem : temList){
                EntitytypesInterpolationTemEx temEx = GsonUtil.ObjectToEntity(tem,EntitytypesInterpolationTemEx.class);

                QueryWrapper<EntitytypesInterpolationFieldTem> queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.eq("interpolation_id",tem.getPkid());
                queryWrapper1.eq("entity_type_id",entitytypeid);
                queryWrapper1.orderByAsc("sort_num");
                //查寻插值字段表
                List<EntitytypesInterpolationFieldTem> fieldTemList = entitytypesInterpolationFieldTemMapper.selectList(queryWrapper1);
                List<EntitytypesInterpolationFieldTemEx> fieldTemExList = new ArrayList<>();
                if (!JavaUtils.isEmtryOrNull(fieldTemList)){
                    for (EntitytypesInterpolationFieldTem fieldTem : fieldTemList){
                        EntitytypesInterpolationFieldTemEx fieldTemEx = GsonUtil.ObjectToEntity(fieldTem,EntitytypesInterpolationFieldTemEx.class);
                        Entitytypestem entitytypestem = entitytypestemMapper.selectById(fieldTemEx.getEntityTypeIdAtt());
                        if (entitytypestem != null){
                            fieldTemEx.setEntityTypeIdAttName(entitytypestem.getEntitytypealias());
                        }
                        Datasetfieldtem datasetfieldtem = datasetfieldtemMapper.selectById(fieldTem.getFieldId());
                        if (datasetfieldtem != null) {
                            fieldTemEx.setFieldIdName(datasetfieldtem.getFieldalias());
                        }
                        QueryWrapper<EntitytypesInterpolationFieldValueTem> queryWrapper2 = new QueryWrapper<>();
                        queryWrapper2.eq("entity_type_id",entitytypeid);
                        queryWrapper2.eq("interpolation_id",tem.getPkid());
                        queryWrapper2.eq("interpolation_filed_id",fieldTem.getPkid());
                        queryWrapper2.orderByAsc("sort_num");
                        List<EntitytypesInterpolationFieldValueTem> fieldValueTemList = entitytypesInterpolationFieldValueTemMapper.selectList(queryWrapper2);
                        fieldTemEx.setFieldValueTemList(fieldValueTemList);
                        fieldTemExList.add(fieldTemEx);
                    }
                }
                temEx.setFieldTemList(fieldTemExList);
                temExList.add(temEx);
            }
        }

        return temExList;
    }

    //插值表
//    @Autowired
//    private EntitytypesInterpolationTemMapper entitytypesInterpolationTemMapper;
//    //插值字段表
//    @Autowired
//    private EntitytypesInterpolationFieldTemMapper entitytypesInterpolationFieldTemMapper;
//    //插值存储值表
//    @Autowired
//    private EntitytypesInterpolationFieldValueTemMapper entitytypesInterpolationFieldValueTemMapper;
     //更新插值表
     public void updateInterpolation(String entityTypeId, EntitytypesTemConfig entitytypesTemConfig){
        //先删除 后插入
         QueryWrapper queryWrapper = new QueryWrapper();
         queryWrapper.eq("entity_type_id",entityTypeId);
         entitytypesInterpolationTemMapper.delete(queryWrapper);
         entitytypesInterpolationFieldTemMapper.delete(queryWrapper);
         entitytypesInterpolationFieldValueTemMapper.delete(queryWrapper);
         saveInterpolation( entityTypeId, entitytypesTemConfig);
     }
//    updateInterpolation(String entitytypestem.getPkid(),entitytypesTemConfig);


}
