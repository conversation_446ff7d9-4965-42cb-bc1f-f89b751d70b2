package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 存储临时数据集与临时目录节点的关联关系。 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_tmp_directory_dataref")
@ApiModel(value="PldTmpDirectorydataref对象", description="存储临时数据集与临时目录节点的关联关系。")
public class TmpDirectorydataref implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "目录编号")
    @TableField("directory_id")
    private String directoryid;

    @ApiModelProperty(value = "批次号(年月日时分秒-用户名)")
    @TableField("batch_code")
    private String batchcode;

    @ApiModelProperty(value = "数据源(udbx文件)文在本地的存储路径")
    @TableField("data_source_path")
    private String datasourcepath;

    @ApiModelProperty(value = "数据集名称")
    @TableField("data_set_name")
    private String datasetname;

    @ApiModelProperty(value = "数据集类型枚举值")
    @TableField("data_set_type")
    private String datasettype;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
