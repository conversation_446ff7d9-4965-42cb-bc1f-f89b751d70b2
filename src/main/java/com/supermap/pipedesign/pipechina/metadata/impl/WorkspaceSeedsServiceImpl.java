package com.supermap.pipedesign.pipechina.metadata.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.supermap.config.exception.BusinessException;
import com.supermap.data.FieldInfo;
import com.supermap.data.*;
import com.supermap.layout.MapLayout;
import com.supermap.mapping.*;
import com.supermap.mapping.Map;
import com.supermap.pipedesign.pipechina.engineering.dao.DesignsubTaskMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Design;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignsubTask;
import com.supermap.pipedesign.pipechina.engineering.entity.ESUnit;
import com.supermap.pipedesign.pipechina.engineering.entity.Engineering;
import com.supermap.pipedesign.pipechina.engineering.service.IDesignService;
import com.supermap.pipedesign.pipechina.engineering.service.IESUnitService;
import com.supermap.pipedesign.pipechina.engineering.service.IEngineeringService;
import com.supermap.pipedesign.pipechina.metadata.entity.*;
import com.supermap.pipedesign.pipechina.metadata.service.*;
import com.supermap.pipedesign.pipechina.metadata.utils.enums.EntityDatasetFieldType;
import com.supermap.pipedesign.pipechina.metadata.utils.enums.EntityDatasetType;
import com.supermap.pipedesign.pipechina.metadata.utils.enums.Errors;
import com.supermap.pipedesign.pipechina.utils.ExcelUtils;
import com.supermap.realspace.*;
import com.supermap.tools.base.FileGenerateUtils;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.base.RedisCommonUtil;
import com.supermap.tools.file.FileUtils;
import com.supermap.tools.file.PathUtils;
import com.supermap.tools.sqlite.SqliteUtils;
import com.supermap.tools.sqlite.vo.FiledInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.StringBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.AopInvocationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.net.Socket;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.WritableByteChannel;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service("WorkspaceSeedsServiceImpl")
public class WorkspaceSeedsServiceImpl implements IWorkspaceSeedsService {

    private final ReentrantLock lock = new ReentrantLock(true);

    @Resource(name = "EntitytypesImpl")
    private IEntitytypesService iEntitytypesService;
    /*    @Resource(name = "EntitytypestemImpl")
        private IEntitytypestemService iEntitytypestemService;*/
    @Resource(name = "EntitytypestemImpl")
    private IEntitytypestemService iEntitytypestemService;
    @Resource(name = "DatasetinfotemImpl")
    private IDatasetinfotemService iDatasetinfotemService;
    @Resource(name = "DatasetfieldtemImpl")
    private IDatasetfieldtemService iDatasetfieldtemService;
    @Resource(name = "EntitytypestylesImpl")
    private IEntitytypestylesService iEntitytypestylesService;
    @Resource(name = "EntitytypethemestylesImpl")
    private IEntitytypethemestylesService iEntitytypethemestylesService;
    @Resource(name = "DatasetinfoImpl")
    private IDatasetinfoService iDatasetinfoService;
    @Resource(name = "DatasetfieldImpl")
    private IDatasetfieldService iDatasetfieldService;
    @Resource(name = "EngineeringImpl")
    private IEngineeringService iEngineeringService;
    @Resource(name = "DesignImpl")
    private IDesignService iDesignService;
    @Resource(name = "ESUnitImpl")
    private IESUnitService iesUnitService;
    @Autowired
    private DesignsubTaskMapper designsubTaskMapper;
    @Autowired
    private RedisCommonUtil _redisCommonUtil;

    @Value("${workseed.font.fontname}")
    private String strMapLabelFontName;
    @Value("${workseed.font.size}")
    private double dblMapLabelFontSize;
    @Value("${workseed.font.height}")
    private double dblMapLabelFontHeiht;
    @Value("${workseed.font.color}")
    private String strMapLabelFontColor;
    @Value("${workseed.font.outlinecolor}")
    private String strMapLabelBorderColor;
    @Value("${workseed.font.outlinewidth}")
    private int iMapLabelOutLineWidth;
    @Value("${gisdatatools.ip}")
    private String strGISDataToolIP;
    @Value("${gisdatatools.port}")
    private int iGISDataToolPort;
    @Value("${javapath}")
    private String strJavaPath;
    @Value("${externaljarpath}")
    private String strExternalJARPath;

    @Value("${workseed.generationmethod}")
    private String strWorkspaceGenerationMethod;

    //最大可启动的工作空间生成器进程数
    @Value("${workseed.maxparallelprocesscount}")
    private int iMaxParallelProcessCount;

    @Value("${download.data.puretabulardatasetnames}")
    private String strPureTabluarDatasetNames;

    @Override
    public void fetchNewWorkspaceFiles(HttpServletResponse response) {
        loadNewWorkspace("ee9b15c63836754ddd970ed6b4e80913", "68d5aed115679ce3736e5cdaac9fe17c", System.getProperty("user.dir") + "/static/sqllitedb/", JavaUtils.getDateTimeSSS());
    }

    // 拉取最新工作空间
    @Override
    public Boolean loadNewWorkspace(String projectId, String unitId, String filePath, String strExecuteTime) {

        if (projectId == null || projectId.trim().length() < 1 || unitId == null || unitId.trim().length() < 1) {
            return false;
        }
        String strKey = unitId + "-" + strExecuteTime;
        String strMsg = "";
        /*DesignsubTask designsubTask = designsubTaskMapper.selectById(unitId);
        if (designsubTask != null) {
            unitId = designsubTask.getDesignid();
        } else {
            return false;
        }*/
        Engineering engineering = iEngineeringService.detail(projectId);
        if (engineering == null) {
            return false;
        }
//        String strStageName = engineering.getStageinnername();      //目前数据库中是用stageInnerName作为施工阶段名称
        String SRID = "";
        //预可研、可研、初设三个阶段的项目从设计段中读取坐标系及度带信息
        if (unitId != null && unitId.trim().length() > 0) {
//由于管理端没有考虑到施工阶段不同要区分为工程单元和设计段，全部按工程单元来存了。所以暂时全部按照设计段来处理了
            if (true||engineering.getStageinnercode().equals("0") || engineering.getStageinnercode().equals("1") || engineering.getStageinnercode().equals("2")) {
                //预可研、可研、初设三个阶段从设计段中获取坐标系及度带信息
                Design design = iDesignService.getById(unitId);
                if (design == null) {
                    return false;
                }
                SRID = design.getSrid();
            } else if (false&&(engineering.getStageinnercode().equals("3") || engineering.getStageinnercode().equals("4"))) {
                //施工图和竣工图从工程单元中读取坐标系及度带信息
                ESUnit esUnit = iesUnitService.getById(unitId);
                SRID = esUnit.getSrid();
            }
        } else {
            return false;
        }

        //String strWorkspacePath = System.getProperty("user.dir");//"/static/sqllitedb/"
        /*  String path = strWorkspacePath + File.separator+"static\\sqllitedb" + File.separator+"EntityDesign.sxwu";*/
        Workspace workspace = new Workspace();
        WorkspaceConnectionInfo workspaceConnectionInfo = new WorkspaceConnectionInfo();
        workspaceConnectionInfo.setType(WorkspaceType.SXWU);
        workspaceConnectionInfo.setServer(filePath + File.separator + "EntityDesign.sxwu");
        Boolean blResult = workspace.open(workspaceConnectionInfo);
        if (blResult == false || workspace.getDatasources().getCount() < 1) {       //工作空间打开失败
            workspace.saveAs(workspaceConnectionInfo);
        }
        //从工作空间中获取实体二维地图
        String strMapXML = workspace.getMaps().getMapXML("EntitiesMap");
        Map mapEntity = new Map(workspace);
        mapEntity.fromXML(strMapXML);

        while (mapEntity.getLayers().getCount() > 0) {
            mapEntity.getLayers().remove(0);
        }
        PrjCoordSys prjCoordSys = new PrjCoordSys();
        if(null != SRID && SRID.equals("900913")){
            try {
                /*String strTDTWorkspaceFile = filePath + File.separator + "TDTWorkspaceTemplate.smwu";
                File fileWorkspace = new File(strTDTWorkspaceFile);
                if(fileWorkspace.exists()){
                    Workspace workspaceTDT = new Workspace();
                    WorkspaceConnectionInfo workspaceConnectionInfoTDT = new WorkspaceConnectionInfo();
                    workspaceConnectionInfoTDT.setType(WorkspaceType.SMWU);
                    workspaceConnectionInfoTDT.setServer(strTDTWorkspaceFile);
                    workspaceTDT.open(workspaceConnectionInfoTDT);
                    if(workspaceTDT!=null&&workspaceTDT.getDatasources().getCount()>0){
                        Datasource datasourceTDT = workspaceTDT.getDatasources().get(0);
                        prjCoordSys = datasourceTDT.getDatasets().get(0).getPrjCoordSys();
                        workspaceTDT.close();
                    }
                }*/
                //prjCoordSys.fromFile(PathUtils.getDesignPath() + File.separator + "TDTUserDefineCoordSystem.xml", PrjFileType.SUPERMAP);
                prjCoordSys= new PrjCoordSys(3857);
            }catch (Exception e){
                strMsg = "为地图设置天地图坐标系失败！";
                log.error(strMsg);
                _redisCommonUtil.setKey(strKey, strMsg);
                return false;
            }
        }else if(null != SRID && SRID.trim().length()>0){
            try {
                prjCoordSys = new PrjCoordSys(Integer.parseInt(SRID));
            }catch (Exception e){
                log.error("构造EPSGCode为" + SRID + "的坐标系对象失败！");
                return false;
            }
        }
        if(prjCoordSys==null){
            log.info("构造EPSGCode为" + SRID + "的坐标系对象失败！");
            return false;
        }
        //为地图设置坐标系
        try {
            //System.out.println("开始为地图设置编号为"+SRID+"的坐标系。");
            mapEntity.setPrjCoordSys(prjCoordSys);
            //System.out.println("为地图设置编号为"+SRID+"的坐标系成功！");
        }catch (Exception e){
            strMsg = "error:为地图"+mapEntity.getName()+"设置坐标系失败！";
            log.error(strMsg);
            _redisCommonUtil.setKey(strKey, strMsg);
            return false;
        }
        //从工作空间中获取实体三维场景
        Scene sceneEntity = new Scene(workspace);
        if (workspace.getScenes().getCount() > 0) {
            try {
                String strSceneXML = workspace.getScenes().getSceneXML("EntitiesScene");
                sceneEntity.fromXML(strSceneXML);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            try {
                ClassPathResource resource = new ClassPathResource("SceneTemplate.xml");
                InputStream is = resource.getInputStream();
                InputStreamReader isr = new InputStreamReader(is);
                BufferedReader br = new BufferedReader(isr);
                StringBuilder stringBuilder = new StringBuilder();
                String line = null;
                String ls = System.getProperty("line.separator");
                while ((line = br.readLine()) != null) {
                    stringBuilder.append(line);
                    stringBuilder.append(ls);
                }
                stringBuilder.deleteCharAt(stringBuilder.length() - 1); // 删除最后一个新行分隔符
                is.close();

                br.close();
                isr.close();

                String strSceneXML = stringBuilder.toString();
                sceneEntity.fromXML(strSceneXML);
            } catch (Exception e) {

            }
        }
        //三维场景由于是球面坐标系，所以就把天地图影像的图层保留下来作为背景图层
        //TODO 还需要将TIN地形缓存数据加载到场景中，但是因为全国的TIN地形缓存太大，下载到本地不现实
        // 所以需要在iDesktopX中的三维场景中是加载TIN地形缓存服务，作为基础图层。经测试Java版的桌面加载iServer地形服务有问题，所以还需要等待研发将该问题解决之后才能走通该功能
        while (sceneEntity.getLayers().getCount() > 1) {
            sceneEntity.getLayers().remove(0);
        }
        //为场景设置坐标系
        if (null != SRID && SRID.length() > 0) {
            try {
                //System.out.println("开始为场景设置编号为"+SRID+"的坐标系。");
                sceneEntity.setPrjCoordSys(prjCoordSys);
                //System.out.println("为场景设置编号为"+SRID+"的坐标系成功！");
            }catch (Exception e){
                log.error("为场景"+sceneEntity.getName()+"设置坐标系失败！");
            }
        }
        sceneEntity.setOverlapDisplayed(true);      //显示压盖对象

//        _redisCommonUtil.setKey(strKey, "正在创建数据文件的结构");
        Datasource datasource = workspace.getDatasources().get("EntitiesAchivments");

        /**
         * 提前把数据集注册表里的所有记录全部查询出来，防止后期遍历每类实体时频繁查库而带来的性能问题
         */

        List<Datasetinfo> lstDatasetInfo = iDatasetinfoService.list(projectId);
        if (lstDatasetInfo == null || lstDatasetInfo.size() < 1) {
            return false;
        }

        String strMainDatasetId = null;         //存储当前实体数据集对应的主数据集ID
        //String strMainDatasetName = null;       //存储当前实体数据集对应的主数据集名称
        List<Datasetfield> lstDatasetFields = iDatasetfieldService.list();

        _redisCommonUtil.setKey(strKey, "正在配置地图风格");

        // 查询默认风格
        List<Entitytypestyles> lstStyle = iEntitytypestylesService.list(projectId);
        if (lstStyle == null || lstStyle.size() < 1) {
            return false;
        }
        // 查询专题图风格
        List<Entitytypethemestyles> lstThemeStyle = iEntitytypethemestylesService.list(projectId);
        //List<Entitytypes> lstEntityTypes = iEntitytypesService.list();
        //按照order_index对实体类型进行排序，从而按照该顺序显示图层
        List<Entitytypes> lstEntityTypes = iEntitytypesService.getListBySort("order_index", "desc");

        long lBeginTime = System.currentTimeMillis();                   //开始为设置地图风格功能计时
        for (int i = 0; i < lstEntityTypes.size(); i++) {
            Entitytypes entitytypes = lstEntityTypes.get(i);
            if (entitytypes.getSequencestate() < 1 || !entitytypes.getProjectid().equalsIgnoreCase(projectId)) {
                continue;
            }
            String strEntityID = entitytypes.getPkid();
            //第一步：对于每个实体，先去实体关联数据集表里找到关联的所有数据集
            for (int j = 0; j < lstDatasetInfo.size(); j++) {
                Datasetinfo datasetinfo = lstDatasetInfo.get(j);
                if (null != datasetinfo.getEntitytypeid() && datasetinfo.getEntitytypeid().equalsIgnoreCase(strEntityID)) {
                    String strDatasetName = datasetinfo.getTablename();     //本来应该直接使用datasetname来作为数据集名称的，但因为之前开发人员不清楚，所以使用了tablename字段来存储真正的数据集名称
                    String strDatasetAlias = datasetinfo.getDatasetalias();
/*  如下两行代码用于调试模型数据集的加载功能。不用时注释掉
                    if(strDatasetName.indexOf("_M")>0)
                        strDatasetName+="";
 */
                    Boolean blHasAddTheme = false;
                    Boolean blHasAddStyle = false;
                    if(datasource.getDatasets().get(strDatasetName)==null) {
                        log.info("udbx数据源中不包含名称为"+strDatasetName+"的数据集，跳过该数据集不创建对应的图层。");
                        continue;
                    }
                    //第二步：去实体专题图定义表里找到该实体针对该图层定义的单值专题图，构造专题图图层，分别添加到Map和Scene里
                    //有专题图只构造专题图不构造普通图层风格
                    if (lstThemeStyle != null && lstThemeStyle.size() > 0) {
                        String strFieldName = "";
                        String strFieldID = "";
                        ThemeUnique themeUnique = null;
                        Theme3DUnique theme3DUnique = null;
                        for (int k = 0; k < lstThemeStyle.size(); k++) {
                            if (lstThemeStyle.get(k).getEntitytypeid().equalsIgnoreCase(strEntityID) && lstThemeStyle.get(k).getDatasetid().equalsIgnoreCase(datasetinfo.getPkid())) {
                                Entitytypethemestyles entitytypethemestyles = lstThemeStyle.get(k);
                                if (entitytypethemestyles.getThemefieldid() != null) {
                                    for (int l = 0; l < lstDatasetFields.size(); l++) {
                                        if (lstDatasetFields.get(l).getPkid().equalsIgnoreCase(entitytypethemestyles.getThemefieldid())) {
                                            strFieldName = lstDatasetFields.get(l).getFieldname();
                                            break;
                                        }
                                    }
                                    //字段一变就创建新的专题图层
                                    if (!entitytypethemestyles.getThemefieldid().equalsIgnoreCase(strFieldID)) {
                                        if (themeUnique != null) {     //如果之前已经构造过themeUnique了，说明上一个字段对应的单值专题已遍历和构造完毕
                                            //就把构造好的themeUnique添加到地图图层集合里
                                            mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), themeUnique, true);
                                            sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), theme3DUnique, true);
                                        } else {                    //如果之前没有初始化，就进行themeUnique和theme3DUnique初始化
                                            strFieldID = entitytypethemestyles.getThemefieldid();
                                            themeUnique = new ThemeUnique();
                                            themeUnique.setUniqueExpression(strFieldName);
                                            theme3DUnique = new Theme3DUnique();
                                            theme3DUnique.setUniqueExpression(strFieldName);
                                        }
                                    } //else {  //字段没变就去构造themeUnique里的单值风格，并添加到themeUnique里
                                    Color fillColor = Color.black;
                                    try {
                                        if (entitytypethemestyles.getTwodlinecolor() != null && entitytypethemestyles.getTwodlinecolor().toUpperCase().indexOf("0X") > -1) {          //以16进制方式存储的颜色
                                            fillColor = toColorFromString(entitytypethemestyles.getTwodfillcolor());
                                        } else if (entitytypethemestyles.getTwodlinecolor() != null && entitytypethemestyles.getTwodlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                            fillColor = toColorFromRGBA(entitytypethemestyles.getTwodfillcolor());
                                        }
                                    } catch (Exception e) {
                                        log.error(e.getMessage());
                                    }
                                    Color fillColor3D = Color.white;
                                    try{
                                        if(entitytypethemestyles.getThreedlinecolor()!=null&&entitytypethemestyles.getThreedlinecolor().toUpperCase().indexOf("0X")>-1){
                                            fillColor3D = toColorFromString(entitytypethemestyles.getThreedlinecolor());
                                        } else if (entitytypethemestyles.getThreedlinecolor() != null && entitytypethemestyles.getThreedlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                            fillColor3D = toColorFromRGBA(entitytypethemestyles.getThreedlinecolor());
                                        } else if(entitytypethemestyles.getTwodlinecolor() != null) {
                                            fillColor3D = fillColor;
                                        }
                                    }catch (Exception e){
                                        log.error(e.getMessage());
                                    }
                                    Color lineColor = Color.BLACK;
                                    try {
                                        if (entitytypethemestyles.getTwodlinecolor() != null && entitytypethemestyles.getTwodlinecolor().toUpperCase().indexOf("0X") > -1) {          //以16进制方式存储的颜色
                                            lineColor = toColorFromString(entitytypethemestyles.getTwodlinecolor());
                                        } else if (entitytypethemestyles.getTwodlinecolor() != null && entitytypethemestyles.getTwodlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                            lineColor = toColorFromRGBA(entitytypethemestyles.getTwodlinecolor());
                                        }
                                    } catch (Exception e) {
                                    }
                                    Color lineColor3D = Color.white;
                                    try {
                                        if (entitytypethemestyles.getThreedlinecolor() != null && entitytypethemestyles.getThreedlinecolor().toUpperCase().indexOf("0X") > -1) {          //以16进制方式存储的颜色
                                            lineColor3D = toColorFromString(entitytypethemestyles.getThreedlinecolor());
                                        } else if (entitytypethemestyles.getThreedlinecolor() != null && entitytypethemestyles.getThreedlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                            lineColor3D = toColorFromRGBA(entitytypethemestyles.getThreedlinecolor());
                                        } else if(entitytypethemestyles.getTwodlinecolor()!=null){
                                            lineColor3D = lineColor;
                                        }
                                    } catch (Exception e) {
                                    }

                                    Integer markerSymbolID = 0;
                                    try {
                                        markerSymbolID = Integer.parseInt(entitytypethemestyles.getTwodsymbalcode());
                                    } catch (Exception se) {
                                    }
                                    Integer threeDMarkerSymbolID = 0;
                                    try {
                                        threeDMarkerSymbolID = Integer.parseInt(entitytypethemestyles.getThreedsymbalcode());
                                    } catch (Exception se) {
                                    }
                                    Integer markerSymbolSize = entitytypethemestyles.getTwodsymbalsize();
                                    Integer markerSymbolWidth = entitytypethemestyles.getTwodsymbalwidth();
                                    Integer markerSymbolHeight = entitytypethemestyles.getTwodsymbalhigh();
                                    Integer threeDSymbolSize = 0;
                                    if (entitytypethemestyles.getThreedsymbalsize() != null) {
                                        threeDSymbolSize = entitytypethemestyles.getThreedsymbalsize();
                                    }else if(entitytypethemestyles.getThreedsymbalwidth()!=null) {
                                        threeDSymbolSize = entitytypethemestyles.getThreedsymbalwidth();
                                    }
                                    Integer lineSymbolID = 0;
                                    try {
                                        lineSymbolID = Integer.parseInt(entitytypethemestyles.getTwodlinetypecode());
                                    } catch (Exception le) {
                                    }
                                    Integer lineSymbolID3D = 0;
                                    try{
                                        lineSymbolID3D = Integer.parseInt(entitytypethemestyles.getThreedlinetypecode());
                                    }catch (Exception le){}
                                    if(lineSymbolID3D==0&&lineSymbolID>0)
                                        lineSymbolID3D = lineSymbolID;
                                    Integer lineWidth = 2;
                                    try {
                                        lineWidth = entitytypethemestyles.getTwodlinewidth();
                                    }catch (Exception e){}
                                    if (lineWidth == null) {
                                        lineWidth = 1;
                                    }
                                    Integer lineWidth3D = 4;
                                    try{
                                        lineWidth3D = entitytypethemestyles.getThreedlinewidth();
                                    }catch (Exception e){}
                                    Integer fillSymbolID = 0;
                                    try {
                                        fillSymbolID = Integer.parseInt(entitytypethemestyles.getTwodfilltypecode());
                                    } catch (Exception se) {
                                    }
                                    Integer fillSymbolID3D = 0;
                                    try{
                                        fillSymbolID3D = Integer.parseInt(entitytypethemestyles.getThreedfilltypecode());
                                    }catch (Exception te){}
                                    if(fillSymbolID3D==0)
                                        fillSymbolID3D = fillSymbolID;
                                    Integer iFillOpacity = entitytypethemestyles.getTwodfillopacity();
                                    if(iFillOpacity==null)
                                        iFillOpacity = 100;
                                    Integer iFillOpacity3D = entitytypethemestyles.getThreedfillopacity();
                                    if(iFillOpacity3D==null)
                                        iFillOpacity3D = iFillOpacity;
                                    String strCaption = entitytypethemestyles.getThemevalue();
                                    if(strCaption==null||strCaption.trim().length()>0) {
                                        ThemeUniqueItem item = getItem(markerSymbolID, markerSymbolWidth, markerSymbolHeight, lineSymbolID, Double.parseDouble(lineWidth + ""), lineColor, fillSymbolID, fillColor, iFillOpacity, strCaption);
                                        try {
                                            themeUnique.add(item);
                                        } catch (Exception e) {
                                            log.error("单值" + strCaption + "对于二维地图来说已存在");
                                        }
                                        Theme3DUniqueItem item3D = get3DItem(threeDMarkerSymbolID, Double.parseDouble(threeDSymbolSize + ""), lineSymbolID3D, Double.parseDouble(lineWidth3D + ""), lineColor3D, fillSymbolID3D, fillColor3D, strCaption);
                                        try {
                                            theme3DUnique.add(item3D);
                                        } catch (Exception e) {
                                            log.error("单值" + strCaption + "对于三维场景来说已存在");
                                        }
                                    }
                                }

                            }
                        }
                        if (themeUnique != null) {
                            if(datasource.getDatasets().get(strDatasetName)!=null) {
                                Layer layer = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), themeUnique, true);
                                layer.setCaption(strDatasetAlias);
                                if(datasetinfo.getTablename().equalsIgnoreCase("center_line_L")) {
                                    layer.setSnapable(true);
                                }else{
                                    layer.setSnapable(false);
                                }
                                blHasAddTheme = true;
                            }
                        }
                        if (theme3DUnique != null) {
                            /*Layer3DSettingVector layer3DSetting = new Layer3DSettingVector();
                            Layer3DDataset layer3DDataset = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName),layer3DSetting,true);
                            layer3DDataset.setCaption(strDatasetAlias);*/
                            if(datasource.getDatasets().get(strDatasetName)!=null) {
                                Layer3DDataset layer3DDatasetTheme = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), theme3DUnique, true);
                                layer3DDatasetTheme.setCaption(strDatasetAlias);
                                sceneEntity.refresh();
                                blHasAddTheme = true;
                            }
                        }
                    }
                    if (blHasAddTheme == false) {  //如果没有专题图才设置图层风格
                        for (int k = 0; k < lstStyle.size(); k++) {
                            //第二步：去实体风格定义表里找到图层风格，构造图层，添加到Map里
                            if (lstStyle.get(k).getEntitytypeid().equalsIgnoreCase(strEntityID) && lstStyle.get(k).getDatasetid().equalsIgnoreCase(datasetinfo.getPkid())) {
/*                              Boolean blMapHasAdded = false;
                                for (int i1 = 0; i1 < mapEntity.getLayers().getCount(); i1++) {
                                    if (mapEntity.getLayers().get(i1).getDataset().getName().equalsIgnoreCase(strDatasetName)) {
                                        blMapHasAdded = true;
                                        break;
                                    }
                                }
                                Boolean blSceneHasAdded = false;
                                for (int i1 = 0; i1 < sceneEntity.getLayers().getCount(); i1++) {
                                    if (sceneEntity.getLayers().get(i1).getDataName().equalsIgnoreCase(strDatasetName)) {
                                        blSceneHasAdded = true;
                                        break;
                                    }
                                }*/
                                Entitytypestyles entitytypestyles = lstStyle.get(k);
                                GeoStyle style = new GeoStyle();
                                GeoStyle3D style3D = new GeoStyle3D();
                                //二维点符号大小
                                if (null != entitytypestyles.getTwodsymbalsize()) {
                                    style.setMarkerSize(new Size2D(entitytypestyles.getTwodsymbalsize(), entitytypestyles.getTwodsymbalsize()));
                                } else if(null!=entitytypestyles.getTwodsymbalwidth()) {
                                    style.setMarkerSize(new Size2D(entitytypestyles.getTwodsymbalwidth(), entitytypestyles.getTwodsymbalhigh()));
                                } else {
                                    style.setMarkerSize(new Size2D(7, 7));
                                }
                                //对于中线桩，固定图标大小为6mm
                                if(datasetinfo.getTablename().equalsIgnoreCase("center_line_P")) {
                                    style.setMarkerSize(new Size2D(6,6));
                                }
                                //三维点符号大小
                                if (null != entitytypestyles.getThreedsymbalsize()) {
                                    style3D.setMarkerSize(entitytypestyles.getThreedsymbalsize());
                                } else if(null!=entitytypestyles.getThreedsymbalwidth()) {
                                    style3D.setMarkerSize(entitytypestyles.getThreedsymbalwidth());
                                } else {
                                    if(datasetinfo.getTablename().toUpperCase().indexOf("_P")>0) {
                                        style3D.setMarker3D(true);
                                        style3D.setAltitudeMode(AltitudeMode.ABSOLUTE);
                                    }else if(datasetinfo.getTablename().toUpperCase().indexOf("_L")>0||datasetinfo.getTablename().toUpperCase().indexOf("_R")>0) {
                                        style3D.setAltitudeMode(AltitudeMode.RELATIVE_TO_GROUND);
                                    }
                                }
                                //二维点符号ID
                                if (null != entitytypestyles.getTwodsymbalcode() && entitytypestyles.getTwodsymbalcode().trim().length() > 0) {
                                    try {
                                        style.setMarkerSymbolID(Integer.parseInt(entitytypestyles.getTwodsymbalcode()));
                                    } catch (Exception se) {
                                        log.error("设置二维点符号ID失败！");
                                    }
                                }
                                //三维点符号ID
                                if (null != entitytypestyles.getThreedsymbalcode() && entitytypestyles.getThreedsymbalcode().trim().length() > 0) {
                                    try {
                                        style3D.setMarkerSymbolID(Integer.parseInt(entitytypestyles.getThreedsymbalcode()));
                                    } catch (Exception se) {
                                        log.error("设置三维点符号ID失败！");
                                    }
                                } else if(null != entitytypestyles.getTwodsymbalcode() && entitytypestyles.getTwodsymbalcode().trim().length() > 0) {
                                    style3D.setMarkerSymbolID(style.getMarkerSymbolID());   //如果没有设置三维符号ID，就复用二维里的符号ID
                                }

                                if (entitytypestyles.getTwodlinecolor() != null && entitytypestyles.getTwodlinecolor().trim().length() > 0) {
                                    try {
                                        Color color = Color.BLACK;
                                        if (entitytypestyles.getTwodlinecolor() != null && entitytypestyles.getTwodlinecolor().toUpperCase().indexOf("0X") > -1)           //以16进制方式存储的颜色
                                        {
                                            color = toColorFromString(entitytypestyles.getTwodlinecolor());
                                        } else if (entitytypestyles.getTwodlinecolor() != null && entitytypestyles.getTwodlinecolor().toLowerCase().indexOf("rgba(") == 0)   //已rgba()方式存储的颜色
                                        {
                                            color = toColorFromRGBA(entitytypestyles.getTwodlinecolor());
                                        }
                                        //二维线色
                                        style.setLineColor(color);
                                    } catch (Exception el) {
                                        log.error("设置图层线色风格失败！");
                                    }
                                }
                                if (entitytypestyles.getThreedlinecolor() != null && entitytypestyles.getThreedlinecolor().trim().length() > 0) {
                                    try {
                                        Color color = Color.BLACK;
                                        if (entitytypestyles.getThreedlinecolor() != null && entitytypestyles.getThreedlinecolor().toUpperCase().indexOf("0X") > -1)           //以16进制方式存储的颜色
                                        {
                                            color = toColorFromString(entitytypestyles.getTwodlinecolor());
                                        } else if (entitytypestyles.getThreedlinecolor() != null && entitytypestyles.getThreedlinecolor().toLowerCase().indexOf("rgba(") == 0)   //已rgba()方式存储的颜色
                                        {
                                            color = toColorFromRGBA(entitytypestyles.getThreedlinecolor());
                                        }
                                        style3D.setLineColor(color);
                                    } catch (Exception e1) {

                                    }
                                }else if(entitytypestyles.getTwodlinecolor() != null && entitytypestyles.getTwodlinecolor().trim().length() > 0) {
                                    //如果没有配置三维线色，就用二维的颜色
                                    style3D.setLineColor(style.getLineColor());
                                }

                                if (null != entitytypestyles.getTwodlinewidth() && entitytypestyles.getTwodlinewidth() > 0) {
                                    try {
                                        //二维线宽
                                        style.setLineWidth(Double.parseDouble(entitytypestyles.getTwodlinewidth() + ""));
                                    } catch (Exception se) {
                                        log.error("为" + entitytypes.getEntitytypename() + "设置二维线宽失败！");
                                    }
                                }
                                if (null != entitytypestyles.getThreedlinewidth() && entitytypestyles.getThreedlinewidth() > 0) {
                                    try {
                                        //三维线宽
                                        style3D.setLineWidth(Double.parseDouble(entitytypestyles.getThreedlinewidth() + ""));
                                    } catch (Exception se) {
                                        log.error("为" + entitytypes.getEntitytypename() + "设置三维线宽失败！");
                                    }
                                }else if (null != entitytypestyles.getTwodlinewidth() && entitytypestyles.getTwodlinewidth() > 0) {
                                    style3D.setLineWidth(style.getLineWidth());
                                }
                                if (entitytypestyles.getTwodlinetypecode() != null && entitytypestyles.getTwodlinetypecode().trim().length() > 0) {
                                    try {
                                        Integer iLineTypeCode = Integer.parseInt(entitytypestyles.getTwodlinetypecode());
                                        //二维线型符号
                                        style.setLineSymbolID(iLineTypeCode);
                                    } catch (Exception se) {
                                        log.error("为" + entitytypes.getEntitytypename() + "设置二维线符号风格失败！");
                                    }
                                }
                                if (entitytypestyles.getThreedlinetypecode() != null && entitytypestyles.getThreedlinetypecode().trim().length() > 0) {
                                    try {
                                        Integer iLineTypeCode = Integer.parseInt(entitytypestyles.getThreedlinetypecode());
                                        //三维线型符号
                                        style3D.setLineSymbolID(iLineTypeCode);
                                    } catch (Exception se) {
                                        log.error("为" + entitytypes.getEntitytypename() + "设置三维线符号风格失败！");
                                    }
                                }else if (entitytypestyles.getTwodlinetypecode() != null && entitytypestyles.getTwodlinetypecode().trim().length() > 0) {
                                    style3D.setLineSymbolID(style.getLineSymbolID());
                                }
                                if (entitytypestyles.getTwodfillcolor() != null && entitytypestyles.getTwodfillcolor().trim().length() > 0) {
                                    try {
                                        Color color = Color.BLACK;
                                        if (entitytypestyles.getTwodfillcolor() != null && entitytypestyles.getTwodfillcolor().toUpperCase().indexOf("0X") > -1)           //以16进制方式存储的颜色
                                        {
                                            color = toColorFromString(entitytypestyles.getTwodlinecolor());
                                        } else if (entitytypestyles.getTwodfillcolor() != null && entitytypestyles.getTwodfillcolor().toLowerCase().indexOf("rgba(") == 0)   //已rgba()方式存储的颜色
                                        {
                                            color = toColorFromRGBA(entitytypestyles.getTwodlinecolor());
                                        }
                                        //二维填充色
                                        style.setFillForeColor(color);
                                    } catch (Exception el) {
                                        log.error("为" + entitytypes.getEntitytypename() + "设置二维图层填充色失败！");
                                    }
                                }
                                if (entitytypestyles.getThreedfillcolor() != null && entitytypestyles.getThreedfillcolor().trim().length() > 0) {
                                    try {
                                        Color color = Color.BLACK;
                                        if (entitytypestyles.getThreedfillcolor() != null && entitytypestyles.getThreedfillcolor().toUpperCase().indexOf("0X") > -1)           //以16进制方式存储的颜色
                                        {
                                            color = toColorFromString(entitytypestyles.getThreedfillcolor());
                                        } else if (entitytypestyles.getThreedfillcolor() != null && entitytypestyles.getThreedfillcolor().toLowerCase().indexOf("rgba(") == 0)   //已rgba()方式存储的颜色
                                        {
                                            color = toColorFromRGBA(entitytypestyles.getThreedfillcolor());
                                        }
                                        //三维填充色
                                        style3D.setFillForeColor(color);
                                    } catch (Exception el) {
                                        log.error("为" + entitytypes.getEntitytypename() + "设置三维图层填充色失败！");
                                    }
                                }

                                if (entitytypestyles.getTwodfilltypecode() != null && entitytypestyles.getTwodfilltypecode().trim().length() > 0) {
                                    try {
                                        Integer iFillSymbolID = Integer.parseInt(entitytypestyles.getTwodfilltypecode().trim());
                                        //二维面填充风格
                                        style.setFillSymbolID(iFillSymbolID);
                                    } catch (Exception se) {
                                        log.error("为" + entitytypes.getEntitytypename() + "设置二维符号ID失败！");
                                    }
                                }
                                if (entitytypestyles.getThreedfilltypecode() != null && entitytypestyles.getThreedfilltypecode().trim().length() > 0) {
                                    try {
                                        Integer iFillSymbolID = Integer.parseInt(entitytypestyles.getThreedfilltypecode().trim());
                                        //三维面填充风格
                                        style3D.setFillSymbolID(iFillSymbolID);
                                    } catch (Exception e) {
                                        log.error("为" + entitytypes.getEntitytypename() + "设置三维符号ID失败！");
                                    }
                                }
                                if (entitytypestyles.getTwodfillopacity() != null && entitytypestyles.getTwodfillopacity() > 0) {
                                    try {
                                        Integer iFillOpacityRate = entitytypestyles.getTwodfillopacity();
                                        //二维面透明度
                                        style.setFillOpaqueRate(iFillOpacityRate);
                                        //三维面透明度设置方法没找到，暂未设置
                                    } catch (Exception se) {
                                        log.error("为" + entitytypes.getEntitytypename() + "设置二维填充透明度失败！");
                                    }
                                }
                                LayerSettingVector layerSettingVector = new LayerSettingVector();
                                layerSettingVector.setStyle(style);
                                Layer3DSettingVector layer3DSettingVector = new Layer3DSettingVector();
                                layer3DSettingVector.setStyle(style3D);

                                /*if (datasource.getDatasets().get(strDatasetName) != null)*/ {
                                    //添加图层到二维地图中
                                    /*if (blMapHasAdded == false)*/ {
                                        Layer layer = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), layerSettingVector, true);
                                        layer.setCaption(strDatasetAlias);
                                        if(datasetinfo.getTablename().equalsIgnoreCase("center_line_L")) {
                                            layer.setSnapable(true);
                                        }else{
                                            layer.setSnapable(false);
                                        }
                                    }
                                    //添加图层到三维场景中
                                    /*if (blSceneHasAdded == false)*/ {
                                        Layer3DDataset layer3DDataset = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), layer3DSettingVector, true);
                                        layer3DDataset.setCaption(strDatasetAlias);
                                    }
                                }
                                blHasAddStyle = true;
                                break;       //找到记录了就没必要再往后循环了，在风格记录数很大的情况下能够提升系统执行效率
                            }
                        }
                        if (strDatasetName.toUpperCase().lastIndexOf("_M") == strDatasetName.length()-2) {
                            //如果是三维模型数据集，需要通过如下方式将三维模型数据集添加到场景里
                            Layer3DSettingVector layer3DSettingVector = new Layer3DSettingVector();
                            layer3DSettingVector.getStyle().setAltitudeMode(AltitudeMode.ABSOLUTE);
                            layer3DSettingVector.getStyle().setFillMode(FillMode3D.FILL);
                            layer3DSettingVector.getStyle().setLineWidth(1.0d);
                            if(datasource.getDatasets().get(strDatasetName)!=null) {
                                Layer3D layer3D = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), layer3DSettingVector, true);
                                if (strDatasetAlias.indexOf('表') == strDatasetAlias.length() - 1)       //数据库中注册的模型数据集名称最后面一个字写成了“表”，如果存在这种情况的话设置图层别名时就把最后一个“表”字去掉
                                    strDatasetAlias = strDatasetAlias.substring(0, strDatasetAlias.indexOf('表'));
                                layer3D.setCaption(strDatasetAlias);
                            }
                        }
                    }
                    if(!blHasAddTheme&&!blHasAddStyle&&!(strDatasetName.toUpperCase().lastIndexOf("_M") == strDatasetName.length() - 2)){
                        Layer layer = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), true);
                        layer.setCaption(strDatasetAlias);
                        Layer3DSettingVector layer3DSettingVector = new Layer3DSettingVector();
                        layer3DSettingVector.getStyle().setAltitudeMode(AltitudeMode.ABSOLUTE);
                        layer3DSettingVector.getStyle().setFillMode(FillMode3D.FILL);
                        layer3DSettingVector.getStyle().setLineWidth(1.0d);
                        Layer3D layer3D = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), layer3DSettingVector, true);
                        layer3D.setCaption(strDatasetAlias);
                    }

                    /**
                     * 根据实体类型定义表中的tag_text字段(存储表达式)来生成矩阵标签专题图
                     * tag_text中存储的表达式为多行表达式，格式为：'文本11'||实体名称.字段名1||'文本12'&&'文本21'||实体名称.字段名2||'文本22'&&......
                     * 上述表达式中，多行之间用“&&”分割，同一行的表达式内“||”为字符串连接符
                     */
                    if (!datasetinfo.getDatasettype().equals(EntityDatasetType.TABULAR.getValue())) {
/*下面两行代码用于调试专题图表达式的断点捕获代码。不用时注释掉。
                        if(datasetinfo.getTablename().indexOf("center_line")>-1)
                            System.out.println(datasetinfo.getDatasetalias());
*/
                        //第一步：先从对应实体类型中获取到tag_text表达式
                        String strThemeExpression = entitytypes.getTagtext();
                        if (strThemeExpression == null || strThemeExpression.trim().length() < 1) {
                            continue;
                        }
                        String strMainEntityName = datasetinfo.getTablename().substring(0, datasetinfo.getTablename().lastIndexOf('_'));
                        strThemeExpression = strThemeExpression.replaceAll(strMainEntityName, datasetinfo.getTablename());     //将表达式中的实体类型名称替换为实体的某一类数据集名称。因为实体属性表是不能添加到地图里的，更不能用其制作专题图
                        //第二步：用"&&"对表达式进行分割，确定出行数
                        if(!datasetinfo.getDatasettype().equals(EntityDatasetType.MODEL.getValue())&&null!=datasource.getDatasets().get(strDatasetName)) {
                            /*
                                标签专题图表达式格式为：<实体名称>.<字段名称>||<实体名称>.<字段名称>......<br><实体名称>.<字段名称>
                                上述表达式中，“||”为连接运算符；如果想用多行来展示标签，则在需要换行的地方用<br>来标识
                                如果表达式中有<br>(即换行符)，就通过矩阵标签专题图来展示；如果没有<br>就通过普通标签专题图来展示
                             */
                            if(strThemeExpression.indexOf("&&")>-1)
                                strThemeExpression=strThemeExpression.replaceAll("&&", "");
                            if(strThemeExpression.indexOf("||||")>-1)       //对于四个连续的|，iDesktopX会出错，从而无法展示矩阵标签专题图，需要将其替换成两个|
                                strThemeExpression=strThemeExpression.replaceAll("[|][|][|][|]", "||");
                            if(strThemeExpression.startsWith("||"))
                                strThemeExpression=strThemeExpression.substring(2);
                            if(strThemeExpression.endsWith("||"))
                                strThemeExpression=strThemeExpression.substring(0, strThemeExpression.length()-2);
                            if(strThemeExpression.indexOf("<br>")>0) {      //表达式中有<br>就构造矩阵标签专题图
                                String[] arrExpressions = strThemeExpression.split("<br>");
                                LabelMatrix labelMatrix = new LabelMatrix(1, arrExpressions.length);
                                //第三步：构造每行表达式
                                for (int k = 0; k < arrExpressions.length; k++) {
                                    ThemeLabel themeLabel = new ThemeLabel();
                                    themeLabel.setLabelExpression(arrExpressions[k]);
                                    TextStyle textStyle = new TextStyle();
                                    //字体名称
                                    if(strMapLabelFontName!=null&&strMapLabelFontName.trim().length()>0)
                                        textStyle.setFontName(strMapLabelFontName);
                                    //字体颜色
                                    try {
                                        Field field = Class.forName("java.awt.Color").getField(strMapLabelFontColor);
                                        Color color = (Color)field.get(null);
                                        textStyle.setForeColor(color);
                                    } catch (Exception e) {
                                        textStyle.setForeColor(Color.WHITE);
                                    }
                                    //显示轮廓线
                                    textStyle.setOutline(true);
                                    //字体大小
                                    try{
                                        textStyle.setFontWidth(dblMapLabelFontSize);
                                    }catch (Exception e){}
                                    //字体轮廓线宽度
                                    try{
                                        textStyle.setOutlineWidth(iMapLabelOutLineWidth);
                                    }catch (Exception e){}
                                    //字体背景色(轮廓色)
                                    try{
                                        Field field = Class.forName("java.awt.Color").getField(strMapLabelBorderColor);
                                        Color color = (Color)field.get(null);
                                        textStyle.setBackColor(color);
                                    }catch (Exception e){}
                                    //字高
                                    try{
                                        textStyle.setFontHeight(dblMapLabelFontHeiht/10);
                                    }catch (Exception e) {
                                        textStyle.setFontHeight(4.0d);
                                    }
                                    textStyle.setSizeFixed(true);
                                    themeLabel.setUniformStyle(textStyle);
                                    labelMatrix.set(0, k, themeLabel);
                                }
                                //二维地图添加矩阵标签专题图
                                ThemeLabel themeLabelMatrix = new ThemeLabel();
                                themeLabelMatrix.setLabels(labelMatrix);
                                try {
                                    Layer layerTheme = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), themeLabelMatrix, true);
                                    layerTheme.setCaption(strDatasetAlias + "标签");
                                    layerTheme.setSnapable(false);
                                } catch (Exception e) {
                                    log.error("为数据集" + strDatasetName + "添加二维矩阵标签专题图失败！");
                                }
                            }else{      //表达式中没有<br>就是用普通标签专题图
                                ThemeLabel themeLabel = new ThemeLabel();
                                themeLabel.setLabelExpression(strThemeExpression);
                                TextStyle textStyle = new TextStyle();
                                //字体名称
                                if(strMapLabelFontName!=null&&strMapLabelFontName.trim().length()>0)
                                    textStyle.setFontName(strMapLabelFontName);
                                //字体颜色
                                try {
                                    Field field = Class.forName("java.awt.Color").getField(strMapLabelFontColor);
                                    Color color = (Color)field.get(null);
                                    textStyle.setForeColor(color);
                                } catch (Exception e) {
                                    textStyle.setForeColor(Color.WHITE);
                                }
                                //显示轮廓线
                                textStyle.setOutline(true);
                                //字体大小
                                try{
                                    textStyle.setFontWidth(dblMapLabelFontSize);
                                }catch (Exception e){}
                                //字体轮廓线宽度
                                try{
                                    textStyle.setOutlineWidth(iMapLabelOutLineWidth);
                                }catch (Exception e){}
                                //字体背景色(轮廓色)
                                try{
                                    Field field = Class.forName("java.awt.Color").getField(strMapLabelBorderColor);
                                    Color color = (Color)field.get(null);
                                    textStyle.setBackColor(color);
                                }catch (Exception e){}
                                //字高
                                try{
                                    textStyle.setFontHeight(dblMapLabelFontHeiht/10);
                                }catch (Exception e) {
                                    textStyle.setFontHeight(4.0d);
                                }
                                textStyle.setSizeFixed(true);
                                themeLabel.setUniformStyle(textStyle);
                                themeLabel.setSmallGeometryLabeled(true);
                                Layer layerThemeLabel = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), themeLabel, true);
                                layerThemeLabel.setCaption(strDatasetAlias + "标签");
                            }
                        }

                        //三维场景中不支持矩阵标签专题图，添加普通标签专题图
                        Theme3DLabel theme3DLabel = new Theme3DLabel();
                        strThemeExpression = strThemeExpression.replaceAll("&&", "||','||");    //由于“&&”是特殊字符，替换为','，否则无法创建三维标签专题图
                        strThemeExpression = strThemeExpression.replaceAll("<br>", "\\n");      //将<br>替换为"\n",因为三维场景不支持矩阵标签专题图，但是可以通过“\n”来换行
                        theme3DLabel.setLabelExpression(strThemeExpression);
                        TextStyle textStyle = new TextStyle();
                        textStyle.setFontHeight(8d);
                        //textStyle.setFontWidth(40d);
                        textStyle.setForeColor(Color.YELLOW);
                        textStyle.setSizeFixed(true);
                        theme3DLabel.setUniformStyle(textStyle);
                        Layer3DDataset layer3D= null;
                        try {
                            layer3D = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), theme3DLabel, true);
                            layer3D.setCaption(strDatasetAlias + "标签");

                            Layer3DSettingVector layer3DSettingVector = new Layer3DSettingVector();
                            GeoStyle3D geoStyle3D = new GeoStyle3D();
                            geoStyle3D.setAltitudeMode (AltitudeMode.CLAMP_TO_GROUND);
                            layer3DSettingVector.setStyle(geoStyle3D);
                            layer3D.setAdditionalSetting(layer3DSettingVector);
                            //layer3D.setMaxVisibleAltitude(1000d);     //暂时不要设标签最大可见高度，让用户自己去控制
                            layer3D.updateData();
                        }catch (Exception e){
                            log.error("为数据集" + strDatasetName + "添加三维标签专题图失败！");
                        }

                    }
                }
            }
        }
        long lEndTime = System.currentTimeMillis();
        strMsg = "构造地图图层风格共耗时：";
        long lTimeSpan = lEndTime-lBeginTime;
        long lTime = lTimeSpan/1000l;
        if(lTime>60)
            strMsg += new Long(lTime/60).intValue()+"分钟";
        else
            strMsg += lTime+"秒";
        log.info(strMsg);

        //防止因为没有数据而导致地图加载后范围不对的问题
        com.supermap.data.Rectangle2D rectangle2D = new Rectangle2D();
        rectangle2D.setBottom(-90d);
        rectangle2D.setLeft(-180d);
        rectangle2D.setRight(180d);
        rectangle2D.setTop(90d);
        mapEntity.setViewBounds(rectangle2D);

        GeoRectangle geoRectangle = new GeoRectangle();
        geoRectangle.setBounds(rectangle2D);
        PrjCoordSys sourcePrjCoordSys = new PrjCoordSys();
        sourcePrjCoordSys.setEPSGCode(4490);
        CoordSysTranslator.convert(geoRectangle, sourcePrjCoordSys, prjCoordSys, new CoordSysTransParameter(), CoordSysTransMethod.China_2D_7P);

        _redisCommonUtil.setKey(strKey, "正在对地图图层进行排序");

        //对地图和场景中的图层进行排序，将注记图层放到最上面，下面是点图层，再下面是面图层
        //对二维地图中的图层进行排序
        //先插入位于中间部位的线图层
        lBeginTime = System.currentTimeMillis();
        ArrayList<Layer> lstSortedLayers = new ArrayList<Layer>();
        for (int i = 0; i < mapEntity.getLayers().getCount(); i++) {
            Layer layer = mapEntity.getLayers().get(i);
            if ((layer.getDataset().getType().equals(DatasetType.LINE) || layer.getDataset().getType().equals(DatasetType.LINE3D))&&layer.getName().indexOf("#1")<0) {
                lstSortedLayers.add(layer);
            }
        }
        //再往头部插入点图层，尾部插入面图层
        for (int i = 0; i < mapEntity.getLayers().getCount(); i++) {
            Layer layer = mapEntity.getLayers().get(i);
            if((layer.getDataset().getType().equals(DatasetType.POINT) || layer.getDataset().getType().equals(DatasetType.POINT3D))&&layer.getName().indexOf("#1")<0) {
                lstSortedLayers.add(0, layer);
            }else if ((layer.getDataset().getType().equals(DatasetType.REGION) || layer.getDataset().getType().equals(DatasetType.REGION3D))&&layer.getName().indexOf("#1")<0) {
                lstSortedLayers.add(lstSortedLayers.size(), layer);
            }
        }
        //最后往头部插入注记图层
        for (int i = 0; i < mapEntity.getLayers().getCount(); i++) {
            Layer layer = mapEntity.getLayers().get(i);
            if(layer.getName().indexOf("#1")>-1){
                lstSortedLayers.add(0, layer);
            }
        }
        Map mapEntityNew = new Map(workspace);
        mapEntityNew.setPrjCoordSys(mapEntity.getPrjCoordSys());
        mapEntityNew.setName(mapEntity.getName());
        for(int i=0;i<lstSortedLayers.size();i++) {
            mapEntityNew.getLayers().add(lstSortedLayers.get(i));
        }
        mapEntityNew.setViewBounds(geoRectangle.getBounds());

        mapEntityNew.setLineAntialias(true);    //开启线型反走样
        mapEntityNew.setTextAntialias(true);    //开启文本反走样
        //显示压盖文本对象
        MapOverlapDisplayedOptions mapOverlapDisplayedOptions = new MapOverlapDisplayedOptions();
        mapOverlapDisplayedOptions.setAllowTextOverlap(true);
        mapEntityNew.setOverlapDisplayedOptions(mapOverlapDisplayedOptions);

/*      下面这个排序算法单独提出来执行没有问题，但是集成到这里运行就有问题
        for (int i = 0; i < mapEntity.getLayers().getCount(); i++) {
            Layer layer = mapEntity.getLayers().get(i);
            System.out.println(i+":"+layer.getCaption());
            if(layer.getCaption().equals("站场-面")||layer.getName().indexOf("station_R")>-1)
                System.out.println(layer.getName());
            if ((layer.getDataset().getType().equals(DatasetType.REGION) || layer.getDataset().getType().equals(DatasetType.REGION3D))&&layer.getName().indexOf("#1")<0) {
                int iLastLineOrPointOrTextPos = 0;
                for (int j = i + 1; j < mapEntity.getLayers().getCount(); j++) {
                    Layer layerTemp = mapEntity.getLayers().get(j);
                    Boolean blIsThemeLabel = false;
                    Theme theme = layerTemp.getTheme();
                    if(theme!=null&&theme.getType()!=null&&theme.getType().equals(ThemeType.LABEL)) {
                        blIsThemeLabel = true;
                    }
                    if (layerTemp.getDataset().getType().equals(DatasetType.LINE)
                            || layerTemp.getDataset().getType().equals(DatasetType.LINE3D)
                            || layerTemp.getDataset().getType().equals(DatasetType.POINT)
                            || layerTemp.getDataset().getType().equals(DatasetType.POINT3D)
                            || blIsThemeLabel == true) {
                        //如果下面的任意一个图层为点图层或注记图层。循环结束后iLastLineOrPointOrTextPos就是最后一个点图层或注记图层的位置。
                        iLastLineOrPointOrTextPos = j;
                    }
                }
                if(iLastLineOrPointOrTextPos>0) { //如果线图层下面最后一个点、注记的位置是iLastPointOrTextPos，则将线图层移到注记的位置是iLastPointOrTextPos
                    mapEntity.getLayers().moveTo(i, iLastLineOrPointOrTextPos);
                    continue;
                }
            }else if ((layer.getDataset().getType().equals(DatasetType.LINE) || layer.getDataset().getType().equals(DatasetType.LINE3D))&&layer.getName().indexOf("#1")<0) {
                int iLastPointOrTextPos = 0;
                for (int j = i + 1; j < mapEntity.getLayers().getCount(); j++) {
                    Layer layerTemp = mapEntity.getLayers().get(j);
                    //TODO 获取一下图层是否为标签专题图
                    Boolean blIsThemeLabel = false;
                    Theme theme = layerTemp.getTheme();
                    if(theme!=null&&theme.getType()!=null&&theme.getType().equals(ThemeType.LABEL)) {
                        blIsThemeLabel = true;
                    }
                    if (layerTemp.getDataset().getType().equals(DatasetType.POINT)
                            || layerTemp.getDataset().getType().equals(DatasetType.POINT3D)
                            || blIsThemeLabel == true) {
                        //如果下面的任意一个图层为点图层或注记图层。循环结束后iLastPointOrTextPos就是最后一个点图层或注记图层的位置。
                        iLastPointOrTextPos = j;
                    }
                }
                if(iLastPointOrTextPos>0) { //如果线图层下面最后一个点、注记的位置是iLastPointOrTextPos，则将线图层移到注记的位置是iLastPointOrTextPos
                    mapEntity.getLayers().moveTo(i, iLastPointOrTextPos);
                    continue;
                }
            }else if((layer.getDataset().getType().equals(DatasetType.POINT) || layer.getDataset().getType().equals(DatasetType.POINT3D))&&layer.getName().indexOf("#1")<0) {
                int iLastTextPos = 0;
                for(int j=i+1;j<mapEntity.getLayers().getCount();j++) {
                    Layer layerTemp = mapEntity.getLayers().get(j);
                    //TODO 获取一下图层是否为标签专题图
                    Boolean blIsThemeLabel = false;
                    Theme theme = layerTemp.getTheme();
                    if(theme==null||theme.getType()==null) {
                        continue;
                    }
                    if (theme.getType().equals(ThemeType.LABEL)) {
                        blIsThemeLabel = true;
                    }
                    if (blIsThemeLabel == true) {
                        //如果下面的任意一个图层为注记图层。循环结束后iLastTextPos就是最后一个点图层或注记图层的位置。
                        iLastTextPos = j;
                    }
                }
                if(iLastTextPos>0){
                    mapEntity.getLayers().moveTo(i, iLastTextPos);
                    continue;
                }
            }
        }*/
/*      对三维场景中的图层进行排序
        for(int i=0;i<sceneEntity.getLayers().getCount();i++){
            Layer3D layer3D = sceneEntity.getLayers().get(i);
            DatasetType datasetType = datasource.getDatasets().get(layer3D.getDataName()).getType();
            if(datasetType.equals(DatasetType.LINE3D)){
                int iLastPointOrTextPos = 0;
                for (int j = i + 1; j < sceneEntity.getLayers().getCount(); j++) {
                    Layer3D layerTemp = sceneEntity.getLayers().get(j);
                    Boolean blIsThemeLabel = false;
                    Theme3D theme = layerTemp.getTheme3D();
                    if (theme.getType().equals(ThemeType.LABEL))
                        blIsThemeLabel = true;
                    DatasetType datasetTypeTemp = datasource.getDatasets().get(layerTemp.getDataName()).getType();
                    if (datasetTypeTemp.equals(DatasetType.POINT)
                            || datasetTypeTemp.equals(DatasetType.POINT3D)
                            || blIsThemeLabel == true) {
                        //如果下面的任意一个图层为点图层或注记图层。循环结束后iLastPointOrTextPos就是最后一个点图层或注记图层的位置。
                        iLastPointOrTextPos = j;
                    }
                }
                if(iLastPointOrTextPos>0) { //如果线图层下面最后一个点、注记的位置是iLastPointOrTextPos，则将线图层移到注记的位置是iLastPointOrTextPos
                    sceneEntity.getLayers().moveTo(i, iLastPointOrTextPos);
                    continue;
                }
            }
        }*/
        strMsg = "对地图图层进行排序共耗时：";
        lTimeSpan = lEndTime-lBeginTime;
        lTime = lTimeSpan/1000l;
        if(lTime>60)
            strMsg += new Long(lTime/60).intValue()+"分钟";
        else
            strMsg += lTime+"秒";
        log.info(strMsg);

        //保存地图、场景及工作空间
        workspace.getMaps().remove(mapEntity.getName());
        workspace.getMaps().add(mapEntityNew.getName(), mapEntityNew.toXML());
        blResult = workspace.save();
        workspace.getScenes().remove(sceneEntity.getName());
        workspace.getScenes().add(sceneEntity.getName(), sceneEntity.toXML());

        blResult = workspace.save();
        datasource.close();
        workspace.close();
        return blResult;
    }

    @Async
    @Override
    public String reCreateWorkspaceSeedsUDBX(String entityTypeId, String strTaskId) {
        try {
            lock.lock();
            String strKey = strTaskId;
/*
        //提前把数据集注册表里的所有记录全部查询出来，防止后期遍历每类实体时频繁查库而带来的性能问题
        List<Datasetinfotem> lstDatasetInfo = iDatasetinfotemService.list();
        if (lstDatasetInfo == null || lstDatasetInfo.size() < 1) {
            _redisCommonUtil.setKeyAndTime(strKey, "没有找到注册的数据集信息", 10, TimeUnit.MINUTES);
            return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.HASNODATASETS.getName() + "\"}";
        }*/
            List<Entitytypestem> lstEntitytypeTems = iEntitytypestemService.list();
            if (lstEntitytypeTems == null || lstEntitytypeTems.size() < 1) {
                _redisCommonUtil.setKeyAndTime(strKey, "没有找到系统级实体信息", 10, TimeUnit.MINUTES);
                return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.HASNOENTITYTYPES.getName() + "\"}";
            }
            _redisCommonUtil.setKey("udbxtemplatelock", "true");
            String strMsg = "正在创建数据文件的结构";
            _redisCommonUtil.setKey(strKey, strMsg);
            String strPrjWSPath = PathUtils.getDesignPath();// + File.separator + projectId;
            File filePrjWSPath = new File(strPrjWSPath);
            if (!filePrjWSPath.exists())
                filePrjWSPath.mkdir();
            else if (filePrjWSPath.listFiles().length > 0) {
                //filePrjWSPath.deleteOnExit();
                //filePrjWSPath.mkdir();
            }
            Workspace workspace = new Workspace();
            Datasource datasource = workspace.getDatasources().get("EntitiesAchivments");
            if (datasource == null || workspace.getDatasources().getCount() < 1) {
                DatasourceConnectionInfo connectionInfo = new DatasourceConnectionInfo();
                connectionInfo.setEngineType(EngineType.UDBX);
                connectionInfo.setAlias("EntitiesAchivments");
                connectionInfo.setServer(filePrjWSPath + File.separator + "EntitiesAchivments.udbx");
                try {
                    datasource = workspace.getDatasources().open(connectionInfo);
                } catch (Exception e) {
                    datasource = workspace.getDatasources().create(connectionInfo);
                }
                if (datasource == null || !datasource.isOpened()) {
                    datasource = workspace.getDatasources().create(connectionInfo);
                    strMsg = "创建文件型数据源失败！";
                    _redisCommonUtil.setKeyAndTime(strKey, strMsg, 10, TimeUnit.MINUTES);
                    filePrjWSPath.delete();
                    return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.CREATEDATASOURCEFAILURE.getName() + "\"}";
                }
                //PrjCoordSys prjCoordSys = new PrjCoordSys(4528);
                //datasource.setPrjCoordSys(prjCoordSys);                     //按照李浩的要求，于2023-7-8 21:25增加该设置，从而让设计端先能出图
            }

            if (datasource.getDatasets().getCount() > 0) {
                if (entityTypeId != null && entityTypeId.trim().length() > 0) {

                } else {
                    while (datasource.getDatasets().getCount() > 0) {
                        datasource.getDatasets().delete(0);     //清空所有数据集
                    }
                }
            }
            //创建内存型数据源用来创建数据集和字段结构，从而节约执行时间
            //在内存型数据源中创建好规定格式的数据集后再批量拷贝到文件型和数据库型数据源中，从而提升了运行效率，节约了时间
            DatasourceConnectionInfo datasourceConnectionInfoMem = new DatasourceConnectionInfo();
            datasourceConnectionInfoMem.setServer(":memory:");
            datasourceConnectionInfoMem.setAlias("memory");
            Datasource datasourceMem = workspace.getDatasources().create(datasourceConnectionInfoMem);

            String strMainDatasetId = null;         //存储当前实体数据集对应的主数据集ID
            //String strMainDatasetName = null;       //存储当前实体数据集对应的主数据集名称
//        List<Datasetfieldtem> lstDatasetFields = iDatasetfieldtemService.list();    //一次性全查出来，后面通过条件过滤，避免反复与数据库交互，提升性能
            _redisCommonUtil.setKey(strKey, "正在生成文件型数据源结构");
            String strEntityTypeDatasetName = null;
            int iCreatedCount = 0;
            int iCopiedCount = 0;
            for (int i = 0; i < lstEntitytypeTems.size(); i++) {
                Entitytypestem entitytypestem = lstEntitytypeTems.get(i);
                String strEntityTypeTemId = entitytypestem.getPkid();
                if (entityTypeId != null && entityTypeId.trim().length() > 0) {
                    if (!entityTypeId.equalsIgnoreCase(strEntityTypeTemId))
                        continue;
                }
                QueryWrapper<Datasetinfotem> queryWrapperDatasetInfoTem = new QueryWrapper<Datasetinfotem>();
                queryWrapperDatasetInfoTem.eq("entity_type_id", strEntityTypeTemId);
                List<Datasetinfotem> lstDatasetInfo = iDatasetinfotemService.list(queryWrapperDatasetInfoTem);
//        if (lstDatasetInfo.size() > 0) {

/*            if (lstDatasetFields == null || lstDatasetFields.size() < 1) {
                strMsg = "没有获取到字段注册信息";
                _redisCommonUtil.setKeyAndTime(strKey, strMsg, 10, TimeUnit.MINUTES);
                return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.HASNOFIELDS.getName() + "\"}";
            }*/
                if (lstDatasetInfo == null || lstDatasetInfo.size() < 1) {
                    strMsg = "没有获取到系统级数据集信息！";
                    _redisCommonUtil.setKeyAndTime(strKey, strMsg, 10, TimeUnit.MINUTES);
                    return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.HASNODATASETS.getName() + "\"}";
                }

                long lBeginTime = System.currentTimeMillis();           //获取创建数据集及字段执行开始时间(用于计算功能执行耗时)
                for (int j = 0; j < lstDatasetInfo.size(); j++) {
                    Datasetinfotem datasetinfo = lstDatasetInfo.get(j);
                    if (datasetinfo.getDatasettype().equals(DatasetType.TABULAR.value())) {
                        continue;
                    }
                    //if(datasetinfo.getPkid().equalsIgnoreCase(""))
                    if (entityTypeId != null && entityTypeId.length() > 0) {
                        if (!datasetinfo.getEntitytypeid().equalsIgnoreCase(entityTypeId))
                            continue;
                        strEntityTypeDatasetName = datasetinfo.getTablename();  //能走到这一步，说明就是传入实体类型对应的数据集
                        strMsg = "正在为实体“" + entitytypestem.getEntitytypealias() + "”创建表结构。";
                    } else
                        strMsg = "正在为实体“" + entitytypestem.getEntitytypealias() + "”创建表结构。还剩" + (lstEntitytypeTems.size() - 1 - i) + "个实体未创建完毕。";
                    _redisCommonUtil.setKey(strKey, strMsg);
                    DatasetVectorInfo datasetVectorInfo = new DatasetVectorInfo();
                    DatasetVectorInfo datasetVectorMInfo = new DatasetVectorInfo();
                    String strTableName = datasetinfo.getTablename();
                    if (strTableName.indexOf("-") > -1) {
                        continue;
/* 对于名称中含有“-”的数据集名称，先做跳过处理，暂时不自动处理。因为都是测试用的实体
                    strMsg = "数据集"+ strTableName + "名称中含有非法字符“-”，改为使用名称";
                    strTableName = strTableName.replaceAll("-", "_");
                    strMsg += strTableName + "创建数据集。";
                    logger.error(strMsg);
 */
                    }
                    try {
                        datasetVectorInfo.setName(strTableName);
                        datasetVectorMInfo.setName(strTableName);
                    } catch (Exception e) {
                        //System.out.println("pkid="+datasetinfo.getPkid()+",datasetname="+datasetinfo.getDatasetname());
                    }
                /*if (datasetinfo.getDatasettype().equals(EntityDatasetType.TABULAR.getValue()))
                    datasetVectorInfo.setType(DatasetType.TABULAR);
                else */
                    if (datasetinfo.getDatasettype().equals(EntityDatasetType.POINT3D.getValue())) {
                        datasetVectorInfo.setType(DatasetType.POINT3D);
                    } else if (datasetinfo.getDatasettype().equals(EntityDatasetType.LINE3D.getValue())) {
                        datasetVectorInfo.setType(DatasetType.LINE3D);
                    } else if (datasetinfo.getDatasettype().equals(EntityDatasetType.POLYGON3D.getValue())) {
                        datasetVectorInfo.setType(DatasetType.REGION3D);
                    } else if (datasetinfo.getDatasettype().equals(EntityDatasetType.MODEL.getValue())) {
                        datasetVectorMInfo.setType(DatasetType.MODEL);
                    } else if (datasetinfo.getDatasettype().equals(EntityDatasetType.CAD.getValue())) {
                        datasetVectorInfo.setType(DatasetType.CAD);
                    }
                    //创建三维矢量数据集
                    DatasetVector datasetVector = null;
                    DatasetVector datasetVectorM = null;
                    if (!datasetVectorInfo.getType().equals(DatasetType.POINT)) {     //当没有给datasetVectorInfo设置数据集类型时，默认会为POINT类型，此时就不该被创建了
                        if (!datasourceMem.getDatasets().contains(datasetVectorInfo.getName()))
                            datasetVector = datasourceMem.getDatasets().create(datasetVectorInfo);
                    }
                    if (datasetVectorMInfo.getType().equals(DatasetType.MODEL)) {
                        //创建三维模型数据集
                        if (!datasourceMem.getDatasets().contains(datasetVectorMInfo.getName()))
                            datasetVectorM = datasourceMem.getDatasets().create(datasetVectorMInfo);
                    }
                    if (!datasourceMem.getDatasets().contains(strTableName)) {
                        strMsg = "创建数据集" + strTableName + "失败！";
                        _redisCommonUtil.setKeyAndTime(strKey, strMsg, 10, TimeUnit.MINUTES);
                        log.error(strMsg);
                        continue;
                    }
/*                if (datasetVector == null && datasetVectorM == null) {
                    strMsg = "创建数据集" + datasetinfo.getTablename() + "失败！";
                    _redisCommonUtil.setKey(strKey, strMsg);
                    //System.out.println("创建数据集" + datasetinfo.getTablename() + "失败！");
                    logger.error(strMsg);
                    continue;
                }*/
                    iCreatedCount++;
                    /**
                     * 为数据集创建字段。操作步骤如下：
                     * 第一步：先获取其对应的属性数据集
                     * 第二步：将属性数据集注册的所有字段添加到当前数据集里
                     * 第三步：创建当前数据集注册的自有字段
                     */
                    //第一步：先找到当前数据集对应的属性数据集，拿到属性数据集的PKID和数据集名称
                    if (!datasetinfo.getDatasettype().equals(EntityDatasetType.TABULAR.getValue())) {
                        for (int k = 0; k < lstDatasetInfo.size(); k++) {
                            Datasetinfotem datasetinfoTemp = lstDatasetInfo.get(k);
                            if (datasetinfoTemp.getEntitytypeid() != null && datasetinfoTemp.getEntitytypeid().equalsIgnoreCase(datasetinfo.getEntitytypeid())) { //如果游标数据集记录中的实体类型与当前数据集对应的实体类型一致，说明是同一个实体类型的不同数据集
                                String strDatasetName = datasetinfoTemp.getTablename();
                                if (strDatasetName == null || strDatasetName.trim().length() < 1) {
                                    continue;
                                }
                                strDatasetName = strDatasetName.toUpperCase();
                                if ((!strDatasetName.endsWith("_P")) && (!strDatasetName.endsWith("_L")) && (!strDatasetName.endsWith("_R"))
                                        && (!strDatasetName.endsWith("_M"))) {
                                    strMainDatasetId = datasetinfoTemp.getPkid();
                                    //strMainDatasetName = datasetinfoTemp.getTablename();
                                    break;
                                }
                            }
                        }
                    }
                    if (null == strMainDatasetId && datasetinfo.getDatasettype().equals(EntityDatasetType.CAD.getValue())) {
                        //对于纯CAD交互实体来说，主数据集就是其自身
                        strMainDatasetId = datasetinfo.getPkid();
                    }
                    /**
                     * 第二步：再去字段注册表中过滤出属性数据集PKID对应的所有字段信息
                     *       并将当前数据集的PKID放到lstDatasetFields列表里对应位置元素的descinfo字段里
                     */
/*                for (int j = 0; j < lstDatasetFields.size(); j++) {
                    Datasetfieldtem datasetfield = lstDatasetFields.get(j);
                    if (datasetfield.getDatasetid()!=null && datasetfield.getDatasetid().equalsIgnoreCase(strMainDatasetId)) {
                        datasetfield.setDescinfo(datasetinfo.getPkid());
                    }
                }*/
                    /**
                     * 第三步：遍历当前数据集对应的lstDatasetFields，然后创建对应的字段
                     *       与当前数据集匹配的条件有两种情况：
                     *          (1)要么其datasetid为当前datasetInfo的pkid，表示就是点线面数据集自己特有的字段
                     *          (2)要么其descinfo为当前datasetInfo的pkid，表示其为所属属性数据集中的公共字段
                     */
                    QueryWrapper<Datasetfieldtem> queryWrapperField = new QueryWrapper<Datasetfieldtem>();
                    queryWrapperField.eq("entity_type_id", strEntityTypeTemId);
                    queryWrapperField.eq("data_set_id", strMainDatasetId);
                    queryWrapperField.orderByAsc("order_index");
                    List<Datasetfieldtem> lstDatasetFields = iDatasetfieldtemService.list(queryWrapperField);
                    if (lstDatasetFields == null || lstDatasetFields.size() < 1)
                        continue;
                    for (int k = 0; k < lstDatasetFields.size(); k++) {
                        Datasetfieldtem datasetfield = lstDatasetFields.get(k);
//                    if (null!=datasetfield.getEntitytypeid()&&//datasetfield.getDatasetid().equalsIgnoreCase(strMainDatasetId) &&
//                            datasetfield.getEntitytypeid().equalsIgnoreCase(datasetinfo.getEntitytypeid())) {
                        if (datasetfield.getFieldname() == null || datasetfield.getFieldname().trim().length() < 1 ||
                                datasetfield.getFieldname().length() >= 30) {   //如果字段长度超过30个字符就跳过不予创建，否则创建字段时会报错
                            continue;
                        }
                        if (datasetVector != null && datasetVector.getFieldInfos().get(datasetfield.getFieldname()) != null)
                            continue;       //如果数据集中已经有这个字段了，就不再添加了
                        if (datasetVectorM != null && datasetVectorM.getFieldInfos().get(datasetfield.getFieldname()) != null)
                            continue;       //如果数据集中已经有这个字段了，就不再添加了
                        FieldInfo fieldInfo = new FieldInfo();
                        try {
                            fieldInfo.setName(datasetfield.getFieldname());
                        } catch (Exception fe) {
                            //System.out.println("为字段设置名称"+datasetfield.getFieldname()+"失败！");
                            continue;
                        }
                        if (datasetfield.getFieldalias() != null && datasetfield.getFieldalias().trim().length() > 0) {
                            if (StringUtils.isNotEmpty(datasetfield.getUnits())) { // 判断实体属性是否带有单位，如有数据集别名加上单位
//                                fieldInfo.setCaption(datasetfield.getFieldalias().trim() + "(" + datasetfield.getUnits().trim() + ")"); // 2024-3-26修改 先不带上单位
                                fieldInfo.setCaption(datasetfield.getFieldalias().trim());
                            } else {
                                fieldInfo.setCaption(datasetfield.getFieldalias().trim());
                            }
                        }
                        if (datasetfield.getFieldtype().length() > 0) {
                            if (datasetfield.getFieldtype().equalsIgnoreCase("字符串")) {
                                fieldInfo.setType(FieldType.TEXT);
                            } else if (datasetfield.getFieldtype().equalsIgnoreCase("数值")) {
                                fieldInfo.setType(FieldType.SINGLE);
                            } else if (datasetfield.getFieldtype().equalsIgnoreCase("长文本")) {
                                fieldInfo.setType(FieldType.LONGBINARY);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.INTEGER.getValue()) {
                                fieldInfo.setType(FieldType.INT32);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.FLOAT.getValue()) {
                                fieldInfo.setType(FieldType.SINGLE);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.DOUBLE.getValue()) {
                                fieldInfo.setType(FieldType.DOUBLE);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.STRING.getValue()) {
                                fieldInfo.setType(FieldType.TEXT);
                                if (datasetfield.getFieldsize() != null && datasetfield.getFieldsize() > 0)
                                    fieldInfo.setMaxLength(datasetfield.getFieldsize());
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.DATE.getValue()) {
                                fieldInfo.setType(FieldType.DATE);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.BYTEA.getValue()) {
                                fieldInfo.setType(FieldType.LONGBINARY);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.CLOB.getValue()) {
                                fieldInfo.setType(FieldType.LONGBINARY);
                                if (datasetfield.getFieldsize() != null && datasetfield.getFieldsize() > 0)
                                    fieldInfo.setMaxLength(datasetfield.getFieldsize());
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.BOOLEAN.getValue()) {
                                fieldInfo.setType(FieldType.BOOLEAN);
                            }
                        }
                        //创建字段
                        if (datasetVector != null) {
                            try {
                                if (datasetVector.getFieldInfos().get(fieldInfo.getName()) == null)
                                    datasetVector.getFieldInfos().add(fieldInfo);
                            } catch (Exception e) {
                                log.error("为数据集" + datasetVector.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。");
                                fieldInfo.setName(fieldInfo.getName() + "_");   //如果要创建的是系统字段，则在字段名后面追加一个“_”，这样就能创建了
                                try {
                                    datasetVector.getFieldInfos().add(fieldInfo);
                                } catch (Exception e1) {
                                }
                            }
                        }
                        if (datasetVectorM != null) {
                            try {
                                if (datasetVectorM.getFieldInfos().get(fieldInfo.getName()) == null)
                                    datasetVectorM.getFieldInfos().add(fieldInfo);
                            } catch (Exception e) {
                                log.error("为数据集" + datasetVectorM.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。",e);
                                fieldInfo.setName(fieldInfo.getName() + "_");   //如果要创建的是系统字段，则在字段名后面追加一个“_”，这样就能创建了
                                try {
                                    datasetVectorM.getFieldInfos().add(fieldInfo);
                                } catch (Exception e1) {
                                    log.error("为数据集" + datasetVectorM.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。",e1);
                                    fieldInfo.setName(fieldInfo.getName() + "_");
                                }
                            }
                        }
//                    }
                    }
                }
                long lEndTime = System.currentTimeMillis();             //获取创建数据集及字段执行结束时间(用于计算功能执行耗时)
                strMsg = "生成内存型数据集共耗时：";
                Long lTimeSpan = lEndTime - lBeginTime;
                Long lTime = lTimeSpan / 1000l;                           //计算创建数据集及字段执行耗时
                if (lTime > 60)
                    strMsg += new Long(lTime / 60).intValue() + "分钟";
                else
                    strMsg += lTime + "秒";
                log.info(strMsg);
            }
            /*----------------添加其他独立数据集--------------*/
            //从模板Excel中读取数据集模板创建相应的数据集，从而可以支持版本化创建
            //属性数据集模板文件的路径为sqlite/template/tabulars.xlsx
            String strTabularTemplatesPath = PathUtils.getTemplatePath() + File.separator + "tabulars.xlsx";
            File fileTabularTemplates = new File(strTabularTemplatesPath);
            if (fileTabularTemplates.exists()) {
                InputStream in = FileUtil.getInputStream(fileTabularTemplates);
                java.util.Map<String, java.util.Map<Integer, List<String>>> mapTabularInfos = null;
                try {
                    mapTabularInfos = ExcelUtils.getExcel(in, 7, strTabularTemplatesPath);
                } catch (Exception e) {
                }
                if (mapTabularInfos != null && !mapTabularInfos.isEmpty()) {
                    Iterator<String> iterator = mapTabularInfos.keySet().iterator();
                    while (iterator.hasNext()) {
                        String strSheetName = iterator.next();        //拿到每个sheet的名字
                        String strDatasetName = strSheetName;
                        String strDatasetAlias = strSheetName;
                        if (strSheetName.indexOf('<') > 0 && strSheetName.indexOf('>') > 0) {
                            strDatasetAlias = strDatasetName.substring(strDatasetName.indexOf('<') + 1, strDatasetName.indexOf('>'));
                            strDatasetName = strDatasetName.substring(0, strDatasetName.indexOf('<'));
                        }
                        if (datasourceMem.getDatasets().contains(strDatasetName)) {       //如果udbx文件中已经有同名数据集了，就将已存在的数据集复制一份作为历史版本数据，然后再删掉后重新创建数据集
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(new Date());
                            int year = calendar.get(Calendar.YEAR);
                            int day = calendar.get(Calendar.DAY_OF_MONTH);//获取当前天数
                            int time = calendar.get(Calendar.HOUR_OF_DAY);   //获取当前小时
                            int min = calendar.get(Calendar.MINUTE);  //获取当前分钟
                            int sec = calendar.get(Calendar.SECOND);  //获取当前秒
                            String strNewDatasetName = strDatasetName + "-" + year + (day < 10 ? "0" + day : day) + (time < 10 ? "0" + time : time) + (sec < 10 ? "0" + sec : sec);
                            datasourceMem.copyDataset(datasourceMem.getDatasets().get(strDatasetName), strNewDatasetName, datasourceMem.getDatasets().get(strDatasetName).getEncodeType());
                            datasourceMem.getDatasets().delete(strDatasetName);
                        }
                        DatasetVectorInfo datasetTabularInfo = new DatasetVectorInfo();
                        datasetTabularInfo.setType(DatasetType.TABULAR);
                        datasetTabularInfo.setName(strDatasetName);
                        DatasetVector datasetTabular = null;
                        try {
                            datasetTabular = datasourceMem.getDatasets().create(datasetTabularInfo);
                        } catch (Exception e1) {
                            datasetTabularInfo.setName(strDatasetName + "_");
                            try {
                                datasetTabular = datasourceMem.getDatasets().create(datasetTabularInfo);
                            } catch (Exception e2) {
                            }
                        }
                        if (datasetTabular != null) {
                            java.util.Map<Integer, List<String>> mapRowDatas = mapTabularInfos.get(strSheetName);
                            for (int j = 1; j <= mapRowDatas.keySet().size(); j++) {
                                List<String> lstRowDatas = mapRowDatas.get(j);
                                FieldInfo fieldInfo = new FieldInfo();
                                fieldInfo.setName(lstRowDatas.get(1));
                                fieldInfo.setCaption(lstRowDatas.get(2));
                                String strFieldType = lstRowDatas.get(3);
                                switch (strFieldType) {
                                    case "单精度型":
                                        fieldInfo.setType(FieldType.SINGLE);
                                        if (lstRowDatas.get(4) != null && lstRowDatas.get(4).trim().length() > 0) {
                                            try {
                                                fieldInfo.setMaxLength(Integer.parseInt(lstRowDatas.get(4)));
                                            } catch (Exception e) {
                                            }
                                        }
                                        break;
                                    case "双精度型":
                                        fieldInfo.setType(FieldType.DOUBLE);
                                        if (lstRowDatas.get(4) != null && lstRowDatas.get(4).trim().length() > 0) {
                                            try {
                                                fieldInfo.setMaxLength(Integer.parseInt(lstRowDatas.get(4)));
                                            } catch (Exception e) {
                                            }
                                        }
                                        break;
                                    case "整型":
                                        if (Integer.parseInt(lstRowDatas.get(4)) < 3)
                                            fieldInfo.setType(FieldType.INT16);
                                        else if (Integer.parseInt(lstRowDatas.get(4)) < 5)
                                            fieldInfo.setType(FieldType.INT32);
                                        else
                                            fieldInfo.setType(FieldType.INT64);
                                        break;
                                    case "日期型":
                                        fieldInfo.setType(FieldType.DATETIME);
                                        break;
                                    case "文本型":
                                        fieldInfo.setType(FieldType.TEXT);
                                        try {
                                            fieldInfo.setMaxLength(Integer.parseInt(lstRowDatas.get(4)));
                                        } catch (Exception e) {
                                            fieldInfo.setMaxLength(50);
                                        }
                                        break;
                                    case "字节型":
                                        fieldInfo.setType(FieldType.BYTE);
                                        break;
                                    case "二进制型":
                                    case "长文本型":
                                        fieldInfo.setType(FieldType.LONGBINARY);
                                        break;
                                }
                                try {
                                    datasetTabular.getFieldInfos().add(fieldInfo);
                                } catch (Exception e1) {
                                    log.error("为数据集" + datasetTabular.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。",e1);
                                    fieldInfo.setName(fieldInfo.getName() + "_");
                                    try {
                                        datasetTabular.getFieldInfos().add(fieldInfo);
                                    } catch (Exception e2) {
                                        log.error("为数据集" + datasetTabular.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。",e2);

                                    }
                                }
                            }
                        }
                    }
                }
                try {
                    in.close();
                } catch (IOException e) {
                }
            }

            _redisCommonUtil.setKey(strKey, "正在复制数据到本地数据文件");
            //将内存型数据源datasourceMem中的数据集批量复制到UDBx型数据源datasource中
            Long lBeginTime = System.currentTimeMillis();
            for (int i = 0; i < datasourceMem.getDatasets().getCount(); i++) {
                Dataset datasetSrc = datasourceMem.getDatasets().get(i);
                try {
                    _redisCommonUtil.setKey(strKey, "正在复制数据集" + datasetSrc.getName() + "。还剩" + (datasourceMem.getDatasets().getCount() - 1 - i) + "个数据集未复制。");
                    if (entityTypeId != null && entityTypeId.trim().length() > 0 && datasource.getDatasets().contains(datasetSrc.getName())) {
                        datasource.getDatasets().delete(datasetSrc.getName());  //如果是传入实体对应的数据集，则先删除udbx中原有的数据集
                    }
                    Dataset datasetCopied = datasource.copyDataset(datasetSrc, datasetSrc.getName(), datasetSrc.getEncodeType());
                    if (null != datasetCopied)
                        iCopiedCount++;
                } catch (Exception e) {
                    log.error("复制数据集" + datasetSrc.getName() + "到UDBX数据源中失败！，原因如下：" + e.getMessage(),e);
                }
            }
            datasourceMem.close();  //用完内存型数据源后将其关闭掉
            workspace.close();
            strMsg = "从内存型数据源向UDBX数据源拷贝数据共耗时：";
            Long lEndTime = System.currentTimeMillis();
            Long lTimeSpan = lEndTime - lBeginTime;
            Long lTime = lTimeSpan / 1000l;                           //计算创建数据集及字段执行耗时
            if (lTime > 60)
                strMsg += new Long(lTime / 60).intValue() + "分钟";
            else
                strMsg += lTime + "秒";
            _redisCommonUtil.setKey(strKey, strMsg);
            _redisCommonUtil.setKeyAndTime(strKey, "UDBX模板文件更新完毕。", 6, TimeUnit.HOURS);
            _redisCommonUtil.delete("udbxtemplatelock");
            if (_redisCommonUtil != null) {
                return "{\"result\":\"success\"}";
            }else{
                return "{\"result\":\"failure\"}";
            }

        } finally {
            lock.unlock();
        }
    }

    @Override
    public String createProjectDB(String projectid, String desginId, String strTaskKey,String filePath) {
        try {
            //不允许并发
            lock.lock();
            long start = System.currentTimeMillis();
            StringBuilder resultSB = new StringBuilder();

            log.info("开始创建设计段文件，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId);
            _redisCommonUtil.setKey(strTaskKey, "开始生成空间数据。");
            try {
                //用db-design下的EntitiesAchivments.udbx覆盖种子工作空间下自带的同名文件，否则一旦执行了重刷udbx，项目下载的依然是旧的udbx
                File udbxFile = new File(PathUtils.getDesignPath() + File.separator + projectid + File.separator + "EntitiesAchivments.udbx");
                if (!udbxFile.exists()) {
                    log.info("EntitiesAchivments.udbx重新创建开始，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId);
                    //定时检测
                    String resultJson = reCreateWorkspaceSeedsUDBXFromProject(null, strTaskKey, projectid);

                    cn.hutool.json.JSONObject jsonObjectResult = new JSONObject(resultJson);
                    if(jsonObjectResult.get("message")!=null){
                        resultSB.append(jsonObjectResult.get("message"));
                    }
                    if(!resultJson.contains("success")){
                        log.error("EntitiesAchivments.udbx重新创建失败，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId);
                        throw new BusinessException("生成空间数据失败，请稍后！");
                    }
                    log.info("EntitiesAchivments.udbx重新创建完成，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId);
                }else{
                    log.info("EntitiesAchivments.udbx已存在，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId);
                }
                _redisCommonUtil.setKey(strTaskKey, "空间数据生成完成。");
            } catch (Exception e) {
                //e.printStackTrace();
                log.error("生成空间数据失败。请重试。", e);
                resultSB.append("生成空间数据失败。请重试。");
                return resultSB.toString();
                //throw new BusinessException("生成空间数据失败，请稍后！");
            }

            //15s
            log.info("EntitiesAchivments.udbx,耗时:" + (System.currentTimeMillis() - start) / 1000.0 + "s");
            start = System.currentTimeMillis();

            try{
                //先去db-design/<项目ID>下看是否已经生成了项目对应的工作空间
                String strDBDesignProjectPath = PathUtils.getDesignPath() + File.separator + projectid;
                if (!new File(strDBDesignProjectPath).exists()) {
                    new File(strDBDesignProjectPath).mkdir();
                }
                File fileWS = new File(strDBDesignProjectPath + File.separator + "EntityDesign.sxwu");
                if (!fileWS.exists()) {      //如果“db-design/<项目ID>”目录下没有生成工作空间文件，则先生成该文件
                    // 当并发量再高的时候，单独的外部程序无法支持高并发运行时，可以将下面这段代码放到同步锁中实现，只要在
                    // 当前服务中启动的一个外部进程数量超过了阈值，就需要等待该次进程执行完毕后才能启动新的进程
                    String message = buildProjectWorkspace(projectid, strTaskKey);
                    if(StringUtils.isNotEmpty(message)){
                        resultSB.append(message);
                    }
                }

                log.info("工作空间生成完毕,耗时:" + (System.currentTimeMillis() - start) / 1000.0 + "s");
                start = System.currentTimeMillis();

                FileGenerateUtils.copyDir(strDBDesignProjectPath, filePath);   //从“db-design/<项目ID>”目录下直接将生成好的工作空间文件拷贝到filePath下，从而免去了每次都需要重新生成工作空间的操作，节约了大量执行时间
                //先将tabular.xlsx中的表创建到db-design下的entitiesachivments.db里
                //然后再将db-design下的entitiesachivments.db复制到要打包和下载的路径下
                File dbFile = new File(PathUtils.getDesignPath() + File.separator + projectid + File.separator + "entitiesachivments.db");
                if (!dbFile.exists()) {
                    log.info("entitiesachivments.db创建，开始,taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId);

                    reCreateEntityDB(null, projectid);
                }else{
                    log.info("entitiesachivments.db检测，开始,taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId);

                    // 检查db里的表和字段是否和元数据一致
                    checkDbTablesFields(dbFile.getPath(), projectid);
                }

                log.info("EntitiesAchivments.db,耗时:" + (System.currentTimeMillis() - start) / 1000.0 + "s");
                start = System.currentTimeMillis();

                FileUtils.copyFile(PathUtils.getDesignPath() + File.separator + projectid + File.separator + "entitiesachivments.db", filePath, true);
                FileUtils.copyFile(PathUtils.getDesignPath() + File.separator + projectid + File.separator + "EntitiesAchivments.udbx", filePath, true);

            }catch (Exception e){
                log.error("createProjectDB异常,taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,desginId,e);
                _redisCommonUtil.setKey(strTaskKey, "正在刷新数据文件模板，请稍后......");
                throw new BusinessException("正在刷新数据文件模板，请稍后！");
            }
            return resultSB.toString();
        } finally {
            lock.unlock();
        }
    }

    private String buildProjectWorkspace(String projectid, String strTaskKey){
        String message = null;
        try {
            log.info("EntityDesign.sxwu创建开始，taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,"");

            _redisCommonUtil.setKey(strTaskKey, "正在生成工作空间");
//                    workspaceSeedsService.loadNewWorkspace(projectid, desginId, filePath, strExecuteTime);
            if (null == strWorkspaceGenerationMethod || strWorkspaceGenerationMethod.trim().length() < 1 ||
                    strWorkspaceGenerationMethod.equalsIgnoreCase("inside")) {
                //workseed.generationmethod配置的是inside，则在服务内部生成工作空间。该方式在高并发环境下会报错
                rebuildProjectWorkspaceSeed(projectid);
            } else if (strWorkspaceGenerationMethod.equalsIgnoreCase("outside")) {
                //workseed.generationmethod配置的是outside，则启动外部的进程来生成工作空间。该方式在高并发环境下的稳定性更好
                java.util.Map<String, Object> mapResult = rebuildProjectWorkspaceSeedDistributed(projectid, strTaskKey);

                if(mapResult!=null&&mapResult.containsKey("message")){
                    message = mapResult.get("message").toString();
                }

                String strMsg = _redisCommonUtil.getKey(projectid);
                if (null != strMsg) {
                    try {
                        cn.hutool.json.JSONObject jsonObjectResult = new JSONObject(strMsg);
                        if (null!=jsonObjectResult.getStr("result")&&jsonObjectResult.getStr("result").equalsIgnoreCase("success")) {
                            log.info("外部jar已为项目" + projectid + "成功生成工作空间！");
                        }
                    } catch (Exception e) {
                        log.error("EntityDesign.sxwu创建，解析返回结果异常,taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,"",e);
                    }
                    _redisCommonUtil.delete(projectid);
                }else{
                    log.info("外部jar未能为项目" + projectid + "生成工作空间！");
                    _redisCommonUtil.delete(projectid);
                }
            }
/*                    String strWSVersion = "{\"version\":\"" + JavaUtils.getDateTimeSSS() + "\"}";
                    cn.hutool.core.io.file.FileWriter writer = new FileWriter(filePath + File.separator + "version.json");
                    writer.setCharset(Charset.forName("UTF-8"));
                    writer.write(strWSVersion);*/
            _redisCommonUtil.setKey(strTaskKey, "工作空间生成完毕");

        } catch (Exception e) {
            log.error("EntityDesign.sxwu创建，异常,taskkey:{}，projectid:{}，desginid:{}",strTaskKey,projectid,"",e);
            throw new BusinessException("工作空间失败，稍后请重试！");
        }finally {
            return message;
        }
    }

    @Async
    @Override
    public String reCreateWorkspaceSeedsUDBXFromProject(String entityTypeId, String strTaskId, String projectId) {
        List<String> resultList = Collections.synchronizedList(new ArrayList<>());
        Workspace workspace = null;
        try {
            lock.lock();
            String strKey = strTaskId;
        /*
        //提前把数据集注册表里的所有记录全部查询出来，防止后期遍历每类实体时频繁查库而带来的性能问题
        List<Datasetinfotem> lstDatasetInfo = iDatasetinfotemService.list();
        if (lstDatasetInfo == null || lstDatasetInfo.size() < 1) {
            _redisCommonUtil.setKeyAndTime(strKey, "没有找到注册的数据集信息", 10, TimeUnit.MINUTES);
            return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.HASNODATASETS.getName() + "\"}";
        }*/
            // 获取到项目级的实体类型
            QueryWrapper<Entitytypes> entitytypesQueryWrapper = new QueryWrapper<>();
            entitytypesQueryWrapper.eq("project_id", projectId);
            List<Entitytypes> lstEntitytypes = iEntitytypesService.list(entitytypesQueryWrapper);
            if (lstEntitytypes == null || lstEntitytypes.size() < 1) {
                log.error("EntitiesAchivments.udbx重新创建中，实体信息为空，taskkey:{}，projectid:{}",strTaskId,projectId);
                _redisCommonUtil.setKeyAndTime(strKey, "没有找到项目级实体信息", 10, TimeUnit.MINUTES);
                resultList.add(String.format("EntitiesAchivments.udbx重新创建中，实体信息为空，taskkey:%s，projectid:%s",strTaskId,projectId));
                return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.HASNOENTITYTYPES.getName()+"\",\"message\":\"" + String.join(";",resultList) + "\"}";
            }
            log.info("EntitiesAchivments.udbx重新创建中，实体总数：{}，taskkey:{}，projectid:{}",lstEntitytypes.size(),strTaskId,projectId);
            //_redisCommonUtil.setKey("udbxtemplatelock" + projectId, "true");
            String strMsg = "正在创建数据文件的结构";
            _redisCommonUtil.setKey(strKey, strMsg);
            String strPrjWSPath = PathUtils.getDesignPath() + File.separator + projectId;
            File filePrjWSPath = new File(strPrjWSPath);
            if (!filePrjWSPath.exists())
                filePrjWSPath.mkdir();
            else if (filePrjWSPath.listFiles().length > 0) {
                //filePrjWSPath.deleteOnExit();
                //filePrjWSPath.mkdir();
            }
            workspace = new Workspace();
            Datasource datasource = workspace.getDatasources().get("EntitiesAchivments");
            if (datasource == null || workspace.getDatasources().getCount() < 1) {
                DatasourceConnectionInfo connectionInfo = new DatasourceConnectionInfo();
                connectionInfo.setEngineType(EngineType.UDBX);
                connectionInfo.setAlias("EntitiesAchivments");
                connectionInfo.setServer(filePrjWSPath + File.separator + "EntitiesAchivments.udbx");
                try {
                    datasource = workspace.getDatasources().open(connectionInfo);
                } catch (Exception e) {
                    log.error("EntitiesAchivments.udbx重新创建中，taskkey:{}，projectid:{}",lstEntitytypes.size(),strTaskId,projectId,e);
                    datasource = workspace.getDatasources().create(connectionInfo);
                }
                if (datasource == null || !datasource.isOpened()) {
                    datasource = workspace.getDatasources().create(connectionInfo);
                    strMsg = "创建文件型数据源失败！";
                    _redisCommonUtil.setKeyAndTime(strKey, strMsg, 10, TimeUnit.MINUTES);
                    filePrjWSPath.delete();
                    log.error("EntitiesAchivments.udbx重新创建中，udbx创建失败，taskkey:{}，projectid:{}",lstEntitytypes.size(),strTaskId,projectId);

                    resultList.add(String.format("EntitiesAchivments.udbx重新创建中，udbx创建失败，taskkey:%s，projectid:%s",lstEntitytypes.size(),strTaskId,projectId));
                    return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.CREATEDATASOURCEFAILURE.getName()+",\"message\":\"" + String.join(";",resultList) + "\"}";
                }
                //PrjCoordSys prjCoordSys = new PrjCoordSys(4528);
                //datasource.setPrjCoordSys(prjCoordSys);                     //按照李浩的要求，于2023-7-8 21:25增加该设置，从而让设计端先能出图
            }

            if (datasource.getDatasets().getCount() > 0) {
                if (entityTypeId != null && entityTypeId.trim().length() > 0) {

                } else {
                    while (datasource.getDatasets().getCount() > 0) {
                        datasource.getDatasets().delete(0);     //清空所有数据集
                    }
                }
            }
            //创建内存型数据源用来创建数据集和字段结构，从而节约执行时间
            //在内存型数据源中创建好规定格式的数据集后再批量拷贝到文件型和数据库型数据源中，从而提升了运行效率，节约了时间
            DatasourceConnectionInfo datasourceConnectionInfoMem = new DatasourceConnectionInfo();
            datasourceConnectionInfoMem.setServer(":memory:");
            datasourceConnectionInfoMem.setAlias("memory");
            //Datasource datasourceMem = workspace.getDatasources().create(datasourceConnectionInfoMem);
            Datasource datasourceFinal = workspace.getDatasources().create(datasourceConnectionInfoMem);
            //Datasource datasourceFinal = datasource;

            //String strMainDatasetId = null;         //存储当前实体数据集对应的主数据集ID
            //String strMainDatasetName = null;       //存储当前实体数据集对应的主数据集名称
//        List<Datasetfieldtem> lstDatasetFields = iDatasetfieldtemService.list();    //一次性全查出来，后面通过条件过滤，避免反复与数据库交互，提升性能
            _redisCommonUtil.setKey(strKey, "正在生成文件型数据源结构");

            // 开始时间
            long buildStart = System.currentTimeMillis();

            // 为了加快速度，先查出所有数据，再循环判断，减少循环里查询数据库的次数
            // 查询项目下的所有数据集信息
            QueryWrapper<Datasetinfo> queryWrapperDatasetInfo = new QueryWrapper<Datasetinfo>();
            queryWrapperDatasetInfo.eq("project_id", projectId);
            List<Datasetinfo> lstDatasetInfo = iDatasetinfoService.list(queryWrapperDatasetInfo);

            log.info("EntitiesAchivments.udbx重新创建中，DatasetInfo总数：{}，taskkey:{}，projectid:{}",lstDatasetInfo.size(),strTaskId,projectId);

            // 查询项目下的所有字段信息
            QueryWrapper<Datasetfield> queryWrapperField = new QueryWrapper<Datasetfield>();
            queryWrapperField.eq("project_id", projectId);
            queryWrapperField.orderByAsc("order_index");
            List<Datasetfield> lstDatasetFields = iDatasetfieldService.list(queryWrapperField);

            log.info("EntitiesAchivments.udbx重新创建中，Datasetfield总数：{}，taskkey:{}，projectid:{}",lstDatasetFields.size(),strTaskId,projectId);

            //String strEntityTypeId = entitytypes.getPkid();
            //lstDatasetInfo.get(j).getEntitytypeid()
            java.util.Map<String,List<Datasetinfo>> datasetinfoMap = new HashMap<String,List<Datasetinfo>>();
            for(Datasetinfo datasetinfo:lstDatasetInfo){
                String entitytypeid = datasetinfo.getEntitytypeid();
                List<Datasetinfo> datasetinfoList = null;
                if(!datasetinfoMap.containsKey(entitytypeid)){
                    datasetinfoList = new ArrayList<>();
                    datasetinfoMap.put(entitytypeid,datasetinfoList);
                }else{
                    datasetinfoList = datasetinfoMap.get(entitytypeid);
                }
                datasetinfoList.add(datasetinfo);
            }
            java.util.Map<String,String> datasetinfoMainIdMap = new HashMap<String,String>();
            for(String entitytypeid : datasetinfoMap.keySet()){
                List<Datasetinfo> datasetinfoList = datasetinfoMap.get(entitytypeid);
                if(datasetinfoList!=null && datasetinfoList.size()>0){
                    String strMainDatasetId = null;//存储当前实体数据集对应的主数据集ID
                    for(Datasetinfo datasetinfo:datasetinfoList){
                        if (!datasetinfo.getDatasettype().equals(EntityDatasetType.TABULAR.getValue())) {
                            for (Datasetinfo datasetinfoTemp:datasetinfoList) {
                                if (datasetinfoTemp.getEntitytypeid() != null && datasetinfoTemp.getEntitytypeid().equalsIgnoreCase(datasetinfo.getEntitytypeid())) { //如果游标数据集记录中的实体类型与当前数据集对应的实体类型一致，说明是同一个实体类型的不同数据集
                                    String strDatasetName = datasetinfoTemp.getTablename();
                                    if (strDatasetName == null || strDatasetName.trim().length() < 1) {
                                        continue;
                                    }
                                    strDatasetName = strDatasetName.toUpperCase();
                                    if ((!strDatasetName.endsWith("_P")) && (!strDatasetName.endsWith("_L")) && (!strDatasetName.endsWith("_R"))
                                            && (!strDatasetName.endsWith("_M"))) {
                                        strMainDatasetId = datasetinfoTemp.getPkid();
                                        //strMainDatasetName = datasetinfoTemp.getTablename();
                                        break;
                                    }
                                }
                            }
                        }
                        if (null == strMainDatasetId && datasetinfo.getDatasettype().equals(EntityDatasetType.CAD.getValue())) {
                            //对于纯CAD交互实体来说，主数据集就是其自身
                            strMainDatasetId = datasetinfo.getPkid();
                        }
                        if(StringUtils.isNotEmpty(strMainDatasetId)){
                            datasetinfoMainIdMap.put(datasetinfo.getPkid(),strMainDatasetId);
                        }
                    }
                }
            }
            java.util.Map<String,List<Datasetfield>> datasetfieldMap = new HashMap<String,List<Datasetfield>>();
            for(Datasetfield datasetfield:lstDatasetFields){
                String datasetid = datasetfield.getDatasetid();
                List<Datasetfield> datasetfieldList = null;
                if(!datasetfieldMap.containsKey(datasetid)){
                    datasetfieldList = new ArrayList<>();
                    datasetfieldMap.put(datasetid,datasetfieldList);
                }else{
                    datasetfieldList = datasetfieldMap.get(datasetid);
                }
                datasetfieldList.add(datasetfield);
            }

            long buildEnd = System.currentTimeMillis();
            log.info("查数据用时" + (buildEnd - buildStart) / 1000L);

            long lBeginTime = System.currentTimeMillis();           //获取创建数据集及字段执行开始时间(用于计算功能执行耗时)
            //ExecutorService executorService = Executors.newFixedThreadPool(8);

            for(Entitytypes entitytypes:lstEntitytypes){
                //executorService.execute(new Runnable() {
                    //@Override
                    //public void run() {
                //createEntitiesAchivmentsUDBX(datasourceFinal,entitytypes,entityTypeId,datasetinfoMap,datasetfieldMap,datasetinfoMainIdMap,strKey);
                resultList.addAll(createEntitiesAchivmentsUDBX(datasourceFinal,entitytypes,entityTypeId,datasetinfoMap,datasetfieldMap,datasetinfoMainIdMap,strKey));
                    //}
                //});
            }

            /*executorService.shutdown();
            while (true) {
                if (executorService.isTerminated()) {
                    break;
                } else {
                    try {
                        Thread.sleep(5000L);
                    }catch (Exception e){

                    }
                }
            }*/

            long lEndTime = System.currentTimeMillis();             //获取创建数据集及字段执行结束时间(用于计算功能执行耗时)
            strMsg = "实体生成数据集共耗时：";
            Long lTimeSpan = lEndTime - lBeginTime;
            Long lTime = lTimeSpan / 1000l;                           //计算创建数据集及字段执行耗时
            if (lTime > 60)
                strMsg += new Long(lTime / 60).intValue() + "分钟";
            else
                strMsg += lTime + "秒";
            log.info(strMsg);
            log.info("EntitiesAchivments.udbx重新创建中，实体创建完成，taskkey:{}，projectid:{}",strTaskId,projectId);

            /*----------------添加其他独立数据集--------------*/
            //从模板Excel中读取数据集模板创建相应的数据集，从而可以支持版本化创建
            //属性数据集模板文件的路径为sqlite/template/tabulars.xlsx
            log.info("EntitiesAchivments.udbx重新创建中，创建tabulars开始，taskkey:{}，projectid:{}",strTaskId,projectId);
            String strTabularTemplatesPath = PathUtils.getTemplatePath() + File.separator + "tabulars.xlsx";
            File fileTabularTemplates = new File(strTabularTemplatesPath);
            java.util.Map<String,List<String>> tabularsTableInfoMap = new HashMap<>();
            if (fileTabularTemplates.exists()) {
                InputStream in = FileUtil.getInputStream(fileTabularTemplates);
                java.util.Map<String, java.util.Map<Integer, List<String>>> mapTabularInfos = null;
                try {
                    mapTabularInfos = ExcelUtils.getExcel(in, 7, strTabularTemplatesPath);
                } catch (Exception e) {
                    log.error("EntitiesAchivments.udbx重新创建中，创建tabulars异常，taskkey:{}，projectid:{}",strTaskId,projectId,e);

                }
                if (mapTabularInfos != null && !mapTabularInfos.isEmpty()) {
                    Iterator<String> iterator = mapTabularInfos.keySet().iterator();
                    while (iterator.hasNext()) {
                        String strSheetName = iterator.next();        //拿到每个sheet的名字
                        String strDatasetName = strSheetName;
                        String strDatasetAlias = strSheetName;
                        if (strSheetName.indexOf('<') > 0 && strSheetName.indexOf('>') > 0) {
                            strDatasetAlias = strDatasetName.substring(strDatasetName.indexOf('<') + 1, strDatasetName.indexOf('>'));
                            strDatasetName = strDatasetName.substring(0, strDatasetName.indexOf('<'));
                        }
                        if (datasourceFinal.getDatasets().contains(strDatasetName)) {       //如果udbx文件中已经有同名数据集了，就将已存在的数据集复制一份作为历史版本数据，然后再删掉后重新创建数据集
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(new Date());
                            int year = calendar.get(Calendar.YEAR);
                            int day = calendar.get(Calendar.DAY_OF_MONTH);//获取当前天数
                            int time = calendar.get(Calendar.HOUR_OF_DAY);   //获取当前小时
                            int min = calendar.get(Calendar.MINUTE);  //获取当前分钟
                            int sec = calendar.get(Calendar.SECOND);  //获取当前秒
                            String strNewDatasetName = strDatasetName + "-" + year + (day < 10 ? "0" + day : day) + (time < 10 ? "0" + time : time) + (sec < 10 ? "0" + sec : sec);

                            //先备份删除数据源
                            datasourceFinal.copyDataset(datasourceFinal.getDatasets().get(strDatasetName), strNewDatasetName, datasourceFinal.getDatasets().get(strDatasetName).getEncodeType());
                            datasourceFinal.getDatasets().delete(strDatasetName);
                        }

                        tabularsTableInfoMap.put(strDatasetName,new ArrayList<>());

                        DatasetVectorInfo datasetTabularInfo = new DatasetVectorInfo();
                        datasetTabularInfo.setType(DatasetType.TABULAR);
                        datasetTabularInfo.setName(strDatasetName);
                        DatasetVector datasetTabular = null;
                        try {
                            datasetTabular = datasourceFinal.getDatasets().create(datasetTabularInfo);
                        } catch (Exception e1) {
                            datasetTabularInfo.setName(strDatasetName + "_");
                            log.error("EntitiesAchivments.udbx重新创建中，创建tabulars,create异常，taskkey:{}，projectid:{}",strTaskId,projectId,e1);
                            try {
                                datasetTabular = datasourceFinal.getDatasets().create(datasetTabularInfo);
                            } catch (Exception e2) {
                                log.error("EntitiesAchivments.udbx重新创建中，创建tabulars,create1异常，taskkey:{}，projectid:{}",strTaskId,projectId,e1);
                            }
                        }
                        if (datasetTabular != null) {
                            java.util.Map<Integer, List<String>> mapRowDatas = mapTabularInfos.get(strSheetName);
                            for (int j = 1; j <= mapRowDatas.keySet().size(); j++) {
                                List<String> lstRowDatas = mapRowDatas.get(j);
                                FieldInfo fieldInfo = new FieldInfo();
                                fieldInfo.setName(lstRowDatas.get(1));
                                fieldInfo.setCaption(lstRowDatas.get(2));
                                String strFieldType = lstRowDatas.get(3);

                                tabularsTableInfoMap.get(strDatasetName).add(lstRowDatas.get(1));
                                switch (strFieldType) {
                                    case "单精度型":
                                        fieldInfo.setType(FieldType.SINGLE);
                                        if (lstRowDatas.get(4) != null && lstRowDatas.get(4).trim().length() > 0) {
                                            try {
                                                fieldInfo.setMaxLength(Integer.parseInt(lstRowDatas.get(4)));
                                            } catch (Exception e) {
                                                log.error("EntitiesAchivments.udbx重新创建中，创建tabulars,字段{},create1异常，taskkey:{}，projectid:{}",lstRowDatas.get(1),strTaskId,projectId,e);
                                            }
                                        }
                                        break;
                                    case "双精度型":
                                        fieldInfo.setType(FieldType.DOUBLE);
                                        if (lstRowDatas.get(4) != null && lstRowDatas.get(4).trim().length() > 0) {
                                            try {
                                                fieldInfo.setMaxLength(Integer.parseInt(lstRowDatas.get(4)));
                                            } catch (Exception e) {
                                                log.error("EntitiesAchivments.udbx重新创建中，创建tabulars,字段{},create1异常，taskkey:{}，projectid:{}",lstRowDatas.get(1),strTaskId,projectId,e);
                                            }
                                        }
                                        break;
                                    case "整型":
                                        if (Integer.parseInt(lstRowDatas.get(4)) < 3)
                                            fieldInfo.setType(FieldType.INT16);
                                        else if (Integer.parseInt(lstRowDatas.get(4)) < 5)
                                            fieldInfo.setType(FieldType.INT32);
                                        else
                                            fieldInfo.setType(FieldType.INT64);
                                        break;
                                    case "日期型":
                                        fieldInfo.setType(FieldType.DATETIME);
                                        break;
                                    case "文本型":
                                        fieldInfo.setType(FieldType.TEXT);
                                        try {
                                            fieldInfo.setMaxLength(Integer.parseInt(lstRowDatas.get(4)));
                                        } catch (Exception e) {
                                            fieldInfo.setMaxLength(50);
                                            log.warn("EntitiesAchivments.udbx重新创建中，创建tabulars,字段{},超长改为50，taskkey:{}，projectid:{}",lstRowDatas.get(1),strTaskId,projectId,e);
                                        }
                                        break;
                                    case "字节型":
                                        fieldInfo.setType(FieldType.BYTE);
                                        break;
                                    case "二进制型":
                                    case "长文本型":
                                        fieldInfo.setType(FieldType.LONGBINARY);
                                        break;
                                }
                                try {
                                    datasetTabular.getFieldInfos().add(fieldInfo);
                                } catch (Exception e1) {
                                    //logger.error("为数据集" + datasetTabular.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。");
                                    log.error("EntitiesAchivments.udbx重新创建中，创建tabulars,字段{},add异常，taskkey:{}，projectid:{}",fieldInfo.getName(),strTaskId,projectId,e1);
                                    fieldInfo.setName(fieldInfo.getName() + "_");
                                    try {
                                        datasetTabular.getFieldInfos().add(fieldInfo);
                                    } catch (Exception e2) {
                                        log.error("EntitiesAchivments.udbx重新创建中，创建tabulars,字段{},add2异常，taskkey:{}，projectid:{}",fieldInfo.getName(),strTaskId,projectId,e2);
                                    }
                                }
                            }
                        }
                    }
                }
                try {
                    in.close();
                } catch (IOException e) {
                }
            }

            for (String tableName : tabularsTableInfoMap.keySet()) {
                if(datasourceFinal.getDatasets().contains(tableName)){
                    List<String> fieldInfoList = tabularsTableInfoMap.get(tableName);
                    if(fieldInfoList!=null&&fieldInfoList.size()>0){
                        DatasetVector datasetVector = (DatasetVector)datasourceFinal.getDatasets().get(tableName);
                        for(String fieldName:fieldInfoList){
                            FieldInfo fieldInfo = datasetVector.getFieldInfos().get(fieldName);
                            if(fieldInfo==null){
                                resultList.add(String.format("数据集%s字段%s不存在。",tableName,fieldName));
                            }
                        }
                    }else{
                        resultList.add(String.format("数据集%s字段为空。",tableName));
                    }
                }else{
                    resultList.add(String.format("数据集%s不存在。",tableName));
                }
                // 对键值对做相关处理
            }

            _redisCommonUtil.setKey(strKey, "正在复制数据到本地数据文件");
            //将内存型数据源datasourceMem中的数据集批量复制到UDBx型数据源datasource中
            lBeginTime = System.currentTimeMillis();
            for (int i = 0; i < datasourceFinal.getDatasets().getCount(); i++) {
                Dataset datasetSrc = datasourceFinal.getDatasets().get(i);
                try {
                    _redisCommonUtil.setKey(strKey, "正在复制数据集" + datasetSrc.getName() + "。还剩" + (datasourceFinal.getDatasets().getCount() - 1 - i) + "个数据集未复制。");
                    if (entityTypeId != null && entityTypeId.trim().length() > 0 && datasource.getDatasets().contains(datasetSrc.getName())) {
                        datasource.getDatasets().delete(datasetSrc.getName());  //如果是传入实体对应的数据集，则先删除udbx中原有的数据集
                    }
                    Dataset datasetCopied = datasource.copyDataset(datasetSrc, datasetSrc.getName(), datasetSrc.getEncodeType());
                    //if (null != datasetCopied)
                        //iCopiedCount++;
                } catch (Exception e) {
                    log.error("复制数据集" + datasetSrc.getName() + "到UDBX数据源中失败！，原因如下：" + e.getMessage());
                }
            }
            datasourceFinal.close();  //用完内存型数据源后将其关闭掉

            strMsg = "从内存型数据源向UDBX数据源拷贝数据共耗时：";
            lEndTime = System.currentTimeMillis();
            lTimeSpan = lEndTime - lBeginTime;
            lTime = lTimeSpan / 1000l;                           //计算创建数据集及字段执行耗时
            if (lTime > 60)
                strMsg += new Long(lTime / 60).intValue() + "分钟";
            else
                strMsg += lTime + "秒";
            System.out.println(strMsg);
            _redisCommonUtil.setKey(strKey, strMsg);
            _redisCommonUtil.setKeyAndTime(strKey, "UDBX模板文件更新完毕。", 6, TimeUnit.HOURS);


            String message = buildProjectWorkspace(projectId, strTaskId);
            if(StringUtils.isNotEmpty(message)){
                resultList.add(message);
            }
            if (_redisCommonUtil != null) {
                return "{\"result\":\"success\",\"message\":\"" + String.join(";",resultList) + "\"}";
            }else {
                filePrjWSPath.delete();
                log.error("EntitiesAchivments.udbx,重新创建中，ProjectWorkspaceSeed,创建失败，taskkey:{}，projectid:{}",strTaskId,projectId);

                resultList.add(String.format("EntitiesAchivments.udbx,重新创建中，ProjectWorkspaceSeed,创建失败，taskkey:%s，projectid:%s",strTaskId,projectId));
                return "{\"result\":\"failure\",\"message\":\"" + String.join(";",resultList) + "\"}";
            }

        } finally {
            if(workspace!=null){
                workspace.close();
                workspace.dispose();
            }
            lock.unlock();
        }
    }

    private List<String> createEntitiesAchivmentsUDBX( Datasource datasourceMem,Entitytypes entitytypes,String entityTypeId,java.util.Map<String,List<Datasetinfo>> datasetinfoMap,java.util.Map<String,List<Datasetfield>> datasetFieldMap,java.util.Map<String,String> datasetinfoMainIdMap,String strKey){

        log.info("EntitiesAchivments.udbx，创建实体：{}开始，taskkey:{}",entityTypeId,strKey);

        List<String> resultList = new ArrayList<>();
        String strMainDatasetId = null;         //存储当前实体数据集对应的主数据集ID
        String strEntityTypeDatasetName = null;
        String strMsg;
        //Entitytypes entitytypes = lstEntitytypes.get(i);
        String strEntityTypeId = entitytypes.getPkid();
        if (StringUtils.isNotEmpty(entityTypeId) && entityTypeId.trim().length() > 0) {
            if (!entityTypeId.equalsIgnoreCase(strEntityTypeId)) {
                log.info("EntitiesAchivments.udbx，创建实体：{}开始，传入实体：{}，不需要创建，taskkey:{}",entityTypeId,entityTypeId,strKey);
                return resultList;
            }
        }

        _redisCommonUtil.setKey(strKey, "创建实体："+entitytypes.getEntitytypecode());

        long lBeginTime = System.currentTimeMillis();           //获取创建数据集及字段执行开始时间(用于计算功能执行耗时)
        List<Datasetinfo> lstDatasetInfo = datasetinfoMap.get(strEntityTypeId);
        for (int j = 0; j < lstDatasetInfo.size(); j++) {
            Datasetinfo datasetinfo = lstDatasetInfo.get(j);
            try {
                String datasetinfoId = datasetinfo.getPkid();
                String strTableName = datasetinfo.getTablename();
                log.error("EntitiesAchivments.udbx，创建实体：{}开始，entityTypeId:{}，taskkey:{}",strTableName,entityTypeId,strKey);

                //如果是传入实体对应的数据集，则先删除udbx中原有的数据集
                if (entityTypeId != null && entityTypeId.trim().length() > 0 && datasourceMem.getDatasets().contains(strTableName)) {
                    datasourceMem.getDatasets().delete(strTableName);  //如果是传入实体对应的数据集，则先删除udbx中原有的数据集
                }
                if (lstDatasetInfo.get(j).getEntitytypeid() != null && lstDatasetInfo.get(j).getEntitytypeid().equals(strEntityTypeId)) {
                    if (datasetinfo.getDatasettype().equals(DatasetType.TABULAR.value())) {
                        continue;
                    }
                    //if(datasetinfo.getPkid().equalsIgnoreCase(""))
                    if (entityTypeId != null && entityTypeId.length() > 0) {
                        if (!datasetinfo.getEntitytypeid().equalsIgnoreCase(entityTypeId)) {
                            continue;
                        }
                        strEntityTypeDatasetName = datasetinfo.getTablename();  //能走到这一步，说明就是传入实体类型对应的数据集
                        strMsg = "正在为实体“" + entitytypes.getEntitytypealias() + "”创建表结构。";
                    } else {
                        strMsg = "正在为实体“" + entitytypes.getEntitytypealias() + "”创建表结构。";
                    }
                    List<String> ignoreFieldList = new ArrayList<>();
                    _redisCommonUtil.setKey(strKey, strMsg);
                    DatasetVectorInfo datasetVectorInfo = new DatasetVectorInfo();
                    DatasetVectorInfo datasetVectorMInfo = new DatasetVectorInfo();
                    if (strTableName.indexOf("-") > -1) {
                        log.info("EntitiesAchivments.udbx，创建实体：{}失败，entityTypeId：{}，名称中存在-，taskkey:{}",strTableName,entityTypeId,strKey);
                        continue;
                    /* 对于名称中含有“-”的数据集名称，先做跳过处理，暂时不自动处理。因为都是测试用的实体
                        strMsg = "数据集"+ strTableName + "名称中含有非法字符“-”，改为使用名称";
                        strTableName = strTableName.replaceAll("-", "_");
                        strMsg += strTableName + "创建数据集。";
                        logger.error(strMsg);
                    */
                    }
                    try {
                        datasetVectorInfo.setName(strTableName);
                        datasetVectorMInfo.setName(strTableName);
                    } catch (Exception e) {
                        log.error("EntitiesAchivments.udbx，创建实体：{}异常，entityTypeId:{}，taskkey:{}",strTableName,entityTypeId,strKey,e);
                    }
                    /*if (datasetinfo.getDatasettype().equals(EntityDatasetType.TABULAR.getValue()))
                        datasetVectorInfo.setType(DatasetType.TABULAR);
                    else */
                    if (datasetinfo.getDatasettype().equals(EntityDatasetType.POINT3D.getValue())) {
                        datasetVectorInfo.setType(DatasetType.POINT3D);
                    } else if (datasetinfo.getDatasettype().equals(EntityDatasetType.LINE3D.getValue())) {
                        datasetVectorInfo.setType(DatasetType.LINE3D);
                    } else if (datasetinfo.getDatasettype().equals(EntityDatasetType.POLYGON3D.getValue())) {
                        datasetVectorInfo.setType(DatasetType.REGION3D);
                    } else if (datasetinfo.getDatasettype().equals(EntityDatasetType.MODEL.getValue())) {
                        datasetVectorMInfo.setType(DatasetType.MODEL);
                    } else if (datasetinfo.getDatasettype().equals(EntityDatasetType.CAD.getValue())) {
                        datasetVectorInfo.setType(DatasetType.CAD);
                    }
                    //创建三维矢量数据集
                    DatasetVector datasetVector = null;
                    DatasetVector datasetVectorM = null;
                    if (!datasetVectorInfo.getType().equals(DatasetType.POINT)) {     //当没有给datasetVectorInfo设置数据集类型时，默认会为POINT类型，此时就不该被创建了
                        if (!datasourceMem.getDatasets().contains(datasetVectorInfo.getName()))
                            datasetVector = datasourceMem.getDatasets().create(datasetVectorInfo);
                    }
                    if (datasetVectorMInfo.getType().equals(DatasetType.MODEL)) {
                        //创建三维模型数据集
                        if (!datasourceMem.getDatasets().contains(datasetVectorMInfo.getName()))
                            datasetVectorM = datasourceMem.getDatasets().create(datasetVectorMInfo);
                    }
                    if (!datasourceMem.getDatasets().contains(strTableName)) {
                        strMsg = "创建数据集" + strTableName + "失败！";
                        _redisCommonUtil.setKeyAndTime(strKey, strMsg, 10, TimeUnit.MINUTES);
                        log.error("EntitiesAchivments.udbx，创建实体：{}失败，entityTypeId：{}，数据集不存在，taskkey:{}",strTableName,entityTypeId,strKey);

                        resultList.add(strMsg);
                        continue;
                    }
/*                if (datasetVector == null && datasetVectorM == null) {
                    strMsg = "创建数据集" + datasetinfo.getTablename() + "失败！";
                    _redisCommonUtil.setKey(strKey, strMsg);
                    //log.info("创建数据集" + datasetinfo.getTablename() + "失败！");
                    logger.error(strMsg);
                    continue;
                }*/
                    //iCreatedCount++;
                    /**
                     * 为数据集创建字段。操作步骤如下：
                     * 第一步：先获取其对应的属性数据集
                     * 第二步：将属性数据集注册的所有字段添加到当前数据集里
                     * 第三步：创建当前数据集注册的自有字段
                     */
                    //第一步：先找到当前数据集对应的属性数据集，拿到属性数据集的PKID和数据集名称
                    strMainDatasetId = datasetinfoMainIdMap.get(datasetinfoId);
                    if (StringUtils.isEmpty(strMainDatasetId)) {
                        log.error("EntitiesAchivments.udbx，创建实体：{}失败，entityTypeId：{}，strMainDatasetId为空，taskkey:{}", strTableName, entityTypeId, strKey);
                        continue;
                    }
                    /*if (!datasetinfo.getDatasettype().equals(EntityDatasetType.TABULAR.getValue())) {
                        for (int k = 0; k < lstDatasetInfo.size(); k++) {
                            Datasetinfo datasetinfoTemp = lstDatasetInfo.get(k);
                            if (datasetinfoTemp.getEntitytypeid() != null && datasetinfoTemp.getEntitytypeid().equalsIgnoreCase(datasetinfo.getEntitytypeid())) { //如果游标数据集记录中的实体类型与当前数据集对应的实体类型一致，说明是同一个实体类型的不同数据集
                                String strDatasetName = datasetinfoTemp.getTablename();
                                if (strDatasetName == null || strDatasetName.trim().length() < 1) {
                                    continue;
                                }
                                strDatasetName = strDatasetName.toUpperCase();
                                if ((!strDatasetName.endsWith("_P")) && (!strDatasetName.endsWith("_L")) && (!strDatasetName.endsWith("_R"))
                                        && (!strDatasetName.endsWith("_M"))) {
                                    strMainDatasetId = datasetinfoTemp.getPkid();
                                    //strMainDatasetName = datasetinfoTemp.getTablename();
                                    break;
                                }
                            }
                        }
                    }
                    if (null == strMainDatasetId && datasetinfo.getDatasettype().equals(EntityDatasetType.CAD.getValue())) {
                        //对于纯CAD交互实体来说，主数据集就是其自身
                        strMainDatasetId = datasetinfo.getPkid();
                    }*/
                    /**
                     * 第二步：再去字段注册表中过滤出属性数据集PKID对应的所有字段信息
                     *       并将当前数据集的PKID放到lstDatasetFields列表里对应位置元素的descinfo字段里
                     */
/*                for (int j = 0; j < lstDatasetFields.size(); j++) {
                    Datasetfieldtem datasetfield = lstDatasetFields.get(j);
                    if (datasetfield.getDatasetid()!=null && datasetfield.getDatasetid().equalsIgnoreCase(strMainDatasetId)) {
                        datasetfield.setDescinfo(datasetinfo.getPkid());
                    }
                }*/
                    /**
                     * 第三步：遍历当前数据集对应的lstDatasetFields，然后创建对应的字段
                     *       与当前数据集匹配的条件有两种情况：
                     *          (1)要么其datasetid为当前datasetInfo的pkid，表示就是点线面数据集自己特有的字段
                     *          (2)要么其descinfo为当前datasetInfo的pkid，表示其为所属属性数据集中的公共字段
                     */
                    List<Datasetfield> lstDatasetFields = datasetFieldMap.get(strMainDatasetId);
                    if (lstDatasetFields == null || lstDatasetFields.size() < 1) {
                        log.error("EntitiesAchivments.udbx，创建实体：{}失败，entityTypeId：{}，字段信息为空，taskkey:{}", strTableName, entityTypeId, strKey);
                        continue;
                    }
                    for (int k = 0; k < lstDatasetFields.size(); k++) {
                        Datasetfield datasetfield = lstDatasetFields.get(k);
                        if (datasetfield.getDatasetid() == null || !datasetfield.getDatasetid().equals(strMainDatasetId)) {
                            continue;
                        }
//                    if (null!=datasetfield.getEntitytypeid()&&//datasetfield.getDatasetid().equalsIgnoreCase(strMainDatasetId) &&
//                            datasetfield.getEntitytypeid().equalsIgnoreCase(datasetinfo.getEntitytypeid())) {
                        if (datasetfield.getFieldname() == null || datasetfield.getFieldname().trim().length() < 1 ||
                                datasetfield.getFieldname().length() >= 30) {   //如果字段长度超过30个字符就跳过不予创建，否则创建字段时会报错
                            log.warn("EntitiesAchivments.udbx，创建实体：{}失败，entityTypeId：{}，字段{}添加失败，长度超过30，taskkey:{}", strTableName,entityTypeId, datasetfield.getFieldname(), strKey);
                            ignoreFieldList.add(datasetfield.getFieldname());
                            continue;
                        }
                        if (datasetVector != null && datasetVector.getFieldInfos().get(datasetfield.getFieldname()) != null) {
                            log.warn("EntitiesAchivments.udbx，创建实体：{}失败，entityTypeId：{}，字段{}datasetVector已经存在，taskkey:{}",strTableName, entityTypeId, datasetfield.getFieldname(), strKey);
                            continue;       //如果数据集中已经有这个字段了，就不再添加了
                        }
                        if (datasetVectorM != null && datasetVectorM.getFieldInfos().get(datasetfield.getFieldname()) != null) {
                            log.warn("EntitiesAchivments.udbx，创建实体：{}失败，entityTypeId：{}，字段{}datasetVectorM已经存在，taskkey:{}",strTableName, entityTypeId, datasetfield.getFieldname(), strKey);
                            continue;       //如果数据集中已经有这个字段了，就不再添加了
                        }
                        FieldInfo fieldInfo = new FieldInfo();
                        try {
                            fieldInfo.setName(datasetfield.getFieldname());
                        } catch (Exception fe) {
                            //log.info("为字段设置名称"+datasetfield.getFieldname()+"失败！");
                            log.error("EntitiesAchivments.udbx，创建实体：{}异常，entityTypeId:{}，字段{}，taskkey:{}", strTableName,entityTypeId, datasetfield.getFieldname(), strKey,fe);
                            ignoreFieldList.add(datasetfield.getFieldname());
                            continue;
                        }
                        if (datasetfield.getFieldalias() != null && datasetfield.getFieldalias().trim().length() > 0) {
                            if (StringUtils.isNotEmpty(datasetfield.getUnits())) { // 判断实体属性是否带有单位，如有数据集别名加上单位
//                                fieldInfo.setCaption(datasetfield.getFieldalias().trim() + "(" + datasetfield.getUnits().trim() + ")"); // 2024-3-26修改 先不带上单位
                                fieldInfo.setCaption(datasetfield.getFieldalias().trim());
                            } else {
                                fieldInfo.setCaption(datasetfield.getFieldalias().trim());
                            }
                        }
                        if (datasetfield.getFieldtype().length() > 0) {
                            if (datasetfield.getFieldtype().equalsIgnoreCase("字符串")) {
                                fieldInfo.setType(FieldType.TEXT);
                            } else if (datasetfield.getFieldtype().equalsIgnoreCase("数值")) {
                                fieldInfo.setType(FieldType.SINGLE);
                            } else if (datasetfield.getFieldtype().equalsIgnoreCase("长文本")) {
                                fieldInfo.setType(FieldType.LONGBINARY);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.INTEGER.getValue()) {
                                fieldInfo.setType(FieldType.INT32);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.FLOAT.getValue()) {
                                fieldInfo.setType(FieldType.SINGLE);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.DOUBLE.getValue()) {
                                fieldInfo.setType(FieldType.DOUBLE);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.STRING.getValue()) {
                                fieldInfo.setType(FieldType.TEXT);
                                if (datasetfield.getFieldsize() != null && datasetfield.getFieldsize() > 0)
                                    fieldInfo.setMaxLength(datasetfield.getFieldsize());
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.DATE.getValue()) {
                                fieldInfo.setType(FieldType.DATE);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.BYTEA.getValue()) {
                                fieldInfo.setType(FieldType.LONGBINARY);
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.CLOB.getValue()) {
                                fieldInfo.setType(FieldType.LONGBINARY);
                                if (datasetfield.getFieldsize() != null && datasetfield.getFieldsize() > 0)
                                    fieldInfo.setMaxLength(datasetfield.getFieldsize());
                            } else if (Integer.parseInt(datasetfield.getFieldtype()) == EntityDatasetFieldType.BOOLEAN.getValue()) {
                                fieldInfo.setType(FieldType.BOOLEAN);
                            }
                        }
                        //创建字段
                        if (datasetVector != null) {
                            try {
                                if (datasetVector.getFieldInfos().get(fieldInfo.getName()) == null)
                                    datasetVector.getFieldInfos().add(fieldInfo);
                            } catch (Exception e) {
                                log.error("为数据集" + datasetVector.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。",e);
                                fieldInfo.setName(fieldInfo.getName() + "_");   //如果要创建的是系统字段，则在字段名后面追加一个“_”，这样就能创建了
                                try {
                                    datasetVector.getFieldInfos().add(fieldInfo);
                                } catch (Exception e1) {
                                    log.error("为数据集" + datasetVector.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。",e1);
                                }
                            }
                        }
                        if (datasetVectorM != null) {
                            try {
                                if (datasetVectorM.getFieldInfos().get(fieldInfo.getName()) == null)
                                    datasetVectorM.getFieldInfos().add(fieldInfo);
                            } catch (Exception e) {
                                log.error("为数据集" + datasetVectorM.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。",e);
                                fieldInfo.setName(fieldInfo.getName() + "_");   //如果要创建的是系统字段，则在字段名后面追加一个“_”，这样就能创建了
                                try {
                                    datasetVectorM.getFieldInfos().add(fieldInfo);
                                } catch (Exception e1) {
                                    log.error("为数据集" + datasetVectorM.getName() + "创建字段" + fieldInfo.getName() + "失败！使用名称" + fieldInfo.getName() + "_" + "来创建字段。",e1);
                                }
                            }
                        }
//                    }
                    }
                    if (datasetVector != null) {
                        for (int k = 0; k < lstDatasetFields.size(); k++) {
                            Datasetfield datasetfield = lstDatasetFields.get(k);
                            if (datasetfield.getDatasetid() == null || !datasetfield.getDatasetid().equals(strMainDatasetId)) {
                                continue;
                            }
                            String fieldName = datasetfield.getFieldname();
                            FieldInfo fieldInfo = datasetVector.getFieldInfos().get(fieldName);
                            if(fieldInfo==null){
                                fieldInfo = datasetVector.getFieldInfos().get(fieldName+"_");
                                if(fieldInfo==null&&!ignoreFieldList.contains(fieldName)) {
                                    log.error("数据集{}中缺少字段{}", datasetVector.getName(), fieldName);
                                    resultList.add(String.format("数据集%s中缺少字段%s", datasetVector.getName(), fieldName));
                                }
                            }
                        }

                    }
                    if (datasetVectorM != null) {
                        for (int k = 0; k < lstDatasetFields.size(); k++) {
                            Datasetfield datasetfield = lstDatasetFields.get(k);
                            if (datasetfield.getDatasetid() == null || !datasetfield.getDatasetid().equals(strMainDatasetId)) {
                                continue;
                            }
                            String fieldName = datasetfield.getFieldname();
                            FieldInfo fieldInfo = datasetVectorM.getFieldInfos().get(fieldName);
                            if(fieldInfo==null){
                                fieldInfo = datasetVectorM.getFieldInfos().get(fieldName+"_");
                                if(fieldInfo==null&&!ignoreFieldList.contains(fieldName)) {
                                    log.error("数据集{}中缺少字段{}", datasetVectorM.getName(), fieldName);
                                    resultList.add(String.format("数据集%s中缺少字段%s", datasetVectorM.getName(), fieldName));
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("EntitiesAchivments.udbx，创建实体：{}，strEntityTypeId:{}，异常，taskkey:{}", entityTypeId,strEntityTypeId, strKey,e);
            }
        }

        long lEndTime = System.currentTimeMillis();             //获取创建数据集及字段执行结束时间(用于计算功能执行耗时)
        strMsg = entitytypes.getEntitytypealias()+"生成数据集耗时：";
        Long lTimeSpan = lEndTime - lBeginTime;
        Long lTime = lTimeSpan / 1000l;                           //计算创建数据集及字段执行耗时
        if (lTime > 60)
            strMsg += new Long(lTime / 60).intValue() + "分钟";
        else
            strMsg += lTime + "秒";
        log.info(strMsg);
        return resultList;
    }

    @Override
    public java.util.Map<String, Object> rebuildProjectWorkspaceSeed(String projectId) {
//        String strMsg = "正在创建数据文件的结构";
//        _redisCommonUtil.setKey(strKey, strMsg);
        java.util.Map<String, Object> mapResult = new HashMap<String, Object>();
        try {
            lock.lock();
            _redisCommonUtil.setKey(projectId, "{}");
            String strPrjWSPath = PathUtils.getDesignPath() + File.separator + projectId;
            File filePrjWSPath = new File(strPrjWSPath);
            if (!filePrjWSPath.exists())
                filePrjWSPath.mkdir();
            else if (filePrjWSPath.listFiles().length > 0) {
                //filePrjWSPath.deleteOnExit();
                //filePrjWSPath.mkdir();
            }
//        File fileSourceUDBX = new File(PathUtils.getDesignPath() + File.separator + "EntitiesAchivments.udbx");
            String strDestUDBXPath = PathUtils.getDesignPath() + File.separator + projectId + File.separator + "EntitiesAchivments.udbx";
//        try {
//            File fileDestUDBX = new File(strDestUDBXPath);
//            FileUtils.copyFile(fileSourceUDBX, fileDestUDBX);
//        } catch (IOException ioe) {
//            mapResult.put("result", "failure");
//            mapResult.put("exceptioncode", Errors.FILECOPYFAILURE.getName());
//            mapResult.put("projectid", projectId);
//            mapResult.put("sourcefile", fileSourceUDBX.getAbsolutePath());
//            mapResult.put("destfile", strDestUDBXPath);
//            return mapResult;
//            //return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.FILECOPYFAILURE.getName() + "\",\"projectid\":\""+projectId+"\",\"sourcefile\":\"" + fileSourceUDBX.getAbsolutePath() + "\",\"destfile\":\"" + strDestUDBXPath + "\"}";
//        }
            Workspace workspace = new Workspace();
            WorkspaceConnectionInfo workspaceConnectionInfo = new WorkspaceConnectionInfo();
            workspaceConnectionInfo.setType(WorkspaceType.SXWU);
            workspaceConnectionInfo.setServer(filePrjWSPath + File.separator + "EntityDesign.sxwu");

            DatasourceConnectionInfo datasourceConnectionInfo = new DatasourceConnectionInfo();
            datasourceConnectionInfo.setEngineType(EngineType.UDBX);
            datasourceConnectionInfo.setServer(strDestUDBXPath);
            datasourceConnectionInfo.setAlias("EntitiesAchivments");
            Datasource datasource = workspace.getDatasources().open(datasourceConnectionInfo);
            if (datasource == null) {
                try {
                    workspace.getDatasources().create(datasourceConnectionInfo);
                    reCreateWorkspaceSeedsUDBXFromProject(null, "seed_" + projectId + "_" + JavaUtils.getDateTimeSSS(), projectId);
                } catch (Exception e) {
                    log.error("EntityDesign.sxwu创建异常，projectid:{}",projectId,e);
                }
            }
//        datasource.setPrjCoordSys(prjCoordSys);
//        datasource.refresh();

            Map mapEntity = new Map(workspace);
            mapEntity.setName("EntitiesMap");
//        mapEntity.setPrjCoordSys(prjCoordSys);
            workspace.getMaps().add("EntitiesMap", mapEntity.toXML());
            Scene sceneEntity = new Scene(workspace);
            sceneEntity.setName("EntitiesScene");
            workspace.getScenes().add("EntitiesScene", sceneEntity.toXML());
            workspace.setCaption("EntityDesign");
            workspace.saveAs(workspaceConnectionInfo);

//        _redisCommonUtil.setKey(strKey, "正在配置地图风格......");

            // 查询默认风格
            List<Entitytypestyles> lstStyle = iEntitytypestylesService.list(projectId);
            if (lstStyle == null || lstStyle.size() < 1) {
                mapResult.put("result", "failure");
                mapResult.put("exceptioncode", Errors.HASNOLAYERSTYLES.getName());
                mapResult.put("projectid", projectId);
                log.error("EntityDesign.sxwu创建失败,Entitytypestyles为空，projectid:{}",projectId);
                return mapResult;
                //return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.HASNOLAYERSTYLES.getName() + "\",\"projectid\":\""+projectId+"\"}";
            }
            // 查询专题图风格
            List<Entitytypethemestyles> lstThemeStyle = iEntitytypethemestylesService.list(projectId);
            //按照order_index对实体类型进行排序，从而按照该顺序显示图层
            //List<Entitytypes> lstEntityTypes = iEntitytypesService.getListBySort("order_index", "desc");
            List<Entitytypes> lstEntityTypes = iEntitytypesService.getListByProjectId(projectId, "order_index", "desc");
            /**
             * 提前把数据集注册表里的所有记录全部查询出来，防止后期遍历每类实体时频繁查库而带来的性能问题
             */
            List<Datasetinfo> lstDatasetInfo = iDatasetinfoService.list(projectId);
            if (lstDatasetInfo == null || lstDatasetInfo.size() < 1) {
//            _redisCommonUtil.setKey(strKey, "没有找到注册的数据集信息");
                mapResult.put("result", "failure");
                mapResult.put("exceptioncode", Errors.HASNODATASETS.getName());
                mapResult.put("projectid", projectId);
                log.error("EntityDesign.sxwu创建失败,Datasetinfo为空，projectid:{}",projectId);
                return mapResult;
                //return "{\"result\":\"failure\",\"exceptioncode\":\"" + Errors.HASNODATASETS.getName() + "\",\"projectid\":\""+projectId+"\"}";
            }
            String strMainDatasetId = null;         //存储当前实体数据集对应的主数据集ID
            //String strMainDatasetName = null;       //存储当前实体数据集对应的主数据集名称
            //List<Datasetfield> lstDatasetFields = iDatasetfieldService.list();
            List<Datasetfield> lstDatasetFields = iDatasetfieldService.listByProject(projectId);
//        lBeginTime = System.currentTimeMillis();                   //开始为设置地图风格功能计时
            for (int i = 0; i < lstEntityTypes.size(); i++) {
                Entitytypes entitytypes = lstEntityTypes.get(i);
                String strEntityID = entitytypes.getPkid();
                //第一步：对于每个实体，先去实体关联数据集表里找到关联的所有数据集
                for (int j = 0; j < lstDatasetInfo.size(); j++) {
                    Datasetinfo datasetinfo = lstDatasetInfo.get(j);
                    if (null != datasetinfo.getEntitytypeid() && datasetinfo.getEntitytypeid().equalsIgnoreCase(strEntityID)) {
                        String strDatasetName = datasetinfo.getTablename();     //本来应该直接使用datasetname来作为数据集名称的，但因为之前开发人员不清楚，所以使用了tablename字段来存储真正的数据集名称
                        String strDatasetAlias = datasetinfo.getDatasetalias();
                        Boolean blHasAddTheme = false;  //是否添加了单值专题图
                        Boolean blHasAddStyle = false;  //是否添加了风格图层
                        //第二步：去实体专题图定义表里找到该实体针对该图层定义的单值专题图，构造专题图图层，分别添加到Map和Scene里
                        //有专题图只构造专题图不构造普通图层风格
                        if (lstThemeStyle != null && lstThemeStyle.size() > 0) {
                            String strFieldName = "";
                            String strFieldID = "";
                            ThemeUnique themeUnique = null;
                            Theme3DUnique theme3DUnique = null;
                            for (int k = 0; k < lstThemeStyle.size(); k++) {
                                if (lstThemeStyle.get(k).getEntitytypeid().equalsIgnoreCase(strEntityID) && lstThemeStyle.get(k).getDatasetid().equalsIgnoreCase(datasetinfo.getPkid())) {
                                    Entitytypethemestyles entitytypethemestyles = lstThemeStyle.get(k);
                                    if (entitytypethemestyles.getThemefieldid() != null) {
                                        for (int l = 0; l < lstDatasetFields.size(); l++) {
                                            if (lstDatasetFields.get(l).getPkid().equalsIgnoreCase(entitytypethemestyles.getThemefieldid())) {
                                                strFieldName = lstDatasetFields.get(l).getFieldname();
                                                break;
                                            }
                                        }
                                        //字段一变就创建新的专题图层
                                        if (!entitytypethemestyles.getThemefieldid().equalsIgnoreCase(strFieldID)) {
                                            if (themeUnique != null) {     //如果之前已经构造过themeUnique了，说明上一个字段对应的单值专题已遍历和构造完毕
                                                //就把构造好的themeUnique添加到地图图层集合里
                                                mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), themeUnique, true);
                                                sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), theme3DUnique, true);
                                            } else {                    //如果之前没有初始化，就进行themeUnique和theme3DUnique初始化
                                                strFieldID = entitytypethemestyles.getThemefieldid();
                                                themeUnique = new ThemeUnique();
                                                themeUnique.setUniqueExpression(strFieldName);
                                                theme3DUnique = new Theme3DUnique();
                                                theme3DUnique.setUniqueExpression(strFieldName);
                                            }
                                        } //else {  //字段没变就去构造themeUnique里的单值风格，并添加到themeUnique里
                                        Color fillColor = Color.black;
                                        try {
                                            if (entitytypethemestyles.getTwodlinecolor() != null && entitytypethemestyles.getTwodlinecolor().toUpperCase().indexOf("0X") > -1) {          //以16进制方式存储的颜色
                                                fillColor = toColorFromString(entitytypethemestyles.getTwodfillcolor());
                                            } else if (entitytypethemestyles.getTwodlinecolor() != null && entitytypethemestyles.getTwodlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                                fillColor = toColorFromRGBA(entitytypethemestyles.getTwodfillcolor());
                                            }
                                        } catch (Exception e) {
                                            log.error("EntityDesign.sxwu创建失败,fillColor异常，projectid:{}",projectId,e);
                                            //log.info(e.getMessage());
                                        }
                                        Color fillColor3D = Color.white;
                                        try {
                                            if (entitytypethemestyles.getThreedlinecolor() != null && entitytypethemestyles.getThreedlinecolor().toUpperCase().indexOf("0X") > -1) {
                                                fillColor3D = toColorFromString(entitytypethemestyles.getThreedlinecolor());
                                            } else if (entitytypethemestyles.getThreedlinecolor() != null && entitytypethemestyles.getThreedlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                                fillColor3D = toColorFromRGBA(entitytypethemestyles.getThreedlinecolor());
                                            } else if (entitytypethemestyles.getTwodlinecolor() != null) {
                                                fillColor3D = fillColor;
                                            }
                                        } catch (Exception e) {
                                            log.error("EntityDesign.sxwu创建失败,fillColor3D异常，projectid:{}",projectId,e);
                                            //log.info(e.getMessage());
                                        }
                                        Color lineColor = Color.BLACK;
                                        try {
                                            if (entitytypethemestyles.getTwodlinecolor() != null && entitytypethemestyles.getTwodlinecolor().toUpperCase().indexOf("0X") > -1) {          //以16进制方式存储的颜色
                                                lineColor = toColorFromString(entitytypethemestyles.getTwodlinecolor());
                                            } else if (entitytypethemestyles.getTwodlinecolor() != null && entitytypethemestyles.getTwodlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                                lineColor = toColorFromRGBA(entitytypethemestyles.getTwodlinecolor());
                                            }
                                        } catch (Exception e) {
                                            log.error("EntityDesign.sxwu创建失败,lineColor异常，projectid:{}",projectId,e);
                                        }
                                        Color lineColor3D = Color.white;
                                        try {
                                            if (entitytypethemestyles.getThreedlinecolor() != null && entitytypethemestyles.getThreedlinecolor().toUpperCase().indexOf("0X") > -1) {          //以16进制方式存储的颜色
                                                lineColor3D = toColorFromString(entitytypethemestyles.getThreedlinecolor());
                                            } else if (entitytypethemestyles.getThreedlinecolor() != null && entitytypethemestyles.getThreedlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                                lineColor3D = toColorFromRGBA(entitytypethemestyles.getThreedlinecolor());
                                            } else if (entitytypethemestyles.getTwodlinecolor() != null) {
                                                lineColor3D = lineColor;
                                            }
                                        } catch (Exception e) {
                                            log.error("EntityDesign.sxwu创建失败,lineColor3D异常，projectid:{}",projectId);
                                        }

                                        Integer markerSymbolID = 0;
                                        try {
                                            markerSymbolID = Integer.parseInt(entitytypethemestyles.getTwodsymbalcode());
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,markerSymbolID异常，projectid:{}",projectId);
                                        }
                                        Integer threeDMarkerSymbolID = 0;
                                        try {
                                            threeDMarkerSymbolID = Integer.parseInt(entitytypethemestyles.getThreedsymbalcode());
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,threeDMarkerSymbolID异常，projectid:{}",projectId);
                                        }
                                        Integer markerSymbolSize = entitytypethemestyles.getTwodsymbalsize();
                                        Integer markerSymbolWidth = entitytypethemestyles.getTwodsymbalwidth();
                                        Integer markerSymbolHeight = entitytypethemestyles.getTwodsymbalhigh();
                                        Integer threeDSymbolSize = 0;
                                        if (entitytypethemestyles.getThreedsymbalsize() != null) {
                                            threeDSymbolSize = entitytypethemestyles.getThreedsymbalsize();
                                        }
                                        Integer lineSymbolID = 0;
                                        try {
                                            lineSymbolID = Integer.parseInt(entitytypethemestyles.getTwodlinetypecode());
                                        } catch (Exception le) {
                                            log.error("EntityDesign.sxwu创建失败,lineSymbolID异常，projectid:{}",projectId,le);
                                        }
                                        Integer lineSymbolID3D = 0;
                                        try {
                                            lineSymbolID3D = Integer.parseInt(entitytypethemestyles.getThreedlinetypecode());
                                        } catch (Exception le) {
                                            log.error("EntityDesign.sxwu创建失败,lineSymbolID3D异常，projectid:{}",projectId,le);
                                        }
                                        if (lineSymbolID3D == 0 && lineSymbolID > 0)
                                            lineSymbolID3D = lineSymbolID;
                                        Integer lineWidth = entitytypethemestyles.getTwodlinewidth();
                                        if (lineWidth == null) {
                                            lineWidth = 1;
                                        }
                                        Integer lineWidth3D = entitytypethemestyles.getThreedlinewidth();
                                        if (lineWidth3D == null)
                                            lineWidth3D = lineWidth;
                                        Integer fillSymbolID = 0;
                                        try {
                                            fillSymbolID = Integer.parseInt(entitytypethemestyles.getTwodfilltypecode());
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,fillSymbolID异常，projectid:{}",projectId,se);
                                        }
                                        Integer fillSymbolID3D = 0;
                                        try {
                                            fillSymbolID3D = Integer.parseInt(entitytypethemestyles.getThreedfilltypecode());
                                        } catch (Exception te) {
                                            log.error("EntityDesign.sxwu创建失败,fillSymbolID3D异常，projectid:{}",projectId,te);
                                        }
                                        if (fillSymbolID3D == 0)
                                            fillSymbolID3D = fillSymbolID;
                                        Integer iFillOpacity = entitytypethemestyles.getTwodfillopacity();
                                        if (iFillOpacity == null)
                                            iFillOpacity = 100;
                                        Integer iFillOpacity3D = entitytypethemestyles.getThreedfillopacity();
                                        if (iFillOpacity3D == null)
                                            iFillOpacity3D = iFillOpacity;
                                        String strCaption = entitytypethemestyles.getThemevalue();
                                        if (strCaption == null || strCaption.trim().length() > 0) {
                                            ThemeUniqueItem item = getItem(markerSymbolID, markerSymbolWidth, markerSymbolHeight, lineSymbolID, Double.parseDouble(lineWidth + ""), lineColor, fillSymbolID, fillColor, iFillOpacity, strCaption);
                                            try {
                                                themeUnique.add(item);
                                            } catch (Exception e) {
                                                log.error("EntityDesign.sxwu创建失败,ThemeUniqueItem.add异常，projectid:{}",projectId,e);
                                                //log.info("单值" + strCaption + "对于二维地图来说已存在");
                                            }
                                            Theme3DUniqueItem item3D = get3DItem(threeDMarkerSymbolID, Double.parseDouble(threeDSymbolSize + ""), lineSymbolID3D, Double.parseDouble(lineWidth3D + ""), lineColor3D, fillSymbolID3D, fillColor3D, strCaption);
                                            try {
                                                theme3DUnique.add(item3D);
                                            } catch (Exception e) {
                                                log.error("EntityDesign.sxwu创建失败,Theme3DUniqueItem.add异常，projectid:{}",projectId,e);
                                                //log.info("单值" + strCaption + "对于三维场景来说已存在");
                                            }
                                        }
                                        //}
                                    }

                                }
                            }
                            if (themeUnique != null) {
                                if (datasource.getDatasets().get(strDatasetName) != null) {
                                    Layer layer = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), themeUnique, true);
                                    layer.setCaption(strDatasetAlias);
                                    if (datasetinfo.getTablename().equalsIgnoreCase("center_line_L")) {
                                        layer.setSnapable(true);
                                    } else {
                                        layer.setSnapable(false);
                                    }
                                    blHasAddTheme = true;
                                }
                            }
                            if (theme3DUnique != null) {
                                //Layer3DSettingVector layer3DSetting = new Layer3DSettingVector();
                                //Layer3DDataset layer3DDataset = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName),layer3DSetting,true);
                                //layer3DDataset.setCaption(strDatasetAlias);
                                if (datasource.getDatasets().get(strDatasetName) != null) {
                                    Layer3DDataset layer3DDatasetTheme = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), theme3DUnique, true);
                                    layer3DDatasetTheme.setCaption(strDatasetAlias);
                                    sceneEntity.refresh();
                                    blHasAddTheme = true;
                                }
                            }
                        }
                        if (blHasAddTheme == false) {  //如果没有专题图才设置图层风格
                            for (int k = 0; k < lstStyle.size(); k++) {
                                //第二步：去实体风格定义表里找到图层风格，构造图层，添加到Map里
                                if (lstStyle.get(k).getEntitytypeid().equalsIgnoreCase(strEntityID) && lstStyle.get(k).getDatasetid().equalsIgnoreCase(datasetinfo.getPkid())) {
                                    Boolean blMapHasAdded = false;
                                    for (int i1 = 0; i1 < mapEntity.getLayers().getCount(); i1++) {
                                        if (mapEntity.getLayers().get(i1).getDataset().getName().equalsIgnoreCase(strDatasetName)) {
                                            blMapHasAdded = true;
                                            break;
                                        }
                                    }
                                    Boolean blSceneHasAdded = false;
                                    for (int i1 = 0; i1 < sceneEntity.getLayers().getCount(); i1++) {
                                        if (sceneEntity.getLayers().get(i1).getDataName().equalsIgnoreCase(strDatasetName)) {
                                            blSceneHasAdded = true;
                                            break;
                                        }
                                    }
                                    Entitytypestyles entitytypestyles = lstStyle.get(k);
                                    GeoStyle style = new GeoStyle();
                                    GeoStyle3D style3D = new GeoStyle3D();
                                    //二维点符号大小
                                    if (null != entitytypestyles.getTwodsymbalsize()) {
                                        style.setMarkerSize(new Size2D(entitytypestyles.getTwodsymbalsize(), entitytypestyles.getTwodsymbalsize()));
                                    } else if (null != entitytypestyles.getTwodsymbalwidth()) {
                                        style.setMarkerSize(new Size2D(entitytypestyles.getTwodsymbalwidth(), entitytypestyles.getTwodsymbalhigh()));
                                    } else {
                                        style.setMarkerSize(new Size2D(10, 10));
                                    }
                                    //对于中线桩，固定图标大小为6mm
                                    if (datasetinfo.getTablename().equalsIgnoreCase("center_line_P")) {
                                        style.setMarkerSize(new Size2D(6, 6));
                                    }
                                    //三维点符号大小
                                    if (null != entitytypestyles.getThreedsymbalwidth()) {
                                        style3D.setMarkerSize(entitytypestyles.getThreedsymbalwidth());
                                    }
                                /*if (null != entitytypestyles.getThreedsymbalsize()) {
                                    style3D.setMarkerSize(entitytypestyles.getThreedsymbalsize());
                                }*/
                                    else {
                                        if (datasetinfo.getTablename().toUpperCase().indexOf("_P") > 0) {
                                            style3D.setMarker3D(true);
                                            style3D.setAltitudeMode(AltitudeMode.ABSOLUTE);
                                        } else if (datasetinfo.getTablename().toUpperCase().indexOf("_L") > 0 || datasetinfo.getTablename().toUpperCase().indexOf("_R") > 0) {
                                            style3D.setAltitudeMode(AltitudeMode.RELATIVE_TO_GROUND);
                                        }
                                    }
                                    //二维点符号ID
                                    if (null != entitytypestyles.getTwodsymbalcode() && entitytypestyles.getTwodsymbalcode().trim().length() > 0) {
                                        try {
                                            style.setMarkerSymbolID(Integer.parseInt(entitytypestyles.getTwodsymbalcode()));
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,style.setMarkerSymbolID异常，projectid:{}",projectId,se);
                                            //log.info("设置二维点符号ID失败！");
                                        }
                                    }
                                    //三维点符号ID
                                    if (null != entitytypestyles.getThreedsymbalcode() && entitytypestyles.getThreedsymbalcode().trim().length() > 0) {
                                        try {
                                            style3D.setMarkerSymbolID(Integer.parseInt(entitytypestyles.getThreedsymbalcode()));
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,style3D.setMarkerSymbolID异常，projectid:{}",projectId,se);
                                            //log.info("设置三维点符号ID失败！");
                                        }
                                    } else if (null != entitytypestyles.getTwodsymbalcode() && entitytypestyles.getTwodsymbalcode().trim().length() > 0) {
                                        style3D.setMarkerSymbolID(style.getMarkerSymbolID());   //如果没有设置三维符号ID，就复用二维里的符号ID
                                    }

                                    if (entitytypestyles.getTwodlinecolor() != null && entitytypestyles.getTwodlinecolor().trim().length() > 0) {
                                        try {
                                            Color color = Color.BLACK;
                                            if (entitytypestyles.getTwodlinecolor() != null && entitytypestyles.getTwodlinecolor().toUpperCase().indexOf("0X") > -1) {           //以16进制方式存储的颜色
                                                color = toColorFromString(entitytypestyles.getTwodlinecolor());
                                            } else if (entitytypestyles.getTwodlinecolor() != null && entitytypestyles.getTwodlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                                color = toColorFromRGBA(entitytypestyles.getTwodlinecolor());
                                            }
                                            //二维线色
                                            style.setLineColor(color);
                                        } catch (Exception el) {
                                            log.error("EntityDesign.sxwu创建失败,style.setLineColor异常，projectid:{}",projectId,el);
                                            //log.info("设置图层线色风格失败！");
                                        }
                                    }
                                    if (entitytypestyles.getThreedlinecolor() != null && entitytypestyles.getThreedlinecolor().trim().length() > 0) {
                                        try {
                                            Color color = Color.BLACK;
                                            if (entitytypestyles.getThreedlinecolor() != null && entitytypestyles.getThreedlinecolor().toUpperCase().indexOf("0X") > -1) {          //以16进制方式存储的颜色
                                                color = toColorFromString(entitytypestyles.getTwodlinecolor());
                                            } else if (entitytypestyles.getThreedlinecolor() != null && entitytypestyles.getThreedlinecolor().toLowerCase().indexOf("rgba(") == 0) {  //已rgba()方式存储的颜色
                                                color = toColorFromRGBA(entitytypestyles.getThreedlinecolor());
                                            }
                                            style3D.setLineColor(color);
                                        } catch (Exception e1) {
                                            log.error("EntityDesign.sxwu创建失败,style3D.setLineColor异常，projectid:{}",projectId,e1);

                                        }
                                    } else if (entitytypestyles.getTwodlinecolor() != null && entitytypestyles.getTwodlinecolor().trim().length() > 0) {
                                        //如果没有配置三维线色，就用二维的颜色
                                        style3D.setLineColor(style.getLineColor());
                                    }

                                    if (null != entitytypestyles.getTwodlinewidth() && entitytypestyles.getTwodlinewidth() > 0) {
                                        try {
                                            //二维线宽
                                            style.setLineWidth(Double.parseDouble(entitytypestyles.getTwodlinewidth() + ""));
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,style.setLineWidth异常，projectid:{}",projectId,se);
                                            //log.info("为" + entitytypes.getEntitytypename() + "设置二维线宽失败！");
                                        }
                                    } else {
                                        style.setLineWidth(5);
                                    }
                                    if (null != entitytypestyles.getThreedlinewidth() && entitytypestyles.getThreedlinewidth() > 0) {
                                        try {
                                            //三维线宽
                                            style3D.setLineWidth(Double.parseDouble(entitytypestyles.getThreedlinewidth() + ""));
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,style3D.setLineWidth异常，projectid:{}",projectId,se);
                                            //log.info("为" + entitytypes.getEntitytypename() + "设置三维线宽失败！");
                                        }
                                    } else if (null != entitytypestyles.getTwodlinewidth() && entitytypestyles.getTwodlinewidth() > 0) {
                                        style3D.setLineWidth(style.getLineWidth());
                                    }
                                    if (entitytypestyles.getTwodlinetypecode() != null && entitytypestyles.getTwodlinetypecode().trim().length() > 0) {
                                        try {
                                            Integer iLineTypeCode = Integer.parseInt(entitytypestyles.getTwodlinetypecode());
                                            //二维线型符号
                                            style.setLineSymbolID(iLineTypeCode);
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,style.setLineSymbolID异常，projectid:{}",projectId,se);
                                            //log.info("为" + entitytypes.getEntitytypename() + "设置二维线符号风格失败！");
                                        }
                                    }
                                    if (entitytypestyles.getThreedlinetypecode() != null && entitytypestyles.getThreedlinetypecode().trim().length() > 0) {
                                        try {
                                            Integer iLineTypeCode = Integer.parseInt(entitytypestyles.getThreedlinetypecode());
                                            //三维线型符号
                                            style3D.setLineSymbolID(iLineTypeCode);
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,style3D.setLineSymbolID异常，projectid:{}",projectId,se);
                                            //log.info("为" + entitytypes.getEntitytypename() + "设置三维线符号风格失败！");
                                        }
                                    } else if (entitytypestyles.getTwodlinetypecode() != null && entitytypestyles.getTwodlinetypecode().trim().length() > 0) {
                                        style3D.setLineSymbolID(style.getLineSymbolID());
                                    }
                                    if (entitytypestyles.getTwodfillcolor() != null && entitytypestyles.getTwodfillcolor().trim().length() > 0) {
                                        try {
                                            Color color = Color.BLACK;
                                            if (entitytypestyles.getTwodfillcolor() != null && entitytypestyles.getTwodfillcolor().toUpperCase().indexOf("0X") > -1) {           //以16进制方式存储的颜色
                                                color = toColorFromString(entitytypestyles.getTwodlinecolor());
                                            } else if (entitytypestyles.getTwodfillcolor() != null && entitytypestyles.getTwodfillcolor().toLowerCase().indexOf("rgba(") == 0) {   //已rgba()方式存储的颜色
                                                color = toColorFromRGBA(entitytypestyles.getTwodlinecolor());
                                            }
                                            //二维填充色
                                            style.setFillForeColor(color);
                                        } catch (Exception el) {
                                            log.error("EntityDesign.sxwu创建失败,style.setFillForeColor异常，projectid:{}",projectId,el);
                                            //log.info("为" + entitytypes.getEntitytypename() + "设置二维图层填充色失败！");
                                        }
                                    }
                                    if (entitytypestyles.getThreedfillcolor() != null && entitytypestyles.getThreedfillcolor().trim().length() > 0) {
                                        try {
                                            Color color = Color.BLACK;
                                            if (entitytypestyles.getThreedfillcolor() != null && entitytypestyles.getThreedfillcolor().toUpperCase().indexOf("0X") > -1)           //以16进制方式存储的颜色
                                            {
                                                color = toColorFromString(entitytypestyles.getThreedfillcolor());
                                            } else if (entitytypestyles.getThreedfillcolor() != null && entitytypestyles.getThreedfillcolor().toLowerCase().indexOf("rgba(") == 0)   //已rgba()方式存储的颜色
                                            {
                                                color = toColorFromRGBA(entitytypestyles.getThreedfillcolor());
                                            }
                                            //三维填充色
                                            style3D.setFillForeColor(color);
                                        } catch (Exception el) {
                                            log.error("EntityDesign.sxwu创建失败,style3D.setFillForeColor异常，projectid:{}",projectId,el);
                                            //log.info("为" + entitytypes.getEntitytypename() + "设置三维图层填充色失败！");
                                        }
                                    }

                                    if (entitytypestyles.getTwodfilltypecode() != null && entitytypestyles.getTwodfilltypecode().trim().length() > 0) {
                                        try {
                                            Integer iFillSymbolID = Integer.parseInt(entitytypestyles.getTwodfilltypecode().trim());
                                            //二维面填充风格
                                            style.setFillSymbolID(iFillSymbolID);
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,style.setFillSymbolID异常，projectid:{}",projectId,se);
                                            //log.info("为" + entitytypes.getEntitytypename() + "设置二维符号ID失败！");
                                        }
                                    }
                                    if (entitytypestyles.getThreedfilltypecode() != null && entitytypestyles.getThreedfilltypecode().trim().length() > 0) {
                                        try {
                                            Integer iFillSymbolID = Integer.parseInt(entitytypestyles.getThreedfilltypecode().trim());
                                            //三维面填充风格
                                            style3D.setFillSymbolID(iFillSymbolID);
                                        } catch (Exception e) {
                                            log.error("EntityDesign.sxwu创建失败,style3D.setFillSymbolID异常，projectid:{}",projectId,e);
                                            //log.info("为" + entitytypes.getEntitytypename() + "设置三维符号ID失败！");
                                        }
                                    }
                                    if (entitytypestyles.getTwodfillopacity() != null && entitytypestyles.getTwodfillopacity() > 0) {
                                        try {
                                            Integer iFillOpacityRate = entitytypestyles.getTwodfillopacity();
                                            //二维面透明度
                                            style.setFillOpaqueRate(iFillOpacityRate);
                                            //三维面透明度设置方法没找到，暂未设置
                                        } catch (Exception se) {
                                            log.error("EntityDesign.sxwu创建失败,style.setFillOpaqueRate异常，projectid:{}",projectId,se);
                                            //log.info("为" + entitytypes.getEntitytypename() + "设置二维填充透明度失败！");
                                        }
                                    }
                                    LayerSettingVector layerSettingVector = new LayerSettingVector();
                                    layerSettingVector.setStyle(style);
                                    Layer3DSettingVector layer3DSettingVector = new Layer3DSettingVector();
                                    layer3DSettingVector.setStyle(style3D);

                                    if (datasource.getDatasets().get(strDatasetName) != null) {
                                        //添加图层到二维地图中
                                        if (blMapHasAdded == false) {
                                            Layer layer = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), layerSettingVector, true);
                                            layer.setCaption(strDatasetAlias);
                                            if (datasetinfo.getTablename().equalsIgnoreCase("center_line_L")) {
                                                layer.setSnapable(true);
                                            } else {
                                                layer.setSnapable(false);
                                            }
                                        }
                                        //添加图层到三维场景中
                                        if (blSceneHasAdded == false) {
                                            Layer3DDataset layer3DDataset = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), layer3DSettingVector, true);
                                            layer3DDataset.setCaption(strDatasetAlias);
                                        }
                                    } else {
                                        //logger.error("向地图中添加数据集" + strDatasetName + "对应的图层失败！原因是UDBX中不包含该数据集。");

                                        log.error("EntityDesign.sxwu创建失败,原因是UDBX中不包含该数据集，projectid:{}",projectId);
                                    }
                                    blHasAddStyle = true;
                                    break;  //找到风格记录了就没必要再循环了，节约了执行时间
                                }
                            }
                            if (strDatasetName.toUpperCase().indexOf("_M") > 0) {
                                //如果是三维模型数据集，需要通过如下方式将三维模型数据集添加到场景里
                                Layer3DSettingVector layer3DSettingVector = new Layer3DSettingVector();
                                layer3DSettingVector.getStyle().setAltitudeMode(AltitudeMode.ABSOLUTE);
                                layer3DSettingVector.getStyle().setFillMode(FillMode3D.FILL);
                                layer3DSettingVector.getStyle().setLineWidth(1.0d);
                                if (datasource.getDatasets().get(strDatasetName) != null) {
                                    Layer3D layer3D = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), layer3DSettingVector, true);
                                    if (strDatasetAlias.indexOf('表') == strDatasetAlias.length() - 1)       //数据库中注册的模型数据集名称最后面一个字写成了“表”，如果存在这种情况的话设置图层别名时就把最后一个“表”字去掉
                                        strDatasetAlias = strDatasetAlias.substring(0, strDatasetAlias.indexOf('表'));
                                    layer3D.setCaption(strDatasetAlias);
                                }
                            }
                        }

                        //对于没有通过单值专题图和普通图层添加到地图和场景里的非模型类图层，进行一次补救添加操作
                        int iDatasetType = datasetinfo.getDatasettype();
                        if (!blHasAddTheme && !blHasAddStyle
                                && ((iDatasetType >= 1 && iDatasetType <= 3) || iDatasetType == 5)) {
                            GeoStyle style = new GeoStyle();
                            style.setMarkerSize(new Size2D(10, 10));
                            style.setLineWidth(5);
                            LayerSettingVector layerSettingVector = new LayerSettingVector();
                            layerSettingVector.setStyle(style);
                            Layer layer = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), layerSettingVector, true);
                            layer.setCaption(strDatasetAlias);
                            Layer3DSettingVector layer3DSettingVector = new Layer3DSettingVector();
                            layer3DSettingVector.getStyle().setAltitudeMode(AltitudeMode.ABSOLUTE);
                            layer3DSettingVector.getStyle().setFillMode(FillMode3D.FILL);
                            layer3DSettingVector.getStyle().setLineWidth(1.0d);
                            Layer3D layer3D = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), layer3DSettingVector, true);
                            layer3D.setCaption(strDatasetAlias);
                        }

                        /**
                         * 根据实体类型定义表中的tag_text字段(存储表达式)来生成矩阵标签专题图
                         * tag_text中存储的表达式为多行表达式，格式为：'文本11'||实体名称.字段名1||'文本12'&&'文本21'||实体名称.字段名2||'文本22'&&......
                         * 上述表达式中，多行之间用“&&”分割，同一行的表达式内“||”为字符串连接符
                         */

                        if (!datasetinfo.getDatasettype().equals(EntityDatasetType.TABULAR.getValue())) {
                            //第一步：先从对应实体类型中获取到tag_text表达式
                            String strThemeExpression = entitytypes.getTagtext();
                            if (strThemeExpression == null || strThemeExpression.trim().length() < 1) {
                                continue;
                            }
                            //String strMainEntityName = datasetinfo.getTablename().substring(0, datasetinfo.getTablename().lastIndexOf('_'));
                            String strMainEntityName = datasetinfo.getTablename();
                            try {
                                strMainEntityName = datasetinfo.getTablename().substring(0, datasetinfo.getTablename().lastIndexOf('_'));
                            } catch (Exception e) {
                                log.error("EntityDesign.sxwu创建失败,strMainEntityName异常，projectid:{}",projectId,e);
                            }
                            strThemeExpression = strThemeExpression.replaceAll(strMainEntityName + "\\.", datasetinfo.getTablename() + "\\.");     //将表达式中的实体类型名称替换为实体的某一类数据集名称。因为实体属性表是不能添加到地图里的，更不能用其制作专题图
                            //第二步：用"&&"对表达式进行分割，确定出行数
                            if (!datasetinfo.getDatasettype().equals(EntityDatasetType.MODEL.getValue()) && null != datasource.getDatasets().get(strDatasetName)) {
                            /*
                                标签专题图表达式格式为：<实体名称>.<字段名称>||<实体名称>.<字段名称>......<br><实体名称>.<字段名称>
                                上述表达式中，“||”为连接运算符；如果想用多行来展示标签，则在需要换行的地方用<br>来标识
                                如果表达式中有<br>(即换行符)，就通过矩阵标签专题图来展示；如果没有<br>就通过普通标签专题图来展示
                             */
                                //if(entitytypes.getEntitytypealias().equals("浆砌石护岸"))     //通信线路敷设
                                //    log.info(strThemeExpression);
                                if (strThemeExpression.indexOf("&&") > -1)
                                    strThemeExpression = strThemeExpression.replaceAll("&&", "");
                                if (strThemeExpression.indexOf("''") > -1)
                                    strThemeExpression = strThemeExpression.replaceAll("''", "");
                                while (strThemeExpression.indexOf("||||") > -1) {       //对于四个连续的|，iDesktopX会出错，从而无法展示矩阵标签专题图，需要将其替换成两个|
                                    strThemeExpression = strThemeExpression.replaceAll("[|][|][|][|]", "||");
                                }
                                if (strThemeExpression.startsWith("||"))
                                    strThemeExpression = strThemeExpression.substring(2);
                                if (strThemeExpression.endsWith("||")) ;
                                strThemeExpression = strThemeExpression.substring(0, strThemeExpression.length() - 2);
                                strThemeExpression = strThemeExpression.replaceAll("[|][|]", " ||");
/*                            if(strThemeExpression.startsWith("||"))
                                strThemeExpression=strThemeExpression.substring(2);
                            if(strThemeExpression.endsWith("||"))
                                strThemeExpression=strThemeExpression.substring(0, strThemeExpression.length()-2);*/
                                if (strThemeExpression.indexOf("<br>") > 0) {      //表达式中有<br>就构造矩阵标签专题图
                                    String[] arrExpressions = strThemeExpression.split("<br>");
                                    LabelMatrix labelMatrix = new LabelMatrix(1, arrExpressions.length);
                                    //第三步：构造每行表达式
                                    for (int k = 0; k < arrExpressions.length; k++) {
                                        ThemeLabel themeLabel = new ThemeLabel();
                                        if (arrExpressions[k].indexOf(".") > 0)
                                            arrExpressions[k] = parseLabelExpression(arrExpressions[k]);
                                        themeLabel.setLabelExpression(arrExpressions[k]);
                                        themeLabel.setTextExpression(true);
                                        TextStyle textStyle = new TextStyle();
                                        //字体名称
                                        if (strMapLabelFontName != null && strMapLabelFontName.trim().length() > 0)
                                            textStyle.setFontName(strMapLabelFontName);
                                        //字体颜色
                                        try {
                                            Field field = Class.forName("java.awt.Color").getField(strMapLabelFontColor);
                                            Color color = (Color) field.get(null);
                                            textStyle.setForeColor(color);
                                        } catch (Exception e) {
                                            log.warn("EntityDesign.sxwu创建失败,textStyle.setForeColor异常，projectid:{}",projectId,e);
                                            textStyle.setForeColor(Color.WHITE);
                                        }
                                        //显示轮廓线
                                        textStyle.setOutline(true);
                                        //字体大小
                                        try {
                                            textStyle.setFontWidth(dblMapLabelFontSize);
                                        } catch (Exception e) {
                                            log.error("EntityDesign.sxwu创建失败,textStyle.setFontWidth异常，projectid:{}",projectId,e);
                                        }
                                        //字体轮廓线宽度
                                        try {
                                            textStyle.setOutlineWidth(iMapLabelOutLineWidth);
                                        } catch (Exception e) {
                                            log.error("EntityDesign.sxwu创建失败,textStyle.setOutlineWidth异常，projectid:{}",projectId,e);
                                        }
                                        //字体背景色(轮廓色)
                                        try {
                                            Field field = Class.forName("java.awt.Color").getField(strMapLabelBorderColor);
                                            Color color = (Color) field.get(null);
                                            textStyle.setBackColor(color);
                                        } catch (Exception e) {
                                            log.error("EntityDesign.sxwu创建失败,textStyle.setBackColor异常，projectid:{}",projectId,e);
                                        }
                                        //字高
                                        try {
                                            textStyle.setFontHeight(dblMapLabelFontHeiht / 10);
                                        } catch (Exception e) {
                                            log.warn("EntityDesign.sxwu创建失败,textStyle.setFontHeight异常，projectid:{}",projectId,e);
                                            textStyle.setFontHeight(4.0d);
                                        }
                                        textStyle.setSizeFixed(true);
                                        themeLabel.setUniformStyle(textStyle);
                                        labelMatrix.set(0, k, themeLabel);
                                    }
                                    //二维地图添加矩阵标签专题图
                                    ThemeLabel themeLabelMatrix = new ThemeLabel();
                                    themeLabelMatrix.setLabels(labelMatrix);
                                    try {
                                        Layer layerTheme = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), themeLabelMatrix, true);
                                        layerTheme.setCaption(strDatasetAlias + "标签");
                                        layerTheme.setSnapable(false);
                                        layerTheme.setOverlapGeometryEnabled(true);     //显示小对象标签
                                    } catch (Exception e) {
                                        log.error("EntityDesign.sxwu创建失败,layerTheme.setOverlapGeometryEnabled异常，projectid:{}",projectId,e);
                                        //log.info("为数据集" + strDatasetName + "添加二维矩阵标签专题图失败！");
                                    }
                                } else {      //表达式中没有<br>就是用普通标签专题图
                                    ThemeLabel themeLabel = new ThemeLabel();
                                    if (strThemeExpression.indexOf(".") > 0)
                                        strThemeExpression = parseLabelExpression(strThemeExpression);
                                    themeLabel.setLabelExpression(strThemeExpression);
                                    themeLabel.setTextExpression(true);
                                    TextStyle textStyle = new TextStyle();
                                    //字体名称
                                    if (strMapLabelFontName != null && strMapLabelFontName.trim().length() > 0)
                                        textStyle.setFontName(strMapLabelFontName);
                                    //字体颜色
                                    try {
                                        Field field = Class.forName("java.awt.Color").getField(strMapLabelFontColor);
                                        Color color = (Color) field.get(null);
                                        textStyle.setForeColor(color);
                                    } catch (Exception e) {
                                        log.warn("EntityDesign.sxwu创建失败,textStyle.setForeColor异常，projectid:{}",projectId,e);
                                        textStyle.setForeColor(Color.WHITE);
                                    }
                                    //显示轮廓线
                                    textStyle.setOutline(true);
                                    //字体大小
                                    try {
                                        textStyle.setFontWidth(dblMapLabelFontSize);
                                    } catch (Exception e) {
                                        log.error("EntityDesign.sxwu创建失败,textStyle.setFontWidth异常，projectid:{}",projectId,e);
                                    }
                                    //字体轮廓线宽度
                                    try {
                                        textStyle.setOutlineWidth(iMapLabelOutLineWidth);
                                    } catch (Exception e) {
                                        log.error("EntityDesign.sxwu创建失败,textStyle.setOutlineWidth异常，projectid:{}",projectId,e);
                                    }
                                    //字体背景色(轮廓色)
                                    try {
                                        Field field = Class.forName("java.awt.Color").getField(strMapLabelBorderColor);
                                        Color color = (Color) field.get(null);
                                        textStyle.setBackColor(color);
                                    } catch (Exception e) {
                                        log.error("EntityDesign.sxwu创建失败,textStyle.setBackColor异常，projectid:{}",projectId,e);
                                    }
                                    //字高
                                    try {
                                        textStyle.setFontHeight(dblMapLabelFontHeiht / 10);
                                    } catch (Exception e) {
                                        log.warn("EntityDesign.sxwu创建失败,textStyle.setFontHeight异常，projectid:{}",projectId,e);
                                        textStyle.setFontHeight(4.0d);
                                    }
                                    textStyle.setSizeFixed(true);
                                    themeLabel.setUniformStyle(textStyle);
                                    Layer layerThemeLabel = mapEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), themeLabel, true);
                                    layerThemeLabel.setCaption(strDatasetAlias + "标签");
                                    layerThemeLabel.setSnapable(false);
                                }
                            }

                            //三维场景中不支持矩阵标签专题图，添加普通标签专题图
                            Theme3DLabel theme3DLabel = new Theme3DLabel();
                            strThemeExpression = strThemeExpression.replaceAll("&&", "||','||");    //由于“&&”是特殊字符，替换为','，否则无法创建三维标签专题图
                            strThemeExpression = strThemeExpression.replaceAll("<br>", "||','||");  //将<br>替换为普通的“,”,因为三维场景不支持矩阵标签专题图
                            theme3DLabel.setLabelExpression(strThemeExpression);
                            TextStyle textStyle = new TextStyle();
                            textStyle.setFontHeight(8d);
                            //textStyle.setFontWidth(40d);
                            textStyle.setForeColor(Color.YELLOW);
                            textStyle.setSizeFixed(true);
                            theme3DLabel.setUniformStyle(textStyle);
                            Layer3DDataset layer3D = null;
                            try {
                                layer3D = sceneEntity.getLayers().add(datasource.getDatasets().get(strDatasetName), theme3DLabel, true);
                                layer3D.setCaption(strDatasetAlias + "标签");

                                Layer3DSettingVector layer3DSettingVector = new Layer3DSettingVector();
                                GeoStyle3D geoStyle3D = new GeoStyle3D();
                                geoStyle3D.setAltitudeMode(AltitudeMode.CLAMP_TO_GROUND);
                                layer3DSettingVector.setStyle(geoStyle3D);
                                layer3D.setAdditionalSetting(layer3DSettingVector);
                                //layer3D.setMaxVisibleAltitude(1000d);     //暂时不要设标签最大可见高度，让用户自己去控制
                                layer3D.updateData();
                            } catch (Exception e) {
                                log.error("EntityDesign.sxwu创建失败,layer3D.updateData异常，projectid:{}",projectId,e);
                                //log.info("为数据集" + strDatasetName + "添加三维标签专题图失败！");
                            }

                        }
                    }
                }
            }

            //防止因为没有数据而导致地图加载后范围不对的问题
            com.supermap.data.Rectangle2D rectangle2D = new Rectangle2D();
            rectangle2D.setBottom(-90d);
            rectangle2D.setLeft(-180d);
            rectangle2D.setRight(180d);
            rectangle2D.setTop(90d);
            mapEntity.setViewBounds(rectangle2D);

            PrjCoordSys prjCoordSys = new PrjCoordSys(4490);

            GeoRectangle geoRectangle = new GeoRectangle();
            geoRectangle.setBounds(rectangle2D);
            PrjCoordSys sourcePrjCoordSys = new PrjCoordSys();
            sourcePrjCoordSys.setEPSGCode(4490);
            CoordSysTranslator.convert(geoRectangle, sourcePrjCoordSys, prjCoordSys, new CoordSysTransParameter(), CoordSysTransMethod.China_2D_7P);

//        _redisCommonUtil.setKey(strKey, "正在对地图图层进行排序");

            //对地图和场景中的图层进行排序，将注记图层放到最上面，下面是点图层，再下面是面图层
            //对二维地图中的图层进行排序
            //先插入位于中间部位的线图层
//        lBeginTime = System.currentTimeMillis();
            ArrayList<Layer> lstSortedLayers = new ArrayList<Layer>();
            for (int i = 0; i < mapEntity.getLayers().getCount(); i++) {
                Layer layer = mapEntity.getLayers().get(i);
                if ((layer.getDataset().getType().equals(DatasetType.LINE) || layer.getDataset().getType().equals(DatasetType.LINE3D)) && layer.getName().indexOf("#1") < 0) {
                    lstSortedLayers.add(layer);
                }
            }
            //再往头部插入点图层，尾部插入面图层
            for (int i = 0; i < mapEntity.getLayers().getCount(); i++) {
                Layer layer = mapEntity.getLayers().get(i);
                if ((layer.getDataset().getType().equals(DatasetType.POINT) || layer.getDataset().getType().equals(DatasetType.POINT3D)) && layer.getName().indexOf("#1") < 0) {
                    lstSortedLayers.add(0, layer);
                } else if ((layer.getDataset().getType().equals(DatasetType.REGION) || layer.getDataset().getType().equals(DatasetType.REGION3D)) && layer.getName().indexOf("#1") < 0) {
                    lstSortedLayers.add(lstSortedLayers.size(), layer);
                } else {
                    lstSortedLayers.add(lstSortedLayers.size() - 1, layer);
                }
            }
            //最后往头部插入注记图层
            for (int i = 0; i < mapEntity.getLayers().getCount(); i++) {
                Layer layer = mapEntity.getLayers().get(i);
                if (layer.getName().indexOf("#1") > -1) {
                    lstSortedLayers.add(0, layer);
                }
            }
            Map mapEntityNew = new Map(workspace);
            mapEntityNew.setPrjCoordSys(mapEntity.getPrjCoordSys());
            mapEntityNew.setName(mapEntity.getName());
            for (int i = 0; i < lstSortedLayers.size(); i++) {
                mapEntityNew.getLayers().add(lstSortedLayers.get(i));
            }
            mapEntityNew.setViewBounds(geoRectangle.getBounds());

            mapEntityNew.setLineAntialias(true);    //开启线型反走样
            mapEntityNew.setTextAntialias(true);    //开启文本反走样

            //保存地图、场景及工作空间
            workspace.getMaps().remove(mapEntity.getName());
            workspace.getMaps().add(mapEntityNew.getName(), mapEntityNew.toXML());
            workspace.getScenes().remove(sceneEntity.getName());
            workspace.getScenes().add(sceneEntity.getName(), sceneEntity.toXML());

            Boolean blSaveResult = workspace.save();

            //将db-design文件夹下的*.ult布局文件添加到工作空间中
            File fileULTs = new File(PathUtils.getDesignPath());
            File[] files = fileULTs.listFiles();
            for (int i = 0; i < files.length; i++) {
                if (files[i].getName().endsWith(".ult")) {
                    MapLayout mapLayout = new MapLayout();
                    mapLayout.setWorkspace(workspace);
                    mapLayout.loadTemplate(PathUtils.getDesignPath() + File.separator + files[i].getName());
                    workspace.getLayouts().add(files[i].getName().substring(0, files[i].getName().lastIndexOf(".")), mapLayout.toXML());
                }
            }
            workspace.save();

            datasource.close();
            workspace.close();

//        _redisCommonUtil.setKey(strKey, "工作空间生成成功。路径为：" + workspace.getConnectionInfo().getServer());
            if (blSaveResult) {
                mapResult.put("result", "success");
                mapResult.put("workspacefilepath", workspaceConnectionInfo.getServer());
                return mapResult;
                //return "{\"result\":\"success\",\"workspacefilepath\":\"" + workspace.getConnectionInfo().getServer() + "\"}";
            }
            mapResult.put("result", "failure");
            mapResult.put("exceptioncode", Errors.WORKSPACESAVEFAILURE.getName());
            log.error("EntityDesign.sxwu创建失败，projectid:{}",projectId);
            _redisCommonUtil.delete(projectId);
        } finally {
            _redisCommonUtil.delete(projectId);
            lock.unlock();
        }
        return mapResult;
        //return "{\"result\":\"failure\",\"exceptioncode\"" + Errors.WORKSPACESAVEFAILURE.getName() + "\"}";
    }

    @Override
    public java.util.Map<String, Object> rebuildProjectWorkspaceSeedDistributed(String projectId, String strTaskKey) {
        java.util.Map<String, Object> mapResult = new HashMap<String, Object>();
        List<String> resultList = new ArrayList<>();
        try {
            lock.lock();
            _redisCommonUtil.setKey(projectId, "{}");
            List<Entitytypestyles> lstStyle = iEntitytypestylesService.list(projectId);
            if (lstStyle == null || lstStyle.size() < 1) {
                mapResult.put("result", "failure");
                mapResult.put("exceptioncode", Errors.HASNOLAYERSTYLES.getName());
                mapResult.put("projectid", projectId);
                mapResult.put("message", resultList);
                resultList.add("生成工作空间失败。");
                return mapResult;
            }
            // 查询专题图风格
            List<Entitytypethemestyles> lstThemeStyle = iEntitytypethemestylesService.list(projectId);
            List<Entitytypes> lstEntityTypes = iEntitytypesService.getListByProjectId(projectId, "order_index", "desc");
            List<Datasetinfo> lstDatasetInfo = iDatasetinfoService.list(projectId);
            if (lstDatasetInfo == null || lstDatasetInfo.size() < 1) {
//            _redisCommonUtil.setKey(strKey, "没有找到注册的数据集信息");
                mapResult.put("result", "failure");
                mapResult.put("exceptioncode", Errors.HASNODATASETS.getName());
                mapResult.put("projectid", projectId);
                mapResult.put("message", resultList);
                resultList.add("生成工作空间失败。");
                return mapResult;
            }
            String strMainDatasetId = null;         //存储当前实体数据集对应的主数据集ID
            List<Datasetfield> lstDatasetFields = iDatasetfieldService.listByProject(projectId);
            StringBuilder strbMsg = new StringBuilder();
            StringBuilder strbEntityTypes = new StringBuilder("\"entitytypes\":[");
            StringBuilder strbDatasetInfos = new StringBuilder("\"datasetinfos\":[");
            StringBuilder strbDatasetFieldsInfos = new StringBuilder("\"datasetfieldsinfos\":[");
            StringBuilder strbThemeStyles = new StringBuilder("\"themestyles\":[");
            StringBuilder strbLayerStyles = new StringBuilder("\"layerstyles\":[");
            for (int i = 0; i < lstEntityTypes.size(); i++) {
                Entitytypes entitytypes = lstEntityTypes.get(i);
                strbEntityTypes.append("{\"pkid\":\"" + entitytypes.getPkid() + "\",");
                strbEntityTypes.append("\"tagtext\":\"" + (entitytypes.getTagtext() != null ? entitytypes.getTagtext() : "") + "\"},");
            }
            if (lstEntityTypes.size() > 0) {
                strbEntityTypes = strbEntityTypes.deleteCharAt(strbEntityTypes.length() - 1);
            }
            strbEntityTypes.append("]");
            for (int i = 0; i < lstDatasetInfo.size(); i++) {
                Datasetinfo datasetinfo = lstDatasetInfo.get(i);
                strbDatasetInfos.append("{\"pkid\":\"" + datasetinfo.getPkid() + "\",");
                strbDatasetInfos.append("\"entitytypeid\":\"" + (datasetinfo.getEntitytypeid() != null ? datasetinfo.getEntitytypeid() : "") + "\",");
                strbDatasetInfos.append("\"tablename\":\"" + (datasetinfo.getTablename() != null ? datasetinfo.getTablename() : "") + "\",");
                strbDatasetInfos.append("\"datasetalias\":\"" + (datasetinfo.getDatasetalias() != null ? datasetinfo.getDatasetalias() : "") + "\",");
                strbDatasetInfos.append("\"datasettype\":" + (datasetinfo.getDatasettype() != null ? datasetinfo.getDatasettype() : "-1") + "},");
            }
            if (lstDatasetInfo.size() > 0)
                strbDatasetInfos = strbDatasetInfos.deleteCharAt(strbDatasetInfos.length() - 1);
            strbDatasetInfos.append("]");
            for (int i = 0; i < lstDatasetFields.size(); i++) {
                Datasetfield datasetfield = lstDatasetFields.get(i);
                strbDatasetFieldsInfos.append("{\"pkid\":\"" + datasetfield.getPkid() + "\",");
                strbDatasetFieldsInfos.append("\"fieldname\":\"" + (datasetfield.getFieldname() != null ? datasetfield.getFieldname() : "") + "\"},");
            }
            if (lstDatasetFields.size() > 0)
                strbDatasetFieldsInfos = strbDatasetFieldsInfos.deleteCharAt(strbDatasetFieldsInfos.length() - 1);
            strbDatasetFieldsInfos.append("]");
            for (int i = 0; i < lstThemeStyle.size(); i++) {
                Entitytypethemestyles entitytypethemestyles = lstThemeStyle.get(i);
                strbThemeStyles.append("{\"pkid\":\"" + entitytypethemestyles.getPkid() + "\",");
                strbThemeStyles.append("\"entitytypeid\":\"" + (entitytypethemestyles.getEntitytypeid() != null ? entitytypethemestyles.getEntitytypeid() : "") + "\",");
                strbThemeStyles.append("\"datasetid\":\"" + (entitytypethemestyles.getDatasetid() != null ? entitytypethemestyles.getDatasetid() : "") + "\",");
                strbThemeStyles.append("\"themefieldid\":\"" + (entitytypethemestyles.getThemefieldid() != null ? entitytypethemestyles.getThemefieldid() : "") + "\",");
                strbThemeStyles.append("\"twodlinecolor\":\"" + (entitytypethemestyles.getTwodlinecolor() != null ? entitytypethemestyles.getTwodlinecolor() : "") + "\",");
                strbThemeStyles.append("\"threedlinecolor\":\"" + (entitytypethemestyles.getThreedlinecolor() != null ? entitytypethemestyles.getThreedlinecolor() : "") + "\",");
                strbThemeStyles.append("\"twodsymbalcode\":" + (entitytypethemestyles.getTwodsymbalcode() != null ? entitytypethemestyles.getTwodsymbalcode() : "0") + ",");
                strbThemeStyles.append("\"twodsymbalsize\":" + (entitytypethemestyles.getTwodsymbalsize() != null ? entitytypethemestyles.getTwodsymbalsize() : "-1") + ",");
                strbThemeStyles.append("\"twosymbalwidth\":" + (entitytypethemestyles.getTwodsymbalwidth() != null ? entitytypethemestyles.getTwodsymbalwidth() : "-1") + ",");
                strbThemeStyles.append("\"twosymbalheight\":" + (entitytypethemestyles.getTwodsymbalhigh() != null ? entitytypethemestyles.getTwodsymbalhigh() : "-1") + ",");
                strbThemeStyles.append("\"threedsymbalcode\":" + (entitytypethemestyles.getThreedsymbalcode() != null ? entitytypethemestyles.getThreedsymbalcode() : "0") + ",");
                strbThemeStyles.append("\"threedsymbalsize\":" + (entitytypethemestyles.getThreedsymbalsize() != null ? entitytypethemestyles.getThreedsymbalsize() : "-1") + ",");
                strbThemeStyles.append("\"threedsymbalwidth\":" + (entitytypethemestyles.getThreedsymbalwidth() != null ? entitytypethemestyles.getThreedsymbalwidth() : "-1") + ",");
                strbThemeStyles.append("\"threedsymbalheight\":" + (entitytypethemestyles.getThreedsymbalhigh() != null ? entitytypethemestyles.getThreedsymbalhigh() : "-1") + ",");
                strbThemeStyles.append("\"twodlinetypecode\":" + (entitytypethemestyles.getTwodlinetypecode() != null ? entitytypethemestyles.getTwodlinetypecode() : "0") + ",");
                strbThemeStyles.append("\"threedlinetypecode\":" + (entitytypethemestyles.getThreedlinetypecode() != null ? entitytypethemestyles.getThreedlinetypecode() : "0") + ",");
                strbThemeStyles.append("\"twodlinewidth\":" + (entitytypethemestyles.getTwodlinewidth() != null ? entitytypethemestyles.getTwodlinewidth() : "0") + ",");
                strbThemeStyles.append("\"threedlinewidth\":" + (entitytypethemestyles.getThreedlinewidth() != null ? entitytypethemestyles.getThreedlinewidth() : "0") + ",");
                strbThemeStyles.append("\"twodfilltypecode\":" + (entitytypethemestyles.getTwodfilltypecode() != null ? entitytypethemestyles.getTwodfilltypecode() : "-1") + ",");
                strbThemeStyles.append("\"threedfilltypecode\":" + (entitytypethemestyles.getThreedfilltypecode() != null ? entitytypethemestyles.getThreedfilltypecode() : "-1") + ",");
                strbThemeStyles.append("\"twodfillopacity\":" + (entitytypethemestyles.getTwodfillopacity() != null ? entitytypethemestyles.getTwodfillopacity() : "-1") + ",");
                strbThemeStyles.append("\"threedfillopacity\":" + (entitytypethemestyles.getThreedfillopacity() != null ? entitytypethemestyles.getThreedfillopacity() : "-1") + ",");
                strbThemeStyles.append("\"themevalue\":\"" + (entitytypethemestyles.getThemevalue() != null ? entitytypethemestyles.getThemevalue() : "") + "\"},");
            }
            if (lstThemeStyle.size() > 0)
                strbThemeStyles = strbThemeStyles.deleteCharAt(strbThemeStyles.length() - 1);
            strbThemeStyles.append("]");
            for (int i = 0; i < lstStyle.size(); i++) {
                Entitytypestyles entitytypestyles = lstStyle.get(i);
                strbLayerStyles.append("{\"pkid\":\"" + entitytypestyles.getPkid() + "\",");
                strbLayerStyles.append("\"entitytypeid\":\"" + (entitytypestyles.getEntitytypeid() != null ? entitytypestyles.getEntitytypeid() : "") + "\",");
                strbLayerStyles.append("\"datasetid\":\"" + (entitytypestyles.getDatasetid() != null ? entitytypestyles.getDatasetid() : "") + "\",");
                strbLayerStyles.append("\"twodlinecolor\":\"" + (entitytypestyles.getTwodlinecolor() != null ? entitytypestyles.getTwodlinecolor() : "") + "\",");
                strbLayerStyles.append("\"threedlinecolor\":\"" + (entitytypestyles.getThreedlinecolor() != null ? entitytypestyles.getThreedlinecolor() : "") + "\",");
                strbLayerStyles.append("\"twodsymbalcode\":" + (entitytypestyles.getTwodsymbalcode() != null ? entitytypestyles.getTwodsymbalcode() : "0") + ",");
                strbLayerStyles.append("\"twodsymbalsize\":" + (entitytypestyles.getTwodsymbalsize() != null ? entitytypestyles.getTwodsymbalsize() : "-1") + ",");
                strbLayerStyles.append("\"twosymbalwidth\":" + (entitytypestyles.getTwodsymbalwidth() != null ? entitytypestyles.getTwodsymbalwidth() : "-1") + ",");
                strbLayerStyles.append("\"twosymbalheight\":" + (entitytypestyles.getTwodsymbalhigh() != null ? entitytypestyles.getTwodsymbalhigh() : "-1") + ",");
                strbLayerStyles.append("\"threedsymbalcode\":" + (entitytypestyles.getThreedsymbalcode() != null ? entitytypestyles.getThreedsymbalcode() : "-1") + ",");
                strbLayerStyles.append("\"threedsymbalsize\":" + (entitytypestyles.getThreedsymbalsize() != null ? entitytypestyles.getThreedsymbalsize() : "-1") + ",");
                strbLayerStyles.append("\"threedsymbalwidth\":" + (entitytypestyles.getThreedsymbalwidth() != null ? entitytypestyles.getThreedsymbalwidth() : "-1") + ",");
                strbLayerStyles.append("\"threedsymbalheight\":" + (entitytypestyles.getThreedsymbalhigh() != null ? entitytypestyles.getThreedsymbalhigh() : "-1") + ",");
                strbLayerStyles.append("\"twodlinetypecode\":" + (entitytypestyles.getTwodlinetypecode() != null ? entitytypestyles.getTwodlinetypecode() : "0") + ",");
                strbLayerStyles.append("\"threedlinetypecode\":" + (entitytypestyles.getThreedlinetypecode() != null ? entitytypestyles.getThreedlinetypecode() : "0") + ",");
                strbLayerStyles.append("\"twodlinewidth\":" + (entitytypestyles.getTwodlinewidth() != null ? entitytypestyles.getTwodlinewidth() : "0") + ",");
                strbLayerStyles.append("\"threedlinewidth\":" + (entitytypestyles.getThreedlinewidth() != null ? entitytypestyles.getThreedlinewidth() : "0") + ",");
                strbLayerStyles.append("\"twodfilltypecode\":\"" + (entitytypestyles.getTwodfilltypecode() != null ? entitytypestyles.getTwodfilltypecode() : "") + "\",");
                strbLayerStyles.append("\"threedfilltypecode\":\"" + (entitytypestyles.getThreedfilltypecode() != null ? entitytypestyles.getThreedfilltypecode() : "") + "\",");
                strbLayerStyles.append("\"twodfillopacity\":" + (entitytypestyles.getTwodfillopacity() != null ? entitytypestyles.getTwodfillopacity() : "-1") + ",");
                strbLayerStyles.append("\"threedfillopacity\":" + (entitytypestyles.getThreedfillopacity() != null ? entitytypestyles.getThreedfillopacity() : "-1") + ",");
                strbLayerStyles.append("\"twodfillcolor\":\"" + (entitytypestyles.getTwodfillcolor() != null ? entitytypestyles.getTwodfillcolor() : "") + "\",");
                strbLayerStyles.append("\"threedfillcolor\":\"" + (entitytypestyles.getThreedfillcolor() != null ? entitytypestyles.getThreedfillcolor() : "") + "\"},");
            }
            if (lstStyle.size() > 0)
                strbLayerStyles = strbLayerStyles.deleteCharAt(strbLayerStyles.length() - 1);
            strbLayerStyles.append("]");
            strbMsg.append("{\"command\":\"buildprojectworkspace\",");
            strbMsg.append("\"projectid\":\"" + projectId + "\",");
            strbMsg.append("\"templatepath\":\"" + PathUtils.getDesignPath().replaceAll("\\\\", "\\\\\\\\") + "\",");
            strbMsg.append("\"taskkey\":\"" + strTaskKey + "\",");
            strbMsg.append(strbEntityTypes).append(",");
            strbMsg.append(strbDatasetInfos).append(",");
            strbMsg.append(strbDatasetFieldsInfos).append(",");
            strbMsg.append(strbThemeStyles).append(",");
            strbMsg.append(strbLayerStyles);
            strbMsg.append("}");
//        _redisCommonUtil.sendMessage("buildws", strbMsg.toString());
            //_redisCommonUtil.setKeyAndTime(projectId, strbMsg.toString(), 10, TimeUnit.HOURS);
            try {
                _redisCommonUtil.setKeyAndTime(projectId, strbMsg.toString(), 10, TimeUnit.HOURS);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
/*        if(null!=strRunMode&&strRunMode.equalsIgnoreCase("debug")) {
            mapResult.put("result", "success");
            System.out.println(strbMsg);
            return mapResult;
        }*/
            //System.out.println(strbMsg.toString());
/*------该方式采用异步多线程的方式去生成工作空间，也就是向多线程处理程序发送socket消息，多线程处理程序处理完毕后返回处理结果-----
        经测试该方式只能支持2个线程，再多就崩溃了
        Socket socket = new Socket();
        try {
            socket = new Socket(strGISDataToolIP, iGISDataToolPort);
        } catch (IOException e) {
            mapResult.put("result", "failure");
            mapResult.put("exceptioncode", Errors.SOCKETCOMMUNICATEFAILURE.getName());
            return mapResult;
        }
        String strReceivedMsg;
        try {
            OutputStreamWriter out = new OutputStreamWriter(socket.getOutputStream(), StandardCharsets.UTF_8);
            PrintWriter writer = new PrintWriter(out, true);
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(socket.getInputStream(), StandardCharsets.UTF_8));
            writer.println(strbMsg.toString());
            writer.flush();// 关闭writer写入流。
            strReceivedMsg = reader.readLine();
            reader.close();
            writer.close();
            socket.close();
        } catch (Exception e) {
            //return resultPath;
            mapResult.put("result", "failure");
            mapResult.put("exceptioncode", Errors.SOCKETCOMMUNICATEFAILURE.getName());
            return mapResult;
        }
        JSONObject jsonObjectReceiveMsg = new JSONObject();
        jsonObjectReceiveMsg = JSONObject.parseObject(strReceivedMsg);
        mapResult.put("result", jsonObjectReceiveMsg.getString("result"));
        mapResult.put("workspacefilepath", jsonObjectReceiveMsg.getString("workspacepath"));
--------*/
            try {
                ApplicationHome home = new ApplicationHome(WorkspaceSeedsServiceImpl.class);
                File jarFile = home.getSource();

                // 创建一个运行JAR文件的命令行
                String command = (strJavaPath != null && strJavaPath.trim().length() > 0 ? (strJavaPath.trim() + File.separator) : "") + "java -jar ";
                if (strExternalJARPath != null && strExternalJARPath.trim().length() > 0)
                    command += strExternalJARPath;
                else {
                    if (System.getProperty("os.name").toLowerCase().indexOf("linux") > -1) {
                        command += jarFile.getParent();
                    } else if (System.getProperty("os.name").toLowerCase().indexOf("windows") > -1) {
                        command += PathUtils.getPath();
                    }
                }
                command += " " + projectId;

                // 执行命令行
                Process process = Runtime.getRuntime().exec(command);
                //★★★★★★★★★★★读取错误流和正常流的输入，否则会阻塞，不能正确获得结果★★★★★★★★★★
                InputStream stderr = process.getErrorStream(); //获取标准错误输出流
                getInputData(stderr, true);
                InputStream inpbuildtar = process.getInputStream();//获取标准输出流
                getInputData(inpbuildtar, false);
                boolean isProcessFinsh = process.waitFor(20, TimeUnit.SECONDS);
                if (!isProcessFinsh) {
                    log.error("EntitiesAchivments.udbx,重新创建中,外部程序20秒未执行完毕，ProjectWorkspaceSeed,创建超时，taskkey:{}，projectid:{}",strTaskKey,projectId);

                    return null;
                }
            } catch (Exception e) {
                log.error("EntitiesAchivments.udbx,重新创建中，ProjectWorkspaceSeed,创建异常，taskkey:{}，projectid:{}",strTaskKey,projectId,e);
                mapResult.put("result", "failure");
                resultList.add("生成工作空间失败。");
                e.printStackTrace();
            }
            if(resultList!=null && resultList.size()>0) {
                mapResult.put("message", resultList);
            }
            mapResult.put("result", "success");
        } finally {
            lock.unlock();
        }
        return mapResult;
    }

    /**
     * 获取单值专题图子项
     * the items to the object of themeunique
     */
    private ThemeUniqueItem getItem(Integer markerSymbolID, Integer markerSymbolWidth, Integer markerSymbolHeight, Integer lineSymbolID, Double lineWidth, Color lineColor, Integer fillSymbolID, Color foreColor, Integer opaqueRate, String caption) {
        if(caption==null||caption.trim().length()<1) {
            return null;
        }
        // 设置子项属性
        // Set the items to the object of themeunique
        ThemeUniqueItem item = new ThemeUniqueItem();
        item.setUnique(caption);
        item.setCaption(caption);
        item.setVisible(true);

        // 设置子项风格
        // Set the style of the items
        GeoStyle geostyle = new GeoStyle();
        if (markerSymbolID != null && markerSymbolID > 0) {
            geostyle.setMarkerSymbolID(markerSymbolID);
        }
        if (markerSymbolWidth != null && markerSymbolWidth > 0 && markerSymbolHeight!=null&&markerSymbolHeight>0) {
            geostyle.setMarkerSize(new Size2D(markerSymbolWidth, markerSymbolHeight));
        }else {
            geostyle.setMarkerSize(new Size2D(7, 7));
        }
        if (foreColor != null) {
            geostyle.setFillForeColor(foreColor);
        }
        if (lineColor != null) {
            geostyle.setLineColor(lineColor);
        }
        if (lineWidth != null && lineWidth > 0) {
            geostyle.setLineWidth(lineWidth);
        }else {
            geostyle.setLineWidth(5);
        }
        if (opaqueRate != null && opaqueRate > 0) {
            geostyle.setFillOpaqueRate(opaqueRate);
        }
        if (lineSymbolID != null && lineSymbolID > 0) {
            geostyle.setLineSymbolID(lineSymbolID);
        }
        if (fillSymbolID != null && fillSymbolID > 0) {
            geostyle.setFillSymbolID(fillSymbolID);
        }
        item.setStyle(geostyle);
        return item;
    }

    private Theme3DUniqueItem get3DItem(Integer symbolID, Double symbolSize, Integer lineSymbolID, Double lineWidth, Color lineColor, Integer fillSymbolID, Color foreColor, String caption) {
        if(caption==null||caption.trim().length()<1) {
            return null;
        }
        Theme3DUniqueItem theme3DUniqueItem = new Theme3DUniqueItem();
        theme3DUniqueItem.setUnique(caption);
        theme3DUniqueItem.setCaption(caption);
        theme3DUniqueItem.setVisible(true);

        GeoStyle3D style3D = new GeoStyle3D();
        if (symbolID != null && symbolID > 0) {
            style3D.setMarkerSymbolID(symbolID);
            style3D.setAltitudeMode(AltitudeMode.ABSOLUTE);
            style3D.setMarker3D(true);
        }
        if (symbolSize != null && symbolSize > 0) {
//            style3D.setMarkerSize(symbolSize*2.0);          //如果不乘以2，设置后会自动将尺寸减半。所以在设置时乘以2才能保持原有大小
//            style3D.setMarker3DScaleX(8d);                  //如果不进行倍数放大，原始尺寸太小看不见。暂时放大8倍，这样才能看得见
//            style3D.setMarker3DScaleY(8d);
//            style3D.setMarker3DScaleZ(8d);
        }
        if (lineSymbolID != null && lineSymbolID > 0) {
            style3D.setLineSymbolID(lineSymbolID);
        }
        if (lineWidth != null && lineWidth > 0) {
            style3D.setLineWidth(lineWidth);
        }
        if (lineColor != null) {
            style3D.setLineColor(lineColor);
        }
        if (fillSymbolID != null && fillSymbolID > 0) {
            style3D.setFillSymbolID(fillSymbolID);
        }
        if (foreColor != null) {
            style3D.setFillForeColor(foreColor);
        }

        theme3DUniqueItem.setStyle(style3D);
        return theme3DUniqueItem;
    }

    private File compressedFileToZip(String name, List<String> filePaths) {
        String zipName = name.concat(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))).concat(".zip");
        String fileZipPath = System.getProperty("user.dir").concat("/").concat(zipName);
        OutputStream os = null;
        ZipOutputStream zos = null;
        File file = new File(fileZipPath);
        try {
            if (!file.exists()) {
                file.createNewFile();
            }
            os = new FileOutputStream(file);
            zos = new ZipOutputStream(os);
            for (String path : filePaths) {
                File tempFile = new File(path);
                zos.putNextEntry(new ZipEntry(tempFile.getName()));
                FileInputStream ins = new FileInputStream(tempFile);

                WritableByteChannel writableByteChannel = Channels.newChannel(zos);
                FileChannel fileChannel = ins.getChannel();
                fileChannel.transferTo(0, fileChannel.size(), writableByteChannel);
                zos.closeEntry();
                fileChannel.close();
                ins.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return file;
    }

    /**
     * 字符串转换成Color对象
     * @param colorStr 16进制颜色字符串
     * @return Color对象
     */

    private Color toColorFromString(String colorStr) {

        colorStr = colorStr.substring(4);

        Color color = new Color(Integer.parseInt(colorStr, 16));

        return color;

    }

    /**
     * RGBA格式字符串转换成Color对象
     * @param colorStr  RGBA格式颜色字符串
     * @return  Color对象
     */
    private Color toColorFromRGBA(String colorStr) {
        if (colorStr.toLowerCase().indexOf("rgba(") != 0) {
            return null;
        }
        colorStr = colorStr.replaceAll(" ","");
        colorStr = colorStr.substring(5, colorStr.length() - 1);
        String[] arrColorValues = colorStr.split(",");
        if (arrColorValues.length < 3) {
            return null;
        }
        Color color = new Color(0,0,0, 0);
        int iR = 0, iG=0, iB= 0;
        float iA = 0f;
        try {
            iR = Integer.parseInt(arrColorValues[0].trim());
            iG = Integer.parseInt(arrColorValues[1].trim());
            iB = Integer.parseInt(arrColorValues[2].trim());
            if(arrColorValues.length>3) {
                iA = Float.parseFloat(arrColorValues[3]);
            }
            if(iA<=1) {
                iA *= 100;
            }
            if(iA>0&&iA<=100)
                iA = iA/0.39f;        //iDesktopX会将透明度变成正常值的39%，所以拿到值之后再除以39%才是正常透明度
            if(iA>255f)
                iA=255f;
            color = new Color(iR, iG, iB, (int)iA);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return color;
    }

    /**
     * 处理标签专题图中的表达式异常问题，将缺失字段名的实体名称从表达式中过滤掉
     * @param strExpression 要处理的表达式
     * @return  处理好的表达式
     */
    private String parseLabelExpression(String strExpression) {
        String strResult = "";
        String[] arrParts = strExpression.split("\\.");
        for (int i = 0; i < arrParts.length; i++) {
            String strPart = arrParts[i];
            String endWithRegex = "^[_a-z0-9A-Z]+$";
            String startWithRegex = "^[_a-zA-Z]+$";
            String[] arrClass2Parts = strPart.split("[|][|]");
            if(i == arrParts.length - 1) {
                if(strPart.substring(strPart.length() - 1).matches(endWithRegex)) {		// 到了数组末尾且字符串以字母、下划线或数字结束
                    strResult += arrClass2Parts[0];
                }else {
                    strResult += strPart;
                }
            }else {
                if(strPart.substring(strPart.length()-1).matches(endWithRegex)&&!arrParts[i+1].substring(0,1).matches(startWithRegex)) {
                    for (int j = 0; j < arrClass2Parts.length-1; j++)					// 或者下一个数组元素不以字母或下划线开头
                        strResult += arrClass2Parts[j] + " ||";
                }else {
                    strResult += strPart + ".";
                }
            }
        }
        strResult = strResult.replaceAll("[|][|][|][|]","||");
        if(strResult.startsWith("||"))
            strResult = strResult.substring(3);
        if(strResult.endsWith("||"))
            strResult = strResult.substring(0, strResult.length()-2);
        strResult = strResult.trim();
        return strResult;
    }

    /**
     * 读流中数据
     *
     * @param inputStream
     */
    private void getInputData(final InputStream inputStream, boolean isError) {
        //新开一个线程来读取创建的进程日志 必须这样 http://blog.csdn.net/mengxingyuanlove/article/details/50707746
        Thread thread = new Thread(new Runnable(){
            public void run(){
                log.info("启动读取进程流, isError={}", isError);
                try {
                    String msg = IOUtils.toString(inputStream, java.nio.charset.Charset.defaultCharset());
                    if (isError) {
                        log.error("外部JAR返回={}", msg);
                    } else {
                        log.info("外部JAR返回={}", msg);
                    }
                } catch (IOException e) {
                    log.error("外部JAR运行发生错误, errorMsg=" + e.getMessage(), e);
                } finally {
                    IOUtils.closeQuietly(inputStream);
                }
                log.info("结束读取进程流, isError={}", isError);
            }
        });
        thread.start();
    }

    @Override
    public void reCreateEntitytemDB(String entityTypeId) {
        try {
            lock.lock();
            List<Entitytypestem> entitytypestems = iEntitytypestemService.list();

            // 查询所有数据集信息
            QueryWrapper<Datasetinfotem> queryWrapperDataset = new QueryWrapper<>();
            queryWrapperDataset.eq("data_set_type", 0); // 查询属性数据集
            List<Datasetinfotem> datasetinfotemList = iDatasetinfotemService.list(queryWrapperDataset);

            // 查询所有字段信息
            QueryWrapper<Datasetfieldtem> queryWrapperField = new QueryWrapper<>();
            queryWrapperField.orderByAsc("order_index");
            List<Datasetfieldtem> datasetfields = iDatasetfieldtemService.list(queryWrapperField);

            String srcPath = PathUtils.getDesignPath() + File.separator + "entitiesachivments.db";
            File dbFile = new File(srcPath);
            if (!dbFile.exists()) {
                try {
                    dbFile.createNewFile();
                } catch (Exception e) {
                    log.error("db文件创建失败");
                    e.printStackTrace();
                }
            }

            for (int i = 0; i < entitytypestems.size(); i++) {
                Entitytypestem entitytypes = entitytypestems.get(i);
                if (StringUtils.isNotEmpty(entityTypeId) && entityTypeId.trim().length() > 0) {
                    if (entitytypes.getPkid().equals(entityTypeId)) {
                        if (datasetinfotemList != null && datasetinfotemList.size() > 0) {
                            for (Datasetinfotem datasetinfo : datasetinfotemList) {
                                if (datasetinfo.getEntitytypeid() == null || !datasetinfo.getEntitytypeid().equals(entityTypeId)) {
                                    continue;
                                }
                                if (datasetfields == null || datasetfields.size() == 0) {
                                    break;
                                }
                                // 直接删了要更新的表重建
                                try {
                                    SqliteUtils.executeUpdate(srcPath, "drop table if exists " + entitytypes.getEntitytypecode());
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                log.info(entitytypes.getEntitytypecode() + "表增加字段");

                                List<FiledInfo> fields = new ArrayList<>();
                                for (Datasetfieldtem datasetfield : datasetfields) {
                                    if (datasetfield.getDatasetid() == null || !datasetfield.getDatasetid().equals(datasetinfo.getPkid())) {
                                        continue;
                                    }
                                    FiledInfo filedInfo = new FiledInfo();
                                    filedInfo.setAttname(datasetfield.getFieldname());
                                    filedInfo.setColtype(datasetfield.getFieldtype());
                                    fields.add(filedInfo);
                                    log.info("增加字段： " + datasetfield.getFieldname());
                                }
                                createSqliteDbFile(srcPath, entitytypes.getEntitytypecode(), fields);
                                break;
                            }
                        }
                        break;
                    }
                } else {
                    if (datasetinfotemList != null && datasetinfotemList.size() > 0) {
                        for (Datasetinfotem datasetinfo : datasetinfotemList) {
                            if (datasetinfo.getEntitytypeid() == null || !datasetinfo.getEntitytypeid().equals(entitytypes.getPkid())) {
                                continue;
                            }
                            if (datasetfields == null || datasetfields.size() == 0) {
                                continue;
                            }
                            // 直接删了要更新的表重建
                            try {
                                SqliteUtils.executeUpdate(srcPath, "drop table if exists " + datasetinfo.getTablename());
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            log.info(datasetinfo.getTablename() + "表增加字段");

                            List<FiledInfo> fields = new ArrayList<>();
                            for (Datasetfieldtem datasetfield : datasetfields) {
                                if (datasetfield.getDatasetid() == null || !datasetfield.getDatasetid().equals(datasetinfo.getPkid())) {
                                    continue;
                                }
                                FiledInfo filedInfo = new FiledInfo();
                                filedInfo.setAttname(datasetfield.getFieldname());
                                filedInfo.setColtype(datasetfield.getFieldtype());
                                fields.add(filedInfo);
                                log.info("增加字段： " + datasetfield.getFieldname());
                            }
                            createSqliteDbFile(srcPath, datasetinfo.getTablename(), fields);
                        }
                    }
                }
            }
            log.info("---->db实体更新完毕");
        } finally {
            lock.unlock();
        }
    }

    @Override
    public List<String> reCreateEntityDB(String entityTypeId, String projectId) {
        try {
            lock.lock();
            List<String> resultList = new ArrayList<>();
            // 查询所有实体
            QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("project_id", projectId);
            List<Entitytypes> entitytypesList = iEntitytypesService.list(queryWrapper);

            // 查询所有数据集信息
            QueryWrapper<Datasetinfo> queryWrapperDataset = new QueryWrapper<>();
            queryWrapperDataset.eq("data_set_type", 0); // 查询属性数据集
            queryWrapperDataset.eq("project_id", projectId);
            List<Datasetinfo> datasetinfoList = iDatasetinfoService.list(queryWrapperDataset);

            // 查询所有字段信息
            QueryWrapper<Datasetfield> queryWrapperField = new QueryWrapper<>();
            queryWrapperField.eq("project_id", projectId);
            queryWrapperField.orderByAsc("order_index");
            List<Datasetfield> datasetfields = iDatasetfieldService.list(queryWrapperField);

            String srcPath = PathUtils.getDesignPath() + File.separator + projectId + File.separator + "entitiesachivments.db";
            File dbFile = new File(srcPath);
            if (!dbFile.exists()) {
                try {
                    dbFile.createNewFile();
                } catch (Exception e) {
                    log.error("entitiesachivments.db创建，异常,projectid:{}",projectId,e);
                    e.printStackTrace();
                }
            }
            java.util.Map<String,List<String>> tabularsTableInfoMap = new HashMap<>();
            for (int i = 0; i < entitytypesList.size(); i++) {
                Entitytypes entitytypes = entitytypesList.get(i);
                if (StringUtils.isNotEmpty(entityTypeId) && entityTypeId.trim().length() > 0) {
                    if (entitytypes.getPkid().equals(entityTypeId)) {
                        if (datasetinfoList != null && datasetinfoList.size() > 0) {
                            for (Datasetinfo datasetinfo : datasetinfoList) {
                                if (datasetinfo.getEntitytypeid() == null || !datasetinfo.getEntitytypeid().equals(entityTypeId)) {
                                    continue;
                                }
                                if (datasetfields == null || datasetfields.size() == 0) {
                                    break;
                                }
                                // 直接删了要更新的表重建
                                try {
                                    SqliteUtils.executeUpdate(srcPath, "drop table if exists " + entitytypes.getEntitytypecode());
                                } catch (Exception e) {
                                    log.error("entitiesachivments.db创建，删除存在表表异常,表{},projectid:{}",datasetinfo.getTablename(),projectId,e);
                                }
                                log.info(entitytypes.getEntitytypecode() + "表增加字段");
                                tabularsTableInfoMap.put(entitytypes.getEntitytypecode(),new ArrayList<>());
                                List<FiledInfo> fields = new ArrayList<>();
                                for (Datasetfield datasetfield : datasetfields) {
                                    if (datasetfield.getDatasetid() == null || !datasetfield.getDatasetid().equals(datasetinfo.getPkid())) {
                                        continue;
                                    }
                                    tabularsTableInfoMap.get(entitytypes.getEntitytypecode()).add(datasetfield.getFieldname());
                                    FiledInfo filedInfo = new FiledInfo();
                                    filedInfo.setAttname(datasetfield.getFieldname());
                                    filedInfo.setColtype(datasetfield.getFieldtype());
                                    fields.add(filedInfo);
                                    log.info("增加字段： " + datasetfield.getFieldname());
                                }
                                createSqliteDbFile(srcPath, entitytypes.getEntitytypecode(), fields);
                                break;
                            }
                        }
                        break;
                    }
                } else {
                    if (datasetinfoList != null && datasetinfoList.size() > 0) {
                        for (Datasetinfo datasetinfo : datasetinfoList) {
                            if (datasetinfo.getEntitytypeid() == null || !datasetinfo.getEntitytypeid().equals(entitytypes.getPkid())) {
                                continue;
                            }
                            if (datasetfields == null || datasetfields.size() == 0) {
                                continue;
                            }
                            // 直接删了要更新的表重建
                            try {
                                SqliteUtils.executeUpdate(srcPath, "drop table if exists " + datasetinfo.getTablename());
                            } catch (Exception e) {
                                log.error("entitiesachivments.db创建，删除存在表表异常,表{},projectid:{}",datasetinfo.getTablename(),projectId,e);
                            }
                            log.info(datasetinfo.getTablename() + "表增加字段");

                            tabularsTableInfoMap.put(entitytypes.getEntitytypecode(),new ArrayList<>());
                            List<FiledInfo> fields = new ArrayList<>();
                            for (Datasetfield datasetfield : datasetfields) {
                                if (datasetfield.getDatasetid() == null || !datasetfield.getDatasetid().equals(datasetinfo.getPkid())) {
                                    continue;
                                }
                                tabularsTableInfoMap.get(entitytypes.getEntitytypecode()).add(datasetfield.getFieldname());
                                FiledInfo filedInfo = new FiledInfo();
                                filedInfo.setAttname(datasetfield.getFieldname());
                                filedInfo.setColtype(datasetfield.getFieldtype());
                                fields.add(filedInfo);
                                log.info("增加字段： " + datasetfield.getFieldname());
                            }
                            createSqliteDbFile(srcPath, datasetinfo.getTablename(), fields);
                        }
                    }
                }
            }

            try {
                Connection conn = SqliteUtils.getConnection(srcPath);
                String sql = " SELECT name FROM sqlite_master WHERE type ='table' ";
                List<java.util.Map<String, Object>> tables = SqliteUtils.executeQueryMap(conn, sql, Arrays.asList(new String[]{"name"}));

                List<String> tableList = new ArrayList<>();
                for (java.util.Map<String, Object> item : tables) {
                    String name = (String)item.get("name");
                    tableList.add(name);
                }
                for (String tableName : tabularsTableInfoMap.keySet()) {
                    if (tableList.contains(tableName)) {
                        sql = StrUtil.format(" PRAGMA table_info({}) ",tableName);
                        //字段循环
                        List<java.util.Map<String,Object>> fields = SqliteUtils.executeQueryMap(conn, sql, Arrays.asList(new String[]{"name"}));
                        List<String> fieldList = new ArrayList<>();
                        for (java.util.Map<String, Object> item : fields) {
                            String name = (String)item.get("name");
                            fieldList.add(name);
                        }

                        List<String> fieldInfoList = tabularsTableInfoMap.get(tableName);
                        if (fieldInfoList != null && fieldInfoList.size() > 0) {
                            for (String fieldName : fieldInfoList) {
                                if (!fieldList.contains(fieldName)) {
                                    resultList.add(String.format("数据集%s字段%s不存在。", tableName, fieldName));
                                }
                            }
                        } else {
                            resultList.add(String.format("数据集%s字段为空。", tableName));
                        }
                    } else {
                        resultList.add(String.format("数据集%s不存在。", tableName));
                    }
                    // 对键值对做相关处理
                }
                SqliteUtils.closeConnection(conn);
            }catch (Exception e){

            }
            resultList.addAll(createTabularTables(projectId));

            log.info("---->db实体更新完毕");
            return resultList;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 检查entitiesachivments.db里的表字段是否齐全，通过和metadata库的项目级实体字段对比
     * @return
     */
    @Override
    public List<String> checkDbTablesFields(String dbPath, String projectId) {
        List<String> resultList = new ArrayList<>();
        // 查询所有数据集信息
        QueryWrapper<Datasetinfo> queryWrapperDataset = new QueryWrapper<>();
        queryWrapperDataset.eq("data_set_type", 0); // 查询属性数据集
        queryWrapperDataset.eq("project_id", projectId);
        List<Datasetinfo> datasetinfoList = iDatasetinfoService.list(queryWrapperDataset);

        // 查询所有字段信息
        QueryWrapper<Datasetfield> queryWrapperField = new QueryWrapper<>();
        queryWrapperField.eq("project_id", projectId);
        queryWrapperField.orderByAsc("order_index");
        List<Datasetfield> datasetfields = iDatasetfieldService.list(queryWrapperField);
        try {
            java.util.Map<String,List<String>> tabularsTableInfoMap = new HashMap<>();
            // 连接db
            Connection conn = SqliteUtils.getConnection(dbPath);
            String sql = " SELECT name FROM sqlite_master WHERE type ='table' ";
            List<java.util.Map<String,Object>> tables = SqliteUtils.executeQueryMap(conn, sql, Arrays.asList(new String[]{"name"}));
            // 表循环
            for (Datasetinfo datasetinfo: datasetinfoList) {
                boolean isTableExist = false;
                tabularsTableInfoMap.put(datasetinfo.getDatasetname(),new ArrayList<>());
                for (java.util.Map<String, Object> item : tables) {
                    Object name = item.get("name");
                    if (datasetinfo.getDatasetname().equalsIgnoreCase(name.toString())) {
                        isTableExist = true;
                        for (Datasetfield datasetfield: datasetfields) {

                            if (datasetfield.getDatasetid().equals(datasetinfo.getPkid())) {
                                if(!tabularsTableInfoMap.get(datasetinfo.getDatasetname()).contains(datasetfield.getFieldname())){
                                    tabularsTableInfoMap.get(datasetinfo.getDatasetname()).add(datasetfield.getFieldname());
                                }
                                sql = StrUtil.format(" PRAGMA table_info({}) ",name);
                                //字段循环
                                List<java.util.Map<String,Object>> fields = SqliteUtils.executeQueryMap(conn, sql, Arrays.asList(new String[]{"name"}));
                                boolean isFieldExist = false;
                                for (java.util.Map<String, Object> field : fields) {
                                    Object fieldName = field.get("name");
                                    if (datasetfield.getFieldname().equals(fieldName.toString())) {
                                        isFieldExist = true;
                                        break;
                                    }
                                }
                                if (!isFieldExist) {
                                    // 字段不存在则在表里插入字段
                                    String fileType = "";
                                    if (datasetfield.getFieldtype().equals("1")){
                                        //stringBuilder.append(" INTEGER");
                                        fileType = "INTEGER";
                                    }
                                    else if (datasetfield.getFieldtype().equals("2")){
                                        //stringBuilder.append(" REAL");
                                        fileType = "REAL";
                                    } else if (datasetfield.getFieldtype().equals("4")){
                                        //stringBuilder.append(" TEXT");
                                        fileType = "TEXT";
                                    } else if (datasetfield.getFieldtype().equals("3")) {
                                        fileType = "BLOB";
                                    } else {
                                        fileType = "TEXT";
                                    }
                                    String fieldSQL = "ALTER TABLE " + name + " ADD COLUMN " + datasetfield.getFieldname() + " " + fileType;
                                    try {
                                        SqliteUtils.executeUpdate(dbPath, fieldSQL);

                                        log.info("entitiesachivments.db检测，表{},补充字段{}，成功，projectid:{}",name,datasetfield.getFieldname(),projectId);
                                    } catch (SQLException ex) {
                                        log.error("entitiesachivments.db检测，表{},补充字段{}，异常,删除重新创建，projectid:{}",name,datasetfield.getFieldname(),projectId,ex);
                                        // sql执行出错可能是db文件被锁了，删了重新建
                                        FileUtil.del(dbPath);
                                        reCreateEntityDB(null, projectId);
                                    }
                                }
                            }
                        }
                        break;
                    }
                }
                if (!isTableExist) {
                    log.info("entitiesachivments.db检测，不存在，重新创建，projectid:{}",projectId);

                    // 表不存在则刷新db的实体
                    reCreateEntityDB(datasetinfo.getEntitytypeid(), projectId);
                }
            }

            try {
                Connection conn1 = SqliteUtils.getConnection(dbPath);
                String sql1 = " SELECT name FROM sqlite_master WHERE type ='table' ";
                List<java.util.Map<String, Object>> tables1 = SqliteUtils.executeQueryMap(conn, sql1, Arrays.asList(new String[]{"name"}));

                List<String> tableList = new ArrayList<>();
                for (java.util.Map<String, Object> item : tables) {
                    String name = (String)item.get("name");
                    tableList.add(name);
                }
                for (String tableName : tabularsTableInfoMap.keySet()) {
                    if (tableList.contains(tableName)) {
                        sql = StrUtil.format(" PRAGMA table_info({}) ",tableName);
                        //字段循环
                        List<java.util.Map<String,Object>> fields = SqliteUtils.executeQueryMap(conn, sql, Arrays.asList(new String[]{"name"}));
                        List<String> fieldList = new ArrayList<>();
                        for (java.util.Map<String, Object> item : fields) {
                            String name = (String)item.get("name");
                            fieldList.add(name);
                        }

                        List<String> fieldInfoList = tabularsTableInfoMap.get(tableName);
                        if (fieldInfoList != null && fieldInfoList.size() > 0) {
                            for (String fieldName : fieldInfoList) {
                                if (!fieldList.contains(fieldName)) {
                                    resultList.add(String.format("数据集%s字段%s不存在。", tableName, fieldName));
                                }
                            }
                        } else {
                            resultList.add(String.format("数据集%s字段为空。", tableName));
                        }
                    } else {
                        resultList.add(String.format("数据集%s不存在。", tableName));
                    }
                    // 对键值对做相关处理
                }
                SqliteUtils.closeConnection(conn1);
            }catch (Exception e){

            }
            SqliteUtils.closeConnection(conn);
            return resultList;
        } catch (Exception e) {
            e.printStackTrace();
            // sql执行出错可能是db文件被锁了，删了重新建
            FileUtil.del(dbPath);
            log.error("entitiesachivments.db检测，异常，重新创建，projectid:{}",projectId,e);
            //reCreateEntityDB(null, projectId);
            resultList.add(String.format("entitiesachivments.db检测，异常，重新创建，projectid:%s",projectId));
            return resultList;
        }
    }

    /**
     * 向db-design/entitiesachivments.db中创建tabular.xlsx中对应的表结构
     */
    @Override
    public List<String> createTabularTables(String projectId) {
        List<String> resultList = new ArrayList<>();
        String strTabularTemplatePath = PathUtils.getTemplatePath() + File.separator + "tabulars.xlsx";
        File fileTabularTemplates = new File(strTabularTemplatePath);
        if (!fileTabularTemplates.exists()) {
            log.error("entitiesachivments.db创建，tabulars.xlsx不存在,projectid:{}",projectId);
            return resultList;
        }
        InputStream in = FileUtil.getInputStream(fileTabularTemplates);
        java.util.Map<String, java.util.Map<Integer, List<String>>> mapTabularHeaderAndDatas = null;
        try {
            in = FileUtil.getInputStream(fileTabularTemplates);
            mapTabularHeaderAndDatas = ExcelUtils.getExcelTopAndData(in, 7, strTabularTemplatePath);
            if (mapTabularHeaderAndDatas == null || mapTabularHeaderAndDatas.isEmpty()){
                log.error("entitiesachivments.db创建，tabulars.xlsx，mapTabularHeaderAndDatas空,projectid:{}",projectId);
                return resultList;
            }
        } catch (Exception e) {
            log.error("entitiesachivments.db创建，tabulars.xlsx，异常,projectid:{}",projectId,e);
        }
        String strDBPath = PathUtils.getDesignPath() + File.separator + projectId + File.separator + "entitiesachivments.db";
        ResultSet resultSet = null;
        try {
            java.util.Map<String,List<String>> tabularsTableInfoMap = new HashMap<>();
            //判断在entitiesachivments.db中是否已经有tabulars.xlsx中对应的表，没有的话就去建表
            Connection connection = SqliteUtils.getConnection(strDBPath);
            resultSet = SqliteUtils.executeQuery(connection, "select * from sqlite_master where type='table'");
            Iterator<String> iterator = mapTabularHeaderAndDatas.keySet().iterator();
            while (iterator.hasNext()) {
                String strSheetName = iterator.next();
                String strTableName = "";
                if (strSheetName.indexOf('<') > 0 && strSheetName.indexOf('>') > 0) {
                    strTableName = strSheetName.substring(0, strSheetName.indexOf('<'));
                }
                Boolean blHasCreated = false;
                while (resultSet.next()) {
                    log.info(resultSet.getString("name"));
                    if (resultSet.getString("name").equalsIgnoreCase(strTableName)) {
                        blHasCreated = true;
                        break;
                    }
                }
                if (!blHasCreated) {
                    //在entitiesachivments.db中创建对应的表
                    StringBuilder strbCreateSQL = new StringBuilder("create table " + strTableName + " (");
                    tabularsTableInfoMap.put(strTableName,new ArrayList<>());
                    java.util.Map<Integer, List<String>> mapTableFieldsInfo = mapTabularHeaderAndDatas.get(strSheetName);
                    Iterator<Integer> iteratorField = mapTableFieldsInfo.keySet().iterator();
                    if (iteratorField.hasNext())
                        iteratorField.next();//跳过标题所在行
                    while (iteratorField.hasNext()) {
                        Integer iKey = iteratorField.next();
                        List<String> lstFieldInfo = mapTableFieldsInfo.get(iKey);
                        strbCreateSQL.append(lstFieldInfo.get(1) + " ");

                        tabularsTableInfoMap.get(strTableName).add(lstFieldInfo.get(1));
                        switch (lstFieldInfo.get(3)) {
                            case "单精度型":
                                strbCreateSQL.append(" REAL,");
                                break;
                            case "双精度型":
                                strbCreateSQL.append(" REAL,");
                                break;
                            case "整型":
                                strbCreateSQL.append(" INTEGER,");
                                break;
                            case "日期型":
                                strbCreateSQL.append(" DATE,");
                                break;
                            case "字符串型":
                            case "文本型":
                                strbCreateSQL.append(" TEXT");
                                if (lstFieldInfo.get(4) != null && lstFieldInfo.get(4).length() > 0)
                                    strbCreateSQL.append("(" + lstFieldInfo.get(4) + "),");
                                else
                                    strbCreateSQL.append(",");
                                break;
                            case "二进制型":
                            case "长文本型":
                                strbCreateSQL.append(" BLOB,");
                                break;
                        }
                    }
                    if (strbCreateSQL.charAt(strbCreateSQL.length() - 1) == ',')
                        strbCreateSQL.deleteCharAt(strbCreateSQL.length() - 1);
                    strbCreateSQL.append(')');
                    SqliteUtils.executeUpdate(connection, strbCreateSQL.toString());

                    log.info("entitiesachivments.db创建，tabulars.xlsx，成功，表{},projectid:{}",strSheetName,projectId);
                }
                try {
                    resultSet = SqliteUtils.executeQuery(connection, "select * from sqlite_master where type='table'");
                } catch (Exception me) {

                    log.error("entitiesachivments.db创建，tabulars.xlsx，resultSet异常，表{},projectid:{}",strSheetName,projectId,me);
                }
            }

            try {
                Connection conn = SqliteUtils.getConnection(strDBPath);
                String sql = " SELECT name FROM sqlite_master WHERE type ='table' ";
                List<java.util.Map<String, Object>> tables = SqliteUtils.executeQueryMap(conn, sql, Arrays.asList(new String[]{"name"}));

                List<String> tableList = new ArrayList<>();
                for (java.util.Map<String, Object> item : tables) {
                    String name = (String)item.get("name");
                    tableList.add(name);
                }
                for (String tableName : tabularsTableInfoMap.keySet()) {
                    if (tableList.contains(tableName)) {
                        sql = StrUtil.format(" PRAGMA table_info({}) ",tableName);
                        //字段循环
                        List<java.util.Map<String,Object>> fields = SqliteUtils.executeQueryMap(conn, sql, Arrays.asList(new String[]{"name"}));
                        List<String> fieldList = new ArrayList<>();
                        for (java.util.Map<String, Object> item : fields) {
                            String name = (String)item.get("name");
                            fieldList.add(name);
                        }

                        List<String> fieldInfoList = tabularsTableInfoMap.get(tableName);
                        if (fieldInfoList != null && fieldInfoList.size() > 0) {
                            for (String fieldName : fieldInfoList) {
                                if (!fieldList.contains(fieldName)) {
                                    resultList.add(String.format("数据集%s字段%s不存在。", tableName, fieldName));
                                }
                            }
                        } else {
                            resultList.add(String.format("数据集%s字段为空。", tableName));
                        }
                    } else {
                        resultList.add(String.format("数据集%s不存在。", tableName));
                    }
                    // 对键值对做相关处理
                }
                SqliteUtils.closeConnection(conn);
            }catch (Exception e){

            }

            SqliteUtils.closeConnection(connection);
            return resultList;
        } catch (Exception e) {

            log.error("entitiesachivments.db创建，tabulars.xlsx，异常,projectid:{}",projectId,e);
            e.printStackTrace();
            return resultList;
        }
    }

    /**
     * 创建表
     * @param tarPath
     * @param tableName
     * @param fields
     */
    private void createSqliteDbFile(String tarPath, String tableName, List<FiledInfo> fields) {
        java.util.Map<String,Object> hashSet = new HashMap<>();
        List<String> fieldList = new ArrayList<>();
        for( FiledInfo filedInfo : fields ){
            String column = filedInfo.getAttname();
            if (hashSet.get(column)!=null){
                continue;
            }
            hashSet.put(column,column);
            String type = filedInfo.getColtype();
            String fileType = "";
            if (type.equals("1")){
                //stringBuilder.append(" INTEGER");
                fileType = "INTEGER";
            }
            else if (type.equals("2")){
                //stringBuilder.append(" REAL");
                fileType = "REAL";
            } else if (type.equals("4")){
                //stringBuilder.append(" TEXT");
                fileType = "TEXT";
            } else if (type.equals("3")) {
                fileType = "BLOB";
            } else {
                fileType = "TEXT";
            }
            fieldList.add(StrUtil.format(" {} {} ",column,fileType));
        }
        if (hashSet.get("pkid")==null) {
            fieldList.add("pkid TEXT");
        }
        String createSql = StrUtil.format("CREATE TABLE {} ({})",tableName,String.join(",",fieldList));
        try {
            log.info("创建表 -->"+tableName);
            log.info(""+createSql);
            //String path = PathUtils.getInitPath()+File.separator+dbName+".db";
            log.info("db --> "+tarPath);
            SqliteUtils.createTable(tarPath, createSql);
            log.info("entitiesachivments.db创建，成功,表{}",tableName);
        }catch (SQLException exp ){
            log.error("entitiesachivments.db创建，createTable异常,表{}",tableName,exp);
            exp.printStackTrace();
        }
    }

    /**
     * 查询表是否存在，如不存在则创建
     * @param tarPath
     * @param tableName
     */
    private void findTableOrCreate(String tarPath, String tableName) {
        String tableSql = "select name from sqlite_master where type='table' order by name";
        Connection connection = null;
        try {
            connection = SqliteUtils.getConnection(tarPath);
            ResultSet resultSet = SqliteUtils.executeQuery(connection, tableSql);
            boolean isExist = false;
            while (resultSet.next()) {
                if (resultSet.getString("name").equals(tableName)) {
                    isExist = true;
                    break;
                }
            }
            if (!isExist) {
                log.info(tableName + "表不存在，开始新建");
                String createSql = StrUtil.format("CREATE TABLE {} ({})",tableName,"pkid TEXT");
                SqliteUtils.executeUpdate(connection, createSql);
            }
        } catch (SQLException ex) {
            ex.printStackTrace();
        } finally {
        }
    }

    /**
     * 查询sqlite表字段
     * @param tarPath
     * @param tableName
     * @return
     */
    private java.util.Map<String, String> findFieldsByTableName(String tarPath, String tableName) {
        String fieldSQL = "PRAGMA table_info(" + tableName + ")";
        java.util.Map<String, String> fieldMap = new HashMap<>();
        Connection connection = null;
        try {
            connection = SqliteUtils.getConnection(tarPath);
            ResultSet resultSet = SqliteUtils.executeQuery(connection, fieldSQL);
            while (resultSet.next()) {
                fieldMap.put(resultSet.getString("name"), resultSet.getString("type"));
            }
            SqliteUtils.closeConnection(connection);
            return fieldMap;
        } catch (SQLException ex) {
            ex.printStackTrace();
        } finally {
        }
        return fieldMap;
    }

    /**
     * 添加字段到sqlite库表
     * @param tarPath
     * @param tableName
     * @param field
     * @param type
     */
    private void addFieldToTable(String tarPath, String tableName, String field, String type) {
        if(type.equals("1")){
            type = "INTEGER";
        }else if(type.equals("2")){
            type = "REAL";
        }else if(type.equals("4")){
            type = "TEXT";
        }else if (type.equals("3")){
            type = "BLOB";
        } else {
            type = "TEXT";
        }
        String fieldSQL = "ALTER TABLE " + tableName + " ADD COLUMN " + field + " " + type;
        try {
            SqliteUtils.executeUpdate(tarPath, fieldSQL);
        } catch (SQLException ex) {
            ex.printStackTrace();
        }
    }
}
