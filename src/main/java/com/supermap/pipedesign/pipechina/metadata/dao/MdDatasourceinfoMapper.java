package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.MdDatasourceinfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 数据源注册表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Mapper
public interface MdDatasourceinfoMapper extends BaseMapper<MdDatasourceinfo> {

    @Select("select * from pld_md_datasource_info")
    List<MdDatasourceinfo> selAll();
}
