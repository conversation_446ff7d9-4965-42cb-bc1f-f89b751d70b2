package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 规则和算法的关联信息 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_entitytypes_rule_algorit")
@ApiModel(value="EntitytypesRuleAlgorit对象", description="规则和算法的关联信息")
public class EntitytypesRuleAlgorit implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则算法ID")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "实体类型ID")
    @TableField("entity_types_id")
    private String entitytypesid;

    @ApiModelProperty(value = "规则库中算法注册表的PKID")
    @TableField("algorit_id")
    private String algoritid;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
