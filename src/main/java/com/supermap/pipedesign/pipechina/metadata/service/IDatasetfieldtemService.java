package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.metadata.entity.Datasetfield;
import com.supermap.pipedesign.pipechina.metadata.entity.Datasetfieldtem;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字段表 服务类
 * 模板
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IDatasetfieldtemService extends IService<Datasetfieldtem> {

 /**
  * 添加实体模板
  * <AUTHOR>
  * @Description
  * @Date 2023/1/5 17:35
  **/
 int add(List<Datasetfieldtem> datasetfieldtem);

 /*
  * <AUTHOR>
  * @Description
  * @Date 2022/12/30 0:32
  * 又数据集id查字段表
  **/
 List<Datasetfield> insertToDatasetfield(String datasetid);

 /**
  * 根据实体id查属性
  * @param entityTypeId 实体id
  * <AUTHOR>
  * @Description
  * @Date 2023/3/26 13:57
  **/
 List<Map> selFieldByEntityId(String entityTypeId);


 }
