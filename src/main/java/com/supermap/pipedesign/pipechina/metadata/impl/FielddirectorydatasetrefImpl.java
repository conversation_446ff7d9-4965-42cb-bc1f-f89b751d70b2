package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.Fielddirectorydatasetref;
import com.supermap.pipedesign.pipechina.metadata.dao.FielddirectorydatasetrefMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IFielddirectorydatasetrefService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 踏勘目录-数据集关联表(存储外业踏勘目录与数据集的关联关系。如果关联的是文件，则存储文件ID或路径信息。) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("FielddirectorydatasetrefImpl")
public class FielddirectorydatasetrefImpl extends ServiceImpl<FielddirectorydatasetrefMapper, Fielddirectorydatasetref> implements IFielddirectorydatasetrefService {

    @Autowired
    private FielddirectorydatasetrefMapper fielddirectorydatasetrefMapper;

    /**
    * 添加踏勘目录-数据集关联表(存储外业踏勘目录与数据集的关联关系。如果关联的是文件，则存储文件ID或路径信息。)信息
    *
    * @param fielddirectorydatasetref
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int insert(Fielddirectorydatasetref fielddirectorydatasetref) {

        //fielddirectorydatasetref.setUserId(JavaUtils.getUUID36());
        //fielddirectorydatasetref.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return fielddirectorydatasetrefMapper.insert(fielddirectorydatasetref);
    }

    /**
    * 删除踏勘目录-数据集关联表(存储外业踏勘目录与数据集的关联关系。如果关联的是文件，则存储文件ID或路径信息。)信息
    *
    * @param fielddirectorydatasetrefId
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int delete(String fielddirectorydatasetrefId) {
        return fielddirectorydatasetrefMapper.deleteById(fielddirectorydatasetrefId);
    }

    /**
    * 更新踏勘目录-数据集关联表(存储外业踏勘目录与数据集的关联关系。如果关联的是文件，则存储文件ID或路径信息。)信息
    *
    * @param fielddirectorydatasetref
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int update(Fielddirectorydatasetref fielddirectorydatasetref) {
        return fielddirectorydatasetrefMapper.updateById(fielddirectorydatasetref);
    }

    /**
    * 全部查询
    *
    * @param fielddirectorydatasetref
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Fielddirectorydatasetref>
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<Fielddirectorydatasetref> list(Fielddirectorydatasetref fielddirectorydatasetref) {

        QueryWrapper<Fielddirectorydatasetref> queryWrapper = new QueryWrapper<>();

        return fielddirectorydatasetrefMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<Fielddirectorydatasetref> fielddirectorydatasetrefIPage = new Page<>();
        fielddirectorydatasetrefIPage.setCurrent(current);
        fielddirectorydatasetrefIPage.setSize(size);

        QueryWrapper<Fielddirectorydatasetref> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return fielddirectorydatasetrefMapper.selectPage(fielddirectorydatasetrefIPage, queryWrapper);
    }


}
