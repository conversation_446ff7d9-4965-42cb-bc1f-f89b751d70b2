package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.Entitytypedatatyperef;
import com.supermap.pipedesign.pipechina.metadata.dao.EntitytypedatatyperefMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IEntitytypedatatyperefService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 实体类型-数据分类关联表(存储实体类型与数据类型的关联关系。对于指定的实体类型，当在该表中找到了对应的数据类型时，就在对应的目录节点下展示关联的数据类型及下属数据。) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("EntitytypedatatyperefImpl")
public class EntitytypedatatyperefImpl extends ServiceImpl<EntitytypedatatyperefMapper, Entitytypedatatyperef> implements IEntitytypedatatyperefService {

    @Autowired
    private EntitytypedatatyperefMapper entitytypedatatyperefMapper;

    /**
    * 添加实体类型-数据分类关联表(存储实体类型与数据类型的关联关系。对于指定的实体类型，当在该表中找到了对应的数据类型时，就在对应的目录节点下展示关联的数据类型及下属数据。)信息
    *
    * @param entitytypedatatyperef
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int insert(Entitytypedatatyperef entitytypedatatyperef) {

        //entitytypedatatyperef.setUserId(JavaUtils.getUUID36());
        //entitytypedatatyperef.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return entitytypedatatyperefMapper.insert(entitytypedatatyperef);
    }

    /**
    * 删除实体类型-数据分类关联表(存储实体类型与数据类型的关联关系。对于指定的实体类型，当在该表中找到了对应的数据类型时，就在对应的目录节点下展示关联的数据类型及下属数据。)信息
    *
    * @param entitytypedatatyperefId
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int delete(String entitytypedatatyperefId) {
        return entitytypedatatyperefMapper.deleteById(entitytypedatatyperefId);
    }

    /**
    * 更新实体类型-数据分类关联表(存储实体类型与数据类型的关联关系。对于指定的实体类型，当在该表中找到了对应的数据类型时，就在对应的目录节点下展示关联的数据类型及下属数据。)信息
    *
    * @param entitytypedatatyperef
    * @return int
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public int update(Entitytypedatatyperef entitytypedatatyperef) {
        return entitytypedatatyperefMapper.updateById(entitytypedatatyperef);
    }

    /**
    * 全部查询
    *
    * @param entitytypedatatyperef
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Entitytypedatatyperef>
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public List<Entitytypedatatyperef> list(Entitytypedatatyperef entitytypedatatyperef) {

        QueryWrapper<Entitytypedatatyperef> queryWrapper = new QueryWrapper<>();

        return entitytypedatatyperefMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2022-12-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<Entitytypedatatyperef> entitytypedatatyperefIPage = new Page<>();
        entitytypedatatyperefIPage.setCurrent(current);
        entitytypedatatyperefIPage.setSize(size);

        QueryWrapper<Entitytypedatatyperef> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return entitytypedatatyperefMapper.selectPage(entitytypedatatyperefIPage, queryWrapper);
    }


}
