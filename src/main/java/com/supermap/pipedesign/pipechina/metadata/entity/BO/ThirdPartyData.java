package com.supermap.pipedesign.pipechina.metadata.entity.BO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @autthor sqq
 * @date 2023/4/11
 * @Description 第三方目录
 */
@Data
public class ThirdPartyData {
    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "上级目录编号")
    @TableField("parent_id")
    private String parentid;

    @ApiModelProperty(value = "目录节点名称")
    @TableField("node_name")
    private String nodename;

    @ApiModelProperty(value = "目录节点别名")
    @TableField("node_alias")
    private String nodealias;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;

    @ApiModelProperty(value = "资源类型")
    @TableField("resource_type")
    private String resourcetype;

    @ApiModelProperty(value = "资源编号")
    @TableField("resource_id")
    private String resourceid;

}
