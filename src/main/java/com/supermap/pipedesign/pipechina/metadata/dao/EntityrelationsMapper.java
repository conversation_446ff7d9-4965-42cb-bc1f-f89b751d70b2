package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.Entityrelations;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 实体关联表(存储实体和实体之间的关联关系。主要用于存储衍生实体。) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface EntityrelationsMapper extends BaseMapper<Entityrelations> {

}
