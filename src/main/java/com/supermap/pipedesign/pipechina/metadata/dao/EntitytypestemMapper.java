package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.AutoEntityRuleVo;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.EntityTypesVo;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.EntitytypesBO;
import com.supermap.pipedesign.pipechina.metadata.entity.Entitytypestem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 实体类型定义表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface EntitytypestemMapper extends BaseMapper<Entitytypestem> {

    @Select("select t.pkid,t.entity_type_code, t.entity_type_name, t.entity_type_alias,t.entity_type_name,m.entity_type_name as parentname, t.entity_type_alias,t.stage_inner_code " +
            " ,t.create_user_id,t.create_time,t.reuse_diagram_id " +
            " from pld_md_entitytypes_tem t left join pld_md_entitytypes_tem m on t.parent_entity_id = m.pkid  ${ew.customSqlSegment}" )
    IPage<EntitytypesBO> getBySubjectid(IPage<EntitytypesBO> page, @Param(Constants.WRAPPER) Wrapper<EntitytypesBO> queryWrapper);

    @Select("select pkid, entity_type_alias as entitytypealias,subject_id as subjectid,stage_inner_code as stageinnercode from pld_md_entitytypes_tem where is_basics=0" )
    List<Map> selEntityBaseList();

    @Select("select pkid, entity_type_alias,subject_id,stage_inner_code from pld_md_entitytypes_tem where pkid=#{pkid}" )
    Entitytypestem selByPkid(@Param("pkid")String pkid);


    @Select("select t.pkid, t.entity_type_alias as entitytypealias  from pld_md_entitytypes_tem t where subject_id=#{subjectid} " )
    List<Map> selBySubjectId(String subjectId);

    @Select("select t.pkid, t.entity_type_alias as entitytypealias  from pld_md_entitytypes_tem t where subject_id=#{subjectid} and is_auto_matic=1" )
    List<Map> selBySubjectIdWAutoMatic(String subjectId);

    @Select("select * from (select ple.pkid as entityTypePkid,ple.entity_type_alias as entityTypeName,fet.showType as showType,"+
            " fet.interactive_type from pld_md_entitytypes_tem ple left join"+
            " (select array_to_string(array_agg(data_set_type),',')as showType,interactive_type,entity_type_id"+
            " from pld_md_dataset_info_tem pmet where pmet.data_set_type != '3' group by entity_type_id,interactive_type) as fet"+
            " on ple.pkid = fet.entity_type_id where ple.is_auto_matic =1) t where t.interactive_type is not null and  length(t.showType)<4" )
    List<AutoEntityRuleVo> selAllEntityForSelect();


    @Select("select pkid,entity_type_alias,stage_inner_code,is_basics as isBase,subject_id from pld_md_entitytypes_tem t ${ew.customSqlSegment}" )
    List<EntityTypesVo> selBySIdAndStageId(  @Param(Constants.WRAPPER) Wrapper<EntityTypesVo> queryWrapper );

    @Select("select pkid,entity_type_alias,stage_inner_code,is_basics as isBase,subject_id from pld_md_entitytypes_tem t where parent_entity_id=#{parententityid}" )
    List<EntityTypesVo> selByParententityid(String parententityid);

    @Select("<script>" +
            " select entity_type_alias as entitytypealias from pld_md_entitytypes_tem t where pkid in"+
            " <foreach item='item' index='index' collection='pkids' open='(' separator=',' close=')'>" +
            "       #{item}" +
            " </foreach>" +
            "</script>" )
    List<String> selentity_type_names(@Param("pkids") List<String> pkids);


    @Update("update pld_md_entitytypes_tem set stage_inner_code=#{stageinnercode} where pkid=#{pkid}")
    int updateSIdByPkid(  @Param("pkid") String pkid,@Param("stageinnercode")String stageinnercode );

    @Select("select t.pkid, t.entity_type_alias as entitytypealias from pld_md_entitytypes_tem t where parent_entity_id is null" )
    List<Map> seleEntry();

    @Select("select t.pkid, t.entity_type_alias as entitytypealias  from pld_md_entitytypes_tem t where  parent_entity_id=#{parententityid} " )
    List<Map> selByParentId(String parententityid);
    @Select("select pkid,entity_type_alias as entitytypealias from pipe.pld_md_entitytypes_tem pmet2" +
            " left join" +
            " (select entity_type_id ,array_to_string(array_agg(data_set_type),',')  as eit" +
            " from pipe.pld_md_entity_datasref_tem pmet group by entity_type_id) as ref1 " +
            " on pmet2.pkid = ref1.entity_type_id  where ref1.eit='0,1' ")
    List<Map> selEntityByDatasetType();


    @Select("select * from pld_md_entitytypes_tem t where not exists(select * from pld_md_entitytypes p where \n" +
            " t.entity_type_code = p.entity_type_code and p.project_id = #{projectId}) and t.subject_id=#{subjectId} and t.stage_inner_code like '%${stage}%'")
    List<Entitytypestem> diffrentList(@Param("projectId") String projectId, @Param("subjectId") String subjectId,@Param("stage") String stage);

    @Select("select t.pkid, t.entity_type_alias as entitytypealias from pld_md_entitytypes_tem t " )
    List<Map> selAllEntity();
}
