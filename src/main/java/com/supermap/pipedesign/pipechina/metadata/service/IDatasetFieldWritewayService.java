package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.DatasetFieldWriteway;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Repository
public interface IDatasetFieldWritewayService extends IService<DatasetFieldWriteway> {

 /**
 * 添加信息
 *
 * @param datasetFieldWriteway
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int insert(DatasetFieldWriteway datasetFieldWriteway);

 /**
 * 删除信息
 *
 * @param datasetFieldWritewayId
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int delete(String datasetFieldWritewayId);

 /**
 * 更新信息
 *
 * @param datasetFieldWriteway
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int update(DatasetFieldWriteway datasetFieldWriteway);

 /**
 * 全部查询
 *
 * @param datasetFieldWriteway
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.DatasetFieldWriteway>
 * @Date 2023-04-14
 * @auther eomer
 */
 List<DatasetFieldWriteway> list(DatasetFieldWriteway datasetFieldWriteway);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-04-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
