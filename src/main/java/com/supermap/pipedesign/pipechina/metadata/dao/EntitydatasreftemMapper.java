package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.Entitydatasreftem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 实体数据关联表(存储每类实体关联的不同类型数据集的ID) Mapper 接口
 * 模板
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Mapper
public interface EntitydatasreftemMapper extends BaseMapper<Entitydatasreftem> {

    @Select("select data_set_id from pld_md_entity_datasref_tem where entity_type_id=#{entitytypeid}" )
    List<Entitydatasreftem> selByEntityTypeId(String entitytypeid);


    @Select("select data_set_id from pld_md_entity_datasref_tem where entity_type_id=#{entitytypeid} and data_set_type=#{datasettype}" )
    Entitydatasreftem selByEtIdAndDsType(@Param("entitytypeid") String entitytypeid,@Param("datasettype") Integer datasettype);
}
