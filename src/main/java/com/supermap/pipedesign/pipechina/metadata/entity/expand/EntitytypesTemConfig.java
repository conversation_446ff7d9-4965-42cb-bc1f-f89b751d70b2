package com.supermap.pipedesign.pipechina.metadata.entity.expand;

import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.metadata.entity.*;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.DatasetfieldtemVo;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.EntityTypesEngUsageTable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title EntitytypesTemConfig
 * @date 2023/02/12 10:54
 */
@Data
@ApiModel(value="EntitytypesTemConfig", description="实体类型-配置")
public class EntitytypesTemConfig {

    //默认风格
    @ApiModelProperty(value = "实体类型")
    private Entitytypestem entitytypestem;

    @ApiModelProperty(value = "交互方式")
    private DatasetTypeClass datasetType;

    @ApiModelProperty(value = "默认风格点")
    private StylesClassTem p;

    @ApiModelProperty(value = "默认风格面")
    private StylesClassTem r;

    @ApiModelProperty(value = "默认风格线")
    private StylesClassTem l;

    //字段
    @ApiModelProperty(value = "字段")
    private List<Datasetfieldtem> datasetfields;

    @ApiModelProperty(value = "字段")
    private List<DatasetfieldtemVo> datasetfieldsVo;


    @ApiModelProperty(value = "工程量信息表")
    private EntityTypesEngUsageTable entityTypesEngUsageTable;

    @ApiModelProperty(value = "字典值域")
    private List<Dictionary> dictionaryList;

    @ApiModelProperty(value = "通用图")
    private String reusediagram;

    @ApiModelProperty(value = "插值表集合")
    List<EntitytypesInterpolationTemEx> tableTemList;

}

