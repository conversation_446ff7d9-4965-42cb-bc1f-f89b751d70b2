package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 实体元数据定义表(定义实体类型的元数据项) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_entitytype_metadatas")
@ApiModel(value="EntitytypeMetadatas对象", description="实体元数据定义表(定义实体类型的元数据项)")
public class EntitytypeMetadatas implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "实体类型编号")
    @TableField("entity_type_id")
    private String entitytypeid;

    @ApiModelProperty(value = "元数据名称")
    @TableField("metadata_name")
    private String metadataname;

    @ApiModelProperty(value = "元数据别名")
    @TableField("metadata_alias")
    private String metadataalias;

    @ApiModelProperty(value = "顺序号")
    @TableField("order_index")
    private Integer orderindex;

    @ApiModelProperty(value = "字段类型(1:整型;2:浮点型;3:字符串型;4:日期型)")
    @TableField("fld_type")
    private String fldtype;

    @ApiModelProperty(value = "字段大小")
    @TableField("fld_size")
    private Integer fldsize;

    @ApiModelProperty(value = "是否必须存在(是否必须存在)")
    @TableField("is_required")
    private Boolean isrequired;

    @ApiModelProperty(value = "取值范围(内容之间用英文逗号分隔，如果内容不为空，必须从其中选择一个当作值内容)")
    @TableField("field_range")
    private String fieldrange;

    @ApiModelProperty(value = "是否为空")
    @TableField("is_empiy")
    private Boolean isempiy;

    @ApiModelProperty(value = "数据类型")
    @TableField("field_type")
    private String fieldtype;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "数据格式")
    @TableField("data_format")
    private String dataformat;

    @ApiModelProperty(value = "最大值")
    @TableField("max_values")
    private String maxvalues;

    @ApiModelProperty(value = "最小值")
    @TableField("min_values")
    private String minvalues;

    @ApiModelProperty(value = "所属项目ID")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "数据类型")
    @TableField("data_type")
    private String datatype;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
