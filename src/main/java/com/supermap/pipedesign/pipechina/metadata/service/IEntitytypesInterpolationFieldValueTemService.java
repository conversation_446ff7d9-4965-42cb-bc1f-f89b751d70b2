package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationFieldValueTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 实体-插值表-字段表-存储值 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@Repository
public interface IEntitytypesInterpolationFieldValueTemService extends IService<EntitytypesInterpolationFieldValueTem> {

 /**
 * 添加实体-插值表-字段表-存储值信息
 *
 * @param entitytypesInterpolationFieldValueTem
 * @return int
 * @Date 2023-03-25
 * @auther eomer
 */
 int insert(EntitytypesInterpolationFieldValueTem entitytypesInterpolationFieldValueTem);

 /**
 * 删除实体-插值表-字段表-存储值信息
 *
 * @param entitytypesInterpolationFieldValueTemId
 * @return int
 * @Date 2023-03-25
 * @auther eomer
 */
 int delete(String entitytypesInterpolationFieldValueTemId);

 /**
 * 更新实体-插值表-字段表-存储值信息
 *
 * @param entitytypesInterpolationFieldValueTem
 * @return int
 * @Date 2023-03-25
 * @auther eomer
 */
 int update(EntitytypesInterpolationFieldValueTem entitytypesInterpolationFieldValueTem);

 /**
 * 全部查询
 *
 * @param entitytypesInterpolationFieldValueTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationFieldValueTem>
 * @Date 2023-03-25
 * @auther eomer
 */
 List<EntitytypesInterpolationFieldValueTem> list(EntitytypesInterpolationFieldValueTem entitytypesInterpolationFieldValueTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-25
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
