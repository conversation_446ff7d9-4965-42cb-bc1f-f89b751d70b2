package com.supermap.pipedesign.pipechina.metadata.entity.BO;

import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AutoEntityRuleVo {

    @ApiModelProperty(value = "实体类型id")
    private String entityTypePkid;

    @ApiModelProperty(value = "实体类型名称")
    private String entityTypeName;

    @ApiModelProperty(value = "实体展示形式")
    private String showType;

    @ApiModelProperty(value = "实体交互形式")
    private String interactivetype;

    @ApiModelProperty(value = "实体交互形式(字典)")
    private Dictionary dictionary;

}
