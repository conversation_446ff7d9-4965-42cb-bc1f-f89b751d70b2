package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.metadata.dao.DatasynchronizationMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.Datasynchronization;
import com.supermap.pipedesign.pipechina.metadata.service.IDatasynchronizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据同步目录配置
 * <AUTHOR>
 * @date 2023/2/17 11:40
 */
@Service("DatasynchronizationImpl")
public class DatasynchronizationImpl extends ServiceImpl<DatasynchronizationMapper, Datasynchronization> implements IDatasynchronizationService {

    @Autowired
    private DatasynchronizationMapper datasynchronizationMapper;

    /**
     * 查询
     * @param userId
     * @param machinecode
     * @return
     */
    @Override
    public List<Datasynchronization> selectOne(String userId, String machinecode) {
        LambdaQueryWrapper<Datasynchronization> lqw=new LambdaQueryWrapper<>();
        lqw.eq(Datasynchronization::getUserid,userId).eq(Datasynchronization::getMachinecode,machinecode);
        List<Datasynchronization> datasynchronization = datasynchronizationMapper.selectList(lqw);
        return datasynchronization;

    }

    /**
     * 新增
     * @param datasynchronization
     * @return
     */
    @Override
    public int create(String userId , Datasynchronization datasynchronization) {
        datasynchronization.setUserid(userId);
        return datasynchronizationMapper.insert(datasynchronization);
    }
}
