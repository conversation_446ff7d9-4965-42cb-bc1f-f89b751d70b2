package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.DatasetFieldWritewayValueTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 写值方式关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Repository
public interface IDatasetFieldWritewayValueTemService extends IService<DatasetFieldWritewayValueTem> {

 /**
 * 添加写值方式关联表信息
 *
 * @param datasetFieldWritewayValueTem
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int insert(DatasetFieldWritewayValueTem datasetFieldWritewayValueTem);

 /**
 * 删除写值方式关联表信息
 *
 * @param datasetFieldWritewayValueTemId
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int delete(String datasetFieldWritewayValueTemId);

 /**
 * 更新写值方式关联表信息
 *
 * @param datasetFieldWritewayValueTem
 * @return int
 * @Date 2023-04-14
 * @auther eomer
 */
 int update(DatasetFieldWritewayValueTem datasetFieldWritewayValueTem);

 /**
 * 全部查询
 *
 * @param datasetFieldWritewayValueTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.DatasetFieldWritewayValueTem>
 * @Date 2023-04-14
 * @auther eomer
 */
 List<DatasetFieldWritewayValueTem> list(DatasetFieldWritewayValueTem datasetFieldWritewayValueTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-04-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
