package com.supermap.pipedesign.pipechina.metadata.entity.expand;

import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.DatasetfieldtemVo;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.EntityTypesEngUsageTable;
import com.supermap.pipedesign.pipechina.metadata.entity.Datasetfield;
import com.supermap.pipedesign.pipechina.metadata.entity.Entitytypes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Name EntitytypesConfig
 * @Desc 实体类型配置
 * <AUTHOR>
 * @Date 2022/12/28 11:47
 */
@Data
@ApiModel(value="EntitytypesConfig", description="实体类型-配置")
public class EntitytypesConfig {

    private Entitytypes entitytypestem;

    @ApiModelProperty(value = "交互方式")
    private DatasetTypeClass datasetType;

    @ApiModelProperty(value = "默认风格点")
    private StylesClass p;

    @ApiModelProperty(value = "默认风格面")
    private StylesClass r;

    @ApiModelProperty(value = "默认风格线")
    private StylesClass l;

    //字段
    @ApiModelProperty(value = "字段")
    private List<Datasetfield> datasetfields;

    @ApiModelProperty(value = "字段")
    private List<DatasetfieldtemVo> datasetfieldsVo;


    @ApiModelProperty(value = "工程量信息表")
    private EntityTypesEngUsageTable entityTypesEngUsageTable;

    @ApiModelProperty(value = "字典值域")
    private List<Dictionary> dictionaryList;

    @ApiModelProperty(value = "通用图")
    private String reusediagram;

    @ApiModelProperty(value = "插值表集合")
    List<EntitytypesInterpolationEx> tableTemList;

}
