package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_dataset_field_writeway")
@ApiModel(value="DatasetFieldWriteway对象", description="")
public class DatasetFieldWriteway implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "实体id")
    @TableField("entity_type_id")
    private String entityTypeId;

    @ApiModelProperty(value = "实体属性id")
    @TableField("dataset_field_id")
    private String datasetFieldId;

    @ApiModelProperty(value = "写值类型")
    @TableField("write_type")
    private String writeType;

    @ApiModelProperty(value = "内置函数、值域属性")
    @TableField("write_type_att")
    private String writeTypeAtt;

    @ApiModelProperty(value = "运算公式(1加2减3乘4除)")
    @TableField("operation_formula")
    private String operationFormula;

    @ApiModelProperty(value = "运算数字")
    @TableField("operation_num")
    private BigDecimal operationNum;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;


}
