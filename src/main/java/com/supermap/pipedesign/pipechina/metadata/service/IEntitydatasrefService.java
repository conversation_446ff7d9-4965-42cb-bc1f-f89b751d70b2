package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.Entitydatasref;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 实体数据关联表(存储每类实体关联的不同类型数据集的ID) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IEntitydatasrefService extends IService<Entitydatasref> {

 /**
 * 添加实体数据关联表(存储每类实体关联的不同类型数据集的ID)信息
 *
 * @param entitydatasref
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Entitydatasref entitydatasref);

 /**
 * 删除实体数据关联表(存储每类实体关联的不同类型数据集的ID)信息
 *
 * @param entitydatasrefId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String entitydatasrefId);

 /**
 * 更新实体数据关联表(存储每类实体关联的不同类型数据集的ID)信息
 *
 * @param entitydatasref
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Entitydatasref entitydatasref);

 /**
 * 全部查询
 *
 * @param entitydatasref
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Entitydatasref>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Entitydatasref> list(Entitydatasref entitydatasref);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
