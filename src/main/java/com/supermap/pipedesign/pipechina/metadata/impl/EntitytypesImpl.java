package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.common.entity.DirTree;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.BlockMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.*;
import com.supermap.pipedesign.pipechina.engineering.entity.*;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.DictionaryVo;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.FileRoot;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.ImportFileEntity;
import com.supermap.pipedesign.pipechina.engineering.entity.VO.RuleNames;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.CenterlineOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.impl.CenterlineOkImpl;
import com.supermap.pipedesign.pipechina.metadata.dao.*;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.*;
import com.supermap.pipedesign.pipechina.metadata.entity.*;
import com.supermap.pipedesign.pipechina.metadata.entity.expand.EntitytypesConfig;
import com.supermap.pipedesign.pipechina.metadata.entity.expand.EntitytypesEx;
import com.supermap.pipedesign.pipechina.metadata.entity.expand.EntitytypesTemConfig;
import com.supermap.pipedesign.pipechina.metadata.service.IEntitytypesService;
import com.supermap.pipedesign.pipechina.rules.dao.AlgorithmRegisterMapper;
import com.supermap.pipedesign.pipechina.rules.dao.GeneralDrawingMapper;
import com.supermap.pipedesign.pipechina.rules.entity.GeneralDrawing;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.gson.GsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 实体类型定义表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Service("EntitytypesImpl")
public class EntitytypesImpl extends ServiceImpl<EntitytypesMapper, Entitytypes>
        implements IEntitytypesService {

    @Autowired
    private EntitytypesMapper entitytypesMapper;

    @Autowired
    private DatasetinfoMapper datasetinfoMapper;

    // 文件清单
    @Autowired
    private WorkbagFileMapper workbagFileMapper;

    @Autowired
    private SubjectsMapper subjectsMapper;

    @Autowired
    private EntitytypestylesMapper entitytypestylesMapper;

    @Autowired
    private EntitytypethemestylesMapper entitytypethemestylesMapper;

    @Autowired
    private EntitydatasrefMapper entitydatasrefMapper;

    @Autowired
    private DatasetfieldMapper datasetfieldMapper;

    // 设计任务
    @Autowired
    private DesignsubTaskMapper designsubTaskMapper;

    @Autowired
    private SubmitentrustPartMapper submitentrustPartMapper;

    @Autowired
    private SubmitentrustMapper submitentrustMapper;

    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AlgorithmRegisterMapper algorithmRegisterMapper;

    @Autowired
    private BlockMapper blockMapper;

    @Autowired
    private GeneralDrawingMapper generalDrawingMapper;
    @Autowired
    private DirectoriesMapper directoriesMapper;
    @Autowired
    private DesignMapper designMapper;//设计段
    @Autowired
    private RulesTableMapper rulesTableMapper;//规则对应成果表名

    @Autowired
    private SubmitEntrustContentMapper submitEntrustContentMapper;//委托内容
   @Autowired
    private CenterlineOkImpl centerlineService;
    @Autowired
    private static SubmitentrustFileMapper submitentrustFileMapper;//互提资料清单

    @Autowired
    private RefMapper refMapper;


    /**
     * 全部查询
     *
     * @param entitytypes
     * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Entitytypes> @Date
     * 2022-12-17
     * @auther eomer
     */
    @Override
    public List<Entitytypes> list(Entitytypes entitytypes) {
        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        return entitytypesMapper.selectList(queryWrapper);
    }

    @Override
    public List<Entitytypes> getListBySort(String strOrderByFields, String strSortTypes) {
        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        if (strSortTypes.equalsIgnoreCase("asc")) {
            queryWrapper.orderByAsc(strOrderByFields);
        } else if (strSortTypes.equalsIgnoreCase("desc")) {
            queryWrapper.orderByDesc(strOrderByFields);
        }
        return entitytypesMapper.selectList(queryWrapper);
    }

    public List<Entitytypes> getListByProjectId(String projectId, String sortFields, String sortTypes){
        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        if (sortTypes.equalsIgnoreCase("asc")) {
            queryWrapper.orderByAsc(sortFields);
        } else if (sortTypes.equalsIgnoreCase("desc")) {
            queryWrapper.orderByDesc(sortFields);
        }
        queryWrapper.eq("project_id", projectId.trim());
        return entitytypesMapper.selectList(queryWrapper);
    }

    /**
     * 专业下的实体类型 根据id查
     *
     * @param subjectid
     * @return
     */
    @Override
    public List<Entitytypes> mdEntitytypesList(String subjectid) {
        Entitytypes mdEntitytypes = new Entitytypes();
        mdEntitytypes.setSubjectid(subjectid);
        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("pkid", "entity_type_name");
        queryWrapper.setEntity(mdEntitytypes);
        return entitytypesMapper.selectList(queryWrapper);
    }

    /**
     * 专业下的实体类型 根据筛选条件查
     *
     * @param seachname
     * @return
     */
    @Override
    public List<Entitytypes> mdEntityListByName(String seachname) {
        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("pkid", "entity_type_name", "subject_id");
        queryWrapper.like("entity_type_name", seachname);
        return entitytypesMapper.selectList(queryWrapper);
    }


    /**
     * 根据专业查实体类型
     *
     * @param subjectid
     * @return
     */
    @Override
    public IPage getListBySubjectid(
            long current, long size, String subjectid, String entrtyname, String projectid) {
        IPage<EntitytypesBO> entitytypesIPage = new Page<>();
        entitytypesIPage.setCurrent(current);
        entitytypesIPage.setSize(size);
        QueryWrapper<EntitytypesBO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("t.subject_id", subjectid);
        queryWrapper.eq("t.project_id", projectid);
        queryWrapper.ne("t.is_basics",0);
        if (StringUtils.isNotBlank(entrtyname)) {
            // wp -> wp.c().or().wp.d()
            queryWrapper.and(
                    qr ->
                            qr.like("t.entity_type_name", entrtyname)
                                    .or().like("t.entity_type_code", entrtyname)
                                    .or().like("t.entity_type_alias", entrtyname));
        }
        IPage<EntitytypesBO> listBySubjectid = entitytypesMapper.getListBySubjectid(entitytypesIPage, queryWrapper);
        List<EntitytypesBO> records = listBySubjectid.getRecords();
        for (EntitytypesBO bo : records) {
            if (JavaUtils.isNotEmtryOrNull(bo.getCreateuserid())) {
                List<User> users = userMapper.selByUserId(bo.getCreateuserid());
                if (JavaUtils.isEmtryOrNull(users) || users.size() == 0){
                    List<Ref> refs = refMapper.selectByUserId(bo.getCreateuserid());
                    if (!JavaUtils.isEmtryOrNull(refs)){
                        bo.setCreateuserid(refs.get(0).getUserName());
                    }
                }else{
                    bo.setCreateuserid(users.get(0).getUsername());
                }
            }
            if (JavaUtils.isNotEmtryOrNull(bo.getReusediagramid())) {
                String[] rsplit = bo.getReusediagramid().split(",");
                QueryWrapper<GeneralDrawing> generalqueryWrapper = new QueryWrapper<GeneralDrawing>();
                generalqueryWrapper.eq("pkid", rsplit[(rsplit.length - 1)]);
                generalqueryWrapper.eq("project_id", projectid);
                GeneralDrawing generalDrawingTem = generalDrawingMapper.selectOne(generalqueryWrapper);
                if (generalDrawingTem != null) {
                    bo.setReusediagramid(generalDrawingTem.getGdName());
                }
            }
            String stageName = "";
            if (JavaUtils.isNotEmtryOrNull(bo.getStageinnercode())) {
                String[] split = bo.getStageinnercode().split(",");
                for (int i = 0; i < split.length; i++) {
                    if (split[i].equals("0")) {
                        stageName += "、" + "预可研阶段";
                        continue;
                    }
                    if (split[i].equals("1")) {
                        stageName += "、" + "可研阶段";
                        continue;
                    }
                    if (split[i].equals("2")) {
                        stageName += "、" + "初设阶段";
                        continue;
                    }
                    if (split[i].equals("3")) {
                        stageName += "、" + "施工图阶段";
                        continue;
                    }
                    if (split[i].equals("4")) {
                        stageName += "、" + "竣工图阶段";
                        continue;
                    }
                }
                bo.setStageinnercode(stageName.substring(1));
            }

        }
        return listBySubjectid;
    }


    /**
     * 从模板_tem实体表复制到具体表
     *
     * @param projectid
     * @param stageid
     * @return
     */
    @Override
    public int insertTo(String projectid, int stageid) {
        entitytypesMapper.cpEntityTypes(projectid, String.valueOf(stageid));
        algorithmRegisterMapper.cpAlgorithmRule(projectid, String.valueOf(stageid));
        blockMapper.cpDrawingTemplate(projectid, String.valueOf(stageid));
        return 0;
/*    Map<String,String> newOldEntityPkid = new HashedMap<>();
    Map<String,Map<String,String>> newOldFieldEntityPkid = new HashedMap<>();
    QueryWrapper<Entitytypestem> queryWrapper = new QueryWrapper<>();
    queryWrapper.like("stage_inner_code", String.valueOf(stageid));
    int insert = 0;
    List<Entitytypestem> entitytypestems = entitytypestemMapper.selectList(queryWrapper);
    if (entitytypestems.size() > 0) {
      for (Entitytypestem tem : entitytypestems) {
        // TODO 这里遗漏了子实体 ？
        if (tem.getEntitytypealias().equals("中线桩")) {
          tem.getEntitytypename();
        }
        Entitytypes entitytypes = GsonUtil.ObjectToEntity(tem, Entitytypes.class);
        String newEntityTypePkid = UUID.randomUUID().toString();
        entitytypes.setProjectid(projectid);
        entitytypes.setPkid(newEntityTypePkid);
        // 实体不同步问题
        entitytypes.setIsShow(tem.getIsshow());
        entitytypes.setIsBasics(tem.getIsbasics());
        entitytypes.setEntityDesignType(tem.getEntitydesigntype());

        // 插入实体表
        insert = entitytypesMapper.insert(entitytypes);
        for (Entitytypestem nty : entitytypestems) {
          if (JavaUtils.isNotEmtryOrNull(nty.getParententityid()) && nty.getParententityid().equals(tem.getPkid())) {
            nty.setParententityid(entitytypes.getPkid());
          }
        }
        //实体风格
        QueryWrapper<Entitytypestylestem> entitytypestylestemQueryWrapper = new QueryWrapper<>();
        entitytypestylestemQueryWrapper.eq("entity_type_id",tem.getPkid());
        List<Entitytypestylestem> entitytypestylestems = entitytypestylestemMapper.selectList(entitytypestylestemQueryWrapper);
        for (Entitytypestylestem entitytypestylestem : entitytypestylestems) {
          Entitytypestyles entitytypestyles = GsonUtil.ObjectToEntity(entitytypestylestem, Entitytypestyles.class);
          entitytypestyles.setEntitytypeid(entitytypes.getPkid());
          entitytypestyles.setPkid(UUID.randomUUID().toString());
          entitytypestylesMapper.insert(entitytypestyles);
        }
        //数据集 属性字段等
        Map<String,String> newOldFieldMap
                 = entitydatasreftemService.insertToDatasreftem(tem.getPkid(), entitytypes.getPkid(),entitytypes.getEntitytypecode(),projectid);
        newOldFieldEntityPkid.put(tem.getPkid(),newOldFieldMap);
        newOldEntityPkid.put(tem.getPkid(),newEntityTypePkid);
      }

      *//**
         * @Description 创建项目复制规则目录树
         * <AUTHOR>
         *//*

    }*/
        /*return 0;*/
        /*   entitytypesExtendImpl.cpRulesTreeList( projectid,newOldEntityPkid,stageid,newOldFieldEntityPkid );*/
    }

    @Override
    public List<Entitytypes> getListBySubjects(String subjectsName, String projectid, Integer isAutomatic) {
        QueryWrapper<Subjects> subjectsQueryWrapper = new QueryWrapper<>();
        subjectsQueryWrapper.eq("subject_name", subjectsName);
        Subjects subjects = subjectsMapper.selectOne(subjectsQueryWrapper);
        if (subjects == null) {
            throw new BusinessException("未找到该专业，请核查！");
        }
        // 根据专业id查询数据
        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectid);
        queryWrapper.eq("subject_id", subjects.getPkid());
        if (isAutomatic < 2) {
            queryWrapper.eq("is_auto_matic", isAutomatic);
        }
        return entitytypesMapper.selectList(queryWrapper);
    }

    /**
     * 实体类型-树
     *
     * @param subjectsName 专业类型名称
     * @param projectid    项目编号
     * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.expand.EntitytypesEx> @Date
     * 2022/12/20 14:02
     * @auther eomer
     */
    @Override
    public List<EntitytypesEx> treeBySubjects(String subjectsName, String projectid, Integer isAutomatic) {
        List<Entitytypes> treeList = getListBySubjects(subjectsName, projectid, isAutomatic);
        // 获取实体树
        List<EntitytypesEx> tree = new ArrayList<>();
        if (treeList != null) {
            tree = getTree(treeList, null, treeList.get(0).getSubjectid());
        }
        return tree;
    }

    /**
     * 设计目录树
     *
     * @param taskId
     * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.expand.EntitytypesEx> @Date
     * 2022/12/30 14:16
     * @auther eomer
     */
    @Override
    public DirTree tree(String taskId) {
//        DirTree root = new DirTree();
//        root.setId(taskId);
//        // 查询设计任务信息
//        Map taskInfo = designsubTaskMapper.selectNameById(taskId);
//
//        String projectid = (String) taskInfo.get("project_id");
//        String taskname = (String) taskInfo.get("taskname");
//        root.setName(taskname);
//
//        List<DirTree> child = new ArrayList<>();
//        // 获取测量数据
//        //获取质检树
////        List<DirTree> trustTree = getTrustTree1(taskId);
////        child.addAll(trustTree);
//        //第三方数据目录树加载
//        DirTree thirdTree = getThirdTree("b2cf5f33afcab5194424235cc7b92390",taskId);
//        child.add(thirdTree);
//        // 获取勘察数据
//        // 获取设计数据
//        List<DirTree> designTree = getDesignTree(projectid, taskId);
//
//        child.addAll(designTree);
//        root.setChildren(child);
        return getSurveyDirTree(taskId);
    }

    //获取勘察目录树
    public DirTree getSurveyDirTree(String taskId) {
        //根据审计任务查子工程
        DesignsubTask designsubTask = designsubTaskMapper.selectById(taskId);
        //项目id
        String projectId = designsubTask.getProjectid();
        //专业id
        String subjectName="勘察";
        DirTree root = new DirTree();
        root.setName("勘察数据");
        root.setId("k");
        root.setPId(projectId);
        try {

            QueryWrapper<Subjects> queryWrapperSubjects = new QueryWrapper<>();
            queryWrapperSubjects.eq("subject_name", subjectName);
            Subjects Subjects = subjectsMapper.selectOne(queryWrapperSubjects);
            String subjectID=Subjects.getPkid();
            //设计段
            QueryWrapper<Design> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("pkid", designsubTask.getDesignid());
            Design design = designMapper.selectOne(queryWrapper);
            //起始桩号
            String startPileNum = design.getStakepointnostart();
            //结束桩号
            String endPileNum = design.getStakepointnoend();
            //质检类型子集
            List<DirTree> meaChildTree = new ArrayList<>();
            ImportFileEntity importFileEntity = new ImportFileEntity();
            importFileEntity.setState("0");

            //一般段节点
            DirTree generalTree = new DirTree();
            generalTree.setPId("k");
            generalTree.setName("一般段");
            generalTree.setId("yibanduan");
            generalTree.setCus(importFileEntity);
            generalTree.setChildren(getGeneralList(startPileNum, endPileNum, projectId, 1, "yibanduan",subjectID));
            meaChildTree.add(generalTree);
            //单出图节点
            DirTree crossOverTree = new DirTree();
            crossOverTree.setPId("k");
            crossOverTree.setName("单出图线路段");
            crossOverTree.setCus(importFileEntity);
            crossOverTree.setId("danchutu");
            crossOverTree.setChildren(getLeacesDirTreeList(startPileNum, endPileNum, projectId, 3, "danchutu",subjectID));
            meaChildTree.add(crossOverTree);
            //大小型穿跨越节点
            DirTree singleTree = new DirTree();
            singleTree.setPId("k");
            singleTree.setName("大中型穿跨越段");
            singleTree.setCus(importFileEntity);
            singleTree.setId("chuankuayue");
            singleTree.setChildren(getLeacesDirTreeList(startPileNum, endPileNum, projectId, 2, "chuankuayue",subjectID));
            meaChildTree.add(singleTree);
            root.setChildren(meaChildTree);

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return root;
    }

    //一般段树节点下的子集
    public List<DirTree> getGeneralList(String startPileNum, String endPileNum, String projectId, int type, String pid,String subjectID) {
        List<DirTree> generalList = new ArrayList<>();
        List<String> contentIdList = getContentIdList(startPileNum, endPileNum, projectId, type,subjectID);
        QueryWrapper<RuleNames> queryWrap =  new QueryWrapper<>();
        queryWrap.in("content_id",contentIdList);
        List<RuleNames> ruleList = submitentrustFileMapper.getRuleList(queryWrap);
        for (RuleNames ruleNames : ruleList) {
            DirTree dirTre = new DirTree();
            dirTre.setPId(pid);
            dirTre.setName(ruleNames.getRuleName());
            // cus赋值实体
            FileRoot fileRoot = workbagFileMapper.getfile(ruleNames.getFileId());
            ImportFileEntity importFileEntity = new ImportFileEntity();
            importFileEntity.setState("0");
            importFileEntity.setFileOriginPath(fileRoot.getFileurl());
            importFileEntity.setFileidOrnasPath(fileRoot.getFileidOrnasPath());
            importFileEntity.setCacheUrl(fileRoot.getCacheUrl());
            importFileEntity.setIserverUrl(fileRoot.getIserverUrl());
            dirTre.setCus(importFileEntity);
            generalList.add(dirTre);
        }
        return generalList;
    }

    //合并同类项返回内容id集合
    public List<String> getContentIdList(String startPileNum, String endPileNum, String projectId, int type,String subjectID) {
        List<String> contentIdList = new ArrayList<>();
        List<String> pileNumList = new ArrayList<>();
        //获取中线范围
        List<CenterlineOk> centerlineOks = centerlineService.acquireStakePointNo(startPileNum, endPileNum, projectId);
        for (CenterlineOk centerlineOk : centerlineOks) {
            pileNumList.add(centerlineOk.getStakePointNo());
        }
        //开始合并同类项（获取规则）
        QueryWrapper<SubmitEntrustContent> queryWrapper=new QueryWrapper();
        queryWrapper.in("stake_point_no_start",pileNumList);
        queryWrapper.in("stake_point_no_end",pileNumList);
        queryWrapper.eq("project_id",projectId);
        queryWrapper.eq("design_type",type);
        queryWrapper.eq("subject_id",subjectID);

        List<SubmitEntrustContent> startContentList = submitEntrustContentMapper.getStartContentList(queryWrapper);
        for (SubmitEntrustContent submitEntrustContent : startContentList) {
            contentIdList.add(submitEntrustContent.getPkid());
        }
        return contentIdList;
    }

    //获取勘察测量的线路段下的叶子集合（除了一般线路段）
    public List<DirTree> getLeacesDirTreeList(String startPileNum, String endPileNum, String projectId, int type, String pid,String subjectID) {
        List<DirTree> leacesDirTreeList = new ArrayList<>();
        List<String> contentIdList = getContentIdList(startPileNum, endPileNum, projectId, type,subjectID);
        QueryWrapper<SubmitEntrustContent> queryWrapper=new QueryWrapper();
        queryWrapper.in("pkid",contentIdList);
        //获取委托内容集合
        List<SubmitEntrustContent> startContentList = submitEntrustContentMapper.getContentList(queryWrapper);
        for (SubmitEntrustContent submitEntrustContent : startContentList) {
            ImportFileEntity importFileEntity = new ImportFileEntity();
            importFileEntity.setState("0");
            DirTree contentTree = new DirTree();
            contentTree.setCus(importFileEntity);
            contentTree.setId(submitEntrustContent.getPkid());
            contentTree.setName(submitEntrustContent.getContentName());
            contentTree.setPId(pid);
            contentTree.setChildren(getRuleList(submitEntrustContent.getPkid()));
            leacesDirTreeList.add(contentTree);
        }
        return leacesDirTreeList;
    }
     //规则节点
    private List<DirTree> getRuleList(String contentId) {
        List<DirTree> ruleTreeList = new ArrayList<>();
        //互提资料反馈文件清单 根据委托分段id查询所需要质检的规则
        QueryWrapper<SubmitentrustFile> queryWrapperFile = new QueryWrapper<>();
        queryWrapperFile.eq("content_id", contentId);
        List<SubmitentrustFile> submitentrustFileList = submitentrustFileMapper.selectList(queryWrapperFile);
        for (SubmitentrustFile submitentrustFile : submitentrustFileList) {
            DirTree ruleTree = new DirTree();
            //项目分解id
            ruleTree.setPId(contentId);
            //规则id
            ruleTree.setId(submitentrustFile.getFileid());
            //规则名称
            ruleTree.setName(submitentrustFile.getFileName());
            QueryWrapper<RulesTable> queryRulesTable=new QueryWrapper<>();
            queryRulesTable.eq("qua_rule_name", submitentrustFile.getFileName());
            queryRulesTable.eq("delete_flag", 0);
            RulesTable rulesTable=rulesTableMapper.selectOne(queryRulesTable);
            // cus赋值实体
            FileRoot fileRoot = workbagFileMapper.getfile(submitentrustFile.getWorkBagFileId());
            ImportFileEntity importFileEntity = new ImportFileEntity();
            importFileEntity.setFileOriginPath(fileRoot.getFileurl());
            importFileEntity.setDataDefine(rulesTable.getTableName());
            importFileEntity.setFileidOrnasPath(fileRoot.getFileidOrnasPath());
            importFileEntity.setCacheUrl(fileRoot.getCacheUrl());
            importFileEntity.setIserverUrl(fileRoot.getIserverUrl());
            importFileEntity.setRootType(3);
            importFileEntity.setQueRule(submitentrustFile.getFileName());
            importFileEntity.setPartId(submitentrustFile.getPartId());
            ruleTree.setCus(importFileEntity);
            //添加树集合
            ruleTreeList.add(ruleTree);
        }
        return ruleTreeList;
    }



    /**
     * @return
     */
    private DirTree getThirdTree(String projectid, String taskId) {
        DirTree thirdTree = new DirTree();

        QueryWrapper<Directories> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectid);
        queryWrapper.eq("node_name", "第三方数据");
        Directories directories = directoriesMapper.selectOne(queryWrapper);

        thirdTree.setId(directories.getPkid());
        thirdTree.setName(directories.getNodename());
        thirdTree.setPId(taskId);
        thirdTree.setChildren(getList(projectid, directories.getPkid()));
        return thirdTree;
    }

    //递归组织树结构
    public List<DirTree> getList(String projectid, String pid) {
        List<ThirdPartyData> directoriesList = directoriesMapper.getThirdTree(projectid);
        List<DirTree> dirTreeList = new ArrayList<>();
        for (ThirdPartyData thirdPartyData : directoriesList) {
            if (pid.equals(thirdPartyData.getParentid()) && thirdPartyData != null) {
                // cus赋值实体
                ImportFileEntity importFileEntity = new ImportFileEntity();
                importFileEntity.setFileidOrnasPath(thirdPartyData.getResourceid());
                importFileEntity.setDataDefine(thirdPartyData.getResourcetype());
                DirTree thirdTree = new DirTree();
                thirdTree.setId(thirdPartyData.getPkid());
                thirdTree.setName(thirdPartyData.getNodename());
                thirdTree.setPId(thirdPartyData.getParentid());
                thirdTree.setCus(importFileEntity);
                thirdTree.setChildren(getList(projectid, thirdPartyData.getPkid()));
                dirTreeList.add(thirdTree);
            }
        }

        return dirTreeList;
    }

    @Override
    public List<DirTree> qualityTree(String taskId) {
        return getTrustTree1(taskId);
    }
//    public List<DirTree> getTrustTree(String taskId) {
//        //根据委托单id查质检专业
////    List<String> codeList = tmpDirectoryMapper.selectCode(trustIdList);
//        List<String> codeList = new ArrayList<>();
//        codeList.add("测量");
//        codeList.add("勘察");
//        List<DirTree> nodTree = new ArrayList<>();
//        for (int j = 0; j < codeList.size(); j++) {
//            DirTree dirTre = new DirTree();
//            dirTre.setName(codeList.get(j) + "数据");
//            //根据审计任务查子工程
//            DesignsubTask designsubTask = designsubTaskMapper.selectById(taskId);
//            //子工程id
//            String subId = designsubTask.getSubprojectid();
//            List<String> list = submitentrustPartMapper.selectTrustIdBySubId(subId);
//            //根据设计任务id查委托单
//            List<String> trustIdList = submitentrustPartMapper.selectTrustIdByTaskId(taskId);
//            list.addAll(trustIdList);
//            List<DirTree> thTree = new ArrayList<>();
//            //排除子工程的设计段下委托单
//            for (int i = 0; i < list.size(); i++) {
//                List<DirTree> childTree = new ArrayList<>();
//                String submitentrustid = list.get(i);
//                //查询委托 获取委托主题
//                Submitentrust submitentrust = submitentrustMapper.selectById(list.get(i));
//                if (!submitentrust.getAcceptsubject().equals(codeList.get(j))) {
//                    continue;
//                }
//                //委托主题节点
//                DirTree themeTree = new DirTree();
//                themeTree.setName(submitentrust.getTheme());
//
//                // 通过委托单id获取目录id类型    详情
//                QueryWrapper<SubmitentrustPart> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("sub_miten_trust_id", submitentrustid);
//                SubmitentrustPart submitentrustPart = submitentrustPartMapper.selectOne(queryWrapper);
//                // 子工程
//                String subprojectid = submitentrustPart.getSubprojectid();
//                EngineeringSub engineeringSub = engineeringSubMapper.selectById(subprojectid);
//                // 子工程
//                DirTree subTree = new DirTree();
//                subTree.setId(engineeringSub.getPkid());
//                subTree.setName(engineeringSub.getSubname());
//                subTree.setType("0");
//                subTree.setPId(dirTre.getId());
//
//                // 如果是子工程 - 依次查询子节点
//                if (submitentrustPart.getFileidtype() == 0) {
//                    // 工程单元列表
//                    List<DirTree> dirTreeList = submitentrustPartImpl.getSPTForSubProject(subprojectid, 0,0);
//                    List<DirTree> fileList = submitentrustPartImpl.getFileTreeById(subprojectid, "0",0);
//                    dirTreeList.addAll(fileList);
//                    subTree.setChildren(dirTreeList);
//                }
//                // 如果是工程单元
//                else if (submitentrustPart.getFileidtype() == 1) {
//                    List<DirTree> dirTreeList = new ArrayList<>();
//                    String projectunitid = submitentrustPart.getProjectunitid();
//                    ESUnit esUnit = eSUnitMapper.selectById(projectunitid);
//                    DirTree unitTree = new DirTree();
//                    unitTree.setId(esUnit.getPkid());
//                    unitTree.setName(esUnit.getProjectunitname());
//                    unitTree.setType("1");
//                    unitTree.setPId(subprojectid);
//                    // 功能区
//                    List funTree = submitentrustPartImpl.getFunTree(esUnit.getPkid(), 0,0);
//                    unitTree.setChildren(funTree);
//                    dirTreeList.add(unitTree);
//                    //子工程文件
//                    List<DirTree> fileList = submitentrustPartImpl.getFileTreeById(subprojectid, "0",0);
//                    dirTreeList.addAll(fileList);
//                    subTree.setChildren(dirTreeList);
//
//                }
//                // 如果是功能区
//                else if (submitentrustPart.getFileidtype() == 2) {
//                    // 工程单元
//                    String projectunitid = submitentrustPart.getProjectunitid();
//                    ESUnit esUnit = eSUnitMapper.selectById(projectunitid);
//                    DirTree unitTree = new DirTree();
//                    unitTree.setId(esUnit.getPkid());
//                    unitTree.setName(esUnit.getProjectunitname());
//                    unitTree.setType("1");
//                    unitTree.setPId(subprojectid);
//                    // 功能区
//                    ESUFunctionarea esuFunctionarea = eSUFunctionareaMapper.selectById(submitentrustPart.getFunctionalareaid());
//                    DirTree funTree = new DirTree();
//                    funTree.setId(esuFunctionarea.getPkid());
//                    funTree.setName(esuFunctionarea.getFunctionareaname());
//                    funTree.setType("2");
//                    funTree.setPId(subprojectid);
//                    // 功能区文件清单列表
//                    List<DirTree> fileList = submitentrustPartImpl.getFileTreeById(esuFunctionarea.getPkid(), "2",0);
//                    funTree.setChildren(fileList);
//                    // 工程单元文件清单
//                    List<DirTree> unitFiles = submitentrustPartImpl.getFileTreeById(esUnit.getPkid(), "1",0);
//                    unitFiles.add(0, funTree);
//                    unitTree.setChildren(unitFiles);
//
//                    List<DirTree> unitList = new ArrayList<>();
//                    unitList.add(unitTree);
//                    //子工程文件
//                    List<DirTree> subFiles = submitentrustPartImpl.getFileTreeById(subprojectid, "0",0);
//                    unitList.addAll(subFiles);
//                    subTree.setChildren(unitList);
//                }
//                // 成果目录树
//                List<DirTree> subChild = submitentrustPartImpl.getListByCode(codeList.get(j), subprojectid);
//                List<DirTree> child = subTree.getChildren();
//                child.addAll(subChild);
//                subTree.setChildren(child);
////        themeTree.setChildren();
//                childTree.add(subTree);
//                //主题树
//                themeTree.setChildren(childTree);
//                thTree.add(themeTree);
//                // 测量、勘察 目录树
//                dirTre.setChildren(thTree);
////        dirTre.setChildren(childTree);
//            }
//            nodTree.add(dirTre);
//        }
//        return nodTree;
//    }

    public List<DirTree> getTrustTree1(String taskId) {
        String projectId = "";
        String subId = "";
        String unitID = "";
        String funUnitID = "";
        String ruleName = "";
        List<String> codeList = new ArrayList<>();
        codeList.add("测量");
        codeList.add("勘察");
        //反回设计树下的质检
        List<DirTree> nodTree = new ArrayList<>();
        for (int j = 0; j < codeList.size(); j++) {
            DirTree dirTre = new DirTree();
            dirTre.setName(codeList.get(j) + "数据");

            //根据审计任务查子工程
            DesignsubTask designsubTask = designsubTaskMapper.selectById(taskId);
            if (designsubTask == null) {
                continue;
            }
            //子工程id
            subId = designsubTask.getSubprojectid();
            List<String> list = submitentrustPartMapper.selectTrustIdBySubId(subId);
            //根据设计任务id查委托单
            List<String> trustIdList = submitentrustPartMapper.selectTrustIdByTaskId(taskId);
            list.addAll(trustIdList);
            //质检类型子集
            List<DirTree> meaChildTree = new ArrayList<>();
            //排除子工程的设计段下委托单
            for (int i = 0; i < list.size(); i++) {
                List<DirTree> childTree = new ArrayList<>();
                String submitentrustid = list.get(i);
                //查询委托 获取委托主题
                Submitentrust submitentrust = submitentrustMapper.selectById(list.get(i));
                // 通过委托单id获取目录id类型    详情
                QueryWrapper<SubmitentrustPart> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("sub_miten_trust_id", submitentrustid);
                SubmitentrustPart submitentrustPart = submitentrustPartMapper.selectOne(queryWrapper);

                projectId = submitentrustPart.getProjectid();

                if (submitentrustPart.getFileidtype() == 0) {
                    List<DirTree> thTree = new ArrayList<>();
                    if (codeList.get(j).equals("测量")) {
                        dirTre.setId("c");
                        dirTre.setPId(projectId);
                        thTree = getCeliang(projectId, subId, unitID, funUnitID, ruleName);
                    } else {
                        dirTre.setId("k");
                        dirTre.setPId(projectId);
                        thTree = getKanchaList(projectId, subId, unitID, funUnitID, ruleName);
                    }
                    meaChildTree.addAll(thTree);
                } else if (submitentrustPart.getFileidtype() == 1) {
                    List<DirTree> thTree = new ArrayList<>();
                    unitID = submitentrustPart.getProjectunitid();
                    if (codeList.get(j).equals("测量")) {
                        dirTre.setId("c");
                        dirTre.setPId(projectId);
                        thTree = getCeliang(projectId, subId, unitID, funUnitID, ruleName);
                    } else {
                        dirTre.setId("k");
                        dirTre.setPId(projectId);
                        thTree = getKanchaList(projectId, subId, unitID, funUnitID, ruleName);
                    }
                    meaChildTree.addAll(thTree);
                } else if (submitentrustPart.getFileidtype() == 2) {
                    List<DirTree> thTree = new ArrayList<>();
                    unitID = submitentrustPart.getProjectunitid();
                    funUnitID = submitentrustPart.getFunctionalareaid();
                    if (codeList.get(j).equals("测量")) {
                        dirTre.setId("c");
                        dirTre.setPId(projectId);
                        thTree = getCeliang(projectId, subId, unitID, funUnitID, ruleName);
                    } else {
                        dirTre.setId("k");
                        dirTre.setPId(projectId);
                        thTree = getKanchaList(projectId, subId, unitID, funUnitID, ruleName);
                    }
                    meaChildTree.addAll(thTree);
                }
                dirTre.setChildren(meaChildTree);
            }
            nodTree.add(dirTre);
        }
        return nodTree;
    }

    //查询文件源文件路径，服务地址，缓存地址
    private List<DirTree> getPath(String projectId, String subId, String unitID, String funUnitID, String ruleName) {
        List<DirTree> noExcelPath = new ArrayList<>();
        QueryWrapper<Map> queryWrapper = new QueryWrapper<>();

        if (!projectId.equals("")) {
            queryWrapper.eq("wf .project_id", projectId);
        }
        if (!subId.equals("")) {
            queryWrapper.eq("wf .sub_project_id", subId);
        }
        if (!unitID.equals("")) {
            queryWrapper.eq("wf .project_unit_id", unitID);
        }
        if (!funUnitID.equals("")) {
            queryWrapper.eq("wf .functional_area_id", funUnitID);
        }
        if (!ruleName.equals("")) {
            queryWrapper.eq("wf .quality_type", ruleName);
        }
        DesignVo designVo = workbagFileMapper.getPath(queryWrapper);
        //源文件路径地址节点
        DirTree dirTree1 = new DirTree();
        //文件iserver路径地址节点
        DirTree dirTree2 = new DirTree();
        //文件生成缓存路径地址节点
        DirTree dirTree3 = new DirTree();
        if (designVo != null) {
            if (StringUtils.isNotEmpty(designVo.getFileidOrnasPath())) {
                // cus赋值实体
                ImportFileEntity importFileEntity = new ImportFileEntity();
                dirTree1.setId(designVo.getPkid());
                dirTree1.setName("原始数据");
                importFileEntity.setFilePkid(designVo.getPkid());
                importFileEntity.setFileidOrnasPath(designVo.getFileidOrnasPath());
                importFileEntity.setFileType(designVo.getFilesuffix());
                importFileEntity.setOriginData(designVo.getFileName());
                importFileEntity.setQueRule(designVo.getQualityType());

                dirTree1.setCus(importFileEntity);
                noExcelPath.add(dirTree1);
            }
            if (StringUtils.isNotEmpty(designVo.getIserverUrl())) {
                // cus赋值实体
                ImportFileEntity importFileEntity = new ImportFileEntity();
                dirTree2.setId(designVo.getPkid());
                dirTree2.setName("在线服务");
                importFileEntity.setFilePkid(designVo.getPkid());
                importFileEntity.setIserverUrl(designVo.getIserverUrl());
                importFileEntity.setFileType(designVo.getFilesuffix());
                importFileEntity.setOriginData(designVo.getFileName());
                importFileEntity.setQueRule(designVo.getQualityType());
                dirTree2.setCus(importFileEntity);
                noExcelPath.add(dirTree2);
            }
            if (StringUtils.isNotEmpty(designVo.getCacheUrl())) {
                // cus赋值实体
                ImportFileEntity importFileEntity = new ImportFileEntity();
                dirTree3.setId(designVo.getPkid());
                dirTree3.setName("缓冲数据");
                importFileEntity.setFilePkid(designVo.getPkid());
                importFileEntity.setCacheUrl(designVo.getCacheUrl());
                importFileEntity.setFileType(designVo.getFilesuffix());
                importFileEntity.setOriginData(designVo.getFileName());
                importFileEntity.setQueRule(designVo.getQualityType());
                dirTree3.setCus(importFileEntity);
                noExcelPath.add(dirTree3);
            }
        }
        return noExcelPath;
    }

    //测量
    private List<DirTree> getCeliang(String projectId, String subId, String unitID, String funUnitID, String ruleName) {
        List<DirTree> meaChildTree = new ArrayList<>();
        //测量数据类型  @TODO
        String[] measure = {"中线数据", "控制点数据", "断面点数据", "数字线划图-GIS", "数字线划图-CAD", "数字正射影像图", "数字高程模型", "倾斜摄影数据", "激光点云数据"};
        for (String meaNoot : measure) {
            DirTree dirMeaTre = new DirTree();
            ruleName = meaNoot;
            dirMeaTre.setName(meaNoot);
            if (meaNoot.equals("中线数据")) {
                List<DirTree> cLineList = new ArrayList<>();
                cLineList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirMeaTre.setId("c1");
                dirMeaTre.setPId("c");
                dirMeaTre.setChildren(cLineList);
            } else if (meaNoot.equals("控制点数据")) {
                List<DirTree> conPointList = new ArrayList<>();
                conPointList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirMeaTre.setId("c2");
                dirMeaTre.setPId("c");
                dirMeaTre.setChildren(conPointList);
            } else if (meaNoot.equals("断面点数据")) {
                List<DirTree> sPointList = new ArrayList<>();
                sPointList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirMeaTre.setId("c3");
                dirMeaTre.setPId("c");
                dirMeaTre.setChildren(sPointList);
            } else if (meaNoot.equals("数字线划图-GIS")) {
                List<DirTree> gisList = new ArrayList<>();
                gisList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirMeaTre.setId("c4");
                dirMeaTre.setPId("c");
                dirMeaTre.setChildren(gisList);
            } else if (meaNoot.equals("数字线划图-CAD")) {
                List<DirTree> cadLineList = new ArrayList<>();
                cadLineList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirMeaTre.setId("c5");
                dirMeaTre.setPId("c");
                dirMeaTre.setChildren(cadLineList);
            } else if (meaNoot.equals("数字正射影像图")) {
                List<DirTree> domList = new ArrayList<>();
                domList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirMeaTre.setId("c6");
                dirMeaTre.setPId("c");
                dirMeaTre.setChildren(domList);
            } else if (meaNoot.equals("数字高程模型")) {
                List<DirTree> demList = new ArrayList<>();
                demList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirMeaTre.setId("c7");
                dirMeaTre.setPId("c");
                dirMeaTre.setChildren(demList);
            } else if (meaNoot.equals("倾斜摄影数据")) {
                List<DirTree> opList = new ArrayList<>();
                opList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirMeaTre.setId("c8");
                dirMeaTre.setPId("c");
                dirMeaTre.setChildren(opList);
            } else if (meaNoot.equals("激光点云数据")) {
                List<DirTree> lpcList = new ArrayList<>();
                lpcList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirMeaTre.setId("c9");
                dirMeaTre.setPId("c");
                dirMeaTre.setChildren(lpcList);
            }
            meaChildTree.add(dirMeaTre);
        }

        return meaChildTree;
    }

    //勘察
    private List<DirTree> getKanchaList(String projectId, String subId, String unitID, String funUnitID, String ruleName) {
        //勘察数据类型
        String[] survey = {"岩土工程勘察报告", "线路地质分段表", "地质剖面图表", "土壤电阻率表", "勘探点坐标表", "勘探点地层表"};
        List<DirTree> meaChildTree = new ArrayList<>();
        for (String surNoot : survey) {
            DirTree dirSurvey = new DirTree();
            dirSurvey.setName(surNoot);
            ruleName = surNoot;
            if (surNoot.equals("岩土工程勘察报告")) {
                List<DirTree> kanList = new ArrayList<>();
                kanList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirSurvey.setId("k1");
                dirSurvey.setPId("k");
                dirSurvey.setChildren(kanList);
            } else if (surNoot.equals("线路地质分段表")) {
                List<DirTree> xianList = new ArrayList<>();
                xianList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirSurvey.setId("k2");
                dirSurvey.setPId("k");
                dirSurvey.setChildren(xianList);
            } else if (surNoot.equals("地质剖面图表")) {
                List<DirTree> paoList = new ArrayList<>();
                paoList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirSurvey.setId("k3");
                dirSurvey.setPId("k");
                dirSurvey.setChildren(paoList);
            } else if (surNoot.equals("土壤电阻率表")) {
                List<DirTree> tuList = new ArrayList<>();
                tuList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirSurvey.setId("k4");
                dirSurvey.setPId("k");
                dirSurvey.setChildren(tuList);
            } else if (surNoot.equals("勘探点坐标表")) {
                List<DirTree> tanList = new ArrayList<>();
                tanList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirSurvey.setId("k5");
                dirSurvey.setPId("k");
                dirSurvey.setChildren(tanList);
            } else if (surNoot.equals("勘探点地层表")) {
                List<DirTree> pointList = new ArrayList<>();
                pointList = getPath(projectId, subId, unitID, funUnitID, ruleName);
                dirSurvey.setId("k6");
                dirSurvey.setPId("k");
                dirSurvey.setChildren(pointList);
            }
            meaChildTree.add(dirSurvey);
        }
        return meaChildTree;
    }

    /**
     * 获取设计目录树
     *
     * @param projectid 项目id
     * @param taskId    设计任务id
     * @return {@link java.util.List<com.supermap.common.entity.DirTree>} 设计目录树
     * <AUTHOR>
     * @date 2023/1/2 16:43
     */
    private List<DirTree> getDesignTree(String projectid, String taskId) {
        // 获取专业列表
        QueryWrapper<Subjects> subjectsQueryWrapper = new QueryWrapper<>();
        List<Subjects> subjects = subjectsMapper.selectList(subjectsQueryWrapper);
        // 专业列表转节点
        List<DirTree> nodeList = subjectsToTree(subjects, taskId);
        // 根据专业id查询数据
        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectid);
        // queryWrapper.eq("subjectid", subjects.getPkid());
        List<Entitytypes> treeList = entitytypesMapper.selectList(queryWrapper);
        // 获取实体树
        for (int i = 0; i < nodeList.size(); i++) {
            DirTree node = nodeList.get(i);
            List<EntitytypesEx> tree = getTree(treeList, null, nodeList.get(i).getId());
            List<DirTree> children = entityTypesToTree(tree);

            node.setChildren(children);
        }
        return nodeList;
    }

    /**
     * 实体树转目录树
     *
     * @param tree 实体树
     * @return {@link List< DirTree>} 目录树
     * <AUTHOR>
     * @date 2023/1/2 17:52
     */
    private List<DirTree> entityTypesToTree(List<EntitytypesEx> tree) {
        List<DirTree> list = new ArrayList<>();
        for (int i = 0; i < tree.size(); i++) {
            EntitytypesEx item = tree.get(i);

            DirTree node = new DirTree();
            node.setId(item.getPkid());
            node.setPId(item.getParententityid());
            node.setAlias(item.getEntitytypealias());
            node.setName(item.getEntitytypealias());
            node.setCode(item.getEntitytypename());
            node.setDirType("0"); // 设计
            node.setType("2"); // 实体
            node.setLeaf(false);
            node.setFixed(true);
            if (item.getChildren() != null && item.getChildren().size() > 0) {
                List<DirTree> children = entityTypesToTree(item.getChildren());
                node.setChildren(children);
            }
            list.add(node);
        }
        return list;
    }

    /**
     * 专业列表转树节点
     *
     * @param subjects 专业列表
     * @param taskId   设计任务id
     * @return {@link List< DirTree>}
     * <AUTHOR>
     * @date 2023/1/2 15:37
     */
    private List<DirTree> subjectsToTree(List<Subjects> subjects, String taskId) {

        List<DirTree> list = new ArrayList<>();
        for (int i = 0; i < subjects.size(); i++) {
            Subjects item = subjects.get(i);

            DirTree node = new DirTree();
            node.setId(item.getPkid());
            node.setPId(taskId);
            node.setAlias(item.getSubjectalias());
            node.setName(item.getSubjectname());
            node.setDirType("0"); // 设计
            node.setType("1"); // 专业
            node.setLeaf(false);
            node.setFixed(true);
            // node.setCus(item);
            list.add(node);
        }
        return list;
    }

    /**
     * 获取实体类型树
     *
     * @param treeList
     * @param pid
     * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.expand.EntitytypesEx> @Date
     * 2022/12/27 15:55
     * @auther eomer
     */
    private List<EntitytypesEx> getTree(List<Entitytypes> treeList, String pid, String subjectid) {

        List<EntitytypesEx> parent = new ArrayList<>();
        List<Entitytypes> other = new ArrayList<>();
        //
        pid = pid == null ? "" : pid;
        //
        for (int i = 0; i < treeList.size(); i++) {
            Entitytypes item = treeList.get(i);
            if (!subjectid.equals(item.getSubjectid())) {
                continue;
            }
            String parentId = item.getParententityid() == null ? "" : item.getParententityid();
            //
            if (pid.equals(parentId)) {
                EntitytypesEx child = GsonUtil.ObjectToEntity(item, EntitytypesEx.class);
                parent.add(child);
            } else {
                other.add(item);
            }
        }
        // 递归循环
        for (int i = 0; i < parent.size(); i++) {
            EntitytypesEx item = parent.get(i);
            // 子集
            List<EntitytypesEx> child = getTree(other, item.getPkid(), subjectid);
            item.setChildren(child);
        }

        return parent;
    }

    @Override
    public List<EntitytypesConfig> getEntityTypeChildren(String entitytypeid) {
        List<EntitytypesConfig> list = new ArrayList<>();
        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_entity_id", entitytypeid);
        List<Entitytypes> entitytypesList = entitytypesMapper.selectList(queryWrapper);
        for (Entitytypes entitytypes : entitytypesList) {
            EntitytypesConfig item = getEntitytypesConfig(entitytypes.getPkid());
            list.add(item);
        }
        return list;
    }

    /**
     * 实体类型-配置
     *
     * @param entitytypeid 实体类型id
     * @return com.supermap.pipedesign.pipechina.metadata.entity.expand.EntitytypesConfig @Date
     * 2022/12/28 11:49
     * @auther eomer
     */
    @Override
    public EntitytypesConfig entityTypeConfig(String entitytypeid) {
        // 根据专业id查询数据
        Entitytypes entitytypes = entitytypesMapper.selectById(entitytypeid);
        if (entitytypes == null) {
            throw new BusinessException("未找到该实体类型，请核查！");
        }
        EntitytypesConfig config = getEntitytypesConfig(entitytypeid);
        // 中线
    /*if ("CenterLine".equals(entitytypes.getEntitytypecode())) {
      // 获取中线桩
      QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
      queryWrapper.eq("projectid", entitytypes.getProjectid());
      queryWrapper.eq("subjectid", entitytypes.getSubjectid());
      queryWrapper.eq("entitytypecode", "CentrallinePile");
      Entitytypes zxzEntity = entitytypesMapper.selectOne(queryWrapper);
      EntitytypesConfig centerLinePoint = getEntitytypesConfig(zxzEntity.getPkid());
      config.setCenterLinePoint(centerLinePoint);
    }*/
        return config;
    }

    @Override
    public EntitytypesConfig centerLineConfig(String projectId, String userId) {
        //
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("user_id", userId);
        User user = userMapper.selectOne(userQueryWrapper);
        // 根据专业id查询数据
        QueryWrapper<Entitytypes> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("entity_type_code", "center_line");
        entityQueryWrapper.eq("subject_id", user.getSubjectid());
        entityQueryWrapper.eq("project_id", projectId);
        Entitytypes entitytypes = entitytypesMapper.selectOne(entityQueryWrapper);
        if (entitytypes == null) {
            throw new BusinessException("未找到该实体类型，请核查！");
        }
        EntitytypesConfig config = getEntitytypesConfig(entitytypes.getPkid());
        // 中线
    /*if ("CenterLine".equals(entitytypes.getEntitytypecode())) {
      // 获取中线桩
      QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
      queryWrapper.eq("projectid", entitytypes.getProjectid());
      queryWrapper.eq("subjectid", entitytypes.getSubjectid());
      queryWrapper.eq("entitytypecode", "CentrallinePile");
      Entitytypes zxzEntity = entitytypesMapper.selectOne(queryWrapper);
      EntitytypesConfig centerLinePoint = getEntitytypesConfig(zxzEntity.getPkid());
      config.setCenterLinePoint(centerLinePoint);
    }*/
        return config;
    }


    /**
     * 获取实体配置信息
     *
     * @param entitytypesId
     * @return com.supermap.pipedesign.pipechina.metadata.entity.expand.EntitytypesConfig @Date
     * 2022/12/28 15:03
     * @auther eomer
     */
    private EntitytypesConfig getEntitytypesConfig(String entitytypesId) {
        EntitytypesConfig config = new EntitytypesConfig();
        // 默认风格
        QueryWrapper<Entitytypestyles> stylesQueryWrapper = new QueryWrapper<>();
        stylesQueryWrapper.eq("entity_type_id", entitytypesId);
        List<Entitytypestyles> entitytypestyles = entitytypestylesMapper.selectList(stylesQueryWrapper);
        //config.setEntitytypestyles(entitytypestyles);
        // 数据集
        QueryWrapper<Entitydatasref> entitydatasrefQueryWrapper = new QueryWrapper<>();
        entitydatasrefQueryWrapper.eq("entity_type_id", entitytypesId);
        List<Entitydatasref> entitydatasrefList =
                entitydatasrefMapper.selectList(entitydatasrefQueryWrapper);
        List<String> datasetIds = new ArrayList<>();
        Entitydatasref asref = null;
        for (int j = 0; j < entitydatasrefList.size(); j++) {
            Entitydatasref entitydatasref = entitydatasrefList.get(j);
            datasetIds.add(entitydatasref.getDatasetid());
            // 数据集类型(0:属性;1:点;2:线;3:面;4:模型;5:CAD文件)
            if (entitydatasref.getDatasettype() == 0) {
                asref = entitydatasref;
            }
        }
        if (datasetIds.size() > 0) {
            QueryWrapper<Datasetinfo> datasetQueryWrapper = new QueryWrapper<>();
            datasetQueryWrapper.in("pkid", datasetIds);
            List<Datasetinfo> datasetinfoList = datasetinfoMapper.selectList(datasetQueryWrapper);
            //config.setDatasetinfos(datasetinfoList);
        }
        // 属性字段
        if (asref != null) {
            QueryWrapper<Datasetfield> datasetfieldQueryWrapper = new QueryWrapper<>();
            datasetfieldQueryWrapper.eq("data_set_id", asref.getDatasetid());
            List<Datasetfield> datasetfields = datasetfieldMapper.selectList(datasetfieldQueryWrapper);
            config.setDatasetfields(datasetfields);
        }

        return config;
    }

    private EntitytypesTemConfig getEntitytypesConfigPro(String entitytypesId, String projectid) {
        EntitytypesTemConfig config = new EntitytypesTemConfig();
        // 默认风格
        QueryWrapper<Entitytypestyles> stylesQueryWrapper = new QueryWrapper<>();
        stylesQueryWrapper.eq("entity_type_id", entitytypesId);
        stylesQueryWrapper.eq("project_id", projectid);
        List<Entitytypestyles> entitytypestyles = entitytypestylesMapper.selectList(stylesQueryWrapper);
        List<Entitytypestylestem> temList = new ArrayList<>();
        for (Entitytypestyles tyle : entitytypestyles) {
            Entitytypestylestem tem = GsonUtil.ObjectToEntity(tyle, Entitytypestylestem.class);
            temList.add(tem);
        }
        //config.setEntitytypestyles(temList);


        QueryWrapper<Datasetinfo> datasetQueryWrapper = new QueryWrapper<>();
        datasetQueryWrapper.in("entity_type_id", entitytypesId);
        datasetQueryWrapper.eq("project_id", projectid);
        List<Datasetinfo> datasetinfoList = datasetinfoMapper.selectList(datasetQueryWrapper);
        String dataSetType = "";
        List<Datasetinfotem> dtemList = new ArrayList<>();
        for (Datasetinfo datasetinfo : datasetinfoList) {
            if (datasetinfo.getDatasettype() != 0) {
                dataSetType = dataSetType.concat(String.valueOf(datasetinfo.getDatasettype())).concat(",");
            }
            Datasetinfotem fotem = GsonUtil.ObjectToEntity(datasetinfo, Datasetinfotem.class);
            dtemList.add(fotem);
        }
        if (dataSetType.indexOf(",") > -1) {
            dataSetType = dataSetType.substring(0, (dataSetType.length() - 1));
        }
        //config.setDatasetType( dataSetType );
        //config.setDatasetinfos(dtemList);

        // 属性字段
        QueryWrapper<Datasetfield> datasetfieldQueryWrapper = new QueryWrapper<>();
        datasetfieldQueryWrapper.eq("entity_type_id", entitytypesId);
        datasetfieldQueryWrapper.eq("project_id", projectid);
//      datasetfieldQueryWrapper.isNotNull("is_required");
        List<Datasetfield> datasetfields = datasetfieldMapper.selectList(datasetfieldQueryWrapper);
        if (datasetfields != null && datasetfields.size() > 0) {
            List<DatasetfieldtemVo> datasetfieldtemVos = new ArrayList<>();
            for (Datasetfield datasetfield : datasetfields) {
                Datasetfieldtem datasetfieldtem = GsonUtil.ObjectToEntity(datasetfield, Datasetfieldtem.class);
                DatasetfieldtemVo datasetfieldtemVo = new DatasetfieldtemVo();
                datasetfieldtemVo.setPkid(datasetfieldtem.getPkid());
                datasetfieldtemVo.setFieldname(datasetfieldtem.getFieldname());
                datasetfieldtemVo.setDatasetid(datasetfieldtem.getDatasetid());
                datasetfieldtemVo.setFieldalias(datasetfieldtem.getFieldalias());
                datasetfieldtemVo.setFieldtype(datasetfieldtem.getFieldtype());
                datasetfieldtemVo.setFieldenname(datasetfieldtem.getFieldenname());
                datasetfieldtemVo.setFieldsize(datasetfieldtem.getFieldsize());
                datasetfieldtemVo.setFieldrange(datasetfieldtem.getFieldrange());
                datasetfieldtemVo.setDefaultvalue(datasetfieldtem.getDefaultvalue());
                datasetfieldtemVo.setIsrequired(datasetfieldtem.getIsrequired());
                datasetfieldtemVo.setIsmainkeyfield(datasetfieldtem.getIsmainkeyfield());
                datasetfieldtemVo.setCanzerolength(datasetfieldtem.getCanzerolength());
                datasetfieldtemVo.setIsautoincrement(datasetfieldtem.getIsautoincrement());
                datasetfieldtemVo.setIsreadonly(datasetfieldtem.getIsreadonly());
                datasetfieldtemVo.setIsdesignfield(datasetfieldtem.getIsdesignfield());
                datasetfieldtemVo.setIsqueryable(datasetfieldtem.getIsqueryable());
                datasetfieldtemVo.setIsuniquefld(datasetfieldtem.getIsuniquefld());
                datasetfieldtemVos.add(datasetfieldtemVo);
            }
            config.setDatasetfieldsVo(datasetfieldtemVos);
        }

        return config;

    }

    @Override
    public List<DatasetfieldtemVo> selDatasetField(String entityTypeId, String projectId) {
        // Entitydatasref entitydatasref = entitydatasrefMapper.selByEtIdAndDsType( entityTypeId,0 ,projectId);
        // if( entitydatasref != null ){
        //   String dateSetId = entitydatasref.getDatasetid();

        // List<DatasetfieldtemVo> datasetfieldtems = datasetfieldMapper.selByDataSetId(dateSetId);
        List<DatasetfieldtemVo> datasetfieldtems = datasetfieldMapper.selByDataSetId(entityTypeId, projectId);
        if (datasetfieldtems != null && datasetfieldtems.size() > 0) {
            for (DatasetfieldtemVo datasetfieldtemVo : datasetfieldtems) {
                String fieldRang = datasetfieldtemVo.getFieldrange();
                if (StringUtils.isNotEmpty(fieldRang)) {
                    List<Dictionary> dictionaryList = new ArrayList<>();
                    List<String> dictIds = new ArrayList<>();
                    if (fieldRang.indexOf(",") > -1) {
                        dictIds = Arrays.asList(fieldRang.split(","));
                    } else {
                        dictIds.add(fieldRang);
                    }
                    for (String dictPkid : dictIds) {
                        Dictionary dictionary = dictionaryMapper.selectById(dictPkid);
                        dictionaryList.add(dictionary);
                    }
                    datasetfieldtemVo.setFieldRangList(dictionaryList);
                }
            }
        }
        return datasetfieldtems;
        // }
        // return new ArrayList<>();
    }

    @Override
    public List<Map> selByProjectId(String projectid) {
        return entitytypesMapper.selByProjectid(projectid);
    }

    @Override
    public int updateConfig(EntitytypesTemConfig entitytypesTemConfig) {
        return 0;
    }

    @Override
    public List<AutoEntityRuleVo> selBySubjectIdWAutoMatic(String projectid, String subjectId) {
        List<AutoEntityRuleVo> autoEntityRuleVos
                = entitytypesMapper.selAllEntityForSelect(projectid);
        if (autoEntityRuleVos != null && autoEntityRuleVos.size() > 0) {
            for (AutoEntityRuleVo autoEntityRuleVo : autoEntityRuleVos) {
                String interactivetype = autoEntityRuleVo.getInteractivetype();
                DictionaryVo dictionaryVo = getDictionary(interactivetype);
                if (dictionaryVo != null) {
                    Dictionary dictionary = dictionaryMapper.selectById(dictionaryVo.getPkid());
                    autoEntityRuleVo.setDictionary(dictionary);
                }
            }
            return autoEntityRuleVos;
        }
        return new ArrayList<>();
    }

    private DictionaryVo getDictionary(String dicCode) {
        List<DictionaryVo> dictionaryVos
                = dictionaryMapper.selCodeByKey("60056301003");
        if (dictionaryVos != null && dictionaryVos.size() > 0) {
            for (DictionaryVo dictionaryVo : dictionaryVos) {
                List<DictionaryVo> dictionaryChil = dictionaryMapper.selCodeByKey(dictionaryVo.getPkid());
                if (dictionaryChil != null && dictionaryChil.size() > 0) {
                    for (DictionaryVo childDiction : dictionaryChil) {
                        if (childDiction.getKey().equals(dicCode)) {
                            return childDiction;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * @描述 根据项目查询实体列表
     * @日期 2023/04/07 16:02
     * @作者 eomer
     **/
    @Override
    public List<Entitytypes> getListByProjectId(String projectid) {

        QueryWrapper<Entitytypes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectid);
        List<Entitytypes> entitytypesList = entitytypesMapper.selectList(queryWrapper);
        return entitytypesList;
    }

}
