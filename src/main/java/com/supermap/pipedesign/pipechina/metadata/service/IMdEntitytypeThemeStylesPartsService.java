package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeThemeStylesParts;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 实体专题风格部件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-18
 */
@Repository
public interface IMdEntitytypeThemeStylesPartsService extends IService<MdEntitytypeThemeStylesParts> {

 /**
 * 添加实体专题风格部件表信息
 *
 * @param mdEntitytypeThemeStylesParts
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int insert(MdEntitytypeThemeStylesParts mdEntitytypeThemeStylesParts);

 /**
 * 删除实体专题风格部件表信息
 *
 * @param mdEntitytypeThemeStylesPartsId
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int delete(String mdEntitytypeThemeStylesPartsId);

 /**
 * 更新实体专题风格部件表信息
 *
 * @param mdEntitytypeThemeStylesParts
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int update(MdEntitytypeThemeStylesParts mdEntitytypeThemeStylesParts);

 /**
 * 全部查询
 *
 * @param mdEntitytypeThemeStylesParts
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeThemeStylesParts>
 * @Date 2023-03-18
 * @auther eomer
 */
 List<MdEntitytypeThemeStylesParts> list(MdEntitytypeThemeStylesParts mdEntitytypeThemeStylesParts);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-18
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
