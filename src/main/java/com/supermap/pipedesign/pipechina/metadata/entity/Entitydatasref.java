package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 实体数据关联表(存储每类实体关联的不同类型数据集的ID) 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_entity_datasref")
@ApiModel(value="Entitydatasref对象", description="实体数据关联表(存储每类实体关联的不同类型数据集的ID)")
public class Entitydatasref implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "实体类型编号")
    @TableField("entity_type_id")
    private String entitytypeid;

    @ApiModelProperty(value = "数据集类型(0:属性;1:点;2:线;3:面;4:模型;5:CAD文件)")
    @TableField("data_set_type")
    private Integer datasettype;

    @ApiModelProperty(value = "数据集ID(如果DataType<5时，存储表PL_MD_DATASETINFO的PKID；	如果DataType>=5时，存储文件编码或文档服务中心的URL)")
    @TableField("data_set_id")
    private String datasetid;

    @ApiModelProperty(value = "交互方式(来源字典表)")
    @TableField("interactive_type")
    private String interactivetype;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mId;


}
