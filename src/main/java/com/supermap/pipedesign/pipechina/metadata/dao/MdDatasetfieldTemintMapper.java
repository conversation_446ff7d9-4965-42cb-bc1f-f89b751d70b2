package com.supermap.pipedesign.pipechina.metadata.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.DatasetfieldTemint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 字段表模板(所有数据表都共有的字段) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Mapper
public interface MdDatasetfieldTemintMapper extends BaseMapper<DatasetfieldTemint> {


    @Select("select * from pld_md_dataset_field_temint where geo_show_type=#{geo_show_type} order by order_index")
    List<DatasetfieldTemint> selByTypeVal(@Param(value = "geo_show_type") Integer type);
}
