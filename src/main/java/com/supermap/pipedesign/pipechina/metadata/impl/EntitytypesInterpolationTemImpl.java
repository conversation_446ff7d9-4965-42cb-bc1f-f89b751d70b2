package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationTem;
import com.supermap.pipedesign.pipechina.metadata.dao.EntitytypesInterpolationTemMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IEntitytypesInterpolationTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 实体-插值表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@Service("EntitytypesInterpolationTemImpl")
public class EntitytypesInterpolationTemImpl extends ServiceImpl<EntitytypesInterpolationTemMapper, EntitytypesInterpolationTem> implements IEntitytypesInterpolationTemService {

    @Autowired
    private EntitytypesInterpolationTemMapper entitytypesInterpolationTemMapper;

    /**
    * 添加实体-插值表信息
    *
    * @param entitytypesInterpolationTem
    * @return int
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public int insert(EntitytypesInterpolationTem entitytypesInterpolationTem) {

        //entitytypesInterpolationTem.setUserId(JavaUtils.getUUID36());
        //entitytypesInterpolationTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return entitytypesInterpolationTemMapper.insert(entitytypesInterpolationTem);
    }

    /**
    * 删除实体-插值表信息
    *
    * @param entitytypesInterpolationTemId
    * @return int
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public int delete(String entitytypesInterpolationTemId) {
        return entitytypesInterpolationTemMapper.deleteById(entitytypesInterpolationTemId);
    }

    /**
    * 更新实体-插值表信息
    *
    * @param entitytypesInterpolationTem
    * @return int
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public int update(EntitytypesInterpolationTem entitytypesInterpolationTem) {
        return entitytypesInterpolationTemMapper.updateById(entitytypesInterpolationTem);
    }

    /**
    * 全部查询
    *
    * @param entitytypesInterpolationTem
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.EntitytypesInterpolationTem>
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public List<EntitytypesInterpolationTem> list(EntitytypesInterpolationTem entitytypesInterpolationTem) {

        QueryWrapper<EntitytypesInterpolationTem> queryWrapper = new QueryWrapper<>();

        return entitytypesInterpolationTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-03-25
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<EntitytypesInterpolationTem> entitytypesInterpolationTemIPage = new Page<>();
        entitytypesInterpolationTemIPage.setCurrent(current);
        entitytypesInterpolationTemIPage.setSize(size);

        QueryWrapper<EntitytypesInterpolationTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return entitytypesInterpolationTemMapper.selectPage(entitytypesInterpolationTemIPage, queryWrapper);
    }


}
