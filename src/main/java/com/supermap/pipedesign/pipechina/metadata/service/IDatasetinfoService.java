package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.Datasetinfo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 数据集注册表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IDatasetinfoService extends IService<Datasetinfo> {

 /**
 * 添加数据集注册表信息
 *
 * @param datasetinfo
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Datasetinfo datasetinfo);

 /**
 * 删除数据集注册表信息
 *
 * @param datasetinfoId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String datasetinfoId);

 /**
 * 更新数据集注册表信息
 *
 * @param datasetinfo
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Datasetinfo datasetinfo);

 /**
 * 全部查询
 *
 * @param datasetinfo
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Datasetinfo>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Datasetinfo> list(Datasetinfo datasetinfo);


 List<Datasetinfo> list(String projectId);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
