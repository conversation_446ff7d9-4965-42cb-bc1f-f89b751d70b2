package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesPartsPic;
import com.supermap.pipedesign.pipechina.metadata.dao.MdEntitytypeStylesPartsPicMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IMdEntitytypeStylesPartsPicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 默认风格部件贴图关联表-项目级 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service("MdEntitytypeStylesPartsPicImpl")
public class MdEntitytypeStylesPartsPicImpl extends ServiceImpl<MdEntitytypeStylesPartsPicMapper, MdEntitytypeStylesPartsPic> implements IMdEntitytypeStylesPartsPicService {

    @Autowired
    private MdEntitytypeStylesPartsPicMapper mdEntitytypeStylesPartsPicMapper;

    /**
    * 添加默认风格部件贴图关联表-项目级信息
    *
    * @param mdEntitytypeStylesPartsPic
    * @return int
    * @Date 2023-07-17
    * @auther eomer
    */
    @Override
    public int insert(MdEntitytypeStylesPartsPic mdEntitytypeStylesPartsPic) {

        //mdEntitytypeStylesPartsPic.setUserId(JavaUtils.getUUID36());
        //mdEntitytypeStylesPartsPic.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return mdEntitytypeStylesPartsPicMapper.insert(mdEntitytypeStylesPartsPic);
    }

    /**
    * 删除默认风格部件贴图关联表-项目级信息
    *
    * @param mdEntitytypeStylesPartsPicId
    * @return int
    * @Date 2023-07-17
    * @auther eomer
    */
    @Override
    public int delete(String mdEntitytypeStylesPartsPicId) {
        return mdEntitytypeStylesPartsPicMapper.deleteById(mdEntitytypeStylesPartsPicId);
    }

    /**
    * 更新默认风格部件贴图关联表-项目级信息
    *
    * @param mdEntitytypeStylesPartsPic
    * @return int
    * @Date 2023-07-17
    * @auther eomer
    */
    @Override
    public int update(MdEntitytypeStylesPartsPic mdEntitytypeStylesPartsPic) {
        return mdEntitytypeStylesPartsPicMapper.updateById(mdEntitytypeStylesPartsPic);
    }

    /**
    * 全部查询
    *
    * @param mdEntitytypeStylesPartsPic
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeStylesPartsPic>
    * @Date 2023-07-17
    * @auther eomer
    */
    @Override
    public List<MdEntitytypeStylesPartsPic> list(MdEntitytypeStylesPartsPic mdEntitytypeStylesPartsPic) {

        QueryWrapper<MdEntitytypeStylesPartsPic> queryWrapper = new QueryWrapper<>();

        return mdEntitytypeStylesPartsPicMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-07-17
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<MdEntitytypeStylesPartsPic> mdEntitytypeStylesPartsPicIPage = new Page<>();
        mdEntitytypeStylesPartsPicIPage.setCurrent(current);
        mdEntitytypeStylesPartsPicIPage.setSize(size);

        QueryWrapper<MdEntitytypeStylesPartsPic> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return mdEntitytypeStylesPartsPicMapper.selectPage(mdEntitytypeStylesPartsPicIPage, queryWrapper);
    }


}
