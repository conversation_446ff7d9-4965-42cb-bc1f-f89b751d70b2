package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeThemeStylesPartsTem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 实体专题风格部件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-18
 */
@Repository
public interface IMdEntitytypeThemeStylesPartsTemService extends IService<MdEntitytypeThemeStylesPartsTem> {

 /**
 * 添加实体专题风格部件表信息
 *
 * @param mdEntitytypeThemeStylesPartsTem
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int insert(MdEntitytypeThemeStylesPartsTem mdEntitytypeThemeStylesPartsTem);

 /**
 * 删除实体专题风格部件表信息
 *
 * @param mdEntitytypeThemeStylesPartsTemId
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int delete(String mdEntitytypeThemeStylesPartsTemId);

 /**
 * 更新实体专题风格部件表信息
 *
 * @param mdEntitytypeThemeStylesPartsTem
 * @return int
 * @Date 2023-03-18
 * @auther eomer
 */
 int update(MdEntitytypeThemeStylesPartsTem mdEntitytypeThemeStylesPartsTem);

 /**
 * 全部查询
 *
 * @param mdEntitytypeThemeStylesPartsTem
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypeThemeStylesPartsTem>
 * @Date 2023-03-18
 * @auther eomer
 */
 List<MdEntitytypeThemeStylesPartsTem> list(MdEntitytypeThemeStylesPartsTem mdEntitytypeThemeStylesPartsTem);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-03-18
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
