package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.Subjectdatatyperef;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 专业-数据类型关联表(存储专业和数据类型之间对应关系) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface ISubjectdatatyperefService extends IService<Subjectdatatyperef> {

 /**
 * 添加专业-数据类型关联表(存储专业和数据类型之间对应关系)信息
 *
 * @param subjectdatatyperef
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Subjectdatatyperef subjectdatatyperef);

 /**
 * 删除专业-数据类型关联表(存储专业和数据类型之间对应关系)信息
 *
 * @param subjectdatatyperefId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String subjectdatatyperefId);

 /**
 * 更新专业-数据类型关联表(存储专业和数据类型之间对应关系)信息
 *
 * @param subjectdatatyperef
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Subjectdatatyperef subjectdatatyperef);

 /**
 * 全部查询
 *
 * @param subjectdatatyperef
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Subjectdatatyperef>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Subjectdatatyperef> list(Subjectdatatyperef subjectdatatyperef);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
