package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.DatasetfieldTemint;
import com.supermap.pipedesign.pipechina.metadata.dao.MdDatasetfieldTemintMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IMdDatasetfieldTemintService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 字段表模板(所有数据表都共有的字段) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Service("MdDatasetfieldTemintImpl")
public class MdDatasetfieldTemintImpl extends ServiceImpl<MdDatasetfieldTemintMapper, DatasetfieldTemint> implements IMdDatasetfieldTemintService {

    @Autowired
    private MdDatasetfieldTemintMapper mdDatasetfieldTemintMapper;

    /**
    * 添加字段表模板(所有数据表都共有的字段)信息
    *
    * @param datasetfieldTemint
    * @return int
    * @Date 2023-02-16
    * @auther eomer
    */
    @Override
    public int insert(DatasetfieldTemint datasetfieldTemint) {

        //mdDatasetfieldTemint.setUserId(JavaUtils.getUUID36());
        //mdDatasetfieldTemint.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return mdDatasetfieldTemintMapper.insert(datasetfieldTemint);
    }

    /**
    * 删除字段表模板(所有数据表都共有的字段)信息
    *
    * @param mdDatasetfieldTemintId
    * @return int
    * @Date 2023-02-16
    * @auther eomer
    */
    @Override
    public int delete(String mdDatasetfieldTemintId) {
        return mdDatasetfieldTemintMapper.deleteById(mdDatasetfieldTemintId);
    }

    /**
    * 更新字段表模板(所有数据表都共有的字段)信息
    *
    * @param datasetfieldTemint
    * @return int
    * @Date 2023-02-16
    * @auther eomer
    */
    @Override
    public int update(DatasetfieldTemint datasetfieldTemint) {
        return mdDatasetfieldTemintMapper.updateById(datasetfieldTemint);
    }

    /**
    * 全部查询
    *
    * @param datasetfieldTemint
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdDatasetfieldTemint>
    * @Date 2023-02-16
    * @auther eomer
    */
    @Override
    public List<DatasetfieldTemint> list(DatasetfieldTemint datasetfieldTemint) {

        QueryWrapper<DatasetfieldTemint> queryWrapper = new QueryWrapper<>();

        return mdDatasetfieldTemintMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-16
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<DatasetfieldTemint> mdDatasetfieldTemintIPage = new Page<>();
        mdDatasetfieldTemintIPage.setCurrent(current);
        mdDatasetfieldTemintIPage.setSize(size);

        QueryWrapper<DatasetfieldTemint> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return mdDatasetfieldTemintMapper.selectPage(mdDatasetfieldTemintIPage, queryWrapper);
    }


}
