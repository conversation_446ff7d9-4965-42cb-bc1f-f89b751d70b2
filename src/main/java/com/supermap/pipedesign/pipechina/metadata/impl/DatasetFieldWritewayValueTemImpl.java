package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.DatasetFieldWritewayValueTem;
import com.supermap.pipedesign.pipechina.metadata.dao.DatasetFieldWritewayValueTemMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IDatasetFieldWritewayValueTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 写值方式关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service("DatasetFieldWritewayValueTemImpl")
public class DatasetFieldWritewayValueTemImpl extends ServiceImpl<DatasetFieldWritewayValueTemMapper, DatasetFieldWritewayValueTem> implements IDatasetFieldWritewayValueTemService {

    @Autowired
    private DatasetFieldWritewayValueTemMapper datasetFieldWritewayValueTemMapper;

    /**
    * 添加写值方式关联表信息
    *
    * @param datasetFieldWritewayValueTem
    * @return int
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public int insert(DatasetFieldWritewayValueTem datasetFieldWritewayValueTem) {

        //datasetFieldWritewayValueTem.setUserId(JavaUtils.getUUID36());
        //datasetFieldWritewayValueTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return datasetFieldWritewayValueTemMapper.insert(datasetFieldWritewayValueTem);
    }

    /**
    * 删除写值方式关联表信息
    *
    * @param datasetFieldWritewayValueTemId
    * @return int
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public int delete(String datasetFieldWritewayValueTemId) {
        return datasetFieldWritewayValueTemMapper.deleteById(datasetFieldWritewayValueTemId);
    }

    /**
    * 更新写值方式关联表信息
    *
    * @param datasetFieldWritewayValueTem
    * @return int
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public int update(DatasetFieldWritewayValueTem datasetFieldWritewayValueTem) {
        return datasetFieldWritewayValueTemMapper.updateById(datasetFieldWritewayValueTem);
    }

    /**
    * 全部查询
    *
    * @param datasetFieldWritewayValueTem
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.DatasetFieldWritewayValueTem>
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public List<DatasetFieldWritewayValueTem> list(DatasetFieldWritewayValueTem datasetFieldWritewayValueTem) {

        QueryWrapper<DatasetFieldWritewayValueTem> queryWrapper = new QueryWrapper<>();

        return datasetFieldWritewayValueTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-04-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<DatasetFieldWritewayValueTem> datasetFieldWritewayValueTemIPage = new Page<>();
        datasetFieldWritewayValueTemIPage.setCurrent(current);
        datasetFieldWritewayValueTemIPage.setSize(size);

        QueryWrapper<DatasetFieldWritewayValueTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return datasetFieldWritewayValueTemMapper.selectPage(datasetFieldWritewayValueTemIPage, queryWrapper);
    }


}
