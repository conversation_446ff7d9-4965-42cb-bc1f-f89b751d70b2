package com.supermap.pipedesign.pipechina.metadata.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.metadata.dao.TypeRegisterMapper;
import com.supermap.pipedesign.pipechina.metadata.entity.BO.MaterialVo;
import com.supermap.pipedesign.pipechina.metadata.entity.TypeRegister;
import com.supermap.pipedesign.pipechina.metadata.service.ITypeRegisterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * $材料类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-11
 */
@Service("TypeRegisterImpl")
public class TypeRegisterImpl extends ServiceImpl<TypeRegisterMapper, TypeRegister> implements ITypeRegisterService {

    @Autowired
    private TypeRegisterMapper typeRegisterMapper;

    /**
    * 添加$材料类型信息
    *
    * @param typeRegister
    * @return int
    * @Date 2023-03-11
    * @auther eomer
    */
    @Override
    public int insert(TypeRegister typeRegister) {

        //typeRegister.setUserId(JavaUtils.getUUID36());
        //typeRegister.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return typeRegisterMapper.insert(typeRegister);
    }

    /**
     * 删除$材料类型信息
     *
     * @param typeRegisterId
     * @param projectId
     * @return int
     * @Date 2023-03-11
     * @auther eomer
     */
    @Override
    public int delete(String typeRegisterId, String projectId) {
        QueryWrapper queryWrapper =new QueryWrapper();
        queryWrapper.eq("pkid",typeRegisterId);
        queryWrapper.eq("project_id",projectId);
        return typeRegisterMapper.delete(queryWrapper);
    }

    /**
    * 更新$材料类型信息
    *
    * @param typeRegister
    * @return int
    * @Date 2023-03-11
    * @auther eomer
    */
    @Override
    public int update(TypeRegister typeRegister) {
        return typeRegisterMapper.updateById(typeRegister);
    }

    /**
     * 全部查询
     *
     * @param typeRegister
     * @param projectId
     * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.TypeRegister>
     * @Date 2023-03-11
     * @auther eomer
     */
    @Override
    public List<TypeRegister> list(TypeRegister typeRegister, String projectId) {

        QueryWrapper<TypeRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id",projectId);
        return typeRegisterMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @param projectId
     * @return com.baomidou.mybatisplus.core.metadata.IPage
     * @Date 2023-03-11
     * @auther eomer
     */
    @Override
    public IPage pageList(long current, long size, String code, String materialClassCode, String cnDesc, String projectId) {

        IPage<TypeRegister> typeRegisterIPage = new Page<>();
        typeRegisterIPage.setCurrent(current);
        typeRegisterIPage.setSize(size);

        LambdaQueryWrapper<TypeRegister> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(ObjectUtil.isNotEmpty(code), TypeRegister::getMaterialTypeCode,code);
        queryWrapper.like(ObjectUtil.isNotEmpty(materialClassCode), TypeRegister::getMaterialClassCode,materialClassCode);
        queryWrapper.like(ObjectUtil.isNotEmpty(cnDesc), TypeRegister::getCnDesc,cnDesc);
        queryWrapper.eq(ObjectUtil.isNotEmpty(projectId),TypeRegister::getProjectId,projectId);
        return typeRegisterMapper.selectPage(typeRegisterIPage, queryWrapper);
    }

    /**
     * 查询大类材料
     * @return 结果
     */
    @Override
    public List<MaterialVo> getLargeList(String projectId) {
        return typeRegisterMapper.getLargeList(projectId);
    }

    /**
     * 查询中类材料
     * @return 结果
     */
    @Override
    public List<MaterialVo> getMiddleList(String code, String projectId) {
        return typeRegisterMapper.getMiddleList(code,projectId);
    }

    /**
     * 查询小类材料
     * @return 结果
     */
    @Override
    public List<MaterialVo> getSmallList(String code, String projectId) {
        return typeRegisterMapper.getSmallList(code,projectId);
    }

    /**
     * 查询详情
     *
     * @param pkid
     * @param projectId
     * @return 返回结果
     */
    @Override
    public TypeRegister detail(String pkid, String projectId) {
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pkid",pkid);
        queryWrapper.eq("project_id",projectId);
        return typeRegisterMapper.selectOne(queryWrapper);
    }


}
