package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.Directories;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 目录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IDirectoriesService extends IService<Directories> {

 /**
 * 添加目录表信息
 *
 * @param directories
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Directories directories);

 /**
 * 删除目录表信息
 *
 * @param directoriesId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String directoriesId);

 /**
 * 更新目录表信息
 *
 * @param directories
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Directories directories);

 /**
 * 全部查询
 *
 * @param directories
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Directories>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Directories> list(Directories directories);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
