package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("material_field_info")
@ApiModel(value="MaterialFieldInfo对象", description="")
public class MaterialFieldInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @TableField("relation_attribute_id")
    private Integer relationattributeid;

    @TableField("mcc_id")
    private String mccid;

    @TableField("attribute_id")
    private String attributeid;

    @TableField("attribute_name")
    private String attributename;

    @TableField("attribute_value_id")
    private Integer attributevalueid;

    @TableField("attribute_value_code")
    private String attributevaluecode;

    @TableField("attribute_value_name")
    private String attributevaluename;

    @TableField("cn_desc")
    private String cndesc;

    @TableField("en_desc")
    private String endesc;

    @TableField("cns_desc")
    private String cnsdesc;

    @TableField("ens_desc")
    private String ensdesc;

    @TableField("value_sort")
    private Integer valuesort;


}
