package com.supermap.pipedesign.pipechina.metadata.utils.enums;

public enum EntityDatasetType {


    TABULAR("TABULAR", 0),
    POINT3D("POINT3D", 1),
    LINE3D("LINE3D", 2),
    POLYGON3D("POLYGON3D", 3),
    MODEL("MODEL", 4),
    CAD("CAD",5);


    private String name;
    private Integer value;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    private EntityDatasetType(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static EntityDatasetType getEntityDatasetTypeByValue(int value) {
        for (EntityDatasetType entityDatasetType : EntityDatasetType.values()) {
            if (entityDatasetType.getValue().equals(value)) {
                return entityDatasetType;
            }
        }
        return null;
    }

    public static EntityDatasetType getEntityDatasetTypeByName(String name) {
        for (EntityDatasetType entityDatasetType : EntityDatasetType.values()) {
            if (entityDatasetType.getName().equalsIgnoreCase(name)) {
                return entityDatasetType;
            }
        }
        return null;
    }

}
