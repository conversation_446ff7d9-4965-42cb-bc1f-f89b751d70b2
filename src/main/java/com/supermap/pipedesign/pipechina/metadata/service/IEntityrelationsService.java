package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.Entityrelations;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 实体关联表(存储实体和实体之间的关联关系。主要用于存储衍生实体。) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IEntityrelationsService extends IService<Entityrelations> {

 /**
 * 添加实体关联表(存储实体和实体之间的关联关系。主要用于存储衍生实体。)信息
 *
 * @param entityrelations
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(Entityrelations entityrelations);

 /**
 * 删除实体关联表(存储实体和实体之间的关联关系。主要用于存储衍生实体。)信息
 *
 * @param entityrelationsId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String entityrelationsId);

 /**
 * 更新实体关联表(存储实体和实体之间的关联关系。主要用于存储衍生实体。)信息
 *
 * @param entityrelations
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(Entityrelations entityrelations);

 /**
 * 全部查询
 *
 * @param entityrelations
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.Entityrelations>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<Entityrelations> list(Entityrelations entityrelations);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
