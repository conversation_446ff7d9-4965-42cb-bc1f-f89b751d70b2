package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import java.sql.Timestamp;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 贴图管理表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_chartlet")
@ApiModel(value="Chartlet对象", description="贴图管理表")
public class Chartlet implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "文件名称")
    @TableField("file_name")
    private String filename;

    @ApiModelProperty(value = "文件路径")
    @TableField("file_path")
    private String filepath;

    @ApiModelProperty(value = "创建者id")
    @TableField("create_user_id")
    private String createuserid;

    @ApiModelProperty(value = "创建日期")
    @TableField("create_time")
    private Timestamp createtime;

    @ApiModelProperty(value = "项目编号(项目编号为0时，为平台级实体定义)")
    @TableField("project_id")
    private String projectid;

    @ApiModelProperty(value = "系统配置id")
    @TableField("m_id")
    private String mid;


}
