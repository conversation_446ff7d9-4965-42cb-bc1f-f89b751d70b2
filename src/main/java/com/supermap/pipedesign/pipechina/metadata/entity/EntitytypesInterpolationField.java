package com.supermap.pipedesign.pipechina.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pld_md_entitytypes_interpolation_field")
@ApiModel(value="EntitytypesInterpolationField对象", description="")
public class EntitytypesInterpolationField implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;

    @ApiModelProperty(value = "项目编号")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "实体类型id")
    @TableField("entity_type_id")
    private String entityTypeId;

    @ApiModelProperty(value = "插值表id")
    @TableField("interpolation_id")
    private String interpolationId;

    @ApiModelProperty(value = "属性实体id")
    @TableField("entity_type_id_att")
    private String entityTypeIdAtt;

    @ApiModelProperty(value = "字段id")
    @TableField("field_id")
    private String fieldId;

    @ApiModelProperty(value = "属性实体类型(0条件,1结果)")
    @TableField("att_type")
    private Integer attType;

    @ApiModelProperty(value = "排序")
    @TableField("sort_num")
    private Integer sortNum;


}
