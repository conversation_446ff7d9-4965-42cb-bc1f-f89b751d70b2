package com.supermap.pipedesign.pipechina.metadata.entity.BO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 实体数据字典
 * <AUTHOR>
 * @Description
 * @Date 2023/3/11 11:38
 **/
@Data
public class EntityDictionaryVo {


    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "上级编号")
    private String pid;

    @ApiModelProperty(value = "字典名称")
    private String dictionaryname;

    @ApiModelProperty(value = "字典编码(作为枚举值来用)")
    private String dictionarycode;


    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "子集")
    List<EntityDictionaryVo> dictList;


}
