package com.supermap.pipedesign.pipechina.metadata.utils.enums;

public enum EntityDatasetFieldType {


    INTEGER("整型", 1), FLOAT("单精度型", 7),BYTEA("二进制型",3), DECIMAL("数值型", 9), STRING("字符串型", 4), CLOB("块型", 5), DATE("日期型", 6),DOUBLE("双精度型", 2), BOOLEAN("布尔型", 8);


    private String name;
    private Integer value;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    private EntityDatasetFieldType(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static EntityDatasetFieldType getEntityDatasetFieldTypeByValue(int value) {
        for (EntityDatasetFieldType entityDatasetType : EntityDatasetFieldType.values()) {
            if (entityDatasetType.getValue().equals(value)) {
                return entityDatasetType;
            }
        }
        return null;
    }

    public static EntityDatasetFieldType getEntityDatasetFieldTypeByName(String name) {
        for (EntityDatasetFieldType entityDatasetFieldType : EntityDatasetFieldType.values()) {
            if (entityDatasetFieldType.getName().equalsIgnoreCase(name)) {
                return entityDatasetFieldType;
            }
        }
        return null;
    }

}
