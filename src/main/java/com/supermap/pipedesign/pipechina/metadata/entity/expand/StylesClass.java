package com.supermap.pipedesign.pipechina.metadata.entity.expand;


import com.supermap.pipedesign.pipechina.metadata.entity.Entitytypestyles;
import com.supermap.pipedesign.pipechina.metadata.entity.Entitytypethemestyles;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public  class StylesClass
{
    //默认风格
    @ApiModelProperty(value = "默认风格")
    private EntitytypestylesEx entityTypeStyles;

    @ApiModelProperty(value = "符号信息")
    private List<EntitytypethemestylesEx> entityTypeThemeStyles;
}