package com.supermap.pipedesign.pipechina.metadata.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypethemestylesTem;
import com.supermap.pipedesign.pipechina.metadata.dao.MdEntitytypethemestylesTemMapper;
import com.supermap.pipedesign.pipechina.metadata.service.IMdEntitytypethemestylesTemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Service("MdEntitytypethemestylesTemImpl")
public class MdEntitytypethemestylesTemImpl extends ServiceImpl<MdEntitytypethemestylesTemMapper, MdEntitytypethemestylesTem> implements IMdEntitytypethemestylesTemService {

    @Autowired
    private MdEntitytypethemestylesTemMapper mdEntitytypethemestylesTemMapper;

    /**
    * 添加信息
    *
    * @param mdEntitytypethemestylesTem
    * @return int
    * @Date 2023-02-15
    * @auther eomer
    */
    @Override
    public int insert(MdEntitytypethemestylesTem mdEntitytypethemestylesTem) {

        //mdEntitytypethemestylesTem.setUserId(JavaUtils.getUUID36());
        //mdEntitytypethemestylesTem.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return mdEntitytypethemestylesTemMapper.insert(mdEntitytypethemestylesTem);
    }

    /**
    * 删除信息
    *
    * @param mdEntitytypethemestylesTemId
    * @return int
    * @Date 2023-02-15
    * @auther eomer
    */
    @Override
    public int delete(String mdEntitytypethemestylesTemId) {
        return mdEntitytypethemestylesTemMapper.deleteById(mdEntitytypethemestylesTemId);
    }

    /**
    * 更新信息
    *
    * @param mdEntitytypethemestylesTem
    * @return int
    * @Date 2023-02-15
    * @auther eomer
    */
    @Override
    public int update(MdEntitytypethemestylesTem mdEntitytypethemestylesTem) {
        return mdEntitytypethemestylesTemMapper.updateById(mdEntitytypethemestylesTem);
    }

    /**
    * 全部查询
    *
    * @param mdEntitytypethemestylesTem
    * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypethemestylesTem>
    * @Date 2023-02-15
    * @auther eomer
    */
    @Override
    public List<MdEntitytypethemestylesTem> list(MdEntitytypethemestylesTem mdEntitytypethemestylesTem) {

        QueryWrapper<MdEntitytypethemestylesTem> queryWrapper = new QueryWrapper<>();

        return mdEntitytypethemestylesTemMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-02-15
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<MdEntitytypethemestylesTem> mdEntitytypethemestylesTemIPage = new Page<>();
        mdEntitytypethemestylesTemIPage.setCurrent(current);
        mdEntitytypethemestylesTemIPage.setSize(size);

        QueryWrapper<MdEntitytypethemestylesTem> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return mdEntitytypethemestylesTemMapper.selectPage(mdEntitytypethemestylesTemIPage, queryWrapper);
    }


}
