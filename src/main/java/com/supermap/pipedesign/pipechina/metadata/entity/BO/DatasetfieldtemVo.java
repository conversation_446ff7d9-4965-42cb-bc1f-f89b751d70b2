package com.supermap.pipedesign.pipechina.metadata.entity.BO;

import com.supermap.pipedesign.pipechina.engineering.entity.Dictionary;
import com.supermap.pipedesign.pipechina.metadata.entity.expand.DatasetFieldWritewayTemEx;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 字段表 实体类
 * </p>
 *
 * <AUTHOR>
 * @title DatasetfieldtemVo
 * @date 2023/02/12 10:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="Datasetfield对象", description="字段表")
public class DatasetfieldtemVo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    private String pkid;

    @ApiModelProperty(value = "字段名")
    private String fieldname;

    @ApiModelProperty(value = "字段英文名")
    private String fieldenname;

    @ApiModelProperty(value = "字段别名")
    private String fieldalias;

    @ApiModelProperty(value = "字段类型枚举值。各个枚举值代表的含义如下：INTEGER(\"整型\", 1), STRING(\"字符串型\", 3), DOUBLE(\"双精度型\", 2), LONGBINARY(\"二进制型\", 4) ")
    private String fieldtype;

    @ApiModelProperty(value = "字段大小")
    private Integer fieldsize;

    @ApiModelProperty(value = "是否主键字段")
    private Boolean ismainkeyfield;

    @ApiModelProperty(value = "长度可否为0")
    private Boolean canzerolength;

    @ApiModelProperty(value = "是否自增字段")
    private Boolean isautoincrement;

    @ApiModelProperty(value = "默认值")
    private String defaultvalue;

    @ApiModelProperty(value = "值域(0是取值范围 存最大、最小、存正则校验，1是数据字典，id存储在正则，2是材料库，id存储在正则)")
    private String fieldrange;

    @ApiModelProperty(value = "是否必填字段")
    private Boolean isrequired;

    @ApiModelProperty(value = "是否只读字段")
    private Boolean isreadonly;

    @ApiModelProperty(value = "该字段是否参与实体设计及构造")
    private Boolean isdesignfield;

    @ApiModelProperty(value = "可变长度")
    private Integer variablelength;

    @ApiModelProperty(value = "最大值")
    private String maxvalues;

    @ApiModelProperty(value = "最小值")
    private String minvalues;

    @ApiModelProperty(value = "正则表达式")
    private String regularexpression;

    @ApiModelProperty(value = "是否为查询字段")
    private Boolean isqueryable;

    @ApiModelProperty(value = "数据集ID")
    private String datasetid;

    @ApiModelProperty(value = "顺序号")
    private Integer orderindex;

    @ApiModelProperty(value = "字段描述")
    private String descinfo;

    @ApiModelProperty(value = "序列启用时间")
    private Timestamp sequencestart;

    @ApiModelProperty(value = "序列失效时间")
    private Timestamp sequenceend;

    @ApiModelProperty(value = "序列状态(1:生效;-1:失效;0:未被初始化)")
    private Integer sequencestate;

    @ApiModelProperty(value = "序列上一版本ID")
    private String sequenceorgid;

    @ApiModelProperty(value = "是否为唯一字段")
    private Boolean isuniquefld;

    @ApiModelProperty(value = "取值方式")
    private String valuetype;

    @ApiModelProperty(value = "字典表")
    private List<Dictionary> fieldRangList;


    @ApiModelProperty(value = "项目id")
    private String projectid;

    @ApiModelProperty(value = "所属实体类型UUID。将所属的实体类型ID放到这里，而不是单独建关联表去存储，这样才能有效提升系统性能。")
    private String entitytypeid;

    @ApiModelProperty(value = "系统配置id")
    private String mId;

    @ApiModelProperty(value = "值域为数据字典时，反显的文本值")
    private String rangevalue;

    @ApiModelProperty(value = "分组(0为系统字段,1为业务字段,2为汇总属性)")
    private Integer isextend;

    @ApiModelProperty(value = "单位（1、m  2 km ）")
    private String units;

    @ApiModelProperty(value = "精度")
    private Double precision;

    @ApiModelProperty(value = "是否显示阶段（多个逗号分隔  0,1,2,3,4 预可研0、可研1、初设2、施工3、竣工4）")
    private String stagecode;

    @ApiModelProperty(value = "写值方式")
    private DatasetFieldWritewayTemEx writeway;




}

