package com.supermap.pipedesign.pipechina.metadata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.supermap.pipedesign.pipechina.metadata.entity.EntitytypeMetadatas;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 实体元数据定义表(定义实体类型的元数据项) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@Repository
public interface IEntitytypeMetadatasService extends IService<EntitytypeMetadatas> {

 /**
 * 添加实体元数据定义表(定义实体类型的元数据项)信息
 *
 * @param entitytypeMetadatas
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int insert(EntitytypeMetadatas entitytypeMetadatas);

 /**
 * 删除实体元数据定义表(定义实体类型的元数据项)信息
 *
 * @param entitytypeMetadatasId
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int delete(String entitytypeMetadatasId);

 /**
 * 更新实体元数据定义表(定义实体类型的元数据项)信息
 *
 * @param entitytypeMetadatas
 * @return int
 * @Date 2022-12-17
 * @auther eomer
 */
 int update(EntitytypeMetadatas entitytypeMetadatas);

 /**
 * 全部查询
 *
 * @param entitytypeMetadatas
 * @return java.util.List<com.supermap.pipedesign.pipechina.metadata.entity.EntitytypeMetadatas>
 * @Date 2022-12-17
 * @auther eomer
 */
 List<EntitytypeMetadatas> list(EntitytypeMetadatas entitytypeMetadatas);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2022-12-17
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
