package com.supermap.pipedesign.pipechina.metadata.entity.expand;


import com.supermap.pipedesign.pipechina.metadata.entity.Entitytypestylestem;
import com.supermap.pipedesign.pipechina.metadata.entity.MdEntitytypethemestylesTem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
@Data
public  class StylesClassTem
{
    //默认风格
    @ApiModelProperty(value = "默认风格")
    private EntitytypestylestemEx entityTypeStyles;

    @ApiModelProperty(value = "符号信息")
    private List<MdEntitytypethemestylesTemEx> entityTypeThemeStyles;
}