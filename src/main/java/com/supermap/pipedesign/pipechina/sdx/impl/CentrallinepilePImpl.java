package com.supermap.pipedesign.pipechina.sdx.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.sdx.dao.CentrallinepilePMapper;
import com.supermap.pipedesign.pipechina.sdx.entity.CentrallinepileP;
import com.supermap.pipedesign.pipechina.sdx.service.ICentrallinepilePService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Name CenterlineImpl
 * @Desc 中线属性表
 * <AUTHOR>
 * @Date 2022/12/29 18:00
 */
@Service("CentrallinepilePImpl")
public class CentrallinepilePImpl extends ServiceImpl<CentrallinepilePMapper, CentrallinepileP> implements ICentrallinepilePService {

    @Autowired
    private CentrallinepilePMapper centrallinepilePMapper;
}
