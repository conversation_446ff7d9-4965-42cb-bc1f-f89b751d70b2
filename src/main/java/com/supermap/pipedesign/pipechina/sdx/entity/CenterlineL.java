package com.supermap.pipedesign.pipechina.sdx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Name CenterlineL
 * @Desc 中线空间表
 * <AUTHOR>
 * @Date 2022/12/29 17:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("centerline_l")
@ApiModel(value="中线空间表", description="中线空间表")
public class CenterlineL {

    @ApiModelProperty(value = "smid")
    @TableId(value = "smid")
    private Integer smid;

    @ApiModelProperty(value = "smkey")
    @TableField(value = "smkey")
    private int smkey;

    @ApiModelProperty(value = "smsdriw")
    @TableField(value = "smsdriw")
    private float smsdriw;

    @ApiModelProperty(value = "smsdrin")
    @TableField(value = "smsdrin")
    private float smsdrin;

    @ApiModelProperty(value = "smsdrie")
    @TableField(value = "smsdrie")
    private float smsdrie;

    @ApiModelProperty(value = "smsdris")
    @TableField(value = "smsdris")
    private float smsdris;

    @ApiModelProperty(value = "smgranule")
    @TableField(value = "smgranule")
    private float smgranule;

    @ApiModelProperty(value = "smgeometry")
    @TableField(value = "smgeometry")
    private String smgeometry;

    @ApiModelProperty(value = "smuserid")
    @TableField(value = "smuserid")
    private int smuserid;

    @ApiModelProperty(value = "smlength")
    @TableField(value = "smlength")
    private float smlength;

    @ApiModelProperty(value = "smtopoerror")
    @TableField(value = "smtopoerror")
    private int smtopoerror;

    @ApiModelProperty(value = "属性表id")
    @TableField(value = "attributeid")
    private String attributeid;

    @ApiModelProperty(value = "中线id")
    @TableField(value = "centerlineid")
    private String centerlineid;


}
