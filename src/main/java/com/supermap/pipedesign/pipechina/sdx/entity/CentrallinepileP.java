package com.supermap.pipedesign.pipechina.sdx.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Name CentrallinepileP
 * @Desc 中线桩GIS表
 * <AUTHOR>
 * @Date 2022/12/29 17:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("centrallinepile_p")
@ApiModel(value="中线桩GIS表", description="中线桩GIS表")
public class CentrallinepileP {

    @ApiModelProperty(value = "smid")
    @TableId(value = "smid")
    private Integer smid;

    @ApiModelProperty(value = "smkey")
    @TableField(value = "smkey")
    private int smkey;

    @ApiModelProperty(value = "纵坐标X")
    @TableField(value = "smx")
    private Float smx;

    @ApiModelProperty(value = "横坐标Y")
    @TableField(value = "smy")
    private Float smy;

    @ApiModelProperty(value = "高度Z")
    @TableField(value = "smz")
    private Float smz;

    @ApiModelProperty(value = "smuserid")
    @TableField(value = "smuserid")
    private int smuserid;

    @ApiModelProperty(value = "属性表id")
    @TableField(value = "attributeid")
    private String attributeid;

    @ApiModelProperty(value = "中线id")
    @TableField(value = "centerlineid")
    private String centerlineid;

}
