package com.supermap.pipedesign.pipechina.sdx.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.engineering.entity.ESUFunctionarea;
import com.supermap.pipedesign.pipechina.engineering.entity.ESUnit;
import com.supermap.pipedesign.pipechina.engineering.entity.EngineeringSub;
import com.supermap.pipedesign.pipechina.engineering.service.IESUFunctionareaService;
import com.supermap.pipedesign.pipechina.engineering.service.IESUnitService;
import com.supermap.pipedesign.pipechina.engineering.service.IEngineeringSubService;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.CenterlineOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.CenterlineOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.expand.CenterlineOkVo;
import com.supermap.pipedesign.pipechina.sdx.dao.CentrallinepileMapper;
import com.supermap.pipedesign.pipechina.sdx.entity.Centrallinepile;
import com.supermap.pipedesign.pipechina.sdx.service.ICentrallinepileService;
import com.supermap.tools.base.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("CentrallinepileImpl")
public class CentrallinepileImpl  extends ServiceImpl<CentrallinepileMapper, Centrallinepile> implements ICentrallinepileService {

    @Autowired
    private CentrallinepileMapper centrallinepileMapper;

    @Resource(name = "EngineeringSubImpl")
    private IEngineeringSubService engineeringSubService;

    @Resource(name = "ESUnitImpl")
    private IESUnitService esUnitService;

    @Resource(name = "ESUFunctionareaImpl")
    private IESUFunctionareaService eSUFunctionareaImpl;

    @Autowired
    private CenterlineOkMapper centerlineOkMapper;

//    /**
//     * 根据条件查桩号
//     * @param projectid  项目id
//     * @param startpointno 起始桩号
//     * @param endpointno 终止桩号
//     * @return
//     */
//    @Override
//    public List<Centrallinepile> centerlinelist(String projectid, String startpointno, String endpointno){
//        if(!StringUtils.isNotBlank(startpointno) || !StringUtils.isNotBlank(endpointno)){
//            return null;
//        }
////        CenterlineOk
//        CenterlineOk centerlineOk = new CenterlineOk();
//        centerlineOk.setProjectid(projectid);
//        QueryWrapper<Centrallinepile> queryWrapper = new QueryWrapper<>();
//        queryWrapper.select("smid","stakenumber","subnumber","sortno");
//        queryWrapper.eq("projectid",projectid);
//        queryWrapper.between("stakenumber",startpointno,endpointno);
//        queryWrapper.orderByAsc("stakenumber");
//        List<Centrallinepile> centerlines = centrallinepileMapper.selectList(queryWrapper);
//        for (Centrallinepile lie : centerlines) {
//            if(lie.getSubnumber() != 0){
//                lie.setStakenumber((lie.getStakenumber()+"-"+lie.getSubnumber()));
//            }
//        }
//
//
//        return centerlines;
//    }

    /**
     * 查询是否存在桩号
     * @param stakenumber
     * @return
     */
    @Override
    public int total(String stakenumber){
        QueryWrapper<Centrallinepile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("stakenumber",stakenumber);
        return centrallinepileMapper.selectCount(queryWrapper);
    }

    /**
     * 根据起止桩号查集合
     * @param startpoint
     * @param endpoint
     * @return
     */
    @Override
    public List<Centrallinepile> getlistbypoint(String startpoint, String endpoint){
        QueryWrapper<Centrallinepile> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("stakenumber");
        queryWrapper.between("stakenumber",startpoint,endpoint);
        queryWrapper.orderByAsc("stakenumber");
//        List<Centrallinepile> centrallinepiles = centrallinepileMapper.selectList(queryWrapper);
        return centrallinepileMapper.selectList(queryWrapper);

    }

    /**
     * 查询桩号
     * @param projectid
     * @param id
     * @param code
     * @return
     */
    @Override
    public List<CenterlineOkVo> getlistbypoint(String projectid, String id, String code){

        List<CenterlineOk> centerlinelist = new ArrayList<>();
        String stakepointnostart = "";
        String stakepointnoend = "";
        //1 子工程
        if("0".equals(code)){
            EngineeringSub sub = engineeringSubService.getById(id);
            //起始桩点编号
            stakepointnostart = sub.getStakepointnostart();
            //终点桩点编号
            stakepointnoend = sub.getStakepointnoend();
        }
//        //2工程单元
        if("1".equals(code)){
            ESUnit unit = esUnitService.getById(id);
            ////起始桩点编号
            stakepointnostart = unit.getStakepointnostart();
            //终点桩点编号
            stakepointnoend = unit.getStakepointnoend();
        }
//
//        //功能去
        if("2".equals(code)){
            ESUFunctionarea functionarea = eSUFunctionareaImpl.getById(id);
            //起始桩点编号
            stakepointnostart = functionarea.getStakepointnostart();
            //终点桩点编号
            stakepointnoend = functionarea.getStakepointnoend();
        }
        List<CenterlineOkVo> voList = new ArrayList<>();
        if (JavaUtils.isNotEmtryOrNull(stakepointnoend)) {
            centerlinelist = centerlinelist(projectid, stakepointnostart, stakepointnoend);
            for (CenterlineOk line : centerlinelist){
                CenterlineOkVo vo = new CenterlineOkVo();
                vo.setSmid(Long.valueOf(line.getSmid()));
                vo.setStakenumber(line.getStakePointNo());
                voList.add(vo);
            }

        }
        if(JavaUtils.isEmtryOrNull(voList)){
            centerlinelist = centerlinelistByProjectId(projectid);
            for (CenterlineOk line : centerlinelist){
                CenterlineOkVo vo = new CenterlineOkVo();
                vo.setSmid(Long.valueOf(line.getSmid()));
                vo.setStakenumber(line.getStakePointNo());
                voList.add(vo);
            }
        }


        return voList;
    }

    /**
     * 查询桩号
     * @param projectid
     * @return
     */
    @Override
    public List<CenterlineOkVo> getlistbypoint(String projectid){

        List<CenterlineOk> centerlinelist = new ArrayList<>();

        List<CenterlineOkVo> voList = new ArrayList<>();

//            centerlinelist = centerlinelist(projectid);
//            for (CenterlineOk line : centerlinelist){
//                CenterlineOkVo vo = new CenterlineOkVo();
//                vo.setSmid(Long.valueOf(line.getSmid()));
//                vo.setStakenumber(line.getStakePointNo());
//                voList.add(vo);
//            }


//        if(JavaUtils.isEmtryOrNull(voList)){
        try {
            centerlinelist = centerlinelistByProjectId(projectid);
        }catch (Exception e) {
            throw new BusinessException("未查询到项目数据库！");
        }

            for (CenterlineOk line : centerlinelist){
                CenterlineOkVo vo = new CenterlineOkVo();
                vo.setSmid(Long.valueOf(line.getSmid()));
                vo.setStakenumber(line.getStakePointNo());
                voList.add(vo);
            }
//        }


        return voList;
    }

    /**
     * 根据条件查桩号
     * @param projectid  项目id
     * @param startpointno 起始桩号
     * @param endpointno 终止桩号
     * @return
     */
    @Override
    public List<CenterlineOk> centerlinelist(String projectid, String startpointno, String endpointno){
        if(!StringUtils.isNotBlank(startpointno) || !StringUtils.isNotBlank(endpointno)){
            return null;
        }
//        CenterlineOk
        CenterlineOk centerlineOk = new CenterlineOk();
        centerlineOk.setProjectId(projectid);
//        Centrallinepile
        QueryWrapper<CenterlineOk> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("smid","stake_point_no");
        queryWrapper.eq("project_id",projectid);
        queryWrapper.between("stake_point_no",startpointno,endpointno);
        queryWrapper.orderByAsc("stake_point_no");
        List<CenterlineOk> centerlines = centerlineOkMapper.selectList(queryWrapper);
        return centerlines;
    }


    public List<CenterlineOk> centerlinelist(String projectid){

//        CenterlineOk
        CenterlineOk centerlineOk = new CenterlineOk();
        centerlineOk.setProjectId(projectid);
//        Centrallinepile
        QueryWrapper<CenterlineOk> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("smid","stake_point_no");
        queryWrapper.eq("project_id",projectid);
        queryWrapper.orderByAsc("stake_point_no");
        try {
            List<CenterlineOk> centerlines = centerlineOkMapper.selectList(queryWrapper);
            return centerlines;
        }catch (Exception e) {
           throw new BusinessException("未查询到数据源！");
        }
    }

    public List<CenterlineOk> centerlinelistByProjectId(String projectid){

        CenterlineOk centerlineOk = new CenterlineOk();
        centerlineOk.setProjectId(projectid);
//        Centrallinepile
        QueryWrapper<CenterlineOk> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id",projectid);
        try {
            List<CenterlineOk> centerlines = centerlineOkMapper.selectList(queryWrapper);
            return centerlines;
        }catch (Exception e) {
            throw new BusinessException("未查询到数据源！");
        }
    }






}
