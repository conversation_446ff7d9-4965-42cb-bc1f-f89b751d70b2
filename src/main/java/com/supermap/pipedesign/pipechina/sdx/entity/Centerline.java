package com.supermap.pipedesign.pipechina.sdx.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * @Name Centerline
 * @Desc 中线属性表
 * <AUTHOR>
 * @Date 2022/12/29 17:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("centerline")
@ApiModel(value="中线属性表", description="中线属性表")
public class Centerline {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "smid")
    @TableId(value = "smid")
    private Integer smid;

    @ApiModelProperty(value = "smuserid")
    @TableField(value = "smuserid")
    private int smuserid;


    @ApiModelProperty(value = "code")
    @TableField(value = "code")
    private String code;

    @ApiModelProperty(value = "uuid")
    @TableField(value = "uuid")
    private String uuid;

    @ApiModelProperty(value = "initialstakenumber")
    @TableField(value = "initialstakenumber")
    private String initialstakenumber;

    @ApiModelProperty(value = "initialsubnumber")
    @TableField(value = "initialsubnumber")
    private int initialsubnumber;

    @ApiModelProperty(value = "initialrelativemileage")
    @TableField(value = "initialrelativemileage")
    private Float initialrelativemileage;


    @ApiModelProperty(value = "endstakenumber")
    @TableField(value = "endstakenumber")
    private String endstakenumber;

    @ApiModelProperty(value = "endsubnumber")
    @TableField(value = "endsubnumber")
    private int endsubnumber;

    @ApiModelProperty(value = "endrelativemileage")
    @TableField(value = "endrelativemileage")
    private Float endrelativemileage;

    @ApiModelProperty(value = "sequencestate")
    @TableField(value = "sequencestate")
    private Float sequencestate;

    @ApiModelProperty(value = "sequencestart", example = "2021-10-12 01:01:01", dataType = "date", hidden = true)
    @TableField(value = "sequencestart")
    private Timestamp sequencestart;

    @ApiModelProperty(value = "sequenceend", example = "2021-10-12 01:01:01", dataType = "date", hidden = true)
    @TableField(value = "sequenceend")
    private Timestamp sequenceend;

    @ApiModelProperty(value = "sequenceorgid")
    @TableField(value = "sequenceorgid")
    private String sequenceorgid;

    @ApiModelProperty(value = "项目ID")
    @TableField(value = "projectid")
    private String projectid;

    @ApiModelProperty(value = "设计任务ID")
    @TableField(value = "designsubtaskid")
    private String designsubtaskid;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "createid")
    private String createid;

    @ApiModelProperty(value = "人名")
    @TableField(value = "createname")
    private String createname;

    @ApiModelProperty(value = "时间")
    @TableField(value = "createtime")
    private String createtime;

    @ApiModelProperty(value = "版本号")
    @TableField(value = "version")
    private String version;

    @ApiModelProperty(value = "子项目ID")
    @TableField(value = "subprojectid")
    private String subprojectid;
}
