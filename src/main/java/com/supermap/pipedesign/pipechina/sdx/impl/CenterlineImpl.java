package com.supermap.pipedesign.pipechina.sdx.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.*;

import com.supermap.pipedesign.pipechina.entitiesachivment.entity.*;
import com.supermap.pipedesign.pipechina.sdx.service.ICenterlineService;
import com.supermap.pipedesign.pipechina.sdx.dao.CenterlineMapper;
import com.supermap.pipedesign.pipechina.sdx.entity.Centerline;
import com.supermap.tools.file.FileEntry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Name CenterlineImpl
 * @Desc 中线属性表
 * <AUTHOR>
 * @Date 2022/12/29 18:00
 */
@Service("CenterlineImpl")
public class CenterlineImpl extends ServiceImpl<CenterlineMapper, Centerline> implements ICenterlineService {

    @Autowired
    private CenterlineMapper centerlineMapper;
    @Autowired
    private CenterlineOkMapper centerlineOkMapper;
    // 控制点
    @Autowired
    private ControlpointOkMapper controlpointOkMapper;
    //断面点
    @Autowired
    private SectionspointOkMapper sectionspointOkMapper;
    @Autowired
    private LinegeologyfdOkMapper linegeologyfdOkMapper;

    @Autowired
    private GeologyplanetbOkMapper geologyplanetbOkMapper;

    @Autowired
    private SoilresistivityOkMapper soilresistivityOkMapper;
    @Autowired
    private ExploratorypointlayerOkMapper exploratorypointlayerOkMapper;

    @Autowired
    private ExploratorypointOkMapper exploratorypointOkMapper;
    @Autowired
    private CenterlinepileweldedjunctionOkMapper centerlinepileweldedjunctionOkMapper;//---中线桩与焊口
    @Autowired
    private ElbowOkMapper elbowOkMapper;//--弯管
    @Autowired
    private ThreepilesOkMapper threepilesOkMapper;//--三桩
    @Autowired
    private CombinationobstaclesOkMapper combinationobstaclesOkMapper;//--穿跨越与地下障碍物合并表
    @Autowired
    private HydraulicproOkMapper hydraulicproOkMapper;//--水工保护

    @Autowired
    private AddivisionOkMapper addivisionOkMapper; // 行政区划
    @Autowired
    private ChangeorderOkMapper changeorderOkMapper; // 变更单
    @Autowired
    private ValvechamberOkMapper valvechamberOkMapper; // 阀室
    @Autowired
    private BattlefieldOkMapper battlefieldOkMapper; // 站场
    @Autowired
    private HandholeOkMapper handholeOkMapper; // 线路数据
    @Autowired
    private FlatchartOkMapper flatchartOkMapper; // 平面图
    @Autowired
    private PipelayingOkMapper pipelayingOkMapper; // 管道敷设方式
    @Autowired
    private DetectionOkMapper detectionOkMapper; // 监测标石
    @Autowired
    private CommunicationOkMapper communicationOkMapper;  // 通信标石
    @Autowired
    private OpticaboxOkMapper opticaboxOkMapper;  // 光缆接头盒
    @Autowired
    private VhconsequenceOkMapper vhconsequenceOkMapper; // 高后果区视频监控
    @Autowired
    private GeologyreportOkMapper geologyreportOkMapper; // 地质报告
    @Autowired
    private GradeareaOkMapper gradeareaOkMapper; // 管材防腐地区等级
    @Autowired
    private SbagbankOkMapper sbagbankOkMapper;  //草袋素土堡坎
    @Autowired
    private SbagwallOkMapper sbagwallOkMapper;  //草袋素土挡土墙
    @Autowired
    private SbagslopeOkMapper sbagslopeOkMapper;  //草袋素土护坡
    @Autowired
    private CoverOkMapper coverOkMapper;  //草方格固沙护面
    @Autowired
    private DrystoneOkMapper drystoneOkMapper;  //干砌石堡坎
    @Autowired
    private DrystonewaterOkMapper drystonewaterOkMapper;  //干砌石过水面
    @Autowired
    private DrystoneslopeOkMapper drystoneslopeOkMapper;  //干砌石护坡
    @Autowired
    private HangnetOkMapper hangnetOkMapper;  //挂网锚喷护面
    @Autowired
    private BetonOkMapper betonOkMapper;  //混凝土堡坎
    @Autowired
    private BetonwallOkMapper betonwallOkMapper;  //混凝土侧挡墙
    @Autowired
    private BetonwaterOkMapper betonwaterOkMapper;  //混凝土过水面
    @Autowired
    private BetonslopeOkMapper betonslopeOkMapper;  //混凝土护坡
    @Autowired
    private BetonplacOkMapper betonplacOkMapper; //混凝土连续浇筑稳管
    @Autowired
    private BetonbobwOkMapper betonbobwOkMapper;  //混凝土配重块稳管
    @Autowired
    private BetonheterotypeOkMapper betonheterotypeOkMapper; //混凝土异型块护面
    @Autowired
    private GroutedrubbleOkMapper groutedrubbleOkMapper; //浆砌石堡坎
    @Autowired
    private GroutedrubblewallOkMapper groutedrubblewallOkMapper; //浆砌石挡土墙
    @Autowired
    private GroutedrubblefwallOkMapper groutedrubblefwallOkMapper;  //浆砌石防冲墙
    @Autowired
    private GroutedrubbleskeletonOkMapper groutedrubbleskeletonOkMapper;   //浆砌石拱形骨架护坡
    @Autowired
    private GroutedrubblewaterOkMapper groutedrubblewaterOkMapper; //浆砌石过水面
    @Autowired
    private GroutedrubbleslopeOkMapper groutedrubbleslopeOkMapper;  //浆砌石护坡
    @Autowired
    private GroutedrubbledrainOkMapper groutedrubbledrainOkMapper;   //浆砌石截排水沟(渠)
    @Autowired
    private GroutedrubblewaterwallOkMapper groutedrubblewaterwallOkMapper;  //浆砌石截水墙
    @Autowired
    private GroutedrubblerhombusOkMapper groutedrubblerhombusOkMapper;  //浆砌石菱形骨架护坡
    @Autowired
    private GroutedrubblerfigureOkMapper groutedrubblerfigureOkMapper;  //浆砌石人形骨架护坡
    @Autowired
    private GbalanceOkMapper gbalanceOkMapper;   //平衡压袋稳管
    @Autowired
    private GroutedrubblefarmingOkMapper groutedrubblefarmingOkMapper;  //浆砌石农耕道路
    @Autowired
    private ZoologyOkMapper zoologyOkMapper;  //生态袋地坎
    @Autowired
    private ZoologyslopeOkMapper zoologyslopeOkMapper;   //生态袋护坡
    @Autowired
    private GabionwallOkMapper gabionwallOkMapper;  //笼防冲墙
    @Autowired
    private GabionbottomOkMapper gabionbottomOkMapper;  //石笼护底
    @Autowired
    private GabionslopeOkMapper gabionslopeOkMapper;  //石笼护坡
    @Autowired
    private LevelgutterOkMapper levelgutterOkMapper;  //水平沟护面
    @Autowired
    private PlainfaceOkMapper plainfaceOkMapper; //素喷护面
    @Autowired
    private ScalefaceOkMapper scalefaceOkMapper; //鱼鳞坑护面
    @Autowired
    private BeltseedingfaceOkMapper beltseedingfaceOkMapper; //植生带护面


    public List<Centerline> centerlinelist(String projectid, String startpointno, String endpointno) {
        Centerline centerline = new Centerline();
        centerline.setProjectid(projectid);
        QueryWrapper<Centerline> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("smid", "stakepointno");
        queryWrapper.eq("project_id", projectid);
        queryWrapper.between("stakepointno", startpointno, endpointno);
        queryWrapper.orderByAsc("stakepointno");
        List<Centerline> centerlines = centerlineMapper.selectList(queryWrapper);


        return centerlines;
    }


    @Override
    public void excelToTable(List<Map<String, String>> maps, String fileName, String projectId) {

        List entryList = null;
        //测绘数据
        if (fileName.contains("中线数据")) {
            entryList = FileEntry.centerlineList(maps);
            //更新中线成果表数据
            updateCenterLineByNo(entryList, projectId);
        } else if (fileName.contains("控制点数据")) {
            entryList = FileEntry.controlpointList(maps);
            updateControlPointOkByNo(entryList, projectId);
        } else if (fileName.contains("断面点数据")) {
            entryList = FileEntry.sectionspointList(maps);
            updateSectionspointOkByNo(entryList, projectId);
        }
        //勘察数据
        else if (fileName.contains("线路地质分段表")) {
            entryList = FileEntry.lineGeologyfdList(maps);
            updateLinegeologyfdByNo(entryList, projectId);
        } else if (fileName.contains("地质剖面图表")) {
            entryList = FileEntry.geologyPlanetbOkList(maps);
            updatGeologyPlanetbokByNo(entryList, projectId);
        } else if (fileName.contains("土壤电阻率表")) {
            entryList = FileEntry.soilResistivityOkList(maps);
            updatSoilResistivityOkByNo(entryList, projectId);
        } else if (fileName.contains("勘探点坐标表")) {
            entryList = FileEntry.exploratoryPointOkList(maps);
            updatExploratoryPointOkByNo(entryList, projectId);

        } else if (fileName.contains("勘探点地层表")) {
            entryList = FileEntry.exploratoryPointLayerOkList(maps);
            updatExploratoryPointLayerOkByNo(entryList, projectId);
        }
        //线路数据
        else if (fileName.contains("中线桩与焊口")) {
            entryList = FileEntry.centerlinepileweldedjunctionOkList(maps);
            updatCenterlinepileweldedjunctionOk(entryList, projectId);
        } else if (fileName.contains("弯管")) {
            entryList = FileEntry.elbowOkList(maps);
            updatElbowOk(entryList, projectId);
        } else if (fileName.contains("三桩")) {
            entryList = FileEntry.threepilesOkList(maps);
            updateThreepilesOk(entryList, projectId);
        } else if (fileName.contains("穿跨越与地下障碍物合并")) {
            entryList = FileEntry.combinationobstaclesOkList(maps);
            updateCombinationobstaclesOk(entryList, projectId);
        } else if (fileName.contains("管材防腐地区等级")) {
            entryList = FileEntry.gradeareaOkList(maps);
            updatGradeareaOkByNo(entryList, projectId);
        } else if (fileName.contains("地质报告")) {
            entryList = FileEntry.geologyreportOkList(maps);
            updatGeologyreportOkByNo(entryList, projectId);
        } else if (fileName.contains("战场")) {
            entryList = FileEntry.battlefieldOkList(maps);
            updatBattlefieldOkByNo(entryList, projectId);
        } else if (fileName.contains("阀室")) {
            entryList = FileEntry.valvechamberOkList(maps);
            updatValvechamberOkByNo(entryList, projectId);
        } else if (fileName.contains("变更单")) {
            entryList = FileEntry.changeorderOkList(maps);
            updatChangeorderOkByNo(entryList, projectId);
        } else if (fileName.contains("行政区划")) {
            entryList = FileEntry.addivisionOkList(maps);
            updatAddivisionOkByNo(entryList, projectId);
        } else if (fileName.contains("管道敷设方式")) {
            entryList = FileEntry.pipelayingOkList(maps);
            updatPipelayingOkByNo(entryList, projectId);
        } else if (fileName.contains("平面图")) {
            entryList = FileEntry.flatchartOkList(maps);
            updatFlatchartOkByNo(entryList, projectId);
        } else if (fileName.contains("手孔")) {
            entryList = FileEntry.handholeOkList(maps);
            updatHandholeOkByNo(entryList, projectId);
        } else if (fileName.contains("光缆接头盒")) {
            entryList = FileEntry.opticaboxOkList(maps);
            updatOpticaboxOkByNo(entryList, projectId);
        } else if (fileName.contains("通信标石")) {
            entryList = FileEntry.communicationOkList(maps);
            updatCommunicationOkByNo(entryList, projectId);
        } else if (fileName.contains("监测标石")) {
            entryList = FileEntry.detectionOkList(maps);
            updatDetectionOkByNo(entryList, projectId);
        } else if (fileName.contains("高后果区视频监控")) {
            entryList = FileEntry.vhconsequenceOkList(maps);
            updateVhconsequenceOkByNo(entryList, projectId);
        }

    }

    @Override
    public void sheetslToTable(List<Map<String, List<Map<String, String>>>> maps, String projectId) {

        for (Map<String, List<Map<String, String>>> sheetMap : maps) {
            List entryList = null;
            Set<String> strings = sheetMap.keySet();
            for (String fileName : strings) {
                if (fileName.contains("草袋素土堡坎")) {
                    entryList = FileEntry.sbagbankOkList(sheetMap.get(fileName));
                    upSbagbankByNo(entryList, projectId);
                } else if (fileName.contains("草袋素土挡土墙")) {
                    entryList = FileEntry.sbagwallOkList(sheetMap.get(fileName));
                    upSbagwByNo(entryList, projectId);
                } else if (fileName.contains("编织袋稳管")) {
                    entryList = FileEntry.hydraulicproOkList(sheetMap.get(fileName));
                    updatHydraulicproOkByNo(entryList, projectId);
                } else if (fileName.contains("草袋素土护坡")) {
                    entryList = FileEntry.sbagslopeList(sheetMap.get(fileName));
                    upSbagsByNo(entryList, projectId);
                } else if (fileName.contains("草方格固沙护面")) {
                    entryList = FileEntry.coverOkList(sheetMap.get(fileName));
                    upCverByNo(entryList, projectId);
                } else if (fileName.contains("干砌石堡坎")) {
                    entryList = FileEntry.drystoneOkList(sheetMap.get(fileName));
                    upDrystoneByNo(entryList, projectId);
                } else if (fileName.contains("干砌石过水面")) {
                    entryList = FileEntry.drystonewList(sheetMap.get(fileName));
                    upDrystonewByNo(entryList, projectId);
                } else if (fileName.contains("干砌石护坡")) {
                    entryList = FileEntry.drystonesList(sheetMap.get(fileName));
                    upDrystonesByNo(entryList, projectId);
                } else if (fileName.contains("挂网锚喷护面")) {
                    entryList = FileEntry.hangnetOkList(sheetMap.get(fileName));
                    upHangByNo(entryList, projectId);
                } else if (fileName.contains("混凝土堡坎")) {
                    entryList = FileEntry.betonOkList(sheetMap.get(fileName));
                    upBetonByNo(entryList, projectId);
                } else if (fileName.contains("混凝土侧挡墙")) {
                    entryList = FileEntry.betonwallOkList(sheetMap.get(fileName));
                    upBetonwaByNo(entryList, projectId);
                } else if (fileName.contains("混凝土过水面")) {
                    entryList = FileEntry.betonwList(sheetMap.get(fileName));
                    upBetonwByNo(entryList, projectId);
                } else if (fileName.contains("混凝土护坡")) {
                    entryList = FileEntry.betonslopeOkList(sheetMap.get(fileName));
                    upBetonsByNo(entryList, projectId);
                } else if (fileName.contains("混凝土连续浇筑稳管")) {
                    entryList = FileEntry.betonpList(sheetMap.get(fileName));
                    upBetonpByNo(entryList, projectId);
                } else if (fileName.contains("混凝土配重块稳管")) {
                    entryList = FileEntry.betonbwList(sheetMap.get(fileName));
                    upBetonbwByNo(entryList, projectId);
                } else if (fileName.contains("混凝土异型块护面")) {
                    entryList = FileEntry.betonhtList(sheetMap.get(fileName));
                    upBetonhtByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石堡坎")) {
                    entryList = FileEntry.groutebList(sheetMap.get(fileName));
                    upGroutbByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石挡土墙")) {
                    entryList = FileEntry.groutewaList(sheetMap.get(fileName));
                    upGroutewByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石防冲墙")) {
                    entryList = FileEntry.groutefwList(sheetMap.get(fileName));
                    upGroutefwByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石拱形骨架护坡")) {
                    entryList = FileEntry.grouteskList(sheetMap.get(fileName));
                    upGrouteskByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石过水面")) {
                    entryList = FileEntry.groutewList(sheetMap.get(fileName));
                    upGroutwByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石护坡")) {
                    entryList = FileEntry.groutesList(sheetMap.get(fileName));
                    upGroutesByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石截排水沟(渠)")) {
                    entryList = FileEntry.groutedList(sheetMap.get(fileName));
                    upGroutedByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石截水墙")) {
                    entryList = FileEntry.groutwwList(sheetMap.get(fileName));
                    upGroutewwByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石菱形骨架护坡")) {
                    entryList = FileEntry.grouthbList(sheetMap.get(fileName));
                    upGroutedhbByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石人形骨架护坡")) {
                    entryList = FileEntry.grouterfList(sheetMap.get(fileName));
                    upGrouterfByNo(entryList, projectId);
                } else if (fileName.contains("平衡压袋稳管")) {
                    entryList = FileEntry.gbalanceOkList(sheetMap.get(fileName));
                    upGbalanceByNo(entryList, projectId);
                } else if (fileName.contains("浆砌石农耕道路")) {
                    entryList = FileEntry.gdfarmingList(sheetMap.get(fileName));
                    upGdfarmingByNo(entryList, projectId);
                } else if (fileName.contains("生态袋地坎")) {
                    entryList = FileEntry.zoologyOkList(sheetMap.get(fileName));
                    upZoologyByNo(entryList, projectId);
                } else if (fileName.contains("生态袋护坡")) {
                    entryList = FileEntry.zoologysList(sheetMap.get(fileName));
                    upZoologysByNo(entryList, projectId);
                } else if (fileName.contains("石笼防冲墙")) {
                    entryList = FileEntry.gabionwList(sheetMap.get(fileName));
                    upGabionwByNo(entryList, projectId);
                } else if (fileName.contains("石笼护底")) {
                    entryList = FileEntry.gabionbList(sheetMap.get(fileName));
                    upGabionbByNo(entryList, projectId);
                } else if (fileName.contains("石笼护坡")) {
                    entryList = FileEntry.gabionsList(sheetMap.get(fileName));
                    upGabionsByNo(entryList, projectId);
                } else if (fileName.contains("水平沟护面")) {
                    entryList = FileEntry.levelgList(sheetMap.get(fileName));
                    upLevelgByNo(entryList, projectId);
                } else if (fileName.contains("素喷护面")) {
                    entryList = FileEntry.pfaceList(sheetMap.get(fileName));
                    upPfaceByNo(entryList, projectId);
                } else if (fileName.contains("鱼鳞坑护面")) {
                    entryList = FileEntry.sfaceList(sheetMap.get(fileName));
                    upSfaceByNo(entryList, projectId);
                } else if (fileName.contains("植生带护面")) {
                    entryList = FileEntry.bfaceList(sheetMap.get(fileName));
                    upBfaceByNo(entryList, projectId);
                }
            }
        }
    }

    /**
     * @param list
     * @param projectId
     * @desc 更新中线成果表
     */
    public void updateCenterLineByNo(List<CenterlineOk> list, String projectId) {
//    public void updateCenterLineByNo(List<CenterlineOk> list, String projectId,String partid, List<String> stakepointnoList) {
//                List<String> stakepointnoList2=partPointsMapper.getStakepointno(partid);
        try {
            for (CenterlineOk line : list) {
                QueryWrapper<CenterlineOk> queryWrapper = new QueryWrapper<>();
                //queryWrapper.select("smid","stakepointno");
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                CenterlineOk ent = centerlineOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                    line.setPkid(ent.getPkid());
                    centerlineOkMapper.updateById(line);
                } else {
                    centerlineOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测绘数据
     *
     * @param list
     * @param projectId
     * @desc 更新断面点表
     */
    public void updateSectionspointOkByNo(List<SectionspointOk> list, String projectId) {
        try {
            Long a=1L;
            for (SectionspointOk line : list) {
                QueryWrapper<SectionspointOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                SectionspointOk ent = sectionspointOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    sectionspointOkMapper.updateById(line);
                } else {
                    sectionspointOkMapper.insert(line);
                }
                a++;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测绘数据
     *
     * @param list
     * @param projectId
     * @desc 更新控制点表
     */
    public void updateControlPointOkByNo(List<ControlpointOk> list, String projectId) {
        try {
            for (ControlpointOk line : list) {
                QueryWrapper<ControlpointOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("national_code", line.getNationalCode());
                ControlpointOk ent = controlpointOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    controlpointOkMapper.updateById(line);
                } else {
                    controlpointOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 勘察数据
     * 线路地质分段表
     *
     * @param list
     * @param projectId
     */
    public void updateLinegeologyfdByNo(List<LinegeologyfdOk> list, String projectId) {
        try {
            for (LinegeologyfdOk line : list) {
                QueryWrapper<LinegeologyfdOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no_start", line.getStakePointNoStart());
                queryWrapper.eq("stake_point_no_end", line.getStakePointNoEnd());
                LinegeologyfdOk ent = linegeologyfdOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    linegeologyfdOkMapper.updateById(line);
                } else {
                    linegeologyfdOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 勘察数据
     * 地质剖面图表
     *
     * @param list
     * @param projectId
     */
    public void updatGeologyPlanetbokByNo(List<GeologyplanetbOk> list, String projectId) {
        try {
            for (GeologyplanetbOk line : list) {
                QueryWrapper<GeologyplanetbOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no_start", line.getStakePointNoStart());
                queryWrapper.eq("stake_point_no_end", line.getStakePointNoEnd());
                GeologyplanetbOk ent = geologyplanetbOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    geologyplanetbOkMapper.updateById(line);
                } else {
                    geologyplanetbOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 勘察数据
     * 土壤电阻率表
     *
     * @param list
     * @param projectId
     */
    public void updatSoilResistivityOkByNo(List<SoilresistivityOk> list, String projectId) {
        try {
            for (SoilresistivityOk line : list) {
                QueryWrapper<SoilresistivityOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                SoilresistivityOk ent = soilresistivityOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    soilresistivityOkMapper.updateById(line);
                } else {
                    soilresistivityOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 勘察数据
     * 勘探点坐标表
     *
     * @param list
     * @param projectId
     */
    public void updatExploratoryPointOkByNo(List<ExploratorypointOk> list, String projectId) {
        try {
            for (ExploratorypointOk line : list) {
                QueryWrapper<ExploratorypointOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("exploration_point_no", line.getExplorationPointNo());
                ExploratorypointOk ent = exploratorypointOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    exploratorypointOkMapper.updateById(line);
                } else {
                    exploratorypointOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 勘察数据
     * 勘探点地层表
     *
     * @param list
     * @param projectId
     */
    public void updatExploratoryPointLayerOkByNo(List<ExploratorypointlayerOk> list, String projectId) {
        try {
            for (ExploratorypointlayerOk line : list) {
                QueryWrapper<ExploratorypointlayerOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("exploration_point_no", line.getExplorationPointNo());
                ExploratorypointlayerOk ent = exploratorypointlayerOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    exploratorypointlayerOkMapper.updateById(line);
                } else {
                    exploratorypointlayerOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (1)中线桩与焊口
     *
     * @param list
     * @param projectId
     */
    public void updatCenterlinepileweldedjunctionOk(List<CenterlinepileweldedjunctionOk> list, String projectId) {
        try {
            for (CenterlinepileweldedjunctionOk line : list) {
                QueryWrapper<CenterlinepileweldedjunctionOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("center_line_pile", line.getCenterLinePile());
                CenterlinepileweldedjunctionOk ent = centerlinepileweldedjunctionOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    centerlinepileweldedjunctionOkMapper.updateById(line);
                } else {
                    centerlinepileweldedjunctionOkMapper.insert(line);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (2)弯管
     *
     * @param list
     * @param projectId
     */
    public void updatElbowOk(List<ElbowOk> list, String projectId) {
        try {
            for (ElbowOk line : list) {
                QueryWrapper<ElbowOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                ElbowOk ent = elbowOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    elbowOkMapper.updateById(line);
                } else {
                    elbowOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (3)三桩
     *
     * @param list
     * @param projectId
     */
    public void updateThreepilesOk(List<ThreepilesOk> list, String projectId) {
        try {
            for (ThreepilesOk line : list) {
                QueryWrapper<ThreepilesOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                queryWrapper.eq("project_id", projectId);
                ThreepilesOk ent = threepilesOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    threepilesOkMapper.updateById(line);
                } else {
                    threepilesOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (4)穿跨越与地下障碍物合并
     *
     * @param list
     * @param projectId
     */
    public void updateCombinationobstaclesOk(List<CombinationobstaclesOk> list, String projectId) {
        try {
            for (CombinationobstaclesOk line : list) {
                QueryWrapper<CombinationobstaclesOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                CombinationobstaclesOk ent = combinationobstaclesOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    combinationobstaclesOkMapper.updateById(line);
                } else {
                    combinationobstaclesOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (5)水工保护
     * 编织袋稳管
     *
     * @param list
     * @param projectId
     */

    public void updatHydraulicproOkByNo(List<HydraulicproOk> list, String projectId) {
        try {
            for (HydraulicproOk line : list) {
                QueryWrapper<HydraulicproOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                HydraulicproOk ent = hydraulicproOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    hydraulicproOkMapper.updateById(line);
                } else {
                    hydraulicproOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (6)管材防腐地区等级
     *
     * @param list
     * @param projectId
     */
    public void updatGradeareaOkByNo(List<GradeareaOk> list, String projectId) {
        try {
            for (GradeareaOk line : list) {
                QueryWrapper<GradeareaOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GradeareaOk ent = gradeareaOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    gradeareaOkMapper.updateById(line);
                } else {
                    gradeareaOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (7)地质报告
     *
     * @param list
     * @param projectId
     */
    public void updatGeologyreportOkByNo(List<GeologyreportOk> list, String projectId) {
        try {
            for (GeologyreportOk line : list) {
                QueryWrapper<GeologyreportOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GeologyreportOk ent = geologyreportOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    geologyreportOkMapper.updateById(line);
                } else {
                    geologyreportOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (8)站场
     *
     * @param list
     * @param projectId
     */
    public void updatBattlefieldOkByNo(List<BattlefieldOk> list, String projectId) {
        try {
            for (BattlefieldOk line : list) {
                QueryWrapper<BattlefieldOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("celp_num", line.getCelpNum());
                BattlefieldOk ent = battlefieldOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    battlefieldOkMapper.updateById(line);
                } else {
                    battlefieldOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (9)阀室
     *
     * @param list
     * @param projectId
     */
    public void updatValvechamberOkByNo(List<ValvechamberOk> list, String projectId) {
        try {
            for (ValvechamberOk line : list) {
                QueryWrapper<ValvechamberOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                queryWrapper.eq("project_id", projectId);
                ValvechamberOk ent = valvechamberOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    valvechamberOkMapper.updateById(line);
                } else {
                    valvechamberOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (10)变更单
     *
     * @param list
     * @param projectId
     */
    public void updatChangeorderOkByNo(List<ChangeorderOk> list, String projectId) {
        try {
            for (ChangeorderOk line : list) {
                QueryWrapper<ChangeorderOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                ChangeorderOk ent = changeorderOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    changeorderOkMapper.updateById(line);
                } else {
                    changeorderOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (11)行政区划
     *
     * @param list
     * @param projectId
     */

    public void updatAddivisionOkByNo(List<AddivisionOk> list, String projectId) {
        try {
            for (AddivisionOk line : list) {
                QueryWrapper<AddivisionOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                AddivisionOk ent = addivisionOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    addivisionOkMapper.updateById(line);
                } else {
                    addivisionOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (12)管道敷设方式
     *
     * @param list
     * @param projectId
     */
    public void updatPipelayingOkByNo(List<PipelayingOk> list, String projectId) {
        try {
            for (PipelayingOk line : list) {
                QueryWrapper<PipelayingOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                PipelayingOk ent = pipelayingOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    pipelayingOkMapper.updateById(line);
                } else {
                    pipelayingOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (13)平面图
     *
     * @param list
     * @param projectId
     */
    public void updatFlatchartOkByNo(List<FlatchartOk> list, String projectId) {
        try {
            for (FlatchartOk line : list) {
                QueryWrapper<FlatchartOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                FlatchartOk ent = flatchartOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    flatchartOkMapper.updateById(line);
                } else {
                    flatchartOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (14)手孔
     *
     * @param list
     * @param projectId
     */
    public void updatHandholeOkByNo(List<HandholeOk> list, String projectId) {
        try {
            for (HandholeOk line : list) {
                QueryWrapper<HandholeOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                HandholeOk ent = handholeOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    handholeOkMapper.updateById(line);
                } else {
                    handholeOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (15)光缆接头盒
     *
     * @param list
     * @param projectId
     */
    public void updatOpticaboxOkByNo(List<OpticaboxOk> list, String projectId) {
        try {
            for (OpticaboxOk line : list) {
                QueryWrapper<OpticaboxOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                OpticaboxOk ent = opticaboxOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    opticaboxOkMapper.updateById(line);
                } else {
                    opticaboxOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (16)通信标石
     *
     * @param list
     * @param projectId
     */
    public void updatCommunicationOkByNo(List<CommunicationOk> list, String projectId) {
        try {
            for (CommunicationOk line : list) {
                QueryWrapper<CommunicationOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                CommunicationOk ent = communicationOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    communicationOkMapper.updateById(line);
                } else {
                    communicationOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (17)监测标石
     *
     * @param list
     * @param projectId
     */
    public void updatDetectionOkByNo(List<DetectionOk> list, String projectId) {
        try {
            for (DetectionOk line : list) {
                QueryWrapper<DetectionOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                DetectionOk ent = detectionOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    detectionOkMapper.updateById(line);
                } else {
                    detectionOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 线路数据
     * (18)高后果区视频监控
     *
     * @param list
     * @param projectId
     */
    public void updateVhconsequenceOkByNo(List<VhconsequenceOk> list, String projectId) {
        try {
            for (VhconsequenceOk line : list) {
                QueryWrapper<VhconsequenceOk> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("stake_point_no", line.getStakePointNo());
                VhconsequenceOk ent = vhconsequenceOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    vhconsequenceOkMapper.updateById(line);
                } else {
                    vhconsequenceOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 水土保护
     * 草袋素土堡坎 入库
     *
     * @param list
     * @param projectId
     */
    public void upSbagbankByNo(List<SbagbankOk> list, String projectId) {
        try {
            for (SbagbankOk line : list) {
                QueryWrapper<SbagbankOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                SbagbankOk sbagbankOk = sbagbankOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (sbagbankOk != null) {
                    line.setSmid(sbagbankOk.getSmid());
                    sbagbankOkMapper.updateById(line);
                } else {
                    sbagbankOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 草袋素土挡土墙 入库
     *
     * @param list
     * @param projectId
     */
    public void upSbagwByNo(List<SbagwallOk> list, String projectId) {
        try {
            for (SbagwallOk line : list) {
                QueryWrapper<SbagwallOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                SbagwallOk ent = sbagwallOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    sbagwallOkMapper.updateById(line);
                } else {
                    sbagwallOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 草袋素土护坡 入库
     *
     * @param list
     * @param projectId
     */
    public void upSbagsByNo(List<SbagslopeOk> list, String projectId) {
        try {
            for (SbagslopeOk line : list) {
                QueryWrapper<SbagslopeOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                SbagslopeOk ent = sbagslopeOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    sbagslopeOkMapper.updateById(line);
                } else {
                    sbagslopeOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 草方格固沙护面 入库
     *
     * @param list
     * @param projectId
     */
    public void upCverByNo(List<CoverOk> list, String projectId) {
        try {
            for (CoverOk line : list) {
                QueryWrapper<CoverOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                CoverOk ent = coverOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    coverOkMapper.updateById(line);
                } else {
                    coverOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 干砌石堡坎 入库
     *
     * @param list
     * @param projectId
     */
    public void upDrystoneByNo(List<DrystoneOk> list, String projectId) {
        try {
            for (DrystoneOk line : list) {
                QueryWrapper<DrystoneOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                DrystoneOk ent = drystoneOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    drystoneOkMapper.updateById(line);
                } else {
                    drystoneOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 干砌石过水面 入库
     *
     * @param list
     * @param projectId
     */
    public void upDrystonewByNo(List<DrystonewaterOk> list, String projectId) {
        try {
            for (DrystonewaterOk line : list) {
                QueryWrapper<DrystonewaterOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                DrystonewaterOk ent = drystonewaterOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    drystonewaterOkMapper.updateById(line);
                } else {
                    drystonewaterOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 干砌石护坡 入库
     *
     * @param list
     * @param projectId
     */
    public void upDrystonesByNo(List<DrystoneslopeOk> list, String projectId) {
        try {
            for (DrystoneslopeOk line : list) {
                QueryWrapper<DrystoneslopeOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                DrystoneslopeOk ent = drystoneslopeOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    drystoneslopeOkMapper.updateById(line);
                } else {
                    drystoneslopeOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 挂网锚喷护面 入库
     *
     * @param list
     * @param projectId
     */
    public void upHangByNo(List<HangnetOk> list, String projectId) {
        try {
            for (HangnetOk line : list) {
                QueryWrapper<HangnetOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                HangnetOk ent = hangnetOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    hangnetOkMapper.updateById(line);
                } else {
                    hangnetOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 混凝土堡坎 入库
     *
     * @param list
     * @param projectId
     */
    public void upBetonByNo(List<BetonOk> list, String projectId) {
        try {
            for (BetonOk line : list) {
                QueryWrapper<BetonOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                BetonOk ent = betonOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    betonOkMapper.updateById(line);
                } else {
                    betonOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 混凝土侧挡墙 入库
     *
     * @param list
     * @param projectId
     */
    public void upBetonwaByNo(List<BetonwallOk> list, String projectId) {
        try {
            for (BetonwallOk line : list) {
                QueryWrapper<BetonwallOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                BetonwallOk ent = betonwallOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    betonwallOkMapper.updateById(line);
                } else {
                    betonwallOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 混凝土过水面 入库
     *
     * @param list
     * @param projectId
     */
    public void upBetonwByNo(List<BetonwaterOk> list, String projectId) {
        try {
            for (BetonwaterOk line : list) {
                QueryWrapper<BetonwaterOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                BetonwaterOk ent = betonwaterOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    betonwaterOkMapper.updateById(line);
                } else {
                    betonwaterOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 混凝土护坡 入库
     *
     * @param list
     * @param projectId
     */
    public void upBetonsByNo(List<BetonslopeOk> list, String projectId) {
        try {
            for (BetonslopeOk line : list) {
                QueryWrapper<BetonslopeOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                BetonslopeOk ent = betonslopeOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    betonslopeOkMapper.updateById(line);
                } else {
                    betonslopeOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 混凝土连续浇筑稳管 入库
     *
     * @param list
     * @param projectId
     */
    public void upBetonpByNo(List<BetonplacOk> list, String projectId) {
        try {
            for (BetonplacOk line : list) {
                QueryWrapper<BetonplacOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                BetonplacOk ent = betonplacOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    betonplacOkMapper.updateById(line);
                } else {
                    betonplacOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 混凝土配重块稳管 入库
     *
     * @param list
     * @param projectId
     */
    public void upBetonbwByNo(List<BetonbobwOk> list, String projectId) {
        try {
            for (BetonbobwOk line : list) {
                QueryWrapper<BetonbobwOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                BetonbobwOk ent = betonbobwOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    betonbobwOkMapper.updateById(line);
                } else {
                    betonbobwOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 混凝土异型块护面 入库
     *
     * @param list
     * @param projectId
     */
    public void upBetonhtByNo(List<BetonheterotypeOk> list, String projectId) {
        try {
            for (BetonheterotypeOk line : list) {
                QueryWrapper<BetonheterotypeOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                BetonheterotypeOk ent = betonheterotypeOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    betonheterotypeOkMapper.updateById(line);
                } else {
                    betonheterotypeOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石堡坎 入库
     *
     * @param list
     * @param projectId
     */
    public void upGroutbByNo(List<GroutedrubbleOk> list, String projectId) {
        try {
            for (GroutedrubbleOk line : list) {
                QueryWrapper<GroutedrubbleOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubbleOk ent = groutedrubbleOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubbleOkMapper.updateById(line);
                } else {
                    groutedrubbleOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石挡土墙 入库
     *
     * @param list
     * @param projectId
     */
    public void upGroutewByNo(List<GroutedrubblewallOk> list, String projectId) {
        try {
            for (GroutedrubblewallOk line : list) {
                QueryWrapper<GroutedrubblewallOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubblewallOk ent = groutedrubblewallOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubblewallOkMapper.updateById(line);
                } else {
                    groutedrubblewallOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石防冲墙 入库
     *
     * @param list
     * @param projectId
     */
    public void upGroutefwByNo(List<GroutedrubblefwallOk> list, String projectId) {
        try {
            for (GroutedrubblefwallOk line : list) {
                QueryWrapper<GroutedrubblefwallOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubblefwallOk ent = groutedrubblefwallOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubblefwallOkMapper.updateById(line);
                } else {
                    groutedrubblefwallOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石拱形骨架护坡 入库
     *
     * @param list
     * @param projectId
     */
    public void upGrouteskByNo(List<GroutedrubbleskeletonOk> list, String projectId) {
        try {
            for (GroutedrubbleskeletonOk line : list) {
                QueryWrapper<GroutedrubbleskeletonOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubbleskeletonOk ent = groutedrubbleskeletonOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubbleskeletonOkMapper.updateById(line);
                } else {
                    groutedrubbleskeletonOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石过水面 入库
     *
     * @param list
     * @param projectId
     */
    public void upGroutwByNo(List<GroutedrubblewaterOk> list, String projectId) {
        try {
            for (GroutedrubblewaterOk line : list) {
                QueryWrapper<GroutedrubblewaterOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubblewaterOk ent = groutedrubblewaterOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubblewaterOkMapper.updateById(line);
                } else {
                    groutedrubblewaterOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石护坡 入库
     *
     * @param list
     * @param projectId
     */
    public void upGroutesByNo(List<GroutedrubbleslopeOk> list, String projectId) {
        try {
            for (GroutedrubbleslopeOk line : list) {
                QueryWrapper<GroutedrubbleslopeOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubbleslopeOk ent = groutedrubbleslopeOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubbleslopeOkMapper.updateById(line);
                } else {
                    groutedrubbleslopeOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石截排水沟(渠) 入库
     *
     * @param list
     * @param projectId
     */
    public void upGroutedByNo(List<GroutedrubbledrainOk> list, String projectId) {
        try {
            for (GroutedrubbledrainOk line : list) {
                QueryWrapper<GroutedrubbledrainOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubbledrainOk ent = groutedrubbledrainOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubbledrainOkMapper.updateById(line);
                } else {
                    groutedrubbledrainOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石截水墙 入库
     *
     * @param list
     * @param projectId
     */
    public void upGroutewwByNo(List<GroutedrubblewaterwallOk> list, String projectId) {
        try {
            for (GroutedrubblewaterwallOk line : list) {
                QueryWrapper<GroutedrubblewaterwallOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubblewaterwallOk ent = groutedrubblewaterwallOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubblewaterwallOkMapper.updateById(line);
                } else {
                    groutedrubblewaterwallOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石菱形骨架护坡 入库
     *
     * @param list
     * @param projectId
     */
    public void upGroutedhbByNo(List<GroutedrubblerhombusOk> list, String projectId) {
        try {
            for (GroutedrubblerhombusOk line : list) {
                QueryWrapper<GroutedrubblerhombusOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubblerhombusOk ent = groutedrubblerhombusOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubblerhombusOkMapper.updateById(line);
                } else {
                    groutedrubblerhombusOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石人形骨架护坡 入库
     *
     * @param list
     * @param projectId
     */
    public void upGrouterfByNo(List<GroutedrubblerfigureOk> list, String projectId) {
        try {
            for (GroutedrubblerfigureOk line : list) {
                QueryWrapper<GroutedrubblerfigureOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubblerfigureOk ent = groutedrubblerfigureOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubblerfigureOkMapper.updateById(line);
                } else {
                    groutedrubblerfigureOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 平衡压袋稳管 入库
     *
     * @param list
     * @param projectId
     */
    public void upGbalanceByNo(List<GbalanceOk> list, String projectId) {
        try {
            for (GbalanceOk line : list) {
                QueryWrapper<GbalanceOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GbalanceOk ent = gbalanceOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    gbalanceOkMapper.updateById(line);
                } else {
                    gbalanceOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 浆砌石农耕道路 入库
     *
     * @param list
     * @param projectId
     */
    public void upGdfarmingByNo(List<GroutedrubblefarmingOk> list, String projectId) {
        try {
            for (GroutedrubblefarmingOk line : list) {
                QueryWrapper<GroutedrubblefarmingOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GroutedrubblefarmingOk ent = groutedrubblefarmingOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    groutedrubblefarmingOkMapper.updateById(line);
                } else {
                    groutedrubblefarmingOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 生态袋地坎 入库
     *
     * @param list
     * @param projectId
     */
    public void upZoologyByNo(List<ZoologyOk> list, String projectId) {
        try {
            for (ZoologyOk line : list) {
                QueryWrapper<ZoologyOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                ZoologyOk ent = zoologyOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    zoologyOkMapper.updateById(line);
                } else {
                    zoologyOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 生态袋护坡 入库
     *
     * @param list
     * @param projectId
     */
    public void upZoologysByNo(List<ZoologyslopeOk> list, String projectId) {
        try {
            for (ZoologyslopeOk line : list) {
                QueryWrapper<ZoologyslopeOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                ZoologyslopeOk ent = zoologyslopeOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    zoologyslopeOkMapper.updateById(line);
                } else {
                    zoologyslopeOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 石笼防冲墙 入库
     *
     * @param list
     * @param projectId
     */
    public void upGabionwByNo(List<GabionwallOk> list, String projectId) {
        try {
            for (GabionwallOk line : list) {
                QueryWrapper<GabionwallOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GabionwallOk ent = gabionwallOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    gabionwallOkMapper.updateById(line);
                } else {
                    gabionwallOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 石笼护底 入库
     *
     * @param list
     * @param projectId
     */
    public void upGabionbByNo(List<GabionbottomOk> list, String projectId) {
        try {
            for (GabionbottomOk line : list) {
                QueryWrapper<GabionbottomOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GabionbottomOk ent = gabionbottomOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    gabionbottomOkMapper.updateById(line);
                } else {
                    gabionbottomOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 石笼护坡 入库
     *
     * @param list
     * @param projectId
     */
    public void upGabionsByNo(List<GabionslopeOk> list, String projectId) {
        try {
            for (GabionslopeOk line : list) {
                QueryWrapper<GabionslopeOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                GabionslopeOk ent = gabionslopeOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    gabionslopeOkMapper.updateById(line);
                } else {
                    gabionslopeOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 水平沟护面 入库
     *
     * @param list
     * @param projectId
     */
    public void upLevelgByNo(List<LevelgutterOk> list, String projectId) {
        try {
            for (LevelgutterOk line : list) {
                QueryWrapper<LevelgutterOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                LevelgutterOk ent = levelgutterOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    levelgutterOkMapper.updateById(line);
                } else {
                    levelgutterOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 素喷护面 入库
     *
     * @param list
     * @param projectId
     */
    public void upPfaceByNo(List<PlainfaceOk> list, String projectId) {
        try {
            for (PlainfaceOk line : list) {
                QueryWrapper<PlainfaceOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                PlainfaceOk ent = plainfaceOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    plainfaceOkMapper.updateById(line);
                } else {
                    plainfaceOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 水土保护
     * 鱼鳞坑护面 入库
     *
     * @param list
     * @param projectId
     */
    public void upSfaceByNo(List<ScalefaceOk> list, String projectId) {
        try {
            for (ScalefaceOk line : list) {
                QueryWrapper<ScalefaceOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                ScalefaceOk ent = scalefaceOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    scalefaceOkMapper.updateById(line);
                } else {
                    scalefaceOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 入库
     * 水土保护
     * 植生带护面
     *
     * @param list
     * @param projectId
     */
    public void upBfaceByNo(List<BeltseedingfaceOk> list, String projectId) {
        try {
            for (BeltseedingfaceOk line : list) {
                QueryWrapper<BeltseedingfaceOk> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("hpname",line.getHpname());
                queryWrapper.eq("project_id", projectId);
                queryWrapper.eq("start_station", line.getStartStation());
                queryWrapper.eq("end_station", line.getEndStation());
                BeltseedingfaceOk ent = beltseedingfaceOkMapper.selectOne(queryWrapper);
                line.setProjectId(projectId);
                if (ent != null) {
                   line.setPkid(ent.getPkid());
                    beltseedingfaceOkMapper.updateById(line);
                } else {
                    beltseedingfaceOkMapper.insert(line);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
