package com.supermap.pipedesign.pipechina.log.config;

import cn.hutool.core.util.StrUtil;
import com.supermap.common.OperateLog;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/4/2
 */
@Component
@Aspect
@Slf4j
public class OperationLogAop implements Ordered {
    @Value("${operation.log.type}")
    private String logType = "ApiOperation";
    @Value("${operation.log.enabled}")
    private boolean enabled = false;
    @Autowired
    private HandleLogAspect handleLogAspect;

    /**
     * 定义切入点为标记@ApiOperation注解的方法
     */
    @Pointcut(value = "@annotation(io.swagger.annotations.ApiOperation)")
    public void apiOperationCut() {
    }

    /**
     * 定义切入点为标记@OperateLog注解的方法
     */
    @Pointcut(value = "@annotation(com.supermap.common.OperateLog)")
    public void operateLogCut() {
    }


    /**
     * 业务操作环绕通知
     */
    @Around("apiOperationCut()")
    public Object apiLogCut(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        //执行目标方法
        Object result = null;
        String message = null;
        try {
            result = proceedingJoinPoint.proceed();
        } catch (Throwable ex) {
            message = ex.getMessage();
            throw ex;
        } finally {
            if (enabled) {
                if ("*".equals(logType)) {
                    logType = "OperateLog,ApiOperation";
                }
                List<String> logTypes = StrUtil.split(logType, ',');
                if (!logTypes.contains("OperateLog") || logTypes.size() != 1) {
                    //包含两种记录方式时，OperateLog优先使用
                    boolean isOperateLog = logTypes.contains("OperateLog");

                    MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
                    OperateLog annotation2 = signature.getMethod().getAnnotation(OperateLog.class);
                    if (!isOperateLog || annotation2 == null) {
                        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
                        handleLogAspect.writeLog(proceedingJoinPoint, request, true, message);
                    }
                }
            }

        }
        return result;
    }


    /**
     * 业务操作环绕通知
     */
    @Around("operateLogCut()")
    public Object operateCut(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        //执行目标方法
        Object result = null;
        String message = null;
        try {
            result = proceedingJoinPoint.proceed();
        } catch (Throwable ex) {
            message = ex.getMessage();
            throw ex;
        } finally {
            if (enabled) {
                List<String> logTypes = StrUtil.split(logType, ',');
                //包含两种记录方式时，OperateLog优先使用
                boolean isOperateLog = "*".equals(logType) || logTypes.contains("OperateLog");
                if (isOperateLog) {
                    HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
                    handleLogAspect.writeLog(proceedingJoinPoint, request, false, message);
                }
            }

        }
        return result;
    }

    @Override
    public int getOrder() {
        return 1;
    }
}
