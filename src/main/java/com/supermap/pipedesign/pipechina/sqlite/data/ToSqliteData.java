package com.supermap.pipedesign.pipechina.sqlite.data;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @title ToSqliteData
 * @date 2023/03/05 08:34
 * @description 复制项目数据到sqlite库数据
 */
public class ToSqliteData {

    //参数为pkid='项目id'
    public static List<String> engineering_only = Arrays.asList("wbs_engineering");
    //4、项目库PipeChina_PLD_Engineering
    //以上传项目ID
    public static List<String> engineering = Arrays.asList(
            "wbs_design",
            "wbs_design_scheme",
            "wbs_design_scheme_route",
            "wbs_design_se_file_upload_recor",
            "wbs_designsub_person",
            "wbs_designsub_task",
            "wbs_designsub_task_version",
            "wbs_designunit",
            "wbs_e_s_leader",
            "wbs_e_s_u_f_m_workbag",
            "wbs_e_s_u_f_major",
            "wbs_e_s_u_functionarea",
            "wbs_e_s_unit",
            "wbs_engineering_sub",
            "wbs_submit_entrust",
            "wbs_submit_entrust_part",
            "wbs_engineering_versions",
            "wbs_submit_entrust_content",
            "wbs_submit_entrust_file",
            "wbs_submit_entrust_file_ref",
            "wbs_workbag_file",
            "wbs_workbag_file_upload_record",
            "wbs_qi_meta_data_info",
            "wbs_osgb_config",
            "wbs_submit_entrust_dataset_relation");
    //以下不传
    public static List<String> engineering_no_project_id = Arrays.asList(
            "wbs_subjects",
            "wbs_dictionary",
            "wbs_construction_stage",
            "wbs_subject_data",
            "wbs_rules_tables",
//            "wbs_submit_entrust_file",
            "wbs_rules_tables_field",
            "wbs_user",
            "wbs_prjcoordsys_info");
    //数据库2、PipeChina_PLD_MetaData
    //以上传项目ID
    public static List<String> metadata = Arrays.asList(
            "pld_md_chartlet",
            "pld_md_dataset_field",
            "pld_md_dataset_info",
            "pld_md_directories",
            "pld_md_entity_datasref",
            "pld_md_entitytype_styles",
            "pld_md_entitytype_styles_parts",
            "pld_md_entitytype_styles_parts_pic",
            "pld_md_entitytype_theme_styles",
            "pld_md_entitytype_theme_styles_parts",
            "pld_md_entitytype_theme_styles_parts_pic",
            "pld_md_entitytypes",
            "pld_md_entitytypes_interpolation",
            "pld_md_entitytypes_interpolation_field",
            "pld_md_entitytypes_interpolation_field_value",
            "pld_md_entitytypes_rule",
            "pld_md_entitytypes_rule_algorit",
            "pld_md_write_way_att",
            "pld_dataset_field_writeway",
            "pld_dataset_field_writeway_value");
    //以下不传
    public static List<String> metadata_no_project_id = Arrays.asList(
            "pld_md_symbols",
            "pld_md_datasource_info",
            "pld_md_dataset_info_tem",
            "pld_md_directoryresref",
            "pld_md_dataset_field_temint",
            "pld_md_entitytypes_group_tem"); // "material_field_info", "material_type_register",
    //数据库PipeChina_PLDS_Rules
    //以上传项目ID
    public static List<String> rules = Arrays.asList(
            "qi_general_section_anti_corrosion",
            "qi_bend_section",
            "qi_crossing_section",
            "qi_common_tubing",
            "qi_build_sidewalk_line",
            "qi_e_u_fun",
            "qi_e_u_fun_field",
            "qi_elbow_bend_line_section",
            "qi_engineering_unit",
            "qi_entitytype_derive",
            "qi_entitytype_derive_field",
            "qi_entitytype_derive_stage",
            "qi_entitytype_merge",
            "qi_entitytype_merge_entitytype",
            "qi_entitytype_merge_stage",
            "qi_excavation_through",
            "qi_fashi_configure",
            "qi_fashi_create",
            "qi_general_drawing",
            "qi_general_section_slope",
            "qi_high_consequence_area_identify",
            "qi_hydraulic_protection",
            "qi_intelligent_line_selection_rule_list",
            "qi_intelligent_routing_rules_catalog",
            "qi_line_intersect_layer",
            "qi_line_intersect_line",
            "qi_line_reference_object",
            "qi_line_region_create_line",
            "qi_lines_angle",
            "qi_lining_fracture_surface",
            "qi_longitudinal_line",
            "qi_material",
            "qi_material_e_u_fun",
            "qi_material_e_u_fun_field",
            "qi_material_e_u_fun_operation",
            "qi_material_e_u_fun_operation_unit",
            "qi_material_item",
            "qi_material_template",
            "qi_material_template_unit",
            "qi_material_unit",
            "qi_on_line",
            "qi_orient_drill_through",
            "qi_proportion_materials",
            "qi_proportion_protective_measures",
            "qi_put_slope",
            "qi_radius_curvature_corner",
            "qi_ridge_tunnel_through",
            "qi_space_rule_child_register",
            "qi_space_rule_child_register_rcc",
            "qi_stage_rule",
            "qi_subsection_rules",
            "qi_subsection_rules_field",
            "qi_syphon_transition",
            "qi_warm_groud_line",
            "rcc_avoid_point",
            "rcc_avoid_region",
            "rcc_line_angle_line",
            "rcc_line_distance_line",
            "rcc_line_distance_region",
            "rcc_line_parallel_line",
            "rcc_line_parallel_region",
            "rcc_line_vertical_line",
            "rcc_no_point",
            "rcc_no_region",
            "ruledef_direcotries",
            "qi_area_level_partition",
            "qi_line_derive_line",
            "qi_line_derive_spot",
            "qi_spot_derive_spot",
            "qi_point_derive_spot",
            "qi_derive_attribute",
            "qi_engineering_template",
            "qi_engineering_template_unit",
            "qi_e_u_fun_operation",
            "qi_e_u_fun_operation_unit",
            "qi_lines_angle_line",
            "qi_spot_derive_line",
            "qi_engineering_item"
    );//"qi_subsection","qi_subsection_rules",
    //以下不传
    public static List<String> rules_no_project_id = Arrays.asList(
            "qi_algorithm_register_params",
            "qi_algorithm_register",
            "qi_algorithm_regist_params_survey",
            "qi_algorithm_regist_survey",
            "qi_rules_metadata_field_survey",
            "qi_rules_types_survey",
            "qi_rules_table_survey",
            "qi_rules_regist_survey",
            "qi_rules_field_survey",
            "material_type_pro");
    //3、模型库PipeChina_PLD_DrawingTemplate
    //以上传项目ID
    public static List<String> drawingTemplate = Arrays.asList(
            "basic_block",
            "basic_district",
            "block_property_def",
            "drawing_column_config",
            "drawing_components",
            "drawing_components_type",
            "drawing_font_style_config",
            "drawing_line_style_config",
            "drawing_template_locate",
            "pld_md_parts",
            "pld_md_parts_pic",
            "pld_multilingual",
            "pld_template_directory",
            "pld_templatefiles",
            "pld_templates",
            "property_block",
            "sub_offset_distance",
            "sub_offset_distance_extend");
    //以下不传
    public static List<String> drawingTemplate_no_project_id = Arrays.asList(
            "map_frame_config",
            "map_frame_result",
            "map_run_config",
            "drawing_font_style_template_tem",
            "drawing_line_style_template_tem",
            "drawing_column_define_tem",
            "basic_block_tem",
            "basic_district_tem",
            "block_property_def_tem",
            "drawing_column_config_tem",
            "drawing_components_tem",
            "drawing_components_type_tem",
            "drawing_font_style_config_tem",
            "drawing_line_style_config_tem",
            "drawing_template_locate_tem",
            "pld_md_parts_tem",
            "pld_md_parts_pic_tem",
            "pld_multilingual_tem",
            "pld_template_directory_tem",
            "pld_templatefiles_tem",
            "pld_templates_tem",
            "property_block_tem",
            "sub_offset_distance_tem");

    public static List<String> parts_tem_no_project_id = Arrays.asList(
            "pld_md_parts_tem",
            "pld_md_parts_pic_tem");

    public static List<String> parts_project_id = Arrays.asList("pld_md_parts",
            "pld_md_parts_pic");
    /**
     * @作者 eomer
     * @描述 根据表名获取sql
     * @日期 2023/03/05 08:58
     * 参数 tableName 表名
     * 参数 nspname 模式
     * @返回值 java.lang.String
     **/
    public static String getSqlByTableName(String tableName ,String nspname){
        //nspname = "public";
        return "SELECT c.table_name AS relname,\n" +
                "       c.column_name AS attname,\n" +
                "       d.description,\n" +
                "       CASE WHEN c.character_maximum_length IS NOT NULL THEN c.udt_name || '(' || c.character_maximum_length || ')'\n" +
                "            ELSE c.udt_name END AS coltype,\n" +
                "       ROW_NUMBER() OVER (PARTITION BY c.table_name ORDER BY c.ordinal_position) AS colsort\n" +
                "FROM information_schema.columns c\n" +
                "LEFT JOIN pg_description d ON d.objoid = (quote_ident(c.table_schema) || '.' || quote_ident(c.table_name))::regclass AND d.objsubid = c.ordinal_position\n" +
                "WHERE c.table_schema = '"+nspname+"' AND c.table_name = '"+tableName+"'\n" +
                "ORDER BY c.table_name, colsort";
    }

      // postgres数据库使用版本，openGauss使用查询字段类型为null，代码会报空指针异常,所以注释弃用
//    public static String getSqlByTableName(String tableName ,String nspname){
//        //nspname = "public";
//        return "WITH C AS (\n" +
//                "    SELECT oid,relname FROM pg_class C\n" +
//                "    WHERE\n" +
//                "        C.relname ='"+tableName+"'\n" +
//                "        AND relnamespace IN ( SELECT oid FROM pg_namespace WHERE nspname = '"+nspname+"' )\n" +
//                "    ),\n" +
//                "    A AS (\n" +
//                "    SELECT C\n" +
//                "        .relname,\n" +
//                "        A.attname,\n" +
//                "        d.description,\n" +
//                "        concat_ws ( '', T.typname, SUBSTRING ( format_type ( A.atttypid, A.atttypmod ) FROM '\\(.*\\)' ) ) AS coltype,\n" +
//                "        A.attnum colsort\n" +
//                "    FROM\n" +
//                "        C JOIN pg_attribute A ON A.attrelid = C.oid\n" +
//                "        AND A.attnum > 0 AND A.atttypid !=0 \n" +
//                "        LEFT JOIN pg_type T ON A.atttypid = T.oid\n" +
//                "        LEFT JOIN pg_description d ON d.objoid = A.attrelid\n" +
//                "        AND d.objsubid = A.attnum\n" +
//                "    ORDER BY C.relname,A.attnum\n" +
//                "    ) SELECT relname,attname,description,coltype,colsort\n" +
//                "FROM A ORDER BY relname, colsort";
//    }

    /*
        适用于 postgres 和 openGauss
WITH C AS ( SELECT oid,relname FROM pg_class C WHERE C.relname ='wbs_design' AND relnamespace IN ( SELECT oid FROM pg_namespace WHERE nspname = 'test' ) ), A AS ( SELECT C .relname, A.attname, d.description,
T.typname || SUBSTRING ( format_type ( A.atttypid, A.atttypmod ) FROM '\(.*\)' )  AS coltype, A.attnum colsort FROM C JOIN pg_attribute A ON A.attrelid = C.oid AND A.attnum > 0 AND A.atttypid !=0 LEFT JOIN pg_type T ON A.atttypid = T.oid LEFT JOIN pg_description d ON d.objoid = A.attrelid AND d.objsubid = A.attnum ORDER BY C.relname,A.attnum ) SELECT relname,attname,description,coltype,colsort FROM A ORDER BY relname, colsort

     */



}

