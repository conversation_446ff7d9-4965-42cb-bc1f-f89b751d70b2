package com.supermap.pipedesign.pipechina.sqlite.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.supermap.config.exception.BusinessException;
import com.supermap.pipedesign.pipechina.drawingtemplate.dao.DrawingTemplateCommonMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.EngineeringCommonMapper;
import com.supermap.pipedesign.pipechina.file.impl.CreateSqllitePackageImpl;
import com.supermap.pipedesign.pipechina.metadata.dao.MetadataCommonMapper;
import com.supermap.pipedesign.pipechina.rules.dao.RulesCommonMapper;
import com.supermap.pipedesign.pipechina.sqlite.data.ToSqliteData;
import com.supermap.pipedesign.pipechina.sqlite.enums.SqliteEnum;
import com.supermap.pipedesign.pipechina.sqlite.service.IProjectToSqliteService;
import com.supermap.tools.base.JavaUtils;
import com.supermap.tools.file.PathUtils;
import com.supermap.tools.gson.GsonUtil;
import com.supermap.tools.sqlite.SqliteUtils;
import com.supermap.tools.sqlite.vo.FiledInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @title ProjectToSqliteImpl
 * @date 2023/03/04 20:24
 * @description TODO
 */
@Slf4j
@Service("ProjectToSqliteImpl")
public class ProjectToSqliteImpl implements IProjectToSqliteService {

    // 项目库
    @Autowired
    private EngineeringCommonMapper engineeringCommonMapper;
    // 元数据库
    @Autowired
    private MetadataCommonMapper metadataCommonMapper;
    // 规则库
    @Autowired
    private RulesCommonMapper rulesCommonMapper;
    // 模板库
    @Autowired
    private DrawingTemplateCommonMapper drawingTemplateCommonMapper;

    //private String dbPath = "";

    @Value("${toSqliteData.engineering_only}")
    private String toSqliteData_engineering_only;

    @Value("${toSqliteData.engineering}")
    private String toSqliteData_engineering;

    @Value("${toSqliteData.engineering_no_project_id}")
    private String toSqliteData_engineering_no_project_id;

    @Value("${toSqliteData.metadata}")
    private String toSqliteData_metadata;

    @Value("${toSqliteData.metadata_no_project_id}")
    private String toSqliteData_metadata_no_project_id;

    @Value("${toSqliteData.rules}")
    private String toSqliteData_rules;

    @Value("${toSqliteData.rules_no_project_id}")
    private String toSqliteData_rules_no_project_id;

    @Value("${toSqliteData.drawingTemplate}")
    private String toSqliteData_drawingTemplate;

    @Value("${toSqliteData.drawingTemplate_no_project_id}")
    private String toSqliteData_drawingTemplate_no_project_id;

    @Value("${toSqliteData.parts_tem_no_project_id}")
    private String toSqliteData_parts_tem_no_project_id;

    @Value("${toSqliteData.parts_project_id}")
    private String toSqliteData_parts_project_id;



    /**
     * @描述 初始化项目db
     * @日期 2023/04/15 18:18
     * @作者 eomer
     **/
    @Override
    public void initProjectDB(){
        // 循环库 生成表， designrules\drawingtemplate\engineering\metadata
        // \parts
        String[] dbs = "designrules/drawingtemplate/engineering/metadata".split("/");
        for (int i = 0; i < dbs.length; i++) {
            String dbName = dbs[i];
            String srcPath = PathUtils.getInitPath()+File.separator+dbName+".db";
            String tarPath = PathUtils.getProjectPath()+File.separator+dbName+".db";
            FileUtil.del(tarPath);
            FileUtil.copyFile(srcPath,tarPath);
            //查询 数据库下所有表 SELECT tablename FROM pg_tables where schemaname ='dev'  ;
            SqliteEnum sqliteEnum = getByDb(dbName);

            List<Map> tables = getTablesByDb(sqliteEnum);
            for (int j = 0; j < tables.size(); j++) {
                String table = tables.get(j).get("tablename")+"";
                List<FiledInfo> fileds = getTabelHeads(table, sqliteEnum);
                createSqlLiteDbFile(tarPath, table, fileds);
            }
        }

    }

    /**
     * @描述 获取指定数据库下所有表
     * @日期 2023/04/15 18:36
     * @作者 eomer
     **/
    public List<Map> getTablesByDb(SqliteEnum sqliteEnum){
        // ToSqliteData.getSqlByTableName(tableName,dbCurrentSchema);
        String sql = "SELECT tablename FROM pg_tables where schemaname ='{}' ";
        sql = StrUtil.format(sql,dbCurrentSchema);
        List<Map> list = getListByType(sql, sqliteEnum);
        return GsonUtil.ObjectToList(list,Map.class);
    }

    /**
     * @描述 根据名称获取枚举
     * @日期 2023/04/15 18:25
     * @作者 eomer
     **/
    private SqliteEnum getByDb(String db){
        SqliteEnum result = SqliteEnum.metadataCommonMapper;
        if ("designrules".equals(db)) {
            result = SqliteEnum.rulesCommonMapper;
        }
        else if ("drawingtemplate".equals(db)) {
            result = SqliteEnum.drawingTemplateCommonMapper;
        }else if ("engineering".equals(db)) {
            result = SqliteEnum.engineeringCommonMapper;
        }else if ("metadata".equals(db)) {
            result = SqliteEnum.metadataCommonMapper;
        }else if ("parts".equals(db)) {
            result = SqliteEnum.partsCommonMapper;
        }
        return result;
    }

    /**
     * @描述 创建表
     * @日期 2023/04/15 18:31
     * @作者 eomer
     **/
    private void createSqlLiteDbFile(String tarPath, String tableName, List<FiledInfo> fields) {


        Map<String,Object> hashSet = new HashMap<>();
        List<String> fieldList = new ArrayList<>();
        for( FiledInfo filedInfo : fields ){
            String column = filedInfo.getAttname();
            if (hashSet.get(column)!=null){
                continue;
            }
            hashSet.put(column,column);
            String type = filedInfo.getColtype();
            String fileType = "";
            if (type.toLowerCase().startsWith("int")){
                //stringBuilder.append(" INTEGER");
                fileType = "INTEGER";
            }
            else if (type.toLowerCase().startsWith("double") || type.toLowerCase().startsWith("float")){
                //stringBuilder.append(" REAL");
                fileType = "REAL";
            }else{
                //stringBuilder.append(" TEXT");
                fileType = "TEXT";
            }
            fieldList.add(StrUtil.format(" {} {} ",column,fileType));
        }
        if (hashSet.get("pkid")==null) {
            fieldList.add("pkid TEXT");
        }
        String createSql = StrUtil.format("CREATE TABLE {} ({})",tableName,String.join(",",fieldList));
        try {
            log.info("创建表 -->"+tableName);
            log.info(""+createSql);
            //String path = PathUtils.getInitPath()+File.separator+dbName+".db";
            log.info("db --> "+tarPath);
            SqliteUtils.createTable(tarPath, createSql);
        }catch (SQLException exp ){
            exp.printStackTrace();
        }
    }


    @Override
    public List<Map> getList(String projectId){


        /*  copyTableByProjectId(projectId);*/

        //return engineeringCommonMapper.getList(sql);
        return null;
    }

    /**
     * @作者 eomer
     * @描述 根据项目ID 复制数据到 sqlite
     * @日期 2023/03/05 09:40
     * 参数 projectId
     * @返回值 void
     **/
    @Override
    public List<String> copyTableByProjectId(String projectId,String proPath){
        String dbPath = proPath+ File.separator;
        /*
        // 工程
        log.info(StrUtil.format("{}库，开始复制"),"工程");
        copyTableByPkid(Arrays.asList(toSqliteData_engineering_only.split(",")).get(0), SqliteEnum.engineeringCommonMapper, projectId,dbPath);
        copyTableToDB(Arrays.asList(toSqliteData_engineering.split(",")), SqliteEnum.engineeringCommonMapper, projectId,dbPath);
        copyTableToDB(Arrays.asList(toSqliteData_engineering_no_project_id.split(",")), SqliteEnum.engineeringCommonMapper, "",dbPath);
        // 元数据
        log.info(StrUtil.format("{}库，开始复制"),"元数据");
        copyTableToDB(Arrays.asList(toSqliteData_metadata.split(",")), SqliteEnum.metadataCommonMapper, projectId,dbPath);
        copyTableToDB(Arrays.asList(toSqliteData_metadata_no_project_id.split(",")), SqliteEnum.metadataCommonMapper, "",dbPath);
        // 规则
        log.info(StrUtil.format("{}库，开始复制"),"规则");
        copyTableToDB(Arrays.asList(toSqliteData_rules.split(",")), SqliteEnum.rulesCommonMapper, projectId,dbPath);
        copyTableToDB(Arrays.asList(toSqliteData_rules_no_project_id.split(",")), SqliteEnum.rulesCommonMapper, "",dbPath);
        // 模板
        log.info(StrUtil.format("{}库，开始复制"),"模板");
        copyTableToDB(Arrays.asList(toSqliteData_drawingTemplate.split(",")), SqliteEnum.drawingTemplateCommonMapper, projectId,dbPath);
        copyTableToDB(Arrays.asList(toSqliteData_drawingTemplate_no_project_id.split(",")), SqliteEnum.drawingTemplateCommonMapper, "",dbPath);
        //部件
        log.info(StrUtil.format("{}库，开始复制"),"部件");
        copyTableToDB(Arrays.asList(toSqliteData_parts_tem_no_project_id.split(",")), SqliteEnum.partsCommonMapper, "",dbPath);
        copyTableToDB(Arrays.asList(toSqliteData_parts_project_id.split(",")), SqliteEnum.partsCommonMapper, projectId,dbPath);
         */

        List<String> resultList = Collections.synchronizedList(new ArrayList<>());
        Thread threadEngineering = new Thread(() -> {
            resultList.addAll(copyTableEngineeringByProjectId(projectId,dbPath));
        });
        threadEngineering.start();

        Thread threadMetadata = new Thread(() -> {
            resultList.addAll(copyTableMetadataByProjectId(projectId,dbPath));
        });
        threadMetadata.start();

        Thread threadRules = new Thread(() -> {
            resultList.addAll(copyTableRulesByProjectId(projectId,dbPath));
        });
        threadRules.start();

        Thread threadDrawingTemplate = new Thread(() -> {
            resultList.addAll(copyTableDrawingTemplateByProjectId(projectId,dbPath));
        });
        threadDrawingTemplate.start();

        Thread threadParts = new Thread(() -> {
            resultList.addAll(copyTablePartsByProjectId(projectId,dbPath));
        });
        threadParts.start();

        while (true){
            if(!threadEngineering.isAlive()
                    &&!threadMetadata.isAlive()
                    &&!threadRules.isAlive()
                    &&!threadDrawingTemplate.isAlive()
                    &&!threadParts.isAlive()){
                break;
            }
            try {
                Thread.sleep(2000L);
            }catch (Exception e) {
                log.error("copyTableByProjectId线程异常。projectId{},proPath{}", projectId, proPath, e);
                resultList.add("copyTableByProjectId线程异常。");
                return resultList;
            }
        }
        return resultList;
    }

    private List<String> copyTableEngineeringByProjectId(String projectId,String dbPath){
        try {
            // 工程
            log.info("{}库，开始复制","工程");
            List<String> resultList1 = copyTableByPkid(Arrays.asList(toSqliteData_engineering_only.split(",")).get(0), SqliteEnum.engineeringCommonMapper, projectId,dbPath);
            List<String> resultList2 = copyTableToDB(Arrays.asList(toSqliteData_engineering.split(",")), SqliteEnum.engineeringCommonMapper, projectId,dbPath);
            List<String> resultList3 = copyTableToDB(Arrays.asList(toSqliteData_engineering_no_project_id.split(",")), SqliteEnum.engineeringCommonMapper, "",dbPath);
            resultList1.addAll(resultList2);
            resultList1.addAll(resultList3);
            return resultList1;
        }catch (Exception e){
            log.error("复制工程，异常，projectid：{}，dbpath：{}",projectId,dbPath,e);
            return new ArrayList<>();
        }
    }
    private List<String> copyTableMetadataByProjectId(String projectId,String dbPath){
        try {
            // 元数据
            log.info("{}库，开始复制","元数据");
            List<String> resultList1 = copyTableToDB(Arrays.asList(toSqliteData_metadata.split(",")), SqliteEnum.metadataCommonMapper, projectId,dbPath);
            List<String> resultList2 = copyTableToDB(Arrays.asList(toSqliteData_metadata_no_project_id.split(",")), SqliteEnum.metadataCommonMapper, "",dbPath);
            resultList1.addAll(resultList2);
            return resultList1;
        }catch (Exception e){
            log.error("复制元数据，异常，projectid：{}，dbpath：{}",projectId,dbPath,e);
            return new ArrayList<>();
        }
    }
    private List<String> copyTableRulesByProjectId(String projectId,String dbPath){
        try {
            // 规则
            log.info("{}库，开始复制","规则");
            List<String> resultList1 = copyTableToDB(Arrays.asList(toSqliteData_rules.split(",")), SqliteEnum.rulesCommonMapper, projectId,dbPath);
            List<String> resultList2 = copyTableToDB(Arrays.asList(toSqliteData_rules_no_project_id.split(",")), SqliteEnum.rulesCommonMapper, "",dbPath);
            resultList1.addAll(resultList2);
            return resultList1;
        }catch (Exception e){
            log.error("复制规则，异常，projectid：{}，dbpath：{}",projectId,dbPath,e);
            return new ArrayList<>();
        }
    }

    private List<String> copyTableDrawingTemplateByProjectId(String projectId,String dbPath) {

        try {
            // 模板
            log.info("{}库，开始复制","模板");
            List<String> resultList1 = copyTableToDB(Arrays.asList(toSqliteData_drawingTemplate.split(",")), SqliteEnum.drawingTemplateCommonMapper, projectId,dbPath);
            List<String> resultList2 = copyTableToDB(Arrays.asList(toSqliteData_drawingTemplate_no_project_id.split(",")), SqliteEnum.drawingTemplateCommonMapper, "",dbPath);
            resultList1.addAll(resultList2);
            return resultList1;
        } catch (Exception e) {
            log.error("复制模板，异常，projectid：{}，dbpath：{}",projectId,dbPath,e);
            return new ArrayList<>();
        }

    }

    private  List<String> copyTablePartsByProjectId(String projectId,String dbPath) {

        try {
            //部件
            log.info("{}库，开始复制","部件");
            List<String> resultList1 = copyTableToDB(Arrays.asList(toSqliteData_parts_tem_no_project_id.split(",")), SqliteEnum.partsCommonMapper, "",dbPath);
            List<String> resultList2 = copyTableToDB(Arrays.asList(toSqliteData_parts_project_id.split(",")), SqliteEnum.partsCommonMapper, projectId,dbPath);
            resultList1.addAll(resultList2);
            return resultList1;
        } catch (Exception e) {
            log.error("复制部件，异常，projectid：{}，dbpath：{}",projectId,dbPath,e);
            return new ArrayList<>();
        }

    }
    /**
     * @描述 根据pkid复制表
     * @日期 2023/03/05 09:44
     * @作者 eomer
     **/
    public List<String> copyTableByPkid(String table, SqliteEnum sqliteEnum, String projectId,String dbPath){

        List<String> resultList = new ArrayList<>();
        List<FiledInfo> fileds = getTabelHeads(table, sqliteEnum);
        String sql = " select * from "+table+" where pkid='"+projectId+"'";
        List<Map<String,Object>> tableData =  getListByType(sql,sqliteEnum);
        String path = getPathByType(dbPath,sqliteEnum);
        try {
            log.info("复制项目数据copyTableByPkid，开始，表{}，数量：{},projectid:{}，库{}",table,tableData.size(),projectId,path);
            SqliteUtils.executeUpdate(path, "delete from "+table+" where pkid='"+projectId+"'");
            log.info(StrUtil.format("删除项目："+projectId));
            SqliteUtils.batchInsert(path, table, fileds, tableData);
            log.info(StrUtil.format("添加项目："+projectId));
            int count = SqliteUtils.queryCount(path,"select count(1) from "+table+" where pkid='"+projectId+"'");
            int sourceCount = 0;
            if(tableData!=null){
                sourceCount = tableData.size();
            }
            if(count!=sourceCount){
                resultList.add(String.format("%s入库数据数量%s，与源数据不匹配，源数据数量%s",table,count,tableData.size()));
            }
            return resultList;

        } catch (SQLException e) {
            log.error("复制项目数据copyTableByPkid，异常，表{}，数量：{},projectid:{}，库{}",table,tableData.size(),projectId,path,e);
            //e.printStackTrace();
            throw new BusinessException("项目数据写入失败，稍后请重试！");
        }
    }


    /**
     * @作者 eomer
     * @描述 复制数据写入
     * @日期 2023/03/05 09:36
     * 参数 list
     * 参数 sqliteEnum
     * 参数 projectId
     * @返回值 void
     **/
    public List<String> copyTableToDB(List<String> list, SqliteEnum sqliteEnum, String projectId,String dbPath){
        List<String> resultList = new ArrayList<>();
        for (int i = 0; i < list.size();) {
            String table = list.get(i);
            List<FiledInfo> fileds = getTabelHeads(table, sqliteEnum);
            List<Map<String,Object>> tableData = getTableData(sqliteEnum, table, projectId);
            String path = getPathByType(dbPath,sqliteEnum);
            //System.out.println("db 路径："+path);
            try {
                log.info("复制项目数据copyTableToDB，开始，表{}，数量：{},projectid:{}，库{}",table,tableData.size(),projectId,path);
                SqliteUtils.executeUpdate(path,"delete from "+table);
                //System.out.println("清空表："+table);
                SqliteUtils.batchInsert(path, table, fileds, tableData);
                log.info(StrUtil.format("{} 表,  添加数据 -> {} 条 ；项目ID：{}",table,tableData.size(), projectId));
                //System.out.println(StrUtil.format("批量添加数据，表：{} -> 数量：{}",table,tableData.size()));
                int count = SqliteUtils.queryCount(path,"select count(1) from "+table);
                int sourceCount = 0;
                if(tableData!=null){
                    sourceCount = tableData.size();
                }
                if(count!=sourceCount){
                    resultList.add(String.format("%s入库数据数量%s，与源数据不匹配，源数据数量%s",table,count,tableData.size()));
                }
            } catch (SQLException e) {
                log.error("复制项目数据copyTableToDB，异常，表{}，数量：{},projectid:{}，库{}",table,tableData.size(),projectId,path,e);
                //throw new BusinessException(table+"数据写入失败，稍后请重试！");
            }
            i++;
        }
        return resultList;
    }

    /**
     * @描述 根据类型获取 db 路径
     * @日期 2023/03/05 09:49
     * @作者 eomer
     **/
    public String getPathByType(String dbPath ,SqliteEnum sqliteEnum){

        if (sqliteEnum.equals(SqliteEnum.engineeringCommonMapper)){
            dbPath += "engineering.db";
        }
        else if (sqliteEnum.equals(SqliteEnum.metadataCommonMapper)){
            dbPath += "metadata.db";
        }
        else if (sqliteEnum.equals(SqliteEnum.rulesCommonMapper)){
            dbPath += "designrules.db";
        }
        else if (sqliteEnum.equals(SqliteEnum.drawingTemplateCommonMapper)){
            dbPath += "drawingtemplate.db";
        }else if (sqliteEnum.equals(SqliteEnum.partsCommonMapper)){
            dbPath += "parts.db";
        } else{
            log.info("参数错误");
        }
        return dbPath;
    }

    @Value("${db.current.schema}")
    private String dbCurrentSchema;
    /**
     * @作者 eomer
     * @描述 获取表头
     * @日期 2023/03/05 09:00
     * 参数 tableName
     * @返回值 java.lang.String
     **/
    public List<FiledInfo> getTabelHeads(String tableName, SqliteEnum sqliteEnum){
        String sql = ToSqliteData.getSqlByTableName(tableName,dbCurrentSchema);
        List list = getListByType(sql, sqliteEnum);
        return GsonUtil.ObjectToList(list,FiledInfo.class);
    }
    /**
     * @作者 eomer
     * @描述 获取内容
     * @日期 2023/03/05 09:19
     * 参数 tableName
     * 参数 projectId
     * 参数 sqliteEnum
     * @返回值 java.util.List
     **/
    public List getTableData(SqliteEnum sqliteEnum,  String tableName, String projectId){
        String sql = "select * from "+tableName;
        if (!StringUtils.isBlank(projectId)) {
            sql += " where project_id = '" + projectId + "'";
        }
        return getListByType(sql,sqliteEnum);
    }

    /**
     * @描述 根据类型 & sql 查询数据
     * @日期 2023/03/05 09:19
     * @作者 eomer
     **/
    public List getListByType(String sql, SqliteEnum sqliteEnum){
        List result = new ArrayList();
        if (sqliteEnum.equals(SqliteEnum.engineeringCommonMapper)){
            result = getEngineering(sql);
        }
        else if (sqliteEnum.equals(SqliteEnum.metadataCommonMapper)){
            result = getMetadata(sql);
        }
        else if (sqliteEnum.equals(SqliteEnum.rulesCommonMapper)){
            result = getRules(sql);
        }
        else if (sqliteEnum.equals(SqliteEnum.drawingTemplateCommonMapper)){
            result = getDrawingTemplata(sql);
        }else if (sqliteEnum.equals(SqliteEnum.partsCommonMapper)){
            result = getDrawingTemplata(sql);
        } else{
            log.info("参数错误");
        }
        return result;
    }


    public List getEngineering(String sql){
        return engineeringCommonMapper.getList(sql);
    }
    public List getMetadata(String sql){
        return metadataCommonMapper.getList(sql);
    }
    public List getRules(String sql){
        return rulesCommonMapper.getList(sql);
    }
    public List getDrawingTemplata(String sql){
        return drawingTemplateCommonMapper.getList(sql);
    }

    //部件模板复制
    @Override
    public void copyTablePart(String proPath){
        String dbPath = proPath+"/";
        copyTableToDB(Arrays.asList(toSqliteData_parts_tem_no_project_id.split(",")), SqliteEnum.partsCommonMapper, "",dbPath);
    }

    //部件项目复制
    @Override
    public void copyTablePart(String proPath,String projectId){
        String dbPath = proPath+"/";
        copyTableToDB(Arrays.asList(toSqliteData_parts_project_id.split(",")), SqliteEnum.partsCommonMapper, projectId,dbPath);
    }

    //模板复制
    @Override
    public void copyTabledraw(String proPath,String projectId,String templateId){
        String dbPath = proPath+ File.separator;
        if (JavaUtils.isNotEmtryOrNull(projectId)){
            copyTableToDrawDB(Arrays.asList(toSqliteData_drawingTemplate.split(",")), SqliteEnum.drawingTemplateCommonMapper, projectId,templateId,dbPath);
        }else{
            copyTableToDrawDB(Arrays.asList(toSqliteData_drawingTemplate_no_project_id.split(",")), SqliteEnum.drawingTemplateCommonMapper, "",templateId,dbPath);
        }
    }

    public void copyTableToDrawDB(List<String> list, SqliteEnum sqliteEnum, String projectId,String templateId,String dbPath){
        for (int i = 0; i < list.size();) {
            String table = list.get(i);
            List<FiledInfo> fileds = getTabelHeads(table, sqliteEnum);
            List<Map<String,Object>> tableData = getTableDrawData(sqliteEnum, table, projectId,templateId);
            String path = getPathByType(dbPath,sqliteEnum);
            log.info("db 路径："+path);
            try {
                SqliteUtils.executeUpdate(path,"delete from "+table);
                log.info("清空表："+table);
                SqliteUtils.batchInsert(path, table, fileds, tableData);
                log.info(StrUtil.format("批量添加数据，表：{} -> 数量：{}",table,tableData.size()));
            } catch (SQLException e) {
                //e.printStackTrace();
                log.error(table+"数据写入失败，稍后请重试！",e);
                throw new BusinessException(table+"数据写入失败，稍后请重试！");
            }
            i++;
        }
    }
    public static final List<String> str = Arrays.asList("basic_district","drawing_components","drawing_components_type",
            "pld_md_parts","pld_md_parts_pic","pld_multilingual","map_frame_config","map_frame_result","map_run_config",
            "basic_district_tem","drawing_components_tem","drawing_components_type_tem","pld_md_parts_tem","pld_md_parts_pic_tem","pld_multilingual_tem",
            "drawing_font_style_template_tem","drawing_line_style_template_tem","drawing_column_define_tem","pld_templates_tem","pld_templates");

    public List getTableDrawData(SqliteEnum sqliteEnum,  String tableName, String projectId,String templateId){
        String sql = "select * from "+tableName+" where template_id = '"+templateId+"'";
        if (str.contains(tableName)){
            sql = "select * from "+tableName;
            if (!StringUtils.isBlank(projectId)) {
                sql += " where project_id = '" + projectId + "'";
            }
        }else {
            if (!StringUtils.isBlank(projectId)) {
                sql += " and project_id = '" + projectId + "'";
            }
        }


        return getListByType(sql,sqliteEnum);
    }

    @Override
    public void copyTableByProjectIdAgain(String proPath, String projectId) {
        String dbPath = proPath+ File.separator;
        // 工程
        copyTableByPkid(Arrays.asList(toSqliteData_engineering_only.split(",")).get(0), SqliteEnum.engineeringCommonMapper, projectId,dbPath);
        copyTableToDB(Arrays.asList(toSqliteData_engineering.split(",")), SqliteEnum.engineeringCommonMapper, projectId,dbPath);
        copyTableToDB(Arrays.asList(toSqliteData_engineering_no_project_id.split(",")), SqliteEnum.engineeringCommonMapper, "",dbPath);
    }
}

