package com.supermap.pipedesign.pipechina.sqlite.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.supermap.pipedesign.pipechina.engineering.dao.DesignMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.DesignSchemeMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.DesignSchemeRouteMapper;
import com.supermap.pipedesign.pipechina.engineering.dao.DesignsubTaskMapper;
import com.supermap.pipedesign.pipechina.engineering.entity.Design;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignScheme;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignSchemeRoute;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignsubTask;
import com.supermap.pipedesign.pipechina.sqlite.entity.vo.SyncDesignSchemeRouteVo;
import com.supermap.pipedesign.pipechina.sqlite.service.IDesignSchemeRouteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("DesignSchemeRouteImpl")
public class DesignSchemeRouteImpl
        implements IDesignSchemeRouteService {

    @Autowired
    private DesignMapper designMapper;

    @Autowired
    private DesignSchemeMapper designSchemeMapper;

    @Autowired
    private DesignSchemeRouteMapper designSchemeRouteMapper;

    @Autowired
    private DesignsubTaskMapper designsubTaskMapper;


    @Override
    public void syncRoute(SyncDesignSchemeRouteVo syncDesignSchemeRouteVo) {

        if( syncDesignSchemeRouteVo.getDesignSchemes() != null
                && syncDesignSchemeRouteVo.getDesignSchemes().size() > 0 ){
            List<DesignScheme> designSchemes = syncDesignSchemeRouteVo.getDesignSchemes();
            for( DesignScheme designScheme : designSchemes ){
                //方案表
                designSchemeMapper.insert( designScheme );
            }
        }
        List<DesignSchemeRoute> designSchemeRoutes = syncDesignSchemeRouteVo.getDesignSchemeRoutes();
        if( designSchemeRoutes != null
                && designSchemeRoutes.size() > 0 ){
            for( DesignSchemeRoute designSchemeRoute : designSchemeRoutes ){
                //方案路由信息表
                designSchemeRouteMapper.insert( designSchemeRoute );
            }
        }

        List<DesignsubTask> designsubTasks = syncDesignSchemeRouteVo.getDesignsubTasks();
        if( designsubTasks != null
                && designsubTasks.size() > 0 ){
            for( DesignsubTask designsubTask : designsubTasks ){
                designsubTaskMapper.insert( designsubTask );
            }
        }

        List<Design> designs = syncDesignSchemeRouteVo.getDesigns();
        if( designs != null
                && designs.size() > 0 ){
            for( Design design : designs ){
                designMapper.insert( design );
            }
        }
    }

    @Override
    public SyncDesignSchemeRouteVo syncRouteDb( String projectId,String taskId){

        SyncDesignSchemeRouteVo syncDesignSchemeRouteVo = new SyncDesignSchemeRouteVo();
        QueryWrapper<DesignScheme> designSchemeQueryWrapper =  new QueryWrapper<>();
        designSchemeQueryWrapper.eq("project_id",projectId);
        designSchemeQueryWrapper.eq("task_id",taskId);
        List<DesignSchemeRoute> designSchemeRoutes = new ArrayList<>();
        List<DesignsubTask> designsubTasks =  new ArrayList<>();
        List<Design> designs = new ArrayList<>();
        List<DesignScheme> designSchemes
                = designSchemeMapper.selectList( designSchemeQueryWrapper );
        if( designSchemes != null && designSchemes.size() > 0 ) {
            syncDesignSchemeRouteVo.setDesignSchemes(designSchemes);
            for( DesignScheme designScheme : designSchemes ){
                List<DesignSchemeRoute> designSchemeRouteDb
                        = designSchemeRouteMapper.selBySchemeId(designScheme.getPkid());
                if( designSchemeRouteDb != null && designSchemeRouteDb.size() > 0 ){
                    for( DesignSchemeRoute designSchemeRoute : designSchemeRouteDb ){
                        designSchemeRoutes.add( designSchemeRoute );
                        String taskIdDb = designSchemeRoute.getTaskId();
                        DesignsubTask designsubTask = designsubTaskMapper.selectById( taskIdDb );
                        if( designsubTask != null ){
                            designsubTasks.add( designsubTask );
                            String designId = designsubTask.getDesignid();
                            Design design = designMapper.selectById( designId );
                            if( design != null ) {
                                designs.add( design);
                            }
                        }
                    }
                }
            }
            syncDesignSchemeRouteVo.setDesignSchemeRoutes( designSchemeRoutes );
            syncDesignSchemeRouteVo.setDesignsubTasks( designsubTasks );
            syncDesignSchemeRouteVo.setDesigns( designs );
        }
        return syncDesignSchemeRouteVo;
    }
}
