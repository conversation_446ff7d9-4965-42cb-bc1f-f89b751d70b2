package com.supermap.pipedesign.pipechina.sqlite.controller;

import com.supermap.pipedesign.pipechina.sqlite.entity.vo.SyncDesignSchemeRouteVo;
import com.supermap.pipedesign.pipechina.sqlite.service.IDesignSchemeRouteService;
import com.supermap.pipedesign.pipechina.sqlite.service.IProjectToSqliteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title ProjectToSqliteController
 * @date 2023/03/04 21:01
 * @description
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController("ProjectToSqliteController")
@RequestMapping("/pipechina/services/ProjectToDB")
@Api(tags = "测试")
public class ProjectToSqliteController {

    @Resource(name = "ProjectToSqliteImpl")
    private IProjectToSqliteService iProjectToSqliteService;


    @Resource(name = "DesignSchemeRouteImpl")
    private IDesignSchemeRouteService iDesignSchemeRouteService;


    /**
     * @Date 2023-02-06
     * @auther sushile
     */
    @ApiOperation("复制项目到db")
    @GetMapping("/copyToDb")
    public List<Map> getList(@ApiParam(name = "projectId", value = "项目ID") @RequestParam String projectId) {
        return iProjectToSqliteService.getList(projectId);
    }

    @ApiOperation("同步虚拟设计的数据至数据库")
    @PostMapping("/syncRoute")
    public void syncRoute(@RequestBody SyncDesignSchemeRouteVo syncDesignSchemeRouteVo){
        iDesignSchemeRouteService.syncRoute( syncDesignSchemeRouteVo );
    }

    @ApiOperation("同步虚拟设计的数据至数据库db")
    @GetMapping("/syncRouteDb")
    public SyncDesignSchemeRouteVo syncRouteDb( @ApiParam(name = "projectId", value = "项目ID") @RequestParam String projectId,
                             @ApiParam(name = "taskId", value = "任务Id") @RequestParam String taskId){
    return iDesignSchemeRouteService.syncRouteDb(projectId,taskId);
    }

}

