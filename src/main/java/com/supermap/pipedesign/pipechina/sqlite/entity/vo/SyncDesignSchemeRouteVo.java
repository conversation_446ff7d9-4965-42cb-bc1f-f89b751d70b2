package com.supermap.pipedesign.pipechina.sqlite.entity.vo;

import com.supermap.pipedesign.pipechina.engineering.entity.Design;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignScheme;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignSchemeRoute;
import com.supermap.pipedesign.pipechina.engineering.entity.DesignsubTask;
import lombok.Data;

import java.util.List;

@Data
public class SyncDesignSchemeRouteVo {

    //wbs_design表
    private List<Design> designs;

    //wbs_design_scheme路由比选方案表
    private List<DesignScheme> designSchemes;

    //wbs_design_scheme_route路由比选路由信息表
    private List<DesignSchemeRoute> designSchemeRoutes;

    //wbs_designsub_task
    private List<DesignsubTask> designsubTasks;
}
