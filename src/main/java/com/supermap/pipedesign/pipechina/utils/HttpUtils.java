package com.supermap.pipedesign.pipechina.utils;

import cn.hutool.core.net.URLEncodeUtil;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;

/**
 * HttpUtil
 *
 * <AUTHOR>
 * @date 21/10/11 16:01
 */
public class HttpUtils {
//@TODO端口号 ip 需要写成动态
 private static final String baseUrl = "http://127.0.0.1:9601";
    public static final String SSOServer_Url = "http://**************:18881";
    public static final String baseUrlServers = "http://************:30000/gateway/file-server/";

    private static final String generateCacheUrl = "http://**************:9066/gisDataServer/";

    public static final String demServiceUrl = "http://**************:9603/pipechina/services/demservice/getintervalelevationsofline";



    /**
     * 文件上传obs服务器
     */
    public static String uploadOBS(File file,Boolean requirePreview,String baseUrlServer) {
        // 创建http客户端
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        // 发送post请求传入地址   "http://************:30000/gateway/file-server/";
        HttpPost post = new HttpPost(baseUrlServer + "api/save");
        String result = "";
        try {
            // 上传文件 别的格式参考 httpEntity
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setCharset(Charset.forName("UTF-8"));
                String fileName = URLEncodeUtil.encode( file.getName(), Charset.forName("UTF-8"));
                ContentBody fileBody = new FileBody(file, ContentType.MULTIPART_FORM_DATA,fileName); // 表单文件域
                builder.addPart("file", fileBody);
            //
            builder.addTextBody("requirePreview", String.valueOf(requirePreview));
            HttpEntity entity = builder.build();
            post.setEntity(entity);
            HttpResponse response = httpClient.execute(post);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                // 返回json格式
                // JSONandMap 下面封装的JSON 转 map对象
                String resEntityStr = EntityUtils.toString(response.getEntity());
                result = new String(resEntityStr.getBytes("utf-8"), "utf-8"); // iso-8859-1
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }
    /**
     * 文件上传NAS服务器
     */
    public static String uploadNAS(File file,String path,String baseUrlServer) {
        // 创建http客户端
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        // 发送post请求传入地址   "http://************:30000/gateway/file-server/";
        HttpPost post = new HttpPost(baseUrlServer + "api/save-nas");
//        post.addHeader("token", getToken());
//        post.addHeader("userId", getUserId());
        String result = "";
        try {
            // 上传文件 别的格式参考 httpEntity
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setCharset(Charset.forName("UTF-8"));
                String fileName = URLEncodeUtil.encode( file.getName(), Charset.forName("UTF-8"));
                ContentBody fileBody = new FileBody(file, ContentType.MULTIPART_FORM_DATA,fileName); // 表单文件域
                builder.addPart("file", fileBody);
            //
           builder.addTextBody("path", String.valueOf(path));
            HttpEntity entity = builder.build();
            post.setEntity(entity);
            HttpResponse response = httpClient.execute(post);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                // 返回json格式
                // JSONandMap 下面封装的JSON 转 map对象
                String resEntityStr = EntityUtils.toString(response.getEntity());
                result = new String(resEntityStr.getBytes("utf-8"), "utf-8"); // iso-8859-1
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * post 请求
     *
     * @param url
     * @param body
     * @return java.lang.String @Date 21/10/12 11:01
     * @auther eomer
     */
//    public static String post(String url, String body, String session, String clientType)
//            throws Exception {
//        HttpClient client = HttpClientBuilder.create().build();
//
//        HttpPost post = new HttpPost(url);
//
//        HttpEntity entity = new StringEntity(body, "utf-8");
//        post.setEntity(entity);
//        post.addHeader("token", session);
//        post.addHeader("userId", getUserId());
//        post.addHeader("projectId", ProjectUtils.getInstance().getProjectId());
//        post.setHeader("clientType", clientType);
//        post.setHeader("Content-Type", "application/json"); // Content-Type: application/json
//        HttpResponse response = client.execute(post);
//        if (response.getStatusLine().getStatusCode() == 200) {
//            String resEntityStr = EntityUtils.toString(response.getEntity());
//            return new String(resEntityStr.getBytes("utf-8"), "utf-8"); // iso-8859-1
//        } else if (response.getStatusLine().getStatusCode() == 404) {
//            throw new Exception("url not found");
//        } else {
//            throw new Exception("http response: " + response.getStatusLine().getStatusCode());
//        }
//    }

//    public static String get(String url, String body, String session, String clientType)
//            throws Exception {
//        HttpClient client = HttpClientBuilder.create().build();
//
//        HttpGet httpGet = new HttpGet(url + "?" + body);
//        // HttpEntity entity = new StringEntity(body, "utf-8");
//        httpGet.setHeader("token", session);
//        httpGet.setHeader("userId", getUserId());
//        httpGet.addHeader("projectId", ProjectUtils.getInstance().getProjectId());
//        httpGet.setHeader("clientType", clientType);
//        httpGet.setHeader("Content-Type", "application/json"); // Content-Type: application/json
//        HttpResponse response = client.execute(httpGet);
//        if (response.getStatusLine().getStatusCode() == 200) {
//            String resEntityStr = EntityUtils.toString(response.getEntity());
//            return new String(resEntityStr.getBytes("utf-8"), "utf-8"); // iso-8859-1
//        } else if (response.getStatusLine().getStatusCode() == 404) {
//            throw new Exception("url not found " + url);
//        } else {
//            throw new Exception("http response: " + response.getStatusLine().getStatusCode());
//        }
//    }

    /**
     * get 请求
     *
     * @param url  请求地址
     * @param body 参数 projectid=0&subjectsName=线路
     * @return java.lang.String @Date 2022/12/25 09:22
     * @auther eomer
     */
//    public static String getGw(String url, String body) {
//
//        String result = "";
//        try {
//            result = HttpUtils.get(baseUrl + url, body, "", "");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return result;
//    }

    /**
     * post请求
     *
     * @param url  地址
     * @param body json字符串
     * @return java.lang.String @Date 2022/12/25 09:21
     * @auther eomer
     */
//    public static String postGw(String url, String body) {
//        String result = "";
//        try {
//            result = HttpUtils.post(baseUrl + url, body, "", "");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return result;
//    }




    /**
     * <AUTHOR> @Description 返回get请求json数据结果 @Date 2022/12/29 21:35
     */
//    public static String reqGet(String url, String body) {
//        try {
//
//            String jsonStr = getGw(url, body);
//            Map result = JSONUtil.toBean(jsonStr, Map.class);
////            java.util.Map result = JSONUtil.toBean(jsonStr, java.util.Map.class);
//            return JSONUtil.toJsonStr(result.get("data"));
//        } catch (Exception v1) {
//            return "";
//        }
//    }

    /**
     * <AUTHOR> @Description 返回post请求json数据结果 @Date 2023/1/4 14:16
     */
//    public static String reqPost(String url, String body) {
//        String jsonStr = "";
//        try {
//            jsonStr = post(baseUrl + url, body, "", "");
//            java.util.Map result = JSONUtil.toBean(jsonStr, java.util.Map.class);
//            return JSONUtil.toJsonStr(result.get("data"));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return jsonStr;
//    }

    // 下载文件
//    public static void downLoadFileByUrl(String urlPath, String filePath) throws IOException {
//        try {
//            CloseableHttpClient httpClient = HttpClientBuilder.create().build();
//            HttpGet get = new HttpGet(baseUrl + urlPath);
//            get.addHeader("token", getToken());
//            get.addHeader("userId", getUserId());
//            get.addHeader("projectId", ProjectUtils.getInstance().getProjectId());
//            CloseableHttpResponse response = httpClient.execute(get);
//            if (response.getStatusLine().getStatusCode() == 200) {
//                // 得到实体
//                HttpEntity entity = response.getEntity();
//                InputStream inputStream = entity.getContent();
//                byte[] getData = readInputStream(inputStream);
//                File file = new File(filePath);
//                File pafile=file.getParentFile();
//                if( !pafile.exists()){
//                    pafile.mkdirs();
//                }
//                FileOutputStream fos = new FileOutputStream(file);
//                fos.write(getData);
//                fos.close();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
    // 下载文件
//    public static void downLoadFile(String urlPath, String filePath) throws IOException {
//        try {
//            CloseableHttpClient httpClient = HttpClientBuilder.create().build();
//            HttpGet get = new HttpGet(baseUrlServers + urlPath);
//            CloseableHttpResponse response = httpClient.execute(get);
//            if (response.getStatusLine().getStatusCode() == 200) {
//                // 得到实体
//                HttpEntity entity = response.getEntity();
//                InputStream inputStream = entity.getContent();
//                byte[] getData = readInputStream(inputStream);
//                File file = new File(filePath);
//                FileOutputStream fos = new FileOutputStream(file);
//                fos.write(getData);
//                fos.close();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }

//    public static byte[] readInputStream(InputStream inputStream) throws IOException {
//        byte[] buffer = new byte[1024];
//        int len = 0;
//        ByteArrayOutputStream bos = new ByteArrayOutputStream();
//        while ((len = inputStream.read(buffer)) != -1) {
//            bos.write(buffer, 0, len);
//        }
//        bos.close();
//        return bos.toByteArray();
//    }


//    public static void main(String[] args) {
//        File file = new File("D:\\Users\\Administrator\\Desktop\\数据质检\\PH02T01-PL001-B09地质剖面图表.xlsx");
//        File file2 = new File("D:\\Users\\Administrator\\Desktop\\数据质检\\PH02T01-PL001-B09勘探点地层表.xlsx");
//        try {
//            List<File> files = new ArrayList<>();
//            files.add(file);
//            files.add(file2);
//
//    } catch (Exception e) {
//      e.printStackTrace();
//    }
//  }



    // 文件上传
//    public static String uploadFile(String apiUrl, String filePath, Map<String,String> params) {
//        File file = new File(filePath);
//        if (!file.exists()) {
//            System.out.println("未找到要上传的文件，请核查");
//            return "";
//        }
//        return uploadFile(apiUrl, file, params);
//    }

    // 文件上传
//    public static String uploadFile(String apiUrl, File file, Map<String,String> params) {
//        // 创建http客户端
//        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
//        // 发送post请求传入地址   "http://************:30000/gateway/file-server/";
//        HttpPost post = new HttpPost(baseUrl + apiUrl);
////        post.addHeader("token", getToken());
////        post.addHeader("userId", getUserId());
////        post.addHeader("projectId", ProjectUtils.getInstance().getProjectId());
//        String result = "";
//        try {
//            // 上传文件 别的格式参考 httpEntity
//            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
//            builder.setCharset(Charset.forName("UTF-8"));
//            String fileName = URLEncodeUtil.encode( file.getName(), Charset.forName("UTF-8"));
//            ContentBody fileBody = new FileBody(file, ContentType.MULTIPART_FORM_DATA,fileName); // 表单文件域
//            builder.addPart("file", fileBody);
//
//            for (Map.Entry<String, String> entry : params.entrySet()) {
//                builder.addTextBody(entry.getKey(), entry.getValue());
//            }
//
//            HttpEntity entity = builder.build();
//            post.setEntity(entity);
//            HttpResponse response = httpClient.execute(post);
//            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
//                // 返回json格式// JSONandMap 下面封装的JSON 转 map对象
//                String resEntityStr = EntityUtils.toString(response.getEntity());
//                result = new String(resEntityStr.getBytes("utf-8"), "utf-8"); // iso-8859-1
//            }
//        } catch (ClientProtocolException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return result;
//    }

}
