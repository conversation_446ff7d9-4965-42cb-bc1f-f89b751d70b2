package com.supermap.pipedesign.pipechina.utils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import cn.hutool.json.JSONArray;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

/**
 *	服务发布工具类
 * <AUTHOR>
 * @since  2023-3-31
 */
public class PublishService {

	/**
	 * 根据传入的iServer连接信息及服务类型发布对应的服务
	 * @param ipandport			iServer的IP地址和端口号(格式为“IP:Port”)
	 * @param username			iServer管理用户的登录名
	 * @param password			iServer管理用户的密码
	 * @param serverType		要发布的服务类型(RESTMAP:地图服务;RESTDATA:数据服务;RESTREALSPACE:三维场景服务)
	 * @param workspacePath		要发布的工作空间文件路径
	 * @return					服务发布结果(发布成功的各个服务的类型及服务地址JSON数组字符串)
	 */
	public static String publishMapAndDataService(String ipandport,String username,String password,String serverType,String workspacePath){

		String os = System.getProperty("os.name");
		if (os.toLowerCase(Locale.ROOT).contains("windows")) {
			if(workspacePath.indexOf('\\')>0)
				workspacePath = workspacePath.replace(File.separator+"", "//");
		}
		String cookie = login(ipandport,username,password);
		
		if(cookie == "") {
			return null;
		}
		try {

		//	String json1 = "{\"servicesTypes\": [\"RESTMAP\"],\"workspaceConnectionInfo\": \""+workspacePath+"\"}";
			String json = "{\"servicesTypes\": [\""+serverType+"\"],\"workspaceConnectionInfo\": \""+workspacePath+"\"}";

			URL url = new URL("http://"+ipandport+"/iserver/manager/workspaces.json");
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			connection.setDoInput(true);
			connection.setDoOutput(true);
			connection.setRequestMethod("POST");
			connection.setUseCaches(false);
			connection.setInstanceFollowRedirects(true);
			connection.setRequestProperty("Content-Type","application/json;charset=utf-8");
			connection.setRequestProperty("cookie", cookie);
			connection.connect();
			DataOutputStream out = new DataOutputStream(connection.getOutputStream());
			out.write(json.getBytes(StandardCharsets.UTF_8));
			out.flush();
			out.close();
			
			BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
			String lines;
			StringBuffer sbf = new StringBuffer();
			while ((lines = reader.readLine()) != null) {
				lines = new String(lines.getBytes(), "utf-8");
				sbf.append(lines);
			}
			
			reader.close();
			connection.disconnect();
			
			Gson gson = new Gson();
			List<ServerPublishResult> lstResult = gson.fromJson(sbf.toString(), new TypeToken<ArrayList<ServerPublishResult>>(){}.getType());
			String strResult = "[";
			for(int i=0;lstResult!=null&&i<lstResult.size();i++) {
				ServerPublishResult serverPublishResult = lstResult.get(i);
				strResult += "{\"servicetype\":\"" + serverPublishResult.getServiceType() + "\","
						+ "\"serviceaddress\":\"" + serverPublishResult.getServiceAddress() + "\"},";
			}
			if(lstResult.size()>0)
				strResult = strResult.substring(0, strResult.length()-1);
			strResult += "]";
			return strResult;

		} catch (MalformedURLException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		return null;
	}
	
	
	
	private static String login(String ipport,String username,String password){
		try {
			
			String json = "{\"username\": \""+username+"\",\"password\": \""+password+"\",\"rememberme\": \"true\"}";

			URL url = new URL("http://"+ipport+"/iserver/services/security/login.json");
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			connection.setDoInput(true);
			connection.setDoOutput(true);
			connection.setRequestMethod("POST");
			connection.setUseCaches(false);
			connection.setInstanceFollowRedirects(true);
			connection.setRequestProperty("Content-Type","application/json");
			connection.connect();
			DataOutputStream out = new DataOutputStream(connection.getOutputStream());

			out.writeBytes(json);	
			out.flush();
			out.close();
			
			BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
			String lines;
			StringBuffer sbf = new StringBuffer();
			while ((lines = reader.readLine()) != null) {
				lines = new String(lines.getBytes(), "utf-8");
				sbf.append(lines);
			}
			System.out.println(sbf);
			
			String cookie = connection.getHeaderField("set-cookie");  
			System.out.println(cookie);  
			
			reader.close();
			connection.disconnect();
			
			return cookie;

		} catch (MalformedURLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return "";
	}

	/**
	 * 存放服务发布结果的内部类
	 */
	class ServerPublishResult {
		//服务类型
		private String serviceType;

		//服务地址
		private String serviceAddress;

		public String getServiceType() {
			return serviceType;
		}
		public void setServiceType(String serviceType) {
			this.serviceType = serviceType;
		}
		public String getServiceAddress() {
			return serviceAddress;
		}
		public void setServiceAddress(String serviceAddress) {
			this.serviceAddress = serviceAddress;
		}
	}

	public static void main(String[] args) {

		String strWorkspacePath = "D://linuxexchange//Datas//workspaces//EntityDesign.smwu";
		//strWorkspacePath = "E://projects//北京市//市级项目//数字化协同设计平台（一期）线路设计子系统开发招标项目//01系统源码//CS//trunk//api//target//classes//sqlite//db-design//versions//ba6f0da7867b8e28c18351dcceee865dA.smwu";
		strWorkspacePath = "D://Temp//ba6f0da7867b8e28c18351dcceee865dA.smwu";
		String strJSONResult = PublishService.publishMapAndDataService("localhost:8091","admin","SuperMap@2023","RESTDATA",strWorkspacePath);
		JSONArray jsonArrayResult = new JSONArray(strJSONResult);
		for(int i = 0 ; i < jsonArrayResult.size() ; i++)
		{
			System.out.println(jsonArrayResult.getJSONObject(i).getStr("serviceType")+":"+jsonArrayResult.getJSONObject(i).getStr("serviceAddress"));
		}
	}

}
