package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.PlainfaceOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-素喷护面 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IPlainfaceOkService extends IService<PlainfaceOk> {

 /**
 * 添加水工保护表-素喷护面信息
 *
 * @param plainfaceOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(PlainfaceOk plainfaceOk);

 /**
 * 删除水工保护表-素喷护面信息
 *
 * @param plainfaceOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String plainfaceOkId);

 /**
 * 更新水工保护表-素喷护面信息
 *
 * @param plainfaceOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(PlainfaceOk plainfaceOk);

 /**
 * 全部查询
 *
 * @param plainfaceOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.PlainfaceOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<PlainfaceOk> list(PlainfaceOk plainfaceOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
