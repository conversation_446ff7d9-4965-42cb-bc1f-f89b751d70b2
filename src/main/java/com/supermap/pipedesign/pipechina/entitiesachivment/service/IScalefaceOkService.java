package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.ScalefaceOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-鱼鳞坑护面 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IScalefaceOkService extends IService<ScalefaceOk> {

 /**
 * 添加水工保护表-鱼鳞坑护面信息
 *
 * @param scalefaceOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(ScalefaceOk scalefaceOk);

 /**
 * 删除水工保护表-鱼鳞坑护面信息
 *
 * @param scalefaceOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String scalefaceOkId);

 /**
 * 更新水工保护表-鱼鳞坑护面信息
 *
 * @param scalefaceOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(ScalefaceOk scalefaceOk);

 /**
 * 全部查询
 *
 * @param scalefaceOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.ScalefaceOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<ScalefaceOk> list(ScalefaceOk scalefaceOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
