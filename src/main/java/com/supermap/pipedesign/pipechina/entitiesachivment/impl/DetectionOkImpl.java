package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.DetectionOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.DetectionOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IDetectionOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 检测标识表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("DetectionOkImpl")
public class DetectionOkImpl extends ServiceImpl<DetectionOkMapper, DetectionOk> implements IDetectionOkService {

    @Autowired
    private DetectionOkMapper detectionOkMapper;

    /**
    * 添加检测标识表信息
    *
    * @param detectionOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(DetectionOk detectionOk) {

        //detectionOk.setUserId(JavaUtils.getUUID36());
        //detectionOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return detectionOkMapper.insert(detectionOk);
    }

    /**
    * 删除检测标识表信息
    *
    * @param detectionOkId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String detectionOkId) {
        return detectionOkMapper.deleteById(detectionOkId);
    }

    /**
    * 更新检测标识表信息
    *
    * @param detectionOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(DetectionOk detectionOk) {
        return detectionOkMapper.updateById(detectionOk);
    }

    /**
    * 全部查询
    *
    * @param detectionOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.DetectionOk>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<DetectionOk> list(DetectionOk detectionOk) {

        QueryWrapper<DetectionOk> queryWrapper = new QueryWrapper<>();

        return detectionOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<DetectionOk> detectionOkIPage = new Page<>();
        detectionOkIPage.setCurrent(current);
        detectionOkIPage.setSize(size);

        QueryWrapper<DetectionOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return detectionOkMapper.selectPage(detectionOkIPage, queryWrapper);
    }


}
