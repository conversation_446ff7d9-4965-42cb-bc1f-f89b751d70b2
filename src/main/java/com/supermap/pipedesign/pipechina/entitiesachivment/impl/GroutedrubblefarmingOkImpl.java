package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.GroutedrubblefarmingOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblefarmingOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IGroutedrubblefarmingOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-浆砌石农耕道路 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("GroutedrubblefarmingOkImpl")
public class GroutedrubblefarmingOkImpl extends ServiceImpl<GroutedrubblefarmingOkMapper, GroutedrubblefarmingOk> implements IGroutedrubblefarmingOkService {

    @Autowired
    private GroutedrubblefarmingOkMapper groutedrubblefarmingOkMapper;

    /**
    * 添加水工保护表-浆砌石农耕道路信息
    *
    * @param groutedrubblefarmingOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(GroutedrubblefarmingOk groutedrubblefarmingOk) {

        //groutedrubblefarmingOk.setUserId(JavaUtils.getUUID36());
        //groutedrubblefarmingOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return groutedrubblefarmingOkMapper.insert(groutedrubblefarmingOk);
    }

    /**
    * 删除水工保护表-浆砌石农耕道路信息
    *
    * @param groutedrubblefarmingOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String groutedrubblefarmingOkId) {
        return groutedrubblefarmingOkMapper.deleteById(groutedrubblefarmingOkId);
    }

    /**
    * 更新水工保护表-浆砌石农耕道路信息
    *
    * @param groutedrubblefarmingOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(GroutedrubblefarmingOk groutedrubblefarmingOk) {
        return groutedrubblefarmingOkMapper.updateById(groutedrubblefarmingOk);
    }

    /**
    * 全部查询
    *
    * @param groutedrubblefarmingOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblefarmingOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<GroutedrubblefarmingOk> list(GroutedrubblefarmingOk groutedrubblefarmingOk) {

        QueryWrapper<GroutedrubblefarmingOk> queryWrapper = new QueryWrapper<>();

        return groutedrubblefarmingOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<GroutedrubblefarmingOk> groutedrubblefarmingOkIPage = new Page<>();
        groutedrubblefarmingOkIPage.setCurrent(current);
        groutedrubblefarmingOkIPage.setSize(size);

        QueryWrapper<GroutedrubblefarmingOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return groutedrubblefarmingOkMapper.selectPage(groutedrubblefarmingOkIPage, queryWrapper);
    }


}
