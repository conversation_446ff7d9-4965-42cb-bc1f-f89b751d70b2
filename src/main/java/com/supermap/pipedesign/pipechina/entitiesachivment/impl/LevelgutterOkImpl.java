package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.LevelgutterOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.LevelgutterOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.ILevelgutterOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-水平沟护面 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("LevelgutterOkImpl")
public class LevelgutterOkImpl extends ServiceImpl<LevelgutterOkMapper, LevelgutterOk> implements ILevelgutterOkService {

    @Autowired
    private LevelgutterOkMapper levelgutterOkMapper;

    /**
    * 添加水工保护表-水平沟护面信息
    *
    * @param levelgutterOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(LevelgutterOk levelgutterOk) {

        //levelgutterOk.setUserId(JavaUtils.getUUID36());
        //levelgutterOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return levelgutterOkMapper.insert(levelgutterOk);
    }

    /**
    * 删除水工保护表-水平沟护面信息
    *
    * @param levelgutterOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String levelgutterOkId) {
        return levelgutterOkMapper.deleteById(levelgutterOkId);
    }

    /**
    * 更新水工保护表-水平沟护面信息
    *
    * @param levelgutterOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(LevelgutterOk levelgutterOk) {
        return levelgutterOkMapper.updateById(levelgutterOk);
    }

    /**
    * 全部查询
    *
    * @param levelgutterOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.LevelgutterOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<LevelgutterOk> list(LevelgutterOk levelgutterOk) {

        QueryWrapper<LevelgutterOk> queryWrapper = new QueryWrapper<>();

        return levelgutterOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<LevelgutterOk> levelgutterOkIPage = new Page<>();
        levelgutterOkIPage.setCurrent(current);
        levelgutterOkIPage.setSize(size);

        QueryWrapper<LevelgutterOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return levelgutterOkMapper.selectPage(levelgutterOkIPage, queryWrapper);
    }


}
