package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblefarmingOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-浆砌石农耕道路 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IGroutedrubblefarmingOkService extends IService<GroutedrubblefarmingOk> {

 /**
 * 添加水工保护表-浆砌石农耕道路信息
 *
 * @param groutedrubblefarmingOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(GroutedrubblefarmingOk groutedrubblefarmingOk);

 /**
 * 删除水工保护表-浆砌石农耕道路信息
 *
 * @param groutedrubblefarmingOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String groutedrubblefarmingOkId);

 /**
 * 更新水工保护表-浆砌石农耕道路信息
 *
 * @param groutedrubblefarmingOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(GroutedrubblefarmingOk groutedrubblefarmingOk);

 /**
 * 全部查询
 *
 * @param groutedrubblefarmingOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblefarmingOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<GroutedrubblefarmingOk> list(GroutedrubblefarmingOk groutedrubblefarmingOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
