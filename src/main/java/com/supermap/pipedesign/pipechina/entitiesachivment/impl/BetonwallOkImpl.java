package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.BetonwallOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.BetonwallOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IBetonwallOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-混凝土侧挡墙 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("BetonwallOkImpl")
public class BetonwallOkImpl extends ServiceImpl<BetonwallOkMapper, BetonwallOk> implements IBetonwallOkService {

    @Autowired
    private BetonwallOkMapper betonwallOkMapper;

    /**
    * 添加水工保护表-混凝土侧挡墙信息
    *
    * @param betonwallOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(BetonwallOk betonwallOk) {

        //betonwallOk.setUserId(JavaUtils.getUUID36());
        //betonwallOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return betonwallOkMapper.insert(betonwallOk);
    }

    /**
    * 删除水工保护表-混凝土侧挡墙信息
    *
    * @param betonwallOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String betonwallOkId) {
        return betonwallOkMapper.deleteById(betonwallOkId);
    }

    /**
    * 更新水工保护表-混凝土侧挡墙信息
    *
    * @param betonwallOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(BetonwallOk betonwallOk) {
        return betonwallOkMapper.updateById(betonwallOk);
    }

    /**
    * 全部查询
    *
    * @param betonwallOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.BetonwallOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<BetonwallOk> list(BetonwallOk betonwallOk) {

        QueryWrapper<BetonwallOk> queryWrapper = new QueryWrapper<>();

        return betonwallOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<BetonwallOk> betonwallOkIPage = new Page<>();
        betonwallOkIPage.setCurrent(current);
        betonwallOkIPage.setSize(size);

        QueryWrapper<BetonwallOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return betonwallOkMapper.selectPage(betonwallOkIPage, queryWrapper);
    }


}
