package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.HandholeOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 手孔表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface IHandholeOkService extends IService<HandholeOk> {

 /**
 * 添加手孔表信息
 *
 * @param handholeOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(HandholeOk handholeOk);

 /**
 * 删除手孔表信息
 *
 * @param handholeOkId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String handholeOkId);

 /**
 * 更新手孔表信息
 *
 * @param handholeOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(HandholeOk handholeOk);

 /**
 * 全部查询
 *
 * @param handholeOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.HandholeOk>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<HandholeOk> list(HandholeOk handholeOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
