package com.supermap.pipedesign.pipechina.entitiesachivment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.sql.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *   地质报告表实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("geology_report_survey")
@ApiModel(value="GeologyreportOk对象", description="地质报告表")
public class GeologyreportOk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "SmID")
    @TableField(value = "smid")
    private Integer smid;

    @ApiModelProperty(value = "设计人员ID")
    @TableField("designer_id")
    private String designerId;

    @ApiModelProperty(value = "设计时间")
    @TableField("modify_time")
    private Date modifyTime;

    @ApiModelProperty(value = "描述信息")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "上一版本编号")
    @TableField("sequence_org_id")
    private String sequenceOrgId;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "子工程ID")
    @TableField("sub_project_id")
    private String subProjectId;

    @ApiModelProperty(value = "起始桩号")
    @TableField("start_station")
    private String startStation;

    @ApiModelProperty(value = "相对起始桩里程")
    @TableField("rel_startp_mileage")
    private Double relStartpMileage;

    @ApiModelProperty(value = "结束桩号")
    @TableField("end_station")
    private String endStation;

    @ApiModelProperty(value = "相对结束桩里程")
    @TableField("rel_endp_mileage")
    private Double relEndpMileage;

    @ApiModelProperty(value = "起始里程")
    @TableField("initial_station")
    private Double initialStation;

    @ApiModelProperty(value = "结束里程")
    @TableField("end_milege")
    private Double endMilege;

    @ApiModelProperty(value = "起始东坐标")
    @TableField("initial_easting")
    private Double initialEasting;

    @ApiModelProperty(value = "起始北坐标")
    @TableField("initial_northing")
    private Double initialNorthing;

    @ApiModelProperty(value = "结束东坐标")
    @TableField("end_easting")
    private Double endEasting;

    @ApiModelProperty(value = "结束北坐标")
    @TableField("end_northing")
    private Double endNorthing;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;


}
