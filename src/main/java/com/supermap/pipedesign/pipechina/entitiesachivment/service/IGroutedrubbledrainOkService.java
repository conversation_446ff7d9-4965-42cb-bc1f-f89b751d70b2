package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubbledrainOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-浆砌石截排水沟(渠) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IGroutedrubbledrainOkService extends IService<GroutedrubbledrainOk> {

 /**
 * 添加水工保护表-浆砌石截排水沟(渠)信息
 *
 * @param groutedrubbledrainOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(GroutedrubbledrainOk groutedrubbledrainOk);

 /**
 * 删除水工保护表-浆砌石截排水沟(渠)信息
 *
 * @param groutedrubbledrainOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String groutedrubbledrainOkId);

 /**
 * 更新水工保护表-浆砌石截排水沟(渠)信息
 *
 * @param groutedrubbledrainOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(GroutedrubbledrainOk groutedrubbledrainOk);

 /**
 * 全部查询
 *
 * @param groutedrubbledrainOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubbledrainOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<GroutedrubbledrainOk> list(GroutedrubbledrainOk groutedrubbledrainOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
