package com.supermap.pipedesign.pipechina.entitiesachivment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 离散点表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("discrete_point_survey")
@ApiModel(value="DiscretepointOk对象", description="离散点表")
public class DiscretepointOk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "SmID")
    @TableField(value = "smid")
    private Long smid;

      @ApiModelProperty(value = "纵坐标X")
    @TableField("x")
    private Double x;

      @ApiModelProperty(value = "横坐标Y")
    @TableField("y")
    private Double y;

    @ApiModelProperty(value = "Z")
    @TableField("z")
    private Double z;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "编号")
    @TableField("point_no")
    private String pointNo;

    @ApiModelProperty(value = "高程")
    @TableField("altitude")
    private Double altitude;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;


}
