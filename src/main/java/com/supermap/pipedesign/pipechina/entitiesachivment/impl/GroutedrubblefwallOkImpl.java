package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.GroutedrubblefwallOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblefwallOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IGroutedrubblefwallOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-浆砌石防冲墙 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("GroutedrubblefwallOkImpl")
public class GroutedrubblefwallOkImpl extends ServiceImpl<GroutedrubblefwallOkMapper, GroutedrubblefwallOk> implements IGroutedrubblefwallOkService {

    @Autowired
    private GroutedrubblefwallOkMapper groutedrubblefwallOkMapper;

    /**
    * 添加水工保护表-浆砌石防冲墙信息
    *
    * @param groutedrubblefwallOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(GroutedrubblefwallOk groutedrubblefwallOk) {

        //groutedrubblefwallOk.setUserId(JavaUtils.getUUID36());
        //groutedrubblefwallOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return groutedrubblefwallOkMapper.insert(groutedrubblefwallOk);
    }

    /**
    * 删除水工保护表-浆砌石防冲墙信息
    *
    * @param groutedrubblefwallOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String groutedrubblefwallOkId) {
        return groutedrubblefwallOkMapper.deleteById(groutedrubblefwallOkId);
    }

    /**
    * 更新水工保护表-浆砌石防冲墙信息
    *
    * @param groutedrubblefwallOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(GroutedrubblefwallOk groutedrubblefwallOk) {
        return groutedrubblefwallOkMapper.updateById(groutedrubblefwallOk);
    }

    /**
    * 全部查询
    *
    * @param groutedrubblefwallOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblefwallOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<GroutedrubblefwallOk> list(GroutedrubblefwallOk groutedrubblefwallOk) {

        QueryWrapper<GroutedrubblefwallOk> queryWrapper = new QueryWrapper<>();

        return groutedrubblefwallOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<GroutedrubblefwallOk> groutedrubblefwallOkIPage = new Page<>();
        groutedrubblefwallOkIPage.setCurrent(current);
        groutedrubblefwallOkIPage.setSize(size);

        QueryWrapper<GroutedrubblefwallOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return groutedrubblefwallOkMapper.selectPage(groutedrubblefwallOkIPage, queryWrapper);
    }


}
