package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblerhombusOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-浆砌石菱形骨架护坡 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IGroutedrubblerhombusOkService extends IService<GroutedrubblerhombusOk> {

 /**
 * 添加水工保护表-浆砌石菱形骨架护坡信息
 *
 * @param groutedrubblerhombusOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(GroutedrubblerhombusOk groutedrubblerhombusOk);

 /**
 * 删除水工保护表-浆砌石菱形骨架护坡信息
 *
 * @param groutedrubblerhombusOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String groutedrubblerhombusOkId);

 /**
 * 更新水工保护表-浆砌石菱形骨架护坡信息
 *
 * @param groutedrubblerhombusOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(GroutedrubblerhombusOk groutedrubblerhombusOk);

 /**
 * 全部查询
 *
 * @param groutedrubblerhombusOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblerhombusOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<GroutedrubblerhombusOk> list(GroutedrubblerhombusOk groutedrubblerhombusOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
