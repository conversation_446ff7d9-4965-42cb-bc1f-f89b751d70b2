package com.supermap.pipedesign.pipechina.entitiesachivment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.sql.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 管材防腐地区等级表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("grade_area_survey")
@ApiModel(value="GradeareaOk对象", description="管材防腐地区等级表")
public class GradeareaOk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "SmID")
    @TableField(value = "smid")
    private Integer smid;

    @ApiModelProperty(value = "设计人员ID")
    @TableField("designer_id")
    private String designerId;

    @ApiModelProperty(value = "设计时间")
    @TableField("modify_time")
    private Date modifyTime;

    @ApiModelProperty(value = "描述信息")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "上一版本编号")
    @TableField("sequence_org_id")
    private String sequenceOrgId;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "子工程ID")
    @TableField("sub_project_id")
    private String subProjectId;

    @ApiModelProperty(value = "起始桩号")
    @TableField("start_station")
    private String startStation;

    @ApiModelProperty(value = "相对起始桩里程")
    @TableField("rel_startp_mileage")
    private Double relStartpMileage;

    @ApiModelProperty(value = "结束桩号")
    @TableField("end_station")
    private String endStation;

    @ApiModelProperty(value = "相对起始桩里程")
    @TableField("rel_endp_mileage")
    private Double relEndpMileage;

    @ApiModelProperty(value = "起始里程")
    @TableField("initial_station")
    private Double initialStation;

    @ApiModelProperty(value = "结束里程")
    @TableField("end_mileege")
    private Double endMileege;

    @ApiModelProperty(value = "起始东坐标")
    @TableField("initial_easting")
    private Double initialEasting;

    @ApiModelProperty(value = "起始北坐标")
    @TableField("initial_northing")
    private Double initialNorthing;

    @ApiModelProperty(value = "结束东坐标")
    @TableField("end_easting")
    private Double endEasting;

    @ApiModelProperty(value = "结束北坐标")
    @TableField("end_northing")
    private Double endNorthing;

    @ApiModelProperty(value = "地区等级")
    @TableField("district_level")
    private String districtLevel;

    @ApiModelProperty(value = "设计系数")
    @TableField("design_factor")
    private String designFactor;

    @ApiModelProperty(value = "管径")
    @TableField("pipe_diameter")
    private String pipeDiameter;

    @ApiModelProperty(value = "压力")
    @TableField("stress")
    private String stress;

    @ApiModelProperty(value = "壁厚")
    @TableField("wallt")
    private String wallt;

    @ApiModelProperty(value = "钢级")
    @TableField("steel_grade")
    private String steelGrade;

    @ApiModelProperty(value = "制管形式")
    @TableField("pipe_make_form")
    private String pipeMakeForm;

    @ApiModelProperty(value = "防腐类型")
    @TableField("corrpro_type")
    private String corrproType;

    @ApiModelProperty(value = "防腐等级")
    @TableField("corrosion_grade")
    private String corrosionGrade;

    @ApiModelProperty(value = "补扣形式")
    @TableField("patch_form")
    private String patchForm;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;


}
