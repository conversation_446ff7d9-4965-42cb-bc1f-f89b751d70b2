package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.BetonbobwOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-混凝土配重块稳管 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IBetonbobwOkService extends IService<BetonbobwOk> {

 /**
 * 添加水工保护表-混凝土配重块稳管信息
 *
 * @param betonbobwOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(BetonbobwOk betonbobwOk);

 /**
 * 删除水工保护表-混凝土配重块稳管信息
 *
 * @param betonbobwOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String betonbobwOkId);

 /**
 * 更新水工保护表-混凝土配重块稳管信息
 *
 * @param betonbobwOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(BetonbobwOk betonbobwOk);

 /**
 * 全部查询
 *
 * @param betonbobwOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.BetonbobwOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<BetonbobwOk> list(BetonbobwOk betonbobwOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
