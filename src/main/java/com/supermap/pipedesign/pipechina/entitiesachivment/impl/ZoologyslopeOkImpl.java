package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.ZoologyslopeOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.ZoologyslopeOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IZoologyslopeOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-生态袋护坡 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("ZoologyslopeOkImpl")
public class ZoologyslopeOkImpl extends ServiceImpl<ZoologyslopeOkMapper, ZoologyslopeOk> implements IZoologyslopeOkService {

    @Autowired
    private ZoologyslopeOkMapper zoologyslopeOkMapper;

    /**
    * 添加水工保护表-生态袋护坡信息
    *
    * @param zoologyslopeOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(ZoologyslopeOk zoologyslopeOk) {

        //zoologyslopeOk.setUserId(JavaUtils.getUUID36());
        //zoologyslopeOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return zoologyslopeOkMapper.insert(zoologyslopeOk);
    }

    /**
    * 删除水工保护表-生态袋护坡信息
    *
    * @param zoologyslopeOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String zoologyslopeOkId) {
        return zoologyslopeOkMapper.deleteById(zoologyslopeOkId);
    }

    /**
    * 更新水工保护表-生态袋护坡信息
    *
    * @param zoologyslopeOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(ZoologyslopeOk zoologyslopeOk) {
        return zoologyslopeOkMapper.updateById(zoologyslopeOk);
    }

    /**
    * 全部查询
    *
    * @param zoologyslopeOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.ZoologyslopeOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<ZoologyslopeOk> list(ZoologyslopeOk zoologyslopeOk) {

        QueryWrapper<ZoologyslopeOk> queryWrapper = new QueryWrapper<>();

        return zoologyslopeOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<ZoologyslopeOk> zoologyslopeOkIPage = new Page<>();
        zoologyslopeOkIPage.setCurrent(current);
        zoologyslopeOkIPage.setSize(size);

        QueryWrapper<ZoologyslopeOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return zoologyslopeOkMapper.selectPage(zoologyslopeOkIPage, queryWrapper);
    }


}
