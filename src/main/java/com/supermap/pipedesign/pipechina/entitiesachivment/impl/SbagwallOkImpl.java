package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.SbagwallOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.SbagwallOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.ISbagwallOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-草袋素土裆土墙 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("SbagwallOkImpl")
public class SbagwallOkImpl extends ServiceImpl<SbagwallOkMapper, SbagwallOk> implements ISbagwallOkService {

    @Autowired
    private SbagwallOkMapper sbagwallOkMapper;

    /**
    * 添加水工保护表-草袋素土裆土墙信息
    *
    * @param sbagwallOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(SbagwallOk sbagwallOk) {

        //sbagwallOk.setUserId(JavaUtils.getUUID36());
        //sbagwallOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return sbagwallOkMapper.insert(sbagwallOk);
    }

    /**
    * 删除水工保护表-草袋素土裆土墙信息
    *
    * @param sbagwallOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String sbagwallOkId) {
        return sbagwallOkMapper.deleteById(sbagwallOkId);
    }

    /**
    * 更新水工保护表-草袋素土裆土墙信息
    *
    * @param sbagwallOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(SbagwallOk sbagwallOk) {
        return sbagwallOkMapper.updateById(sbagwallOk);
    }

    /**
    * 全部查询
    *
    * @param sbagwallOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.SbagwallOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<SbagwallOk> list(SbagwallOk sbagwallOk) {

        QueryWrapper<SbagwallOk> queryWrapper = new QueryWrapper<>();

        return sbagwallOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<SbagwallOk> sbagwallOkIPage = new Page<>();
        sbagwallOkIPage.setCurrent(current);
        sbagwallOkIPage.setSize(size);

        QueryWrapper<SbagwallOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return sbagwallOkMapper.selectPage(sbagwallOkIPage, queryWrapper);
    }


}
