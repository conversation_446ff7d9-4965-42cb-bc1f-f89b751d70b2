package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.HydraulicproOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface IHydraulicproOkService extends IService<HydraulicproOk> {

 /**
 * 添加水工保护表信息
 *
 * @param hydraulicproOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(HydraulicproOk hydraulicproOk);

 /**
 * 删除水工保护表信息
 *
 * @param hydraulicproOkId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String hydraulicproOkId);

 /**
 * 更新水工保护表信息
 *
 * @param hydraulicproOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(HydraulicproOk hydraulicproOk);

 /**
 * 全部查询
 *
 * @param hydraulicproOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.HydraulicproOk>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<HydraulicproOk> list(HydraulicproOk hydraulicproOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
