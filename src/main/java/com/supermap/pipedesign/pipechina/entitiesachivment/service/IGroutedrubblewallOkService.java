package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblewallOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-浆砌石挡土墙 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IGroutedrubblewallOkService extends IService<GroutedrubblewallOk> {

 /**
 * 添加水工保护表-浆砌石挡土墙信息
 *
 * @param groutedrubblewallOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(GroutedrubblewallOk groutedrubblewallOk);

 /**
 * 删除水工保护表-浆砌石挡土墙信息
 *
 * @param groutedrubblewallOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String groutedrubblewallOkId);

 /**
 * 更新水工保护表-浆砌石挡土墙信息
 *
 * @param groutedrubblewallOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(GroutedrubblewallOk groutedrubblewallOk);

 /**
 * 全部查询
 *
 * @param groutedrubblewallOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblewallOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<GroutedrubblewallOk> list(GroutedrubblewallOk groutedrubblewallOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
