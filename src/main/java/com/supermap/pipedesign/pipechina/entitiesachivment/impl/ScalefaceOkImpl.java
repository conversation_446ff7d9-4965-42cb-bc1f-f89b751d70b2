package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.ScalefaceOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.ScalefaceOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IScalefaceOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-鱼鳞坑护面 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("ScalefaceOkImpl")
public class ScalefaceOkImpl extends ServiceImpl<ScalefaceOkMapper, ScalefaceOk> implements IScalefaceOkService {

    @Autowired
    private ScalefaceOkMapper scalefaceOkMapper;

    /**
    * 添加水工保护表-鱼鳞坑护面信息
    *
    * @param scalefaceOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(ScalefaceOk scalefaceOk) {

        //scalefaceOk.setUserId(JavaUtils.getUUID36());
        //scalefaceOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return scalefaceOkMapper.insert(scalefaceOk);
    }

    /**
    * 删除水工保护表-鱼鳞坑护面信息
    *
    * @param scalefaceOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String scalefaceOkId) {
        return scalefaceOkMapper.deleteById(scalefaceOkId);
    }

    /**
    * 更新水工保护表-鱼鳞坑护面信息
    *
    * @param scalefaceOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(ScalefaceOk scalefaceOk) {
        return scalefaceOkMapper.updateById(scalefaceOk);
    }

    /**
    * 全部查询
    *
    * @param scalefaceOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.ScalefaceOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<ScalefaceOk> list(ScalefaceOk scalefaceOk) {

        QueryWrapper<ScalefaceOk> queryWrapper = new QueryWrapper<>();

        return scalefaceOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<ScalefaceOk> scalefaceOkIPage = new Page<>();
        scalefaceOkIPage.setCurrent(current);
        scalefaceOkIPage.setSize(size);

        QueryWrapper<ScalefaceOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return scalefaceOkMapper.selectPage(scalefaceOkIPage, queryWrapper);
    }


}
