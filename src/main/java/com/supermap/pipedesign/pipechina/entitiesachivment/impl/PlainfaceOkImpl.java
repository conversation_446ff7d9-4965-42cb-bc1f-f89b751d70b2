package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.PlainfaceOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.PlainfaceOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IPlainfaceOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-素喷护面 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("PlainfaceOkImpl")
public class PlainfaceOkImpl extends ServiceImpl<PlainfaceOkMapper, PlainfaceOk> implements IPlainfaceOkService {

    @Autowired
    private PlainfaceOkMapper plainfaceOkMapper;

    /**
    * 添加水工保护表-素喷护面信息
    *
    * @param plainfaceOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(PlainfaceOk plainfaceOk) {

        //plainfaceOk.setUserId(JavaUtils.getUUID36());
        //plainfaceOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return plainfaceOkMapper.insert(plainfaceOk);
    }

    /**
    * 删除水工保护表-素喷护面信息
    *
    * @param plainfaceOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String plainfaceOkId) {
        return plainfaceOkMapper.deleteById(plainfaceOkId);
    }

    /**
    * 更新水工保护表-素喷护面信息
    *
    * @param plainfaceOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(PlainfaceOk plainfaceOk) {
        return plainfaceOkMapper.updateById(plainfaceOk);
    }

    /**
    * 全部查询
    *
    * @param plainfaceOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.PlainfaceOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<PlainfaceOk> list(PlainfaceOk plainfaceOk) {

        QueryWrapper<PlainfaceOk> queryWrapper = new QueryWrapper<>();

        return plainfaceOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<PlainfaceOk> plainfaceOkIPage = new Page<>();
        plainfaceOkIPage.setCurrent(current);
        plainfaceOkIPage.setSize(size);

        QueryWrapper<PlainfaceOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return plainfaceOkMapper.selectPage(plainfaceOkIPage, queryWrapper);
    }


}
