package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.OpticaboxOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 光缆接头盒表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface IOpticaboxOkService extends IService<OpticaboxOk> {

 /**
 * 添加光缆接头盒表信息
 *
 * @param opticaboxOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(OpticaboxOk opticaboxOk);

 /**
 * 删除光缆接头盒表信息
 *
 * @param opticaboxOkId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String opticaboxOkId);

 /**
 * 更新光缆接头盒表信息
 *
 * @param opticaboxOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(OpticaboxOk opticaboxOk);

 /**
 * 全部查询
 *
 * @param opticaboxOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.OpticaboxOk>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<OpticaboxOk> list(OpticaboxOk opticaboxOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
