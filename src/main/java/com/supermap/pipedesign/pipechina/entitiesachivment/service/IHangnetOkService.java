package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.HangnetOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-挂网锚喷护面 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IHangnetOkService extends IService<HangnetOk> {

 /**
 * 添加水工保护表-挂网锚喷护面信息
 *
 * @param hangnetOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(HangnetOk hangnetOk);

 /**
 * 删除水工保护表-挂网锚喷护面信息
 *
 * @param hangnetOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String hangnetOkId);

 /**
 * 更新水工保护表-挂网锚喷护面信息
 *
 * @param hangnetOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(HangnetOk hangnetOk);

 /**
 * 全部查询
 *
 * @param hangnetOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.HangnetOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<HangnetOk> list(HangnetOk hangnetOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
