package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GeologyreportOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 地质报告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface IGeologyreportOkService extends IService<GeologyreportOk> {

 /**
 * 添加地质报告表信息
 *
 * @param geologyreportOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(GeologyreportOk geologyreportOk);

 /**
 * 删除地质报告表信息
 *
 * @param geologyreportOkId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String geologyreportOkId);

 /**
 * 更新地质报告表信息
 *
 * @param geologyreportOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(GeologyreportOk geologyreportOk);

 /**
 * 全部查询
 *
 * @param geologyreportOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GeologyreportOk>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<GeologyreportOk> list(GeologyreportOk geologyreportOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
