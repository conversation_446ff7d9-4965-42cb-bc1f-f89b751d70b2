package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubbleskeletonOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-浆砌石拱形骨架护坡 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IGroutedrubbleskeletonOkService extends IService<GroutedrubbleskeletonOk> {

 /**
 * 添加水工保护表-浆砌石拱形骨架护坡信息
 *
 * @param groutedrubbleskeletonOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(GroutedrubbleskeletonOk groutedrubbleskeletonOk);

 /**
 * 删除水工保护表-浆砌石拱形骨架护坡信息
 *
 * @param groutedrubbleskeletonOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String groutedrubbleskeletonOkId);

 /**
 * 更新水工保护表-浆砌石拱形骨架护坡信息
 *
 * @param groutedrubbleskeletonOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(GroutedrubbleskeletonOk groutedrubbleskeletonOk);

 /**
 * 全部查询
 *
 * @param groutedrubbleskeletonOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubbleskeletonOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<GroutedrubbleskeletonOk> list(GroutedrubbleskeletonOk groutedrubbleskeletonOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
