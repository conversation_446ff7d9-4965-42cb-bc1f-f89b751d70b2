package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.VhconsequenceOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.VhconsequenceOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IVhconsequenceOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 高后果区视频监控表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("VhconsequenceOkImpl")
public class VhconsequenceOkImpl extends ServiceImpl<VhconsequenceOkMapper, VhconsequenceOk> implements IVhconsequenceOkService {

    @Autowired
    private VhconsequenceOkMapper vhconsequenceOkMapper;

    /**
    * 添加高后果区视频监控表信息
    *
    * @param vhconsequenceOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(VhconsequenceOk vhconsequenceOk) {

        //vhconsequenceOk.setUserId(JavaUtils.getUUID36());
        //vhconsequenceOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return vhconsequenceOkMapper.insert(vhconsequenceOk);
    }

    /**
    * 删除高后果区视频监控表信息
    *
    * @param vhconsequenceOkId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String vhconsequenceOkId) {
        return vhconsequenceOkMapper.deleteById(vhconsequenceOkId);
    }

    /**
    * 更新高后果区视频监控表信息
    *
    * @param vhconsequenceOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(VhconsequenceOk vhconsequenceOk) {
        return vhconsequenceOkMapper.updateById(vhconsequenceOk);
    }

    /**
    * 全部查询
    *
    * @param vhconsequenceOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.VhconsequenceOk>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<VhconsequenceOk> list(VhconsequenceOk vhconsequenceOk) {

        QueryWrapper<VhconsequenceOk> queryWrapper = new QueryWrapper<>();

        return vhconsequenceOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<VhconsequenceOk> vhconsequenceOkIPage = new Page<>();
        vhconsequenceOkIPage.setCurrent(current);
        vhconsequenceOkIPage.setSize(size);

        QueryWrapper<VhconsequenceOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return vhconsequenceOkMapper.selectPage(vhconsequenceOkIPage, queryWrapper);
    }


}
