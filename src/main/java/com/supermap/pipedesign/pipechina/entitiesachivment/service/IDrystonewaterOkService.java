package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.DrystonewaterOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-干砌石过水面 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IDrystonewaterOkService extends IService<DrystonewaterOk> {

 /**
 * 添加水工保护表-干砌石过水面信息
 *
 * @param drystonewaterOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(DrystonewaterOk drystonewaterOk);

 /**
 * 删除水工保护表-干砌石过水面信息
 *
 * @param drystonewaterOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String drystonewaterOkId);

 /**
 * 更新水工保护表-干砌石过水面信息
 *
 * @param drystonewaterOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(DrystonewaterOk drystonewaterOk);

 /**
 * 全部查询
 *
 * @param drystonewaterOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.DrystonewaterOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<DrystonewaterOk> list(DrystonewaterOk drystonewaterOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
