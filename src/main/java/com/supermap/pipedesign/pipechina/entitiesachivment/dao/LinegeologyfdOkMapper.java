package com.supermap.pipedesign.pipechina.entitiesachivment.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.LinegeologyfdOk;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/*
 * <AUTHOR>
 * @Description
 * @Date 2023/1/1 17:17
 * (2)线路地质分段表 成果表 mapper 接口
 **/
@Mapper
public interface LinegeologyfdOkMapper extends BaseMapper<LinegeologyfdOk> {

    //根据子项目id查成果表
    @Select("select * from linegeologyfd_ok t where t.subprojectid=#{subprojectid}")
    List<LinegeologyfdOk> selBySubProjectId(String subprojectid);

}
