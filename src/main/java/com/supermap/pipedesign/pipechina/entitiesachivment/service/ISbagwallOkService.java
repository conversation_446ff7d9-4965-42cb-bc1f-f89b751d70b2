package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.SbagwallOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-草袋素土裆土墙 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface ISbagwallOkService extends IService<SbagwallOk> {

 /**
 * 添加水工保护表-草袋素土裆土墙信息
 *
 * @param sbagwallOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(SbagwallOk sbagwallOk);

 /**
 * 删除水工保护表-草袋素土裆土墙信息
 *
 * @param sbagwallOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String sbagwallOkId);

 /**
 * 更新水工保护表-草袋素土裆土墙信息
 *
 * @param sbagwallOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(SbagwallOk sbagwallOk);

 /**
 * 全部查询
 *
 * @param sbagwallOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.SbagwallOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<SbagwallOk> list(SbagwallOk sbagwallOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
