package com.supermap.pipedesign.pipechina.entitiesachivment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.sql.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 通信标识表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("communication_survey")
@ApiModel(value="CommunicationOk对象", description="通信标识表")
public class CommunicationOk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "SmID")
    @TableField(value = "smid")
    private Integer smid;

    @ApiModelProperty(value = "桩点编号")
    @TableField("stake_point_no")
    private String stakePointNo;

    @ApiModelProperty(value = "设计人员ID")
    @TableField("designer_id")
    private String designerId;

    @ApiModelProperty(value = "设计时间")
    @TableField("modify_time")
    private Date modifyTime;

    @ApiModelProperty(value = "描述信息")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "上一版本编号")
    @TableField("sequence_org_id")
    private String sequenceOrgId;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "子工程ID")
    @TableField("sub_project_id")
    private String subProjectId;

    @ApiModelProperty(value = "相对桩位置")
    @TableField("rpile_position")
    private String rpilePosition;

    @ApiModelProperty(value = "光缆盘留长度")
    @TableField("length_cabletray")
    private String lengthCabletray;

    @ApiModelProperty(value = "是否设置电子标识器数据")
    @TableField("whethee_id")
    private String whetheeId;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;


}
