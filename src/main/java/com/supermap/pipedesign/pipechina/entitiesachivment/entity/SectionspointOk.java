package com.supermap.pipedesign.pipechina.entitiesachivment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.sql.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 断面点表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sections_point_survey")
@ApiModel(value="SectionspointOk对象", description="断面点表")
public class SectionspointOk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "smid")
    @TableField(value = "smid")
    private Long smid;

    @ApiModelProperty(value = "桩号")
    @TableField("stake_point_no")
    private String stakePointNo;

    @ApiModelProperty(value = "里程")
    @TableField("mileage")
    private Double mileage;

    @ApiModelProperty(value = "东坐标y")
    @TableField("y")
    private Double y;

    @ApiModelProperty(value = "北坐标x")
    @TableField("x")
    private Double x;

      @ApiModelProperty(value = "Z")
    @TableField("z")
    private Double z;

    @ApiModelProperty(value = "项目id")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "设计人员")
    @TableField("designer_id")
    private String designerId;

    @ApiModelProperty(value = "设计时间")
    @TableField("modify_time")
    private Date modifyTime;

    @ApiModelProperty(value = "子工程id")
    @TableField("sub_project_id")
    private String subProjectId;

    @ApiModelProperty(value = "上一版本编号")
    @TableField("sequence_org_id")
    private String sequenceOrgId;

    @ApiModelProperty(value = "描述信息")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;


}
