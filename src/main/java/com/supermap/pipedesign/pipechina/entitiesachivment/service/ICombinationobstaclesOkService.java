package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.CombinationobstaclesOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 穿跨越与地下障碍物合并表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface ICombinationobstaclesOkService extends IService<CombinationobstaclesOk> {

 /**
 * 添加穿跨越与地下障碍物合并表信息
 *
 * @param combinationobstaclesOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(CombinationobstaclesOk combinationobstaclesOk);

 /**
 * 删除穿跨越与地下障碍物合并表信息
 *
 * @param combinationobstaclesOkId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String combinationobstaclesOkId);

 /**
 * 更新穿跨越与地下障碍物合并表信息
 *
 * @param combinationobstaclesOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(CombinationobstaclesOk combinationobstaclesOk);

 /**
 * 全部查询
 *
 * @param combinationobstaclesOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.CombinationobstaclesOk>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<CombinationobstaclesOk> list(CombinationobstaclesOk combinationobstaclesOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
