package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.PipelayingOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.PipelayingOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IPipelayingOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 管道敷设方式表     	 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("PipelayingOkImpl")
public class PipelayingOkImpl extends ServiceImpl<PipelayingOkMapper, PipelayingOk> implements IPipelayingOkService {

    @Autowired
    private PipelayingOkMapper pipelayingOkMapper;

    /**
    * 添加管道敷设方式表     	信息
    *
    * @param pipelayingOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(PipelayingOk pipelayingOk) {

        //pipelayingOk.setUserId(JavaUtils.getUUID36());
        //pipelayingOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return pipelayingOkMapper.insert(pipelayingOk);
    }

    /**
    * 删除管道敷设方式表     	信息
    *
    * @param pipelayingOkId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String pipelayingOkId) {
        return pipelayingOkMapper.deleteById(pipelayingOkId);
    }

    /**
    * 更新管道敷设方式表     	信息
    *
    * @param pipelayingOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(PipelayingOk pipelayingOk) {
        return pipelayingOkMapper.updateById(pipelayingOk);
    }

    /**
    * 全部查询
    *
    * @param pipelayingOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.PipelayingOk>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<PipelayingOk> list(PipelayingOk pipelayingOk) {

        QueryWrapper<PipelayingOk> queryWrapper = new QueryWrapper<>();

        return pipelayingOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<PipelayingOk> pipelayingOkIPage = new Page<>();
        pipelayingOkIPage.setCurrent(current);
        pipelayingOkIPage.setSize(size);

        QueryWrapper<PipelayingOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return pipelayingOkMapper.selectPage(pipelayingOkIPage, queryWrapper);
    }


}
