package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.BetonOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.BetonOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IBetonOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-混凝土堡坎 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("BetonOkImpl")
public class BetonOkImpl extends ServiceImpl<BetonOkMapper, BetonOk> implements IBetonOkService {

    @Autowired
    private BetonOkMapper betonOkMapper;

    /**
    * 添加水工保护表-混凝土堡坎信息
    *
    * @param betonOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(BetonOk betonOk) {

        //betonOk.setUserId(JavaUtils.getUUID36());
        //betonOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return betonOkMapper.insert(betonOk);
    }

    /**
    * 删除水工保护表-混凝土堡坎信息
    *
    * @param betonOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String betonOkId) {
        return betonOkMapper.deleteById(betonOkId);
    }

    /**
    * 更新水工保护表-混凝土堡坎信息
    *
    * @param betonOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(BetonOk betonOk) {
        return betonOkMapper.updateById(betonOk);
    }

    /**
    * 全部查询
    *
    * @param betonOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.BetonOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<BetonOk> list(BetonOk betonOk) {

        QueryWrapper<BetonOk> queryWrapper = new QueryWrapper<>();

        return betonOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<BetonOk> betonOkIPage = new Page<>();
        betonOkIPage.setCurrent(current);
        betonOkIPage.setSize(size);

        QueryWrapper<BetonOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return betonOkMapper.selectPage(betonOkIPage, queryWrapper);
    }


}
