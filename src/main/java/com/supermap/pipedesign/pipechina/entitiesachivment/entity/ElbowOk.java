package com.supermap.pipedesign.pipechina.entitiesachivment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.sql.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 弯管表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("elbow_survey")
@ApiModel(value="ElbowOk对象", description="弯管表")
public class ElbowOk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "SmID")
    @TableField(value = "smid")
    private Integer smid;

      @ApiModelProperty(value = "纵坐标X")
    @TableField("x")
    private Double x;

      @ApiModelProperty(value = "横坐标Y")
    @TableField("y")
    private Double y;

    @ApiModelProperty(value = "中线桩号")
    @TableField("stake_point_no")
    private String stakePointNo;

    @ApiModelProperty(value = "设计人员ID")
    @TableField("designer_id")
    private String designerId;

    @ApiModelProperty(value = "设计时间")
    @TableField("modify_time")
    private Date modifyTime;

    @ApiModelProperty(value = "弯制角度")
    @TableField("rotate_angle")
    private String rotateAngle;

    @ApiModelProperty(value = "描述信息")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "上一版本编号")
    @TableField("sequence_org_id")
    private String sequenceOrgId;

    @ApiModelProperty(value = "相对中线桩里程")
    @TableField("relative_mileage")
    private Double relativeMileage;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "子工程ID")
    @TableField("sub_project_id")
    private String subProjectId;

    @ApiModelProperty(value = "管底高程")
    @TableField("altitude")
    private Double altitude;

    @ApiModelProperty(value = "弯管类型")
    @TableField("elbow_type")
    private String elbowType;

    @ApiModelProperty(value = "曲率半径")
    @TableField("curvature")
    private String curvature;

    @ApiModelProperty(value = "弯管方向")
    @TableField("elbow_direction")
    private String elbowDirection;

    @ApiModelProperty(value = "壁厚（mm）")
    @TableField("wallt")
    private String wallt;

    @ApiModelProperty(value = "防腐形式")
    @TableField("antiseptic_form")
    private String antisepticForm;

    @ApiModelProperty(value = "水平连续里程")
    @TableField("level_mileage")
    private Double levelMileage;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;


}
