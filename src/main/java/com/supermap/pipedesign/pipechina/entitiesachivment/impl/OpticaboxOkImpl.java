package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.OpticaboxOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.OpticaboxOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IOpticaboxOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 光缆接头盒表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("OpticaboxOkImpl")
public class OpticaboxOkImpl extends ServiceImpl<OpticaboxOkMapper, OpticaboxOk> implements IOpticaboxOkService {

    @Autowired
    private OpticaboxOkMapper opticaboxOkMapper;

    /**
    * 添加光缆接头盒表信息
    *
    * @param opticaboxOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(OpticaboxOk opticaboxOk) {

        //opticaboxOk.setUserId(JavaUtils.getUUID36());
        //opticaboxOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return opticaboxOkMapper.insert(opticaboxOk);
    }

    /**
    * 删除光缆接头盒表信息
    *
    * @param opticaboxOkId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String opticaboxOkId) {
        return opticaboxOkMapper.deleteById(opticaboxOkId);
    }

    /**
    * 更新光缆接头盒表信息
    *
    * @param opticaboxOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(OpticaboxOk opticaboxOk) {
        return opticaboxOkMapper.updateById(opticaboxOk);
    }

    /**
    * 全部查询
    *
    * @param opticaboxOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.OpticaboxOk>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<OpticaboxOk> list(OpticaboxOk opticaboxOk) {

        QueryWrapper<OpticaboxOk> queryWrapper = new QueryWrapper<>();

        return opticaboxOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<OpticaboxOk> opticaboxOkIPage = new Page<>();
        opticaboxOkIPage.setCurrent(current);
        opticaboxOkIPage.setSize(size);

        QueryWrapper<OpticaboxOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return opticaboxOkMapper.selectPage(opticaboxOkIPage, queryWrapper);
    }


}
