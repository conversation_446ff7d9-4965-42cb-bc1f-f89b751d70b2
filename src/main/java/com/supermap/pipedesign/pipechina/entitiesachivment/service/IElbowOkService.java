package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.ElbowOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 弯管表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface IElbowOkService extends IService<ElbowOk> {

 /**
 * 添加弯管表信息
 *
 * @param elbowOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(ElbowOk elbowOk);

 /**
 * 删除弯管表信息
 *
 * @param elbowOkId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String elbowOkId);

 /**
 * 更新弯管表信息
 *
 * @param elbowOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(ElbowOk elbowOk);

 /**
 * 全部查询
 *
 * @param elbowOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.ElbowOk>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<ElbowOk> list(ElbowOk elbowOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
