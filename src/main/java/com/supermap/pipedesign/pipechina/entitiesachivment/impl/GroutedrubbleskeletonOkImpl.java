package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.GroutedrubbleskeletonOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubbleskeletonOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IGroutedrubbleskeletonOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-浆砌石拱形骨架护坡 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("GroutedrubbleskeletonOkImpl")
public class GroutedrubbleskeletonOkImpl extends ServiceImpl<GroutedrubbleskeletonOkMapper, GroutedrubbleskeletonOk> implements IGroutedrubbleskeletonOkService {

    @Autowired
    private GroutedrubbleskeletonOkMapper groutedrubbleskeletonOkMapper;

    /**
    * 添加水工保护表-浆砌石拱形骨架护坡信息
    *
    * @param groutedrubbleskeletonOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(GroutedrubbleskeletonOk groutedrubbleskeletonOk) {

        //groutedrubbleskeletonOk.setUserId(JavaUtils.getUUID36());
        //groutedrubbleskeletonOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return groutedrubbleskeletonOkMapper.insert(groutedrubbleskeletonOk);
    }

    /**
    * 删除水工保护表-浆砌石拱形骨架护坡信息
    *
    * @param groutedrubbleskeletonOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String groutedrubbleskeletonOkId) {
        return groutedrubbleskeletonOkMapper.deleteById(groutedrubbleskeletonOkId);
    }

    /**
    * 更新水工保护表-浆砌石拱形骨架护坡信息
    *
    * @param groutedrubbleskeletonOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(GroutedrubbleskeletonOk groutedrubbleskeletonOk) {
        return groutedrubbleskeletonOkMapper.updateById(groutedrubbleskeletonOk);
    }

    /**
    * 全部查询
    *
    * @param groutedrubbleskeletonOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubbleskeletonOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<GroutedrubbleskeletonOk> list(GroutedrubbleskeletonOk groutedrubbleskeletonOk) {

        QueryWrapper<GroutedrubbleskeletonOk> queryWrapper = new QueryWrapper<>();

        return groutedrubbleskeletonOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<GroutedrubbleskeletonOk> groutedrubbleskeletonOkIPage = new Page<>();
        groutedrubbleskeletonOkIPage.setCurrent(current);
        groutedrubbleskeletonOkIPage.setSize(size);

        QueryWrapper<GroutedrubbleskeletonOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return groutedrubbleskeletonOkMapper.selectPage(groutedrubbleskeletonOkIPage, queryWrapper);
    }


}
