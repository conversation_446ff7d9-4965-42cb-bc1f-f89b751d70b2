package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.HandholeOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.HandholeOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IHandholeOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 手孔表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("HandholeOkImpl")
public class HandholeOkImpl extends ServiceImpl<HandholeOkMapper, HandholeOk> implements IHandholeOkService {

    @Autowired
    private HandholeOkMapper handholeOkMapper;

    /**
    * 添加手孔表信息
    *
    * @param handholeOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(HandholeOk handholeOk) {

        //handholeOk.setUserId(JavaUtils.getUUID36());
        //handholeOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return handholeOkMapper.insert(handholeOk);
    }

    /**
    * 删除手孔表信息
    *
    * @param handholeOkId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String handholeOkId) {
        return handholeOkMapper.deleteById(handholeOkId);
    }

    /**
    * 更新手孔表信息
    *
    * @param handholeOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(HandholeOk handholeOk) {
        return handholeOkMapper.updateById(handholeOk);
    }

    /**
    * 全部查询
    *
    * @param handholeOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.HandholeOk>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<HandholeOk> list(HandholeOk handholeOk) {

        QueryWrapper<HandholeOk> queryWrapper = new QueryWrapper<>();

        return handholeOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<HandholeOk> handholeOkIPage = new Page<>();
        handholeOkIPage.setCurrent(current);
        handholeOkIPage.setSize(size);

        QueryWrapper<HandholeOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return handholeOkMapper.selectPage(handholeOkIPage, queryWrapper);
    }


}
