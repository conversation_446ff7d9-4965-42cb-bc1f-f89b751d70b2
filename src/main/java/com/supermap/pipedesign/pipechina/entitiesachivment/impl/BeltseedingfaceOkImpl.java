package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.BeltseedingfaceOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.BeltseedingfaceOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IBeltseedingfaceOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-植生带护面 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("BeltseedingfaceOkImpl")
public class BeltseedingfaceOkImpl extends ServiceImpl<BeltseedingfaceOkMapper, BeltseedingfaceOk> implements IBeltseedingfaceOkService {

    @Autowired
    private BeltseedingfaceOkMapper beltseedingfaceOkMapper;

    /**
    * 添加水工保护表-植生带护面信息
    *
    * @param beltseedingfaceOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(BeltseedingfaceOk beltseedingfaceOk) {

        //beltseedingfaceOk.setUserId(JavaUtils.getUUID36());
        //beltseedingfaceOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return beltseedingfaceOkMapper.insert(beltseedingfaceOk);
    }

    /**
    * 删除水工保护表-植生带护面信息
    *
    * @param beltseedingfaceOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String beltseedingfaceOkId) {
        return beltseedingfaceOkMapper.deleteById(beltseedingfaceOkId);
    }

    /**
    * 更新水工保护表-植生带护面信息
    *
    * @param beltseedingfaceOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(BeltseedingfaceOk beltseedingfaceOk) {
        return beltseedingfaceOkMapper.updateById(beltseedingfaceOk);
    }

    /**
    * 全部查询
    *
    * @param beltseedingfaceOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.BeltseedingfaceOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<BeltseedingfaceOk> list(BeltseedingfaceOk beltseedingfaceOk) {

        QueryWrapper<BeltseedingfaceOk> queryWrapper = new QueryWrapper<>();

        return beltseedingfaceOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<BeltseedingfaceOk> beltseedingfaceOkIPage = new Page<>();
        beltseedingfaceOkIPage.setCurrent(current);
        beltseedingfaceOkIPage.setSize(size);

        QueryWrapper<BeltseedingfaceOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return beltseedingfaceOkMapper.selectPage(beltseedingfaceOkIPage, queryWrapper);
    }


}
