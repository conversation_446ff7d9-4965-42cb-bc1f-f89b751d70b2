package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GabionwallOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-石笼防冲墙 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IGabionwallOkService extends IService<GabionwallOk> {

 /**
 * 添加水工保护表-石笼防冲墙信息
 *
 * @param gabionwallOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(GabionwallOk gabionwallOk);

 /**
 * 删除水工保护表-石笼防冲墙信息
 *
 * @param gabionwallOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String gabionwallOkId);

 /**
 * 更新水工保护表-石笼防冲墙信息
 *
 * @param gabionwallOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(GabionwallOk gabionwallOk);

 /**
 * 全部查询
 *
 * @param gabionwallOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GabionwallOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<GabionwallOk> list(GabionwallOk gabionwallOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
