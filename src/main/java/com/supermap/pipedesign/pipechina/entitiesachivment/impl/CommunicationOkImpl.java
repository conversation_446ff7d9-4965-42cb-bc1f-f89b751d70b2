package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.CommunicationOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.CommunicationOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.ICommunicationOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 通信标识表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("CommunicationOkImpl")
public class CommunicationOkImpl extends ServiceImpl<CommunicationOkMapper, CommunicationOk> implements ICommunicationOkService {

    @Autowired
    private CommunicationOkMapper communicationOkMapper;

    /**
    * 添加通信标识表信息
    *
    * @param communicationOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(CommunicationOk communicationOk) {

        //communicationOk.setUserId(JavaUtils.getUUID36());
        //communicationOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return communicationOkMapper.insert(communicationOk);
    }

    /**
    * 删除通信标识表信息
    *
    * @param communicationOkId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String communicationOkId) {
        return communicationOkMapper.deleteById(communicationOkId);
    }

    /**
    * 更新通信标识表信息
    *
    * @param communicationOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(CommunicationOk communicationOk) {
        return communicationOkMapper.updateById(communicationOk);
    }

    /**
    * 全部查询
    *
    * @param communicationOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.CommunicationOk>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<CommunicationOk> list(CommunicationOk communicationOk) {

        QueryWrapper<CommunicationOk> queryWrapper = new QueryWrapper<>();

        return communicationOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<CommunicationOk> communicationOkIPage = new Page<>();
        communicationOkIPage.setCurrent(current);
        communicationOkIPage.setSize(size);

        QueryWrapper<CommunicationOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return communicationOkMapper.selectPage(communicationOkIPage, queryWrapper);
    }


}
