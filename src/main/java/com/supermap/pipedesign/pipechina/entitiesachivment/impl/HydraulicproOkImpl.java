package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.HydraulicproOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.HydraulicproOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IHydraulicproOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("HydraulicproOkImpl")
public class HydraulicproOkImpl extends ServiceImpl<HydraulicproOkMapper, HydraulicproOk> implements IHydraulicproOkService {

    @Autowired
    private HydraulicproOkMapper hydraulicproOkMapper;

    /**
    * 添加水工保护表信息
    *
    * @param hydraulicproOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(HydraulicproOk hydraulicproOk) {

        //hydraulicproOk.setUserId(JavaUtils.getUUID36());
        //hydraulicproOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return hydraulicproOkMapper.insert(hydraulicproOk);
    }

    /**
    * 删除水工保护表信息
    *
    * @param hydraulicproOkId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String hydraulicproOkId) {
        return hydraulicproOkMapper.deleteById(hydraulicproOkId);
    }

    /**
    * 更新水工保护表信息
    *
    * @param hydraulicproOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(HydraulicproOk hydraulicproOk) {
        return hydraulicproOkMapper.updateById(hydraulicproOk);
    }

    /**
    * 全部查询
    *
    * @param hydraulicproOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.HydraulicproOk>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<HydraulicproOk> list(HydraulicproOk hydraulicproOk) {

        QueryWrapper<HydraulicproOk> queryWrapper = new QueryWrapper<>();

        return hydraulicproOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<HydraulicproOk> hydraulicproOkIPage = new Page<>();
        hydraulicproOkIPage.setCurrent(current);
        hydraulicproOkIPage.setSize(size);

        QueryWrapper<HydraulicproOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return hydraulicproOkMapper.selectPage(hydraulicproOkIPage, queryWrapper);
    }


}
