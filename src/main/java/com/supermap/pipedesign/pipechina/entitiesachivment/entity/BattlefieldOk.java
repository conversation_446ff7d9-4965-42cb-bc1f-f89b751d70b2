package com.supermap.pipedesign.pipechina.entitiesachivment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.sql.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 站场表 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("station_yard_survey")
@ApiModel(value="BattlefieldOk对象", description="站场表")
public class BattlefieldOk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "SmID")
    @TableField(value = "smid")
    private Integer smid;

      @ApiModelProperty(value = "纵坐标X")
    @TableField("x")
    private Double x;

      @ApiModelProperty(value = "横坐标Y")
    @TableField("y")
    private Double y;

    @ApiModelProperty(value = "设计人员ID")
    @TableField("designer_id")
    private String designerId;

    @ApiModelProperty(value = "设计时间")
    @TableField("modify_time")
    private Date modifyTime;

    @ApiModelProperty(value = "描述信息")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "上一版本编号")
    @TableField("sequence_org_id")
    private String sequenceOrgId;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "子工程ID")
    @TableField("sub_project_id")
    private String subProjectId;

    @ApiModelProperty(value = "站场名称")
    @TableField("station_name")
    private String stationName;

    @ApiModelProperty(value = "中线桩号")
    @TableField("celp_num")
    private String celpNum;

    @ApiModelProperty(value = "相对中线桩里程")
    @TableField("relative_mileage")
    private Double relativeMileage;

    @ApiModelProperty(value = "站场类型")
    @TableField("station_type")
    private String stationType;

    @ApiModelProperty(value = "水平连续里程")
    @TableField("level_mileage")
    private Double levelMileage;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;


}
