package com.supermap.pipedesign.pipechina.entitiesachivment.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.ExploratorypointOk;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/*
 * <AUTHOR>
 * @Description
 * @Date 2023/1/1 17:16
 * 勘探点坐标表接口 mapper
 **/
@Mapper
public interface ExploratorypointOkMapper extends BaseMapper<ExploratorypointOk> {

    //根据子项目id查成果表
    @Select("select * from exploratorypoint_ok t where t.subprojectid=#{subprojectid}")
    List<ExploratorypointOk> selBySubProjectId(String subprojectid);

}
