package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.DiscretepointOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.DiscretepointOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IDiscretepointOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 离散点表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("DiscretepointOkImpl")
public class DiscretepointOkImpl extends ServiceImpl<DiscretepointOkMapper, DiscretepointOk> implements IDiscretepointOkService {

    @Autowired
    private DiscretepointOkMapper discretepointOkMapper;

    /**
    * 添加离散点表信息
    *
    * @param discretepointOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(DiscretepointOk discretepointOk) {

        //discretepointOk.setUserId(JavaUtils.getUUID36());
        //discretepointOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return discretepointOkMapper.insert(discretepointOk);
    }

    /**
    * 删除离散点表信息
    *
    * @param discretepointOkId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String discretepointOkId) {
        return discretepointOkMapper.deleteById(discretepointOkId);
    }

    /**
    * 更新离散点表信息
    *
    * @param discretepointOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(DiscretepointOk discretepointOk) {
        return discretepointOkMapper.updateById(discretepointOk);
    }

    /**
    * 全部查询
    *
    * @param discretepointOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.DiscretepointOk>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<DiscretepointOk> list(DiscretepointOk discretepointOk) {

        QueryWrapper<DiscretepointOk> queryWrapper = new QueryWrapper<>();

        return discretepointOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<DiscretepointOk> discretepointOkIPage = new Page<>();
        discretepointOkIPage.setCurrent(current);
        discretepointOkIPage.setSize(size);

        QueryWrapper<DiscretepointOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return discretepointOkMapper.selectPage(discretepointOkIPage, queryWrapper);
    }


}
