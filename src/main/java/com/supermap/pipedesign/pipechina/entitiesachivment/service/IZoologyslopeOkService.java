package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.ZoologyslopeOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-生态袋护坡 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IZoologyslopeOkService extends IService<ZoologyslopeOk> {

 /**
 * 添加水工保护表-生态袋护坡信息
 *
 * @param zoologyslopeOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(ZoologyslopeOk zoologyslopeOk);

 /**
 * 删除水工保护表-生态袋护坡信息
 *
 * @param zoologyslopeOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String zoologyslopeOkId);

 /**
 * 更新水工保护表-生态袋护坡信息
 *
 * @param zoologyslopeOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(ZoologyslopeOk zoologyslopeOk);

 /**
 * 全部查询
 *
 * @param zoologyslopeOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.ZoologyslopeOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<ZoologyslopeOk> list(ZoologyslopeOk zoologyslopeOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
