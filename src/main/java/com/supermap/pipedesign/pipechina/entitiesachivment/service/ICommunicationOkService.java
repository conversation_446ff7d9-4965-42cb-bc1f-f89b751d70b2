package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.CommunicationOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 通信标识表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface ICommunicationOkService extends IService<CommunicationOk> {

 /**
 * 添加通信标识表信息
 *
 * @param communicationOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(CommunicationOk communicationOk);

 /**
 * 删除通信标识表信息
 *
 * @param communicationOkId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String communicationOkId);

 /**
 * 更新通信标识表信息
 *
 * @param communicationOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(CommunicationOk communicationOk);

 /**
 * 全部查询
 *
 * @param communicationOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.CommunicationOk>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<CommunicationOk> list(CommunicationOk communicationOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
