package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.PipelayingOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 管道敷设方式表     	 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface IPipelayingOkService extends IService<PipelayingOk> {

 /**
 * 添加管道敷设方式表     	信息
 *
 * @param pipelayingOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(PipelayingOk pipelayingOk);

 /**
 * 删除管道敷设方式表     	信息
 *
 * @param pipelayingOkId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String pipelayingOkId);

 /**
 * 更新管道敷设方式表     	信息
 *
 * @param pipelayingOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(PipelayingOk pipelayingOk);

 /**
 * 全部查询
 *
 * @param pipelayingOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.PipelayingOk>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<PipelayingOk> list(PipelayingOk pipelayingOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
