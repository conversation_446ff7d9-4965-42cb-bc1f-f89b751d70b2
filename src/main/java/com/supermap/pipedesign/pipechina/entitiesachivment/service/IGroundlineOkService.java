package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroundlineOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 地面线成果表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Repository
public interface IGroundlineOkService extends IService<GroundlineOk> {

 /**
 * 添加地面线成果表信息
 *
 * @param groundlineOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int insert(GroundlineOk groundlineOk);

 /**
 * 删除地面线成果表信息
 *
 * @param groundlineOkId
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int delete(String groundlineOkId);

 /**
 * 更新地面线成果表信息
 *
 * @param groundlineOk
 * @return int
 * @Date 2023-01-12
 * @auther eomer
 */
 int update(GroundlineOk groundlineOk);

 /**
 * 全部查询
 *
 * @param groundlineOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroundlineOk>
 * @Date 2023-01-12
 * @auther eomer
 */
 List<GroundlineOk> list(GroundlineOk groundlineOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-12
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
