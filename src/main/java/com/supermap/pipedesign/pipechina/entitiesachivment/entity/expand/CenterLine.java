package com.supermap.pipedesign.pipechina.entitiesachivment.entity.expand;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;

/**
 * <p>
 *  实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Data

public class CenterLine{

    @TableField("pkid")
    private String pkid;

    @TableField("elevation")
    private double elevation;

    @TableField("last_uuid")
    private String lastUuid;

    @TableField("crossing_zone")
    private Integer crossingZone;

    @TableField("is_edge")
    private Integer isEdge;

    @TableField("sub_number")
    private String subNumber;

    @TableField("end_time")
    private String endTime;

    @TableField("uuid")
    private String uuid;

    @TableField("start_time")
    private String startTime;

    @TableField("stake_number")
    private String stakeNumber;

    /**
     * 加桩号
     */
    @TableField("add_number")
    private String addNumber;

    @TableField("data_source")
    private Integer dataSource;

    @TableField("pbscode")
    private String pbscode;

    @TableField("pbs_entity_code")
    private String pbsentitycode;

    //    桩号里程 relative_mileage
//    设计段里程 mileage
//    项目连续里程 continuous_mileage
//    测量连续里程 measure_mileage
    //桩号里程
    @TableField("relative_mileage")
    private double relativeMileage;
    @TableField("mileage")
    private double mileage;
    @TableField("continuous_mileage")
    private double continuousMileage;
    @TableField("measure_mileage")
    private double measureMileage;

    @TableField("x")
    private double x;

    @TableField("y")
    private double y;

//    @TableField("z")
//    private double z;

    @TableField("angle")
    private double angle;

    @TableField("center_line_uuid")
    private String centerLineUuid;

    @TableField("pipe_type")
    private Integer pipeType;

    @TableField("srid")
    private String srid;

    @TableField("measure_angle")
    private double measureAngle;

    @TableField("status")
    private Integer status;

    @TableField("sort")
    private Integer sort;

    @TableField("project_id")
    private String projectId;

    @TableField("segment_id")
    private String segmentId;

    @TableField("project_unit_id")
    private String projectUnitId;

    @TableField("rel_uuid")
    private String relUuid;

}
