package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.GabionwallOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GabionwallOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IGabionwallOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-石笼防冲墙 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("GabionwallOkImpl")
public class GabionwallOkImpl extends ServiceImpl<GabionwallOkMapper, GabionwallOk> implements IGabionwallOkService {

    @Autowired
    private GabionwallOkMapper gabionwallOkMapper;

    /**
    * 添加水工保护表-石笼防冲墙信息
    *
    * @param gabionwallOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(GabionwallOk gabionwallOk) {

        //gabionwallOk.setUserId(JavaUtils.getUUID36());
        //gabionwallOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return gabionwallOkMapper.insert(gabionwallOk);
    }

    /**
    * 删除水工保护表-石笼防冲墙信息
    *
    * @param gabionwallOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String gabionwallOkId) {
        return gabionwallOkMapper.deleteById(gabionwallOkId);
    }

    /**
    * 更新水工保护表-石笼防冲墙信息
    *
    * @param gabionwallOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(GabionwallOk gabionwallOk) {
        return gabionwallOkMapper.updateById(gabionwallOk);
    }

    /**
    * 全部查询
    *
    * @param gabionwallOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GabionwallOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<GabionwallOk> list(GabionwallOk gabionwallOk) {

        QueryWrapper<GabionwallOk> queryWrapper = new QueryWrapper<>();

        return gabionwallOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<GabionwallOk> gabionwallOkIPage = new Page<>();
        gabionwallOkIPage.setCurrent(current);
        gabionwallOkIPage.setSize(size);

        QueryWrapper<GabionwallOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return gabionwallOkMapper.selectPage(gabionwallOkIPage, queryWrapper);
    }


}
