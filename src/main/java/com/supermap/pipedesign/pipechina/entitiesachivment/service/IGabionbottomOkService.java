package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GabionbottomOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-石笼护底 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IGabionbottomOkService extends IService<GabionbottomOk> {

 /**
 * 添加水工保护表-石笼护底信息
 *
 * @param gabionbottomOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(GabionbottomOk gabionbottomOk);

 /**
 * 删除水工保护表-石笼护底信息
 *
 * @param gabionbottomOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String gabionbottomOkId);

 /**
 * 更新水工保护表-石笼护底信息
 *
 * @param gabionbottomOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(GabionbottomOk gabionbottomOk);

 /**
 * 全部查询
 *
 * @param gabionbottomOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GabionbottomOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<GabionbottomOk> list(GabionbottomOk gabionbottomOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
