package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.AddivisionOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.AddivisionOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IAddivisionOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 行政区划表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-12
 */
@Service("AddivisionOkImpl")
public class AddivisionOkImpl extends ServiceImpl<AddivisionOkMapper, AddivisionOk> implements IAddivisionOkService {

    @Autowired
    private AddivisionOkMapper addivisionOkMapper;

    /**
    * 添加行政区划表信息
    *
    * @param addivisionOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int insert(AddivisionOk addivisionOk) {

        //addivisionOk.setUserId(JavaUtils.getUUID36());
        //addivisionOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return addivisionOkMapper.insert(addivisionOk);
    }

    /**
    * 删除行政区划表信息
    *
    * @param addivisionOkId
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int delete(String addivisionOkId) {
        return addivisionOkMapper.deleteById(addivisionOkId);
    }

    /**
    * 更新行政区划表信息
    *
    * @param addivisionOk
    * @return int
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public int update(AddivisionOk addivisionOk) {
        return addivisionOkMapper.updateById(addivisionOk);
    }

    /**
    * 全部查询
    *
    * @param addivisionOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.AddivisionOk>
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public List<AddivisionOk> list(AddivisionOk addivisionOk) {

        QueryWrapper<AddivisionOk> queryWrapper = new QueryWrapper<>();

        return addivisionOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-12
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<AddivisionOk> addivisionOkIPage = new Page<>();
        addivisionOkIPage.setCurrent(current);
        addivisionOkIPage.setSize(size);

        QueryWrapper<AddivisionOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return addivisionOkMapper.selectPage(addivisionOkIPage, queryWrapper);
    }


}
