package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.DrystonewaterOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.DrystonewaterOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IDrystonewaterOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-干砌石过水面 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("DrystonewaterOkImpl")
public class DrystonewaterOkImpl extends ServiceImpl<DrystonewaterOkMapper, DrystonewaterOk> implements IDrystonewaterOkService {

    @Autowired
    private DrystonewaterOkMapper drystonewaterOkMapper;

    /**
    * 添加水工保护表-干砌石过水面信息
    *
    * @param drystonewaterOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(DrystonewaterOk drystonewaterOk) {

        //drystonewaterOk.setUserId(JavaUtils.getUUID36());
        //drystonewaterOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return drystonewaterOkMapper.insert(drystonewaterOk);
    }

    /**
    * 删除水工保护表-干砌石过水面信息
    *
    * @param drystonewaterOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String drystonewaterOkId) {
        return drystonewaterOkMapper.deleteById(drystonewaterOkId);
    }

    /**
    * 更新水工保护表-干砌石过水面信息
    *
    * @param drystonewaterOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(DrystonewaterOk drystonewaterOk) {
        return drystonewaterOkMapper.updateById(drystonewaterOk);
    }

    /**
    * 全部查询
    *
    * @param drystonewaterOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.DrystonewaterOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<DrystonewaterOk> list(DrystonewaterOk drystonewaterOk) {

        QueryWrapper<DrystonewaterOk> queryWrapper = new QueryWrapper<>();

        return drystonewaterOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<DrystonewaterOk> drystonewaterOkIPage = new Page<>();
        drystonewaterOkIPage.setCurrent(current);
        drystonewaterOkIPage.setSize(size);

        QueryWrapper<DrystonewaterOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return drystonewaterOkMapper.selectPage(drystonewaterOkIPage, queryWrapper);
    }


}
