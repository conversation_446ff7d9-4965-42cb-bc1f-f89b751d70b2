package com.supermap.pipedesign.pipechina.entitiesachivment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblefwallOk;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 水工保护表-浆砌石防冲墙 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Repository
public interface IGroutedrubblefwallOkService extends IService<GroutedrubblefwallOk> {

 /**
 * 添加水工保护表-浆砌石防冲墙信息
 *
 * @param groutedrubblefwallOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int insert(GroutedrubblefwallOk groutedrubblefwallOk);

 /**
 * 删除水工保护表-浆砌石防冲墙信息
 *
 * @param groutedrubblefwallOkId
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int delete(String groutedrubblefwallOkId);

 /**
 * 更新水工保护表-浆砌石防冲墙信息
 *
 * @param groutedrubblefwallOk
 * @return int
 * @Date 2023-01-14
 * @auther eomer
 */
 int update(GroutedrubblefwallOk groutedrubblefwallOk);

 /**
 * 全部查询
 *
 * @param groutedrubblefwallOk
 * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubblefwallOk>
 * @Date 2023-01-14
 * @auther eomer
 */
 List<GroutedrubblefwallOk> list(GroutedrubblefwallOk groutedrubblefwallOk);

 /**
 * 分页查询
 *
 * @param current
 * @param size
 * @param startDate
 * @param endDate
 * @return com.baomidou.mybatisplus.core.metadata.IPage
 * @Date 2023-01-14
 * @auther eomer
 */
 IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate);


 }
