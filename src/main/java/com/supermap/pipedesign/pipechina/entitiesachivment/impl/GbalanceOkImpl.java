package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.GbalanceOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GbalanceOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IGbalanceOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-平衡压袋稳管 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("GbalanceOkImpl")
public class GbalanceOkImpl extends ServiceImpl<GbalanceOkMapper, GbalanceOk> implements IGbalanceOkService {

    @Autowired
    private GbalanceOkMapper gbalanceOkMapper;

    /**
    * 添加水工保护表-平衡压袋稳管信息
    *
    * @param gbalanceOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(GbalanceOk gbalanceOk) {

        //gbalanceOk.setUserId(JavaUtils.getUUID36());
        //gbalanceOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return gbalanceOkMapper.insert(gbalanceOk);
    }

    /**
    * 删除水工保护表-平衡压袋稳管信息
    *
    * @param gbalanceOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String gbalanceOkId) {
        return gbalanceOkMapper.deleteById(gbalanceOkId);
    }

    /**
    * 更新水工保护表-平衡压袋稳管信息
    *
    * @param gbalanceOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(GbalanceOk gbalanceOk) {
        return gbalanceOkMapper.updateById(gbalanceOk);
    }

    /**
    * 全部查询
    *
    * @param gbalanceOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GbalanceOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<GbalanceOk> list(GbalanceOk gbalanceOk) {

        QueryWrapper<GbalanceOk> queryWrapper = new QueryWrapper<>();

        return gbalanceOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<GbalanceOk> gbalanceOkIPage = new Page<>();
        gbalanceOkIPage.setCurrent(current);
        gbalanceOkIPage.setSize(size);

        QueryWrapper<GbalanceOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return gbalanceOkMapper.selectPage(gbalanceOkIPage, queryWrapper);
    }


}
