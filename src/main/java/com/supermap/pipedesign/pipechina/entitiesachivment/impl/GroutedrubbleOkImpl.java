package com.supermap.pipedesign.pipechina.entitiesachivment.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.supermap.pipedesign.pipechina.entitiesachivment.dao.GroutedrubbleOkMapper;
import com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubbleOk;
import com.supermap.pipedesign.pipechina.entitiesachivment.service.IGroutedrubbleOkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.sql.Timestamp;
import java.util.List;


/**
 * <p>
 * 水工保护表-浆砌石堡坎 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-14
 */
@Service("GroutedrubbleOkImpl")
public class GroutedrubbleOkImpl extends ServiceImpl<GroutedrubbleOkMapper, GroutedrubbleOk> implements IGroutedrubbleOkService {

    @Autowired
    private GroutedrubbleOkMapper groutedrubbleOkMapper;

    /**
    * 添加水工保护表-浆砌石堡坎信息
    *
    * @param groutedrubbleOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int insert(GroutedrubbleOk groutedrubbleOk) {

        //groutedrubbleOk.setUserId(JavaUtils.getUUID36());
        //groutedrubbleOk.setAccountPwd(LoginEncryption.getEncrypt(user.getAccountPwd()));
        return groutedrubbleOkMapper.insert(groutedrubbleOk);
    }

    /**
    * 删除水工保护表-浆砌石堡坎信息
    *
    * @param groutedrubbleOkId
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int delete(String groutedrubbleOkId) {
        return groutedrubbleOkMapper.deleteById(groutedrubbleOkId);
    }

    /**
    * 更新水工保护表-浆砌石堡坎信息
    *
    * @param groutedrubbleOk
    * @return int
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public int update(GroutedrubbleOk groutedrubbleOk) {
        return groutedrubbleOkMapper.updateById(groutedrubbleOk);
    }

    /**
    * 全部查询
    *
    * @param groutedrubbleOk
    * @return java.util.List<com.supermap.pipedesign.pipechina.entitiesachivment.entity.GroutedrubbleOk>
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public List<GroutedrubbleOk> list(GroutedrubbleOk groutedrubbleOk) {

        QueryWrapper<GroutedrubbleOk> queryWrapper = new QueryWrapper<>();

        return groutedrubbleOkMapper.selectList(queryWrapper);
    }

    /**
    * 分页查询
    *
    * @param current
    * @param size
    * @param startDate
    * @param endDate
    * @return com.baomidou.mybatisplus.core.metadata.IPage
    * @Date 2023-01-14
    * @auther eomer
    */
    @Override
    public IPage pageList(long current, long size, Timestamp startDate, Timestamp endDate) {

        IPage<GroutedrubbleOk> groutedrubbleOkIPage = new Page<>();
        groutedrubbleOkIPage.setCurrent(current);
        groutedrubbleOkIPage.setSize(size);

        QueryWrapper<GroutedrubbleOk> queryWrapper = new QueryWrapper<>();

        if(startDate!=null && endDate!=null){
            queryWrapper.between("col_create_time",startDate,endDate);
        }

        return groutedrubbleOkMapper.selectPage(groutedrubbleOkIPage, queryWrapper);
    }


}
