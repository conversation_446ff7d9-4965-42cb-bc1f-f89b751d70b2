package com.supermap.pipedesign.pipechina.entitiesachivment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.sql.Date;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 水工保护表-干砌石护坡 实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hp_drystone_slope_survey")
@ApiModel(value="DrystoneslopeOk对象", description="水工保护表-干砌石护坡")
public class DrystoneslopeOk implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "SmID")
    @TableField(value = "smid")
    private Integer smid;

    @ApiModelProperty(value = "设计人员ID")
    @TableField("designer_id")
    private String designerId;

    @ApiModelProperty(value = "设计时间")
    @TableField("modify_time")
    private Date modifyTime;

    @ApiModelProperty(value = "描述信息")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "上一版本编号")
    @TableField("sequence_org_id")
    private String sequenceOrgId;

    @ApiModelProperty(value = "项目ID")
    @TableField("project_id")
    private String projectId;

    @ApiModelProperty(value = "子工程ID")
    @TableField("sub_project_id")
    private String subProjectId;

    @ApiModelProperty(value = "终点横坐标")
    @TableField("end_easting")
    private String endEasting;

    @ApiModelProperty(value = "终点纵坐标")
    @TableField("end_eorthing")
    private String endEorthing;

    @ApiModelProperty(value = "水工保护名称")
    @TableField("hp_name")
    private String hpName;

    @ApiModelProperty(value = "起始里程")
    @TableField("initial_station")
    private String initialStation;

    @ApiModelProperty(value = "起始桩号")
    @TableField("start_station")
    private String startStation;

    @ApiModelProperty(value = "相对起始里程")
    @TableField("rel_startp_mileage")
    private String relStartpMileage;

    @ApiModelProperty(value = "起始横坐标")
    @TableField("initial_easting")
    private String initialEasting;

    @ApiModelProperty(value = "起始纵坐标")
    @TableField("initial_northing")
    private String initialNorthing;

    @ApiModelProperty(value = "终止里程")
    @TableField("end_milege")
    private String endMilege;

    @ApiModelProperty(value = "终止桩号")
    @TableField("end_station")
    private String endStation;

    @ApiModelProperty(value = "相对结束里程")
    @TableField("rel_endp_mileage")
    private String relEndpMileage;

    @ApiModelProperty(value = "坡长")
    @TableField("slope_length_l")
    private String slopeLengthL;

    @ApiModelProperty(value = "坡宽")
    @TableField("broad_slope")
    private String broadSlope;

    @ApiModelProperty(value = "工程量")
    @TableField("works")
    private String works;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "pkid",type = IdType.ASSIGN_UUID)
    private String pkid;


}
