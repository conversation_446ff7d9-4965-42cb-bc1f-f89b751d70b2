package com.supermap.tools.file;

import com.supermap.tools.base.JavaUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.util.List;
import java.util.Map;

/**
 * vinci-erp-api - ExcelExport
 *
 * @author: <PERSON><PERSON>
 * @create: 2020-08-02 23:49
 **/
public class ExcelExport {

    public static String HEAD_TYPE_DATE = "DateTime";//日期
    public static String HEAD_TYPE_TIMESTAMP = "TimeStamp";//时间戳
    public static String HEAD_TYPE_STRING = "String";//文本
    public static final int EXCEL_MAX_NUM = 65530;

    /**
     * 设置表头样式
     * @params [workbook]
     * @return org.apache.poi.hssf.usermodel.HSSFCellStyle
     * <AUTHOR>
     * @date 2020/8/2 23:35
     */
    public static HSSFCellStyle getHeaderStyle(HSSFWorkbook workbook) {
        // 创建样式
        HSSFFont font1 = workbook.createFont();
        font1.setFontName("黑体");// 字体加粗
        font1.setFontHeightInPoints((short) 12);// 设置字体大小
        font1.setBold(true);
        //设置表头样式
        HSSFCellStyle headerStyle = workbook.createCellStyle();

        //poi版本3.13备份
//        headerStyle.setAlignment(HSSFCellStyle.VERTICAL_CENTER);// 设置垂直居中
//        headerStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中
//        headerStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);// 设置边框
//        headerStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
//        headerStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
//        headerStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);

        //poi版本3.17测试
        headerStyle.setAlignment(HorizontalAlignment.CENTER);// 设置垂直居中
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 上下居中
        headerStyle.setBorderTop(BorderStyle.THIN);// 设置边框
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        headerStyle.setFont(font1);

        return headerStyle;
    }

    /**
     * 设置内容行样式
     * @params [workbook]
     * @return org.apache.poi.hssf.usermodel.HSSFCellStyle
     * <AUTHOR>
     * @date 2020/8/2 23:36
     */
    public static HSSFCellStyle getContentStyle(HSSFWorkbook workbook) {
        //设置内容行样式
        HSSFCellStyle contentStyle = workbook.createCellStyle();

        //poi版本3.13备份
//        contentStyle.setAlignment(HSSFCellStyle.VERTICAL_CENTER);// 设置垂直居中
//        contentStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中
//        contentStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);// 设置边框
//        contentStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
//        contentStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
//        contentStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);

        //poi版本3.17测试
        contentStyle.setAlignment(HorizontalAlignment.CENTER);// 设置垂直居中
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 上下居中
        contentStyle.setBorderTop(BorderStyle.THIN);// 设置边框
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);

        DataFormat format = workbook.createDataFormat();
        contentStyle.setDataFormat(format.getFormat("@"));
        return contentStyle;
    }

    /**
     * 设置列头
     *
     * @return org.apache.poi.hssf.usermodel.HSSFSheet
     * @params [workbook, headerStyle, contentStyle, sheetName, dateSize]
     * <AUTHOR>
     * @date 2020/8/3 0:16
     */
    public static HSSFSheet setSheetHeadReport(HSSFWorkbook workbook, HSSFCellStyle headerStyle, String sheetName, List<ExportHead> heads) {

        // 创建工作表对象并命名
        HSSFSheet sheet = workbook.createSheet(sheetName);
        sheet.createFreezePane(0, 1, 0, 1);

        int col_le = heads.size();//列数
        int widthInt = 32 * 100;//列宽

        //设置标题行列的默认宽度和高度
        for (int i = 0; i < col_le; i++) {
            sheet.setColumnWidth(i, widthInt);
        }
        HSSFRow sheetRow = sheet.createRow(0);
        sheetRow.setHeightInPoints(25f);// 设置行高度

        for (int i = 0; i < heads.size(); i++) {
            HSSFCell cell = sheetRow.createCell(i);
            cell.setCellValue(heads.get(i).getName());
            cell.setCellStyle(headerStyle);
        }

        return sheet;
    }

    /**
     * 设置内容
     * @param workbook
     * @param headerStyle
     * @param contentStyle
     * @param sheetName
     * @param heads
     * @param list
     * @return void
     * @Date 2021/4/7 10:59
     * @auther eomer
     */
    public static void createSheet(HSSFWorkbook workbook, HSSFCellStyle headerStyle, HSSFCellStyle contentStyle
            , String sheetName, List<ExportHead> heads, List<Map> list){
        //
        HSSFSheet sheet = setSheetHeadReport(workbook,headerStyle,sheetName,heads);

        if(!JavaUtils.isEmtryOrNull(list)){
            int sheetNum = 1;// 当前的sheet
            int maxNum = EXCEL_MAX_NUM; // - 单个sheet最大行数限定[实际值65536] (由于前面固定两行)
            int rowNum = 0;
            Map item = null;
            for(int i=0;i<list.size();i++){
                rowNum++;
                item = list.get(i);
                //添加数据
                HSSFRow contentRow = sheet.createRow(rowNum);//添加一行
                contentRow.setHeightInPoints(20f);// 设置行高度

                for (int j = 0; j < list.size(); j++) {
                    for (int k = 0; k < heads.size(); k++) {
                        //String type = heads.get(i).getType();
                        String key = heads.get(i).getKey();
                        // if (HEAD_TYPE_STRING.equals(type)) {
                        // if (HEAD_TYPE_DATE.equals(type)) {
                        // } else if (HEAD_TYPE_TIMESTAMP.equals(type)) {
                        String cellValue = JavaUtils.getStringByMap(item, key);
                        HSSFCell cell = contentRow.createCell(j);
                        cell.setCellValue(cellValue);
                        cell.setCellStyle(contentStyle);
                    }
                }

                //如果数据量大于Excel的最大值，就新建立一个sheet
                if(i > maxNum) {
                    maxNum = maxNum * 2;
                    rowNum = 0;
                    // 创建工作表对象并命名
                    sheetNum++;
                    // 创建表头
                    sheet = setSheetHeadReport(workbook,headerStyle,sheetName+"_"+sheetNum,heads);
                }
            }
        }
    }



}