package com.supermap.tools.file;

import com.supermap.pipedesign.pipechina.engineering.entity.WbsMetadatainfo;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 数据导入时的读取excel表格
 */
public class ImportExcel {
    public static List<Map<String, String>> redExcel(String filePath) throws Exception {
        File file = new File(filePath);
        if (!file.exists()) {
            try {
                throw new Exception("文件不存在!");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        InputStream in = null;
        try {
            in = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        // 读取整个Excel
//        HSSFWorkbook sheets = null;
        Workbook sheets = null;
        try {
//            sheets = new HSSFWorkbook(in);
            if (filePath.toLowerCase().endsWith("xls")) {
                sheets = new HSSFWorkbook(in);
            } else if (filePath.toLowerCase().endsWith("xlsx")) {
                sheets = new XSSFWorkbook(in);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 获取第一个表单Sheet
//        HSSFSheet sheetAt = sheets.getSheetAt(0);
        Sheet sheetAt = sheets.getSheetAt(0);
        ArrayList<Map<String, String>> list = new ArrayList<>();

        //默认第一行为标题行，i = 0
//        HSSFRow titleRow = sheetAt.getRow(0);
        Row titleRow = sheetAt.getRow(0);
        // 循环获取每一行数据
        for (int i = 1; i < sheetAt.getPhysicalNumberOfRows(); i++) {
//            HSSFRow row = sheetAt.getRow(i);
            Row row = sheetAt.getRow(i);
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            // 读取每一格内容
            for (int index = 0; index < row.getPhysicalNumberOfCells(); index++) {
                Cell titleCell = titleRow.getCell(index);
                Cell cell = row.getCell(index);
                cell.setCellType(CellType.STRING);

                if (cell.getStringCellValue().equals("")) {
                    continue;
                }
                map.put(getString(titleCell), getString(cell));
            }
            if (map.isEmpty()) {
                continue;
            }
            list.add(map);
        }
        in.close();
        return list;
    }

    /**
     * 数据导入时的读取excel多sheet表格
     */
    public static List<Map<String, List<Map<String, String>>>> redExcelSheets(String filePath) throws Exception {
        List<Map<String, List<Map<String, String>>>> list = new ArrayList<>();
        File file = new File(filePath);
        if (!file.exists()) {
            try {
                throw new Exception("文件不存在!");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        InputStream in = null;
        try {
            in = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        // 读取整个Excel
        Workbook sheets = null;
        try {
            if (filePath.toLowerCase().endsWith("xls")) {
                sheets = new HSSFWorkbook(in);
            } else if (filePath.toLowerCase().endsWith("xlsx")) {
                sheets = new XSSFWorkbook(in);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 获取第一个表单Sheet
//        HSSFSheet sheetAt = sheets.getSheetAt(0);
        Map<String, List<Map<String, String>>> sheetMap = new HashMap<>();
        for (int i = 0; i < Objects.requireNonNull(sheets).getNumberOfSheets(); i++) {
            //遍历每一个sheet
            Sheet sheetAt = sheets.getSheetAt(i);
            //默认第一行为标题行，i = 0
            Row titleRow = sheetAt.getRow(0);
            // 循环获取每一行数据
            List<Map<String, String>> sheetList = new ArrayList<>();
            for (int j = 1; j < sheetAt.getPhysicalNumberOfRows(); j++) {
                Row row = sheetAt.getRow(j);
                LinkedHashMap<String, String> map = new LinkedHashMap<>();
                // 读取每一格内容
                for (int index = 0; index < row.getPhysicalNumberOfCells(); index++) {
                    Cell titleCell = titleRow.getCell(index);
                    Cell cell = row.getCell(index);
                    cell.setCellType(CellType.STRING);
                    if (cell.getStringCellValue().equals("")) {
                        continue;
                    }
                    map.put(getString(titleCell), getString(cell));
                }
                sheetList.add(map);
                if (map.isEmpty()) {
                    continue;
                }
            }
            sheetMap.put(sheetAt.getSheetName(), sheetList);
        }
        list.add(sheetMap);
        in.close();
        return list;
    }

    /**
     * 数据导入时的读取excel表格——标头在左侧
     */
    public static List<WbsMetadatainfo> redExcelLeft(String filePath, String uuid) throws Exception {
        List<WbsMetadatainfo> wbsMetadatainfoList = new ArrayList<>();
        File file = new File(filePath);
        if (!file.exists()) {
            try {
                throw new Exception("文件不存在!");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        InputStream in = null;
        try {
            in = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        // 读取整个Excel
        Workbook sheets = null;
        try {
            if (filePath.toLowerCase().endsWith("xls")) {
                sheets = new HSSFWorkbook(Objects.requireNonNull(in));
            } else if (filePath.toLowerCase().endsWith("xlsx")) {
                sheets = new XSSFWorkbook(Objects.requireNonNull(in));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 获取第一个表单Sheet
        Sheet sheetAt = Objects.requireNonNull(sheets).getSheetAt(0);
        // 循环获取每一行数据
        for (int i = 0; i < sheetAt.getPhysicalNumberOfRows(); i++) {
            Row row = sheetAt.getRow(i);
            // 读取每一格内容
            WbsMetadatainfo wbsMetadatainfo = new WbsMetadatainfo();
            Cell titleCell = row.getCell(0);
            Cell cell = row.getCell(1);
            cell.setCellType(CellType.STRING);
            if (cell.getStringCellValue().equals("")) {
                continue;
            }
            String guarantee_time = "";
            if (getString(titleCell).contains("时间") || getString(titleCell).contains("日期")) {
                guarantee_time = ExcelDoubleToDate(getString(cell));
                wbsMetadatainfo.setFieldname(getString(titleCell));
                wbsMetadatainfo.setFieldvalue(guarantee_time);
            } else {
                wbsMetadatainfo.setFieldname(getString(titleCell));
                wbsMetadatainfo.setFieldvalue(getString(cell));
            }
            wbsMetadatainfo.setFileid(uuid);
            wbsMetadatainfoList.add(wbsMetadatainfo);
        }
        return wbsMetadatainfoList;
    }

    /**
     * 将单元格信息转成String类型
     *
     * @param xssfCell
     * @return
     */
    public static String getString(Cell xssfCell) {
        if (xssfCell == null) {
            return "";
        }
        if (xssfCell.getCellTypeEnum() == CellType.NUMERIC) {
            return String.valueOf(xssfCell.getNumericCellValue());
        } else if (xssfCell.getCellTypeEnum() == CellType.BOOLEAN) {
            return String.valueOf(xssfCell.getBooleanCellValue());
        } else {
            return xssfCell.getStringCellValue();
        }
    }

    //解析excel时间类型
    public static String ExcelDoubleToDate(String strDate) {
        if (strDate.length() == 5) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date tDate = DoubleToDate(Double.parseDouble(strDate));
                return sdf.format(tDate);
            } catch (Exception e) {
                e.printStackTrace();
                return strDate;
            }
        }
        return strDate;
    }

    //解析Excel日期格式
    public static Date DoubleToDate(Double dVal) {
        Date tDate = new Date();
        long localOffset = tDate.getTimezoneOffset() * 60000; //系统时区偏移 1900/1/1 到 1970/1/1 的 25569 天
        tDate.setTime((long) ((dVal - 25569) * 24 * 3600 * 1000 + localOffset));
        return tDate;
    }

    public static void main(String[] args) {
        try {
            System.out.println(redExcelSheets("D:\\work\\官网规则\\更新4\\测试数据（正确）\\水工保护\\PHO2T01-PL001-B09#EGE-DW-0101 水工保护.xlsx"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
