package com.supermap.tools.gson;

import com.google.gson.JsonSyntaxException;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;

/**
 * DoubleTypeAdapter
 *
 * <AUTHOR>
 * @date 2020/10/30 13:46
 */
public class DoubleTypeAdapter extends TypeAdapter<Number> {

    @Override
    public void write(JsonWriter out, Number value)
            throws IOException {
        out.value(value);// 调整格式，保留2位小数
//        out.value(new BigDecimal(value.toString()).setScale(2, BigDecimal.ROUND_HALF_UP));
    }

    @Override
    public Number read(JsonReader in) throws IOException {
        if (in.peek() == JsonToken.NULL) {
            in.nextNull();
            return null;
        }
        try {
            String result = in.nextString();
            if ("".equals(result)) {
                return null;
            }
            return Double.parseDouble(result);
        } catch (NumberFormatException e) {
            throw new JsonSyntaxException(e);
        }
    }

}
