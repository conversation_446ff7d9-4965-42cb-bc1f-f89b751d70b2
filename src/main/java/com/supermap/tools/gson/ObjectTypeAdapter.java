package com.supermap.tools.gson;

import com.google.gson.TypeAdapter;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @program: weixin
 * @description:
 * @author: Eomer
 * @create: 2019-02-26 14:19
 **/
public class ObjectTypeAdapter extends TypeAdapter<Object> {
    @Override
    public void write(JsonWriter jsonWriter, Object o) throws IOException {
    }

    @Override
    public Object read(JsonReader in) throws IOException {
        JsonToken token = in.peek();
        switch (token) {
            case BEGIN_ARRAY:
                List<Object> list = new ArrayList<Object>();
                in.beginArray();
                while (in.hasNext()) {
                    list.add(read(in));
                }
                in.endArray();
                return list;

            case BEGIN_OBJECT:
                Map<String, Object> map = new LinkedTreeMap<String, Object>();
                in.beginObject();
                while (in.hasNext()) {
                    map.put(in.nextName(), read(in));
                }
                in.endObject();
                return map;

            case STRING:
                return in.nextString();

            case NUMBER:
                /**
                 * 改写数字的处理逻辑，将数字值分为整型与浮点型。
                 */
                String dbStr = in.nextString();

                double dbNum = Double.parseDouble(dbStr);

                // 数字超过long的最大值，返回浮点类型
                if (dbStr.indexOf(".") > -1 || dbNum > Long.MAX_VALUE) {
                    return dbNum;
                } else {
                    return Long.parseLong(dbStr);
                }

            case BOOLEAN:
                return in.nextBoolean();

            case NULL:
                in.nextNull();
                return null;

            default:
                throw new IllegalStateException();
        }
    }

}