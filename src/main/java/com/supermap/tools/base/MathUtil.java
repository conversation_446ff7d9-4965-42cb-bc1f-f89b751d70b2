package com.supermap.tools.base;

import java.math.BigDecimal;

/**
 * MathUtil
 *
 * <AUTHOR>
 * @date 21/6/16 11:40
 */
public class MathUtil {

    /**
     * BigDecimal 转  double
     * @param p1
     * @return double
     * @Date 21/6/9 19:02
     * @auther eomer
     */
    public static double bigDecimalToDouble(BigDecimal p1){
        return p1.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
    /**
     * BigDecimal 转  double
     * @param p1
     * @param scale 精确到几位
     * @return double
     * @Date 21/6/9 19:02
     * @auther eomer
     */
    public static double bigDecimalToDouble(BigDecimal p1, int scale){
        return p1.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static double bigDecimalToDouble(double m1, int scale){
        BigDecimal p1 = new BigDecimal(Double.toString(m1));
        return p1.setScale(scale, BigDecimal.ROUND_UP).doubleValue();
    }

    /**
     * 加法运算
     * @param m1
     * @param m2
     * @return double
     * @Date 2020/10/22 9:58
     * @auther eomer
     */
    public static double sumForDouble(double m1, double m2) {
        BigDecimal p1 = new BigDecimal(Double.toString(m1));
        BigDecimal p2 = new BigDecimal(Double.toString(m2));
        return bigDecimalToDouble(p1.add(p2));
    }

    public static double sumForDouble(double m1, double m2, double m3) {
        BigDecimal p1 = new BigDecimal(Double.toString(m1));
        BigDecimal p2 = new BigDecimal(Double.toString(m2));
        BigDecimal p3 = new BigDecimal(Double.toString(m3));
        return bigDecimalToDouble(p1.add(p2).add(p3));
    }

    public static double sumForDouble(double m1, double m2, double m3, double m4) {
        BigDecimal p1 = new BigDecimal(Double.toString(m1));
        BigDecimal p2 = new BigDecimal(Double.toString(m2));
        BigDecimal p3 = new BigDecimal(Double.toString(m3));
        BigDecimal p4 = new BigDecimal(Double.toString(m4));
        //ROUND_UNNECESSARY
        return bigDecimalToDouble(p1.add(p2).add(p3).add(p4));//四舍五入
    }

    /**
     * 减法运算
     * @param m1
     * @param m2
     * @return double
     * @Date 2020/10/22 10:04
     * @auther eomer
     */
    public static double subForDouble(double m1, double m2) {
        BigDecimal p1 = new BigDecimal(Double.toString(m1));
        BigDecimal p2 = new BigDecimal(Double.toString(m2));
        return bigDecimalToDouble(p1.subtract(p2));
    }

    /**
     * 乘法运算
     * @param m1
     * @param m2
     * @return
     */
    public static double mulDouble(double m1, double m2) {
        BigDecimal p1 = new BigDecimal(Double.toString(m1));
        BigDecimal p2 = new BigDecimal(Double.toString(m2));
        return bigDecimalToDouble(p1.multiply(p2));
    }

    public static double divDouble(double m1, double m2) {
        return divDouble(m1, m2,2);
    }

    /**
     *  除法运算
     *   @param   m1
     *   @param   m2
     *   @param   scale
     *   @return
     */
    public static double divDouble(double m1, double m2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("Parameter error");
        }
        BigDecimal p1 = new BigDecimal(Double.toString(m1));
        BigDecimal p2 = new BigDecimal(Double.toString(m2));
        if (p2.doubleValue()==BigDecimal.ZERO.doubleValue()) {
            return BigDecimal.ZERO.doubleValue();
        }
        return p1.divide(p2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 比较大小
     * @param m1
     * @param m2
     * @return int < 0 第二位数大！ > 0 第一位数大！== 0 两位数一样大！
     * @Date 2020/11/6 13:54
     * @auther eomer
     */
    public static int compareDouble(double m1, double m2) {
        BigDecimal p1 = new BigDecimal(Double.toString(m1));
        BigDecimal p2 = new BigDecimal(Double.toString(m2));
        return p1.compareTo(p2);
    }

}

