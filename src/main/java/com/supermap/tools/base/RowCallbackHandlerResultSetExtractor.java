package com.supermap.tools.base;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowCallbackHandler;

import java.sql.ResultSet;
import java.sql.SQLException;

public class Row<PERSON>allbackHandlerResultSetExtractor implements ResultSetExtractor<Object> {

    private final RowCallbackHandler rch;

    public RowCallbackHandlerResultSetExtractor(RowCallbackHandler rch) {
        this.rch = rch;
    }


    @Override
    public Object extractData(ResultSet resultSet) throws SQLException, DataAccessException {
        while(resultSet.next()) {
            this.rch.processRow(resultSet);
        }

        return null;
    }
}
