<sml:Scene>
<sml:Version>20090106</sml:Version>
<sml:Name>Scene</sml:Name>
<sml:SceneType>NONEARTH</sml:SceneType>
<sml:DynamicProjection>FALSE</sml:DynamicProjection>
<sml:Bounds>
<sml:Left>0.000000</sml:Left>
<sml:Top>0.000000</sml:Top>
<sml:Right>0.000000</sml:Right>
<sml:Bottom>0.000000</sml:Bottom>
</sml:Bounds>
<sml:BasicSceneSetting>
<sml:EnvironmentParameters>
<sml:EnvironmentLightAmbient>RGBA(0.600000,0.600000,0.600000,1.000000)</sml:EnvironmentLightAmbient>
<sml:EnvironmentLightDiffuse>RGBA(0.800000,0.800000,0.800000,1.000000)</sml:EnvironmentLightDiffuse>
<sml:BackgroundColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:BackgroundColor>
<sml:ImageBrightness>1.000000</sml:ImageBrightness>
<sml:ImageConstrast>1.000000</sml:ImageConstrast>
<sml:ImageHue>0.000000</sml:ImageHue>
<sml:ImageSaturation>1.000000</sml:ImageSaturation>
<sml:ImageGamma>1.000000</sml:ImageGamma>
<sml:TerrainExaggeration>1.000000</sml:TerrainExaggeration>
<sml:BasicTerrainHeight>0.000000</sml:BasicTerrainHeight>
<sml:RenderParameter>
<sml:OverlapDisplayed>TRUE</sml:OverlapDisplayed>
<sml:SceneOverlapDisplayedOptions>
<sml:OverlappedSpaceSize>
<sml:X>0</sml:X>
<sml:Y>0</sml:Y>
</sml:OverlappedSpaceSize>
<sml:IconWithTextDisplay>FALSE</sml:IconWithTextDisplay>
</sml:SceneOverlapDisplayedOptions>
</sml:RenderParameter>
<sml:MultiLevel>0</sml:MultiLevel>
<sml:HighLightMode>TRUE</sml:HighLightMode>
</sml:EnvironmentParameters>
<sml:Cameras>
<sml:Camera>
<sml:Name>name</sml:Name>
<sml:FOV>0.7019543957</sml:FOV>
<sml:Aspect>1.5768725361</sml:Aspect>
<sml:FarDistance>2401047890.966532</sml:FarDistance>
<sml:NearDistance>5144.370000</sml:NearDistance>
<sml:camPosition>
<sml:x>1.896998</sml:x>
<sml:y>0.629191</sml:y>
<sml:z>10288740.000000</sml:z>
</sml:camPosition>
<sml:Orientation>
<sml:x>0.000000000000000</sml:x>
<sml:y>0.000000000000000</sml:y>
<sml:z>0.000000000000000</sml:z>
<sml:w>1.000000000000000</sml:w>
</sml:Orientation>
<sml:ProjectionType3D>PT_PERSPECTIVE</sml:ProjectionType3D>
<sml:CollisionDetection>FALSE</sml:CollisionDetection>
<sml:CollisionDistance>0.50</sml:CollisionDistance>
</sml:Camera>
</sml:Cameras>
<sml:Stereo>
<sml:StereoEnable>FALSE</sml:StereoEnable>
<sml:StereoMode>0</sml:StereoMode>
<sml:StereoParallaxMode>0</sml:StereoParallaxMode>
<sml:StereoCameraSeparation>0.050000</sml:StereoCameraSeparation>
<sml:StereoCameraAngle>-0.012000</sml:StereoCameraAngle>
</sml:Stereo>
<sml:MultiViewportMode>MVM_NONE</sml:MultiViewportMode>
<sml:Fog>
<sml:Type>FOG_NONE</sml:Type>
<sml:Color>RGBA(1.0000000000,1.0000000000,1.0000000000,1.0000000000)</sml:Color>
<sml:Start>0.0</sml:Start>
<sml:End>1.0</sml:End>
<sml:Density>1.0</sml:Density>
<sml:FogAltitude>20000.000000</sml:FogAltitude>
</sml:Fog>
<sml:Star3D>
<sml:Visible>TRUE</sml:Visible>
</sml:Star3D>
<sml:Atmosphere>
<sml:Visible>TRUE</sml:Visible>
</sml:Atmosphere>
<sml:Ocean>
<sml:Visible>FALSE</sml:Visible>
</sml:Ocean>
<sml:OceanEffect>
<sml:Visible>FALSE</sml:Visible>
<sml:ReflectEnable>FALSE</sml:ReflectEnable>
<sml:Quality>0</sml:Quality>
<sml:SprayEnable>TRUE</sml:SprayEnable>
<sml:SeaLevel>50.000000</sml:SeaLevel>
<sml:WindSpeed>10.000000</sml:WindSpeed>
<sml:WindDirection>0.000000</sml:WindDirection>
</sml:OceanEffect>
<sml:GlobalImage>
<sml:Visible>TRUE</sml:Visible>
<sml:Transparency>1.00</sml:Transparency>
<sml:ReceiveVolume>FALSE</sml:ReceiveVolume>
</sml:GlobalImage>
<sml:Underground>
<sml:Visible>FALSE</sml:Visible>
<sml:Depth>1000.000000</sml:Depth>
<sml:UndergroundColor>RGBA(0.0000000000,0.0000000000,0.0000000000,1.0000000000)</sml:UndergroundColor>
</sml:Underground>
<sml:Skirt>FALSE</sml:Skirt>
<sml:LatLongGrid>
<sml:Visible>FALSE</sml:Visible>
<sml:VisibleText>FALSE</sml:VisibleText>
</sml:LatLongGrid>
<sml:StatusBar>
<sml:Visible>TRUE</sml:Visible>
</sml:StatusBar>
<sml:Scale>
<sml:Visible>TRUE</sml:Visible>
</sml:Scale>
<sml:ControlPlane>
<sml:Visible>TRUE</sml:Visible>
</sml:ControlPlane>
<sml:Cross>
<sml:Visible>TRUE</sml:Visible>
</sml:Cross>
<sml:Sun>
<sml:Visible>TRUE</sml:Visible>
<sml:SunTimeZoneInfo>-480</sml:SunTimeZoneInfo>
<sml:SunDateTime>2023-03-08 01:10:38</sml:SunDateTime>
</sml:Sun>
<sml:EnvironmentLight>
<sml:EnvironmentMap>kiara_6_afternoon_2k_ibl.ktx2</sml:EnvironmentMap>
</sml:EnvironmentLight>
</sml:BasicSceneSetting>
<sml:Terrains/>
<sml:GlobalTinTerrain>
<sml:NormalMapEnable>FALSE</sml:NormalMapEnable>
</sml:GlobalTinTerrain>
<sml:ReferenceGeodeticPoint>
<sml:x>108.923611</sml:x>
<sml:y>34.540833</sml:y>
<sml:z>0.000000</sml:z>
</sml:ReferenceGeodeticPoint>
<sml:DynamicSelection>FALSE</sml:DynamicSelection>
<sml:LayerIDUnit>8</sml:LayerIDUnit>
<sml:TransparencyOptimization>FALSE</sml:TransparencyOptimization>
<sml:DepthOptimization>FALSE</sml:DepthOptimization>
<sml:ShadowDarkness>0.000000</sml:ShadowDarkness>
<sml:ClipDataSourceAlias/>
</sml:Scene>
