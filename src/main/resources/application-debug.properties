# ??????
# ????? - ???????????
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# ??Redis???????
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.timeout=2000ms

# ????
server.port=9605

# ????
project.version=1.07.33-DEBUG
project.buildTime=2024-01-01 00:00:00

# ?????????
spring.web.isopenurl=false

# ????
logging.level.com.supermap=DEBUG
logging.level.root=INFO

# ??????
spring.cloud.servicecomb.discovery.enabled=false
spring.cloud.servicecomb.config.enabled=false

# ?????????????
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
