#\u6570\u636E\u5E93\u8FDE\u63A5
# \u5730\u5740\u5185\u5E94\u8BE5\u4E3A// \u800C\u4E0D\u662F :
#spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

#PipeChina_PLD_Engineering \u5DE5\u7A0B
spring.datasource.druid.engineering.dbname=${db.name.engineering}
spring.datasource.druid.engineering.driver-class-name=com.p6spy.engine.spy.P6SpyDriver
spring.datasource.druid.engineering.url=jdbc:p6spy:postgresql://${db.current.ip}:${db.current.port}/${db.name.engineering}?currentSchema=${db.current.schema}
spring.datasource.druid.engineering.username=${db.current.username}
spring.datasource.druid.engineering.password=${db.current.password}
# Druid \u914D\u7F6E
#--\u7EBF\u7A0B\u6C60\u914D\u7F6E
#\u914D\u7F6E\u521D\u59CB\u5316\u5927\u5C0F\u3001\u6700\u5C0F\u3001\u6700\u5927
#\u521D\u59CB\u5316\u8FDE\u63A5\u5927\u5C0F
spring.datasource.druid.engineering.initialSize=9
#\u8FDE\u63A5\u6700\u5C0F\u7A7A\u95F2
spring.datasource.druid.engineering.minIdle=6
#\u8BBE\u7F6E\u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570
spring.datasource.druid.engineering.maxActive=10
#\u914D\u7F6E\u83B7\u53D6\u8FDE\u63A5\u7B49\u5F85\u8D85\u65F6\u7684\u65F6\u95F4
spring.datasource.druid.engineering.maxWait=60000
#\u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.engineering.minEvictableIdleTimeMillis=300000
#\u7528\u6765\u68C0\u6D4B\u8FDE\u63A5\u662F\u5426\u6709\u6548\u7684sql\uFF0C\u8981\u6C42\u662F\u4E00\u4E2A\u67E5\u8BE2\u8BED\u53E5\u3002\u5982\u679CvalidationQuery\u4E3Anull\uFF0CtestOnBorrow\u3001testOnReturn\u3001testWhileIdle\u90FD\u4E0D\u4F1A\u5176\u4F5C\u7528\u3002
#\u5728mysql\u4E2D\u901A\u5E38\u4E3Aselect 'x'\uFF0C\u5728oracle\u4E2D\u901A\u5E38\u4E3A select 1 from dual
#spring.datasource.druid.engineering.validationQuery=SELECT 'x'
#spring.datasource.druid.engineering.timeBetweenEvictionRunsMillis=1000
#\u5355\u4F4D\uFF1A\u79D2\uFF0C\u68C0\u6D4B\u8FDE\u63A5\u662F\u5426\u6709\u6548\u7684\u8D85\u65F6\u65F6\u95F4\u3002\u5E95\u5C42\u8C03\u7528jdbc Statement\u5BF9\u8C61\u7684void setQueryTimeout(int seconds)\u65B9\u6CD5
spring.datasource.druid.engineering.validation-query-timeout=1
#\u7533\u8BF7\u8FDE\u63A5\u65F6\u6267\u884CvalidationQuery\u68C0\u6D4B\u8FDE\u63A5\u662F\u5426\u6709\u6548\uFF0C[\u505A\u4E86\u8FD9\u4E2A\u914D\u7F6E\u4F1A\u964D\u4F4E\u6027\u80FD]\u3002default\uFF1Atrue \uFF08\u7ECF\u8FC7\u6D4B\u8BD5\u6548\u7387\u4E3A10\u500D\u5DE6\u53F3\uFF0C\u5982\u679C\u7F51\u901F\u6162\u5BB9\u6613\u51FA\u73B0\u8FDE\u63A5\u5931\u8D25-yjn\uFF09
spring.datasource.druid.engineering.testOnBorrow=true
#\u5F52\u8FD8\u8FDE\u63A5\u65F6\u6267\u884CvalidationQuery\u68C0\u6D4B\u8FDE\u63A5\u662F\u5426\u6709\u6548\uFF0C[\u505A\u4E86\u8FD9\u4E2A\u914D\u7F6E\u4F1A\u964D\u4F4E\u6027\u80FD]
spring.datasource.druid.engineering.testOnReturn=true
#\u5EFA\u8BAE\u914D\u7F6E\u4E3Atrue\uFF0C\u4E0D\u5F71\u54CD\u6027\u80FD\uFF0C\u5E76\u4E14\u4FDD\u8BC1\u5B89\u5168\u6027\u3002\u7533\u8BF7\u8FDE\u63A5\u7684\u65F6\u5019\u68C0\u6D4B\uFF0C\u5982\u679C\u7A7A\u95F2\u65F6\u95F4\u5927\u4E8EtimeBetweenEvictionRunsMillis\uFF0C\u6267\u884CvalidationQuery\u68C0\u6D4B\u8FDE\u63A5\u662F\u5426\u6709\u6548
spring.datasource.druid.engineering.test-while-idle=true
#\u6709\u4E24\u4E2A\u542B\u4E49\uFF1A\u9ED8\u8BA41\u5206\u949F
#1) Destroy\u7EBF\u7A0B\u4F1A\u68C0\u6D4B\u8FDE\u63A5\u7684\u95F4\u9694\u65F6\u95F4\uFF0C\u5982\u679C\u8FDE\u63A5\u7A7A\u95F2\u65F6\u95F4\u5927\u4E8E\u7B49\u4E8EminEvictableIdleTimeMillis\u5219\u5173\u95ED\u7269\u7406\u8FDE\u63A5\u3002
#2) testWhileIdle\u7684\u5224\u65AD\u4F9D\u636E\uFF0C\u8BE6\u7EC6\u770BtestWhileIdle\u5C5E\u6027\u7684\u8BF4\u660E
spring.datasource.druid.engineering.time-between-eviction-runs-millis=60000
# \u8FDE\u63A5\u4FDD\u6301\u7A7A\u95F2\u800C\u4E0D\u88AB\u9A71\u9010\u7684\u6700\u5C0F\u65F6\u95F4
spring.datasource.druid.engineering.min-evictable-idle-time-millis=600000
# \u8FDE\u63A5\u4FDD\u6301\u7A7A\u95F2\u800C\u4E0D\u88AB\u9A71\u9010\u7684\u6700\u5927\u65F6\u95F4
spring.datasource.druid.engineering.max-evictable-idle-time-millis=900000
#\u6253\u5F00removeAbandoned\u529F\u80FD\uFF08\u653E\u7F6E\u8FDE\u63A5\u6CC4\u9732\uFF09
spring.datasource.druid.engineering.removeAbandoned=true
#1800\u79D2\uFF0C\u4E5F\u5C31\u662F30\u5206\u949F , \u907F\u514D\u65E0\u6CD5\u53CA\u65F6\u56DE\u6536
spring.datasource.druid.engineering.removeAbandonedTimeout=1800
# \u5173\u95EDabanded\u8FDE\u63A5\u65F6\u8F93\u51FA\u9519\u8BEF\u65E5\u5FD7,\u56DE\u6536\u662F\u65E5\u5FD7
spring.datasource.druid.engineering.logAbandoned=true
# \u8FC7\u6EE4\u5668 stat,wall,slf4j
spring.datasource.druid.engineering.filters=stat,slf4j
# \u89E3\u51B3\uFF1A\u8FDD\u53CDsql\u6CE8\u5165\uFF1A\u591A\u58F0\u660E\u4E0D\u88AB\u5141\u8BB8 \u95EE\u9898
spring.datasource.druid.engineering.filter.wall.config.multi-statement-allow=true
# \u8BB0\u5F55\u6162SQL
spring.datasource.druid.engineering.filter.stat.enabled=false
spring.datasource.druid.engineering.filter.stat.log-slow-sql=false
# \u8D85\u8FC710\u79D2\u4E3A\u6162SQL
spring.datasource.druid.engineering.filter.stat.slow-sql-millis=10000
#
#\u76D1\u63A7\u914D\u7F6E
# WebStatFilter\u914D\u7F6E\uFF0C\u8BF4\u660E\u8BF7\u53C2\u8003Druid Wiki\uFF0C\u914D\u7F6E_\u914D\u7F6EWebStatFilter
#\u662F\u5426\u542F\u7528StatFilter\u9ED8\u8BA4\u503Cfalse
spring.datasource.druid.engineering.web-stat-filter.enabled=false
spring.datasource.druid.engineering.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*
# StatViewServlet\u914D\u7F6E\uFF0C\u8BF4\u660E\u8BF7\u53C2\u8003Druid Wiki\uFF0C\u914D\u7F6E_StatViewServlet\u914D\u7F6E
#\u662F\u5426\u542F\u7528StatViewServlet\uFF08\u76D1\u63A7\u9875\u9762\uFF09\u9ED8\u8BA4\u503C\u4E3Afalse\uFF08\u8003\u8651\u5230\u5B89\u5168\u95EE\u9898\u9ED8\u8BA4\u5E76\u672A\u542F\u52A8\uFF0C\u5982\u9700\u542F\u7528\u5EFA\u8BAE\u8BBE\u7F6E\u5BC6\u7801\u6216\u767D\u540D\u5355\u4EE5\u4FDD\u969C\u5B89\u5168\uFF09
spring.datasource.druid.engineering.stat-view-servlet.enabled=false
spring.datasource.druid.engineering.stat-view-servlet.url-pattern=/druid/*
#spring.datasource.druid.engineering.stat-view-servlet.reset-enable=
spring.datasource.druid.engineering.stat-view-servlet.login-username=supermap
spring.datasource.druid.engineering.stat-view-servlet.login-password=12345678
#\u767D\u540D\u5355
spring.datasource.druid.engineering.stat-view-servlet.allow=
#spring.datasource.druid.engineering.stat-view-servlet.deny=
# Spring\u76D1\u63A7\u914D\u7F6E\uFF0C\u8BF4\u660E\u8BF7\u53C2\u8003Druid Github Wiki\uFF0C\u914D\u7F6E_Druid\u548CSpring\u5173\u8054\u76D1\u63A7\u914D\u7F6E
# Spring\u76D1\u63A7AOP\u5207\u5165\u70B9\uFF0C\u5982x.y.z.service.*,\u914D\u7F6E\u591A\u4E2A\u82F1\u6587\u9017\u53F7\u5206\u9694
#spring.datasource.druid.engineering.aop-patterns=




