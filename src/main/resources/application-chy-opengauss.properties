#\u6570\u636E\u5E93\u8FDE\u63A5
# \u5730\u5740\u5185\u5E94\u8BE5\u4E3A// \u800C\u4E0D\u662F :
#spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
db.current.dbtype=yukon
db.current.ip=*************
db.current.port=5432
db.current.username=pipechina
db.current.password=Chinapipe0430
#db.current.password=ENC(Qb8ktoqm19YSi3XLKs9Vo9WOKU4oB9KG)
# \u6570\u636E\u5E93\u6A21\u5F0F\u914D\u7F6E
db.current.schema=test
db.name.metadata = PipeChina_PLD_MetaData
db.name.rules = PipeChina_PLDS_Rules
db.name.inspectroutes = pipechina_pld_inspectroutes
db.name.entitiesachivment = PipeChina_EntitiesAchivments_0
db.name.engineering = PipeChina_PLD_Engineering
db.name.drawingtemplate = PipeChina_PLD_DrawingTemplate

iserver.ip=localhost
iserver.port=8090
iserver.username=admin
iserver.password=SuperMap@2023

spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.shutdown-timeout=100

#\u6750\u6599\u5E93\u540C\u6B65\u914D\u7F6E
material.url=http://************:8081/mdm-mcc-api/mcc/externally/externallyIdentFind

#jasypt.encryptor.password=pipechina
#jasypt.encryptor.algorithm=PBEWithMD5AndDES
#jasypt.encryptor.iv-generator-classname=org.jasypt.iv.NoIvGenerator

workseed.font.fontname=\u9ed1\u4f53
workseed.font.size=12
workseed.font.height=42.5
workseed.font.color=black
workseed.font.outlinecolor=white
workseed.font.outlinewidth=2

gisdatatools.ip=localhost
gisdatatools.port=3584