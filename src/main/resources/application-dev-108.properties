# \u6570\u636E\u5E93\u914D\u7F6E
# yukon or postgresql
db.current.dbtype=yukon
db.current.ip=************
db.current.port=52002
db.current.username=pipechina
db.current.password=%TGB6yhn&^%$
# \u6570\u636E\u5E93\u6A21\u5F0F\u914D\u7F6E
db.current.schema=dev

db.name.metadata = PipeChina_PLD_MetaData
db.name.rules = PipeChina_PLDS_Rules
db.name.inspectroutes = PipeChina_PLD_InspectRoutes
db.name.entitiesachivment = PipeChina_EntitiesAchivments_0
db.name.engineering = PipeChina_PLD_Engineering
db.name.drawingtemplate = PipeChina_PLD_DrawingTemplate

# download???????
iserver.workspacepath=D:\\Data\\workspace
# iserver??????
iserver.deliveryPath=D:\\Data\\workspace
iserver.ip=127.0.0.1
iserver.port=8090
iserver.username=admin
iserver.password=supermap12!

# iserver??????ip
iserver.outside.ipport=http://localhost:8090
iserver.delivery.ipport=
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.shutdown-timeout=100

yukon.dbservice-url=:52000/pipechina/services/db/createspacialdb
yukon.dbservice-url-delete=:52000/pipechina/services/db/deletedb

#\u6750\u6599\u5E93\u540C\u6B65\u914D\u7F6E
material.url=http://**************:8081/mdm-mcc-api/mcc/externally/externallyIdentFind

workseed.font.fontname=\u9ed1\u4f53
workseed.font.size=12
workseed.font.height=42.5
workseed.font.color=black
workseed.font.outlinecolor=white
workseed.font.outlinewidth=2

#\u79cd\u5b50\u5de5\u4f5c\u7a7a\u95f4\u751f\u6210\u65b9\u5f0f\uff1ainside(\u5728\u670d\u52a1\u5185\u90e8\u5904\u7406)|outside(\u5728\u670d\u52a1\u5916\u90e8\u5904\u7406)
workseed.generationmethod=inside
#\u5e76\u884c\u6267\u884c\u7684\u6700\u5927\u7ebf\u7a0b\u6570\u6216\u8005\u5916\u90e8\u7684\u8fdb\u7a0b\u6570\uff0c\u7528\u4e8e\u4fdd\u8bc1\u5728\u9ad8\u5e76\u53d1\u60c5\u51b5\u4e0b\u5404\u4e2a\u8bf7\u6c42\u6392\u961f\u6267\u884c\uff0c\u4ece\u800c\u786e\u4fdd\u670d\u52a1\u7684\u7a33\u5b9a\u6027
workseed.maxparallelprocesscount=10

gisdatatools.ip=localhost
gisdatatools.port=3584

javapath=C:\\java\\corretto-1.8.0_382\\bin
externaljarpath=C:\\CWorkspaceSeedBuildTool-1.0-SNAPSHOT.jar


download.data.puretabulardatasetnames=edit_group,cross_band_point

# web??????
web.license.isopen=false
web.license.ip=*************
web.license.port=52083
# web?????id
web.license.menu=65400,65401,65402,65403,65404

toSqliteData.engineering_only=wbs_engineering
toSqliteData.engineering=wbs_design,wbs_design_scheme,wbs_design_scheme_route,wbs_design_se_file_upload_recor,wbs_design_person,wbs_designunit,wbs_e_s_leader,wbs_e_s_u_f_m_workbag,wbs_e_s_u_f_major,wbs_e_s_u_functionarea,wbs_e_s_unit,wbs_engineering_sub,wbs_submit_entrust,wbs_submit_entrust_part,wbs_engineering_versions,wbs_submit_entrust_content,wbs_submit_entrust_file,wbs_submit_entrust_file_ref,wbs_workbag_file,wbs_workbag_file_upload_record,wbs_qi_meta_data_info,wbs_osgb_config,wbs_submit_entrust_dataset_relation
toSqliteData.engineering_no_project_id=wbs_subjects,wbs_dictionary,wbs_construction_stage,wbs_subject_data,wbs_rules_tables,wbs_rules_tables_field,wbs_user,wbs_prjcoordsys_info

toSqliteData.metadata=pld_md_chartlet,pld_md_dataset_field,pld_md_dataset_info,pld_md_directories,pld_md_entity_datasref,pld_md_entitytype_styles,pld_md_entitytype_styles_parts,pld_md_entitytype_styles_parts_pic,pld_md_entitytype_theme_styles,pld_md_entitytype_theme_styles_parts,pld_md_entitytype_theme_styles_parts_pic,pld_md_entitytypes,pld_md_entitytypes_interpolation,pld_md_entitytypes_interpolation_field,pld_md_entitytypes_interpolation_field_value,pld_md_entitytypes_rule,pld_md_entitytypes_rule_algorit,pld_md_write_way_att,pld_dataset_field_writeway,pld_dataset_field_writeway_value
toSqliteData.metadata_no_project_id=pld_md_symbols,pld_md_datasource_info,pld_md_dataset_info_tem,pld_md_directoryresref,pld_md_dataset_field_temint,pld_md_entitytypes_group_tem

toSqliteData.rules=qi_general_section_anti_corrosion,qi_bend_section,qi_crossing_section,qi_common_tubing,qi_build_sidewalk_line,qi_e_u_fun,qi_e_u_fun_field,qi_elbow_bend_line_section,qi_engineering_unit,qi_entitytype_derive,qi_entitytype_derive_field,qi_entitytype_derive_stage,qi_entitytype_merge,qi_entitytype_merge_entitytype,qi_entitytype_merge_stage,qi_excavation_through,qi_fashi_configure,qi_fashi_create,qi_general_drawing,qi_general_section_slope,qi_high_consequence_area_identify,qi_hydraulic_protection,qi_intelligent_line_selection_rule_list,qi_intelligent_routing_rules_catalog,qi_line_intersect_layer,qi_line_intersect_line,qi_line_reference_object,qi_line_region_create_line,qi_lines_angle,qi_lining_fracture_surface,qi_longitudinal_line,qi_material,qi_material_e_u_fun,qi_material_e_u_fun_field,qi_material_e_u_fun_operation,qi_material_e_u_fun_operation_unit,qi_material_item,qi_material_template,qi_material_template_unit,qi_material_unit,qi_on_line,qi_orient_drill_through,qi_proportion_materials,qi_proportion_protective_measures,qi_put_slope,qi_radius_curvature_corner,qi_ridge_tunnel_through,qi_space_rule_child_register,qi_space_rule_child_register_rcc,qi_stage_rule,qi_subsection_rules,qi_subsection_rules_field,qi_syphon_transition,qi_warm_groud_line,rcc_avoid_point,rcc_avoid_region,rcc_line_angle_line,rcc_line_distance_line,rcc_line_distance_region,rcc_line_parallel_line,rcc_line_parallel_region,rcc_line_vertical_line,rcc_no_point,rcc_no_region,ruledef_direcotries,qi_area_level_partition,qi_line_derive_line,qi_line_derive_spot,qi_spot_derive_spot,qi_point_derive_spot,qi_derive_attribute,qi_engineering_template,qi_engineering_template_unit,qi_e_u_fun_operation,qi_e_u_fun_operation_unit,qi_lines_angle_line,qi_spot_derive_line,qi_engineering_item,qi_subsection_rules_field_operation,qi_design_derive_spot
toSqliteData.rules_no_project_id=qi_algorithm_register_params,qi_algorithm_register,qi_algorithm_regist_params_survey,qi_algorithm_regist_survey,qi_rules_metadata_field_survey,qi_rules_types_survey,qi_rules_table_survey,qi_rules_regist_survey,qi_rules_field_survey,material_type_pro

toSqliteData.drawingTemplate=basic_block,basic_district,block_property_def,drawing_column_config,drawing_components,drawing_components_type,drawing_font_style_config,drawing_line_style_config,drawing_template_locate,pld_md_parts,pld_md_parts_pic,pld_multilingual,pld_template_directory,pld_templatefiles,pld_templates,property_block,sub_offset_distance,sub_offset_distance_extend
toSqliteData.drawingTemplate_no_project_id=map_frame_config,map_frame_result,map_run_config,drawing_font_style_template_tem,drawing_line_style_template_tem,drawing_column_define_tem,basic_block_tem,basic_district_tem,block_property_def_tem,drawing_column_config_tem,drawing_components_tem,drawing_components_type_tem,drawing_font_style_config_tem,drawing_line_style_config_tem,drawing_template_locate_tem,pld_md_parts_tem,pld_md_parts_pic_tem,pld_multilingual_tem,pld_template_directory_tem,pld_templatefiles_tem,pld_templates_tem,property_block_tem,sub_offset_distance_tem

toSqliteData.parts_tem_no_project_id=pld_md_parts_tem,pld_md_parts_pic_tem
toSqliteData.parts_project_id=pld_md_parts,pld_md_parts_pic

# ??·?????clientId
xlsj.host= https://*************/gateway
xlsj.clientId= chinaPipeLineDesignSystem
xlsj.publicKey= MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCIONp9VNcwu6F2NWvu3c0G5I0NTmn1vHUZco6uwowZceYDRwsSYlWEAFdVlglR+tQLVhbMoquEq2cE/NvQCIVOo7Ha1uCmDJW1LiTV7aRizKRW0hNEBbhUPPzhmor3xnjsymq0SUUN0IbncsLBDjrXsoMro34ebf9zi8dvymkYdQIDAQAB
xlsj.privateKey= MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIg42n1U1zC7oXY1a+7dzQbkjQ1OafW8dRlyjq7CjBlx5gNHCxJiVYQAV1WWCVH61AtWFsyiq4SrZwT829AIhU6jsdrW4KYMlbUuJNXtpGLMpFbSE0QFuFQ8/OGaivfGeOzKarRJRQ3QhudywsEOOteygyujfh5t/3OLx2/KaRh1AgMBAAECgYBgd7qq5QkbdtSqDLzpxOMsDXCiSrSZFKPsqIbI1fc3EcTs70O0+jRLLZ6renwRNUsy1/1/X3eNlYB4NMVJtq8H77YbYwPoiTXExsI5KBOK8ja0LJG/aKOYnHZ98+CvlhxiqQuLXEuXTJBMY/VxtCgbgFwYLI7zslu3xB8qCiC6xQJBAMcRqacE0UDNYiAHWKaK1lfWWGtjR7QHz56/SvRZxg4H9jZ3ciGDmzlQ28ZjmMUViL5pCXPXsPygtvUKTOc7Z4cCQQCvLgMDLCmJYrLuINfxBRFgBFy3cO7f6CUU8suuz19BcM9c31w2pLekz2OJA/bcEbZRzbDXVApFKb/7ZNpVsMcjAkB5wu34QNQVRQrd+GBbDdTpSSwwEzvKfHSb7vnT2A1yi6An1Iu0wCDzv+eNw2GwOum3PdwQRT/2Zf9ChphfX1CFAkBoMXHRPT8nW/EY+af2zxDajz2mTiGGQ8lqgE7tlqINhk+P6borNiWUfVWydQ0rL83FMhZwvKevI3nwyLekKBUJAkEAhBt+BYKPIJgld/cRD88uztzBcYjRvJ+/Va31MdB5++CxUG0NtJgMD4Lj7JZUxlkGfv3TenspkFTI2H3c84VPNw==
# ????????host?????
#  host: http://***********:8301
sjgl.host= https://*************/gateway
sjgl.publicKey= MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZkSWgqKpyprkq3EAznByTPAdRm9aof79lLOmpSumabP6eJm+Ac/HAzpq76E3NvqxCdVC1LAlODIdvHOoJaFkGg3tSsreOwk+Tf95DsqNsHvVnJtFZuECzyiv9NNdDdZdgr/NgMF12GEMsMW/mRpcYWmh65Xa4Ngswq0EPcR9r9QIDAQAB
sjgl.wbstdr= http://***********/gateway/auth/authentication/integrationLogin
sjgl.wbstdrdmz=
# ????????host?????
jf.host= http://***********:3006/api
jf.publicKey= MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDbGAyZHosO20dGcz1A8PPh4aDLc98D6TPOajxYzFxWhF0uRGRNyHG7xpz6BeLVEt7NtHeGzVw8jcK2r+YqPBtM5yf2QrOmG+8Qc5gTmSbvNFTEkeFho30Vx7qdHLAgrvVUsM7tQv8KfSJ9MtPCplrkeC7VB0T9ZvfdbWcBgos7jQIDAQAB


delivery.dir=D:\\package\\achivementsdownloadservice\\sqlite\\db-upload\\delivery


