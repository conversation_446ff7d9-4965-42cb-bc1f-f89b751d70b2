# \u6570\u636E\u5E93\u914D\u7F6E
# yukon or postgresql
db.current.dbtype=yukon
db.current.ip=*************
db.current.port=5432
db.current.username=gaussdb
db.current.password=ENC(9QWrWnocwrggpDZ7+yMKTSRBEF4MWUHz)
# \u6570\u636E\u5E93\u6A21\u5F0F\u914D\u7F6E
db.current.schema=test

db.name.metadata = PipeChina_PLD_MetaData
db.name.rules = PipeChina_PLDS_Rules
db.name.inspectroutes = PipeChina_PLD_InspectRoutes
db.name.entitiesachivment = PipeChina_EntitiesAchivments_0
db.name.engineering = PipeChina_PLD_Engineering
db.name.drawingtemplate = PipeChina_PLD_DrawingTemplate

iserver.ip=127.0.0.1
iserver.port=8090
iserver.username=admin
iserver.password=ENC(tjZutLtNetrsTMofyXY4FRLQuh80SxD8)

spring.redis.host=*************
spring.redis.port=6379
spring.redis.password=ENC(JL+cvXBF7W0yR9y5rQZOJgEyMrozo6yW)
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.shutdown-timeout=100

#\u6750\u6599\u5E93\u540C\u6B65\u914D\u7F6E
material.url=http://************:8081/mdm-mcc-api/mcc/externally/externallyIdentFind

workseed.font.fontname=\u9ed1\u4f53
workseed.font.size=12
workseed.font.height=42.5
workseed.font.color=black
workseed.font.outlinecolor=white
workseed.font.outlinewidth=2