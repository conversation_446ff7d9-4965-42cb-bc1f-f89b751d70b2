<?xml version="1.0" encoding="UTF-8"?>
<SuperMapWorkspaceUnicode xmlns:sml="http://www.supermap.com/sml" sml:Version="20120328" sml:Description="" sml:LastUpdateTime="2023-04-21 01:46:52" sml:Caption="EntityDesign">
<sml:Locale>zh_CN</sml:Locale>
<sml:DataSources>
<sml:DataSource>
<sml:EngineType>2054</sml:EngineType>
<sml:encryType>0</sml:encryType>
<sml:WebCoordinate/>
<sml:Server>./EntitiesAchivments.udbx</sml:Server>
<sml:Driver/>
<sml:Database/>
<sml:User/>
<sml:Password/>
<sml:Alias>EntitiesAchivments</sml:Alias>
<sml:Options>
<sml:ReadOnly>FALSE</sml:ReadOnly>
<sml:Encrypt>TRUE</sml:Encrypt>
<sml:Exclusive>TRUE</sml:Exclusive>
<sml:Transacted>FALSE</sml:Transacted>
</sml:Options>
<sml:AutoConnection>TRUE</sml:AutoConnection>
<sml:ExpandOpen>FALSE</sml:ExpandOpen>
<sml:MaxConnPoolNum>1</sml:MaxConnPoolNum>
</sml:DataSource>
<!--<sml:DataSource>
<sml:EngineType>221</sml:EngineType>
<sml:encryType>0</sml:encryType>
<sml:WebCoordinate/>
<sml:Server>**************</sml:Server>
<sml:Driver/>
<sml:Database>pipechina_common_basic_data</sml:Database>
<sml:User>pipechina</sml:User>
<sml:Password>chinapipe0430</sml:Password>
<sml:Alias>公共基础数据</sml:Alias>
<sml:Options>
<sml:ReadOnly>TRUE</sml:ReadOnly>
<sml:Encrypt>TRUE</sml:Encrypt>
<sml:Exclusive>FALSE</sml:Exclusive>
<sml:Transacted>FALSE</sml:Transacted>
</sml:Options>
<sml:AutoConnection>TRUE</sml:AutoConnection>
<sml:ExpandOpen>FALSE</sml:ExpandOpen>
<sml:MaxConnPoolNum>50</sml:MaxConnPoolNum>
</sml:DataSource>-->
</sml:DataSources>
<sml:Maps>
<sml:Map>
<sml:Name>EntitiesMap</sml:Name>
<sml:Description/>
<sml:Version>20120328</sml:Version>
<sml:ColorMode>Default</sml:ColorMode>
<sml:LineSmoothingMode>TRUE</sml:LineSmoothingMode>
<sml:TextSmoothingMode>TRUE</sml:TextSmoothingMode>
<sml:ViewSettings>
<sml:DefaultScale>0.0004430662393142</sml:DefaultScale>
<sml:CoordinateRatio>4.430662393142065</sml:CoordinateRatio>
<sml:DefaultCenter>
<sml:x>11809136.9903787430375814</sml:x>
<sml:y>4145034.3704842180013657</sml:y>
</sml:DefaultCenter>
<sml:MarginWidthViewEntire>50</sml:MarginWidthViewEntire>
</sml:ViewSettings>
<sml:RotationSettings>
<sml:Angle>0</sml:Angle>
<sml:RotateMarker>TRUE</sml:RotateMarker>
<sml:RotateText>TRUE</sml:RotateText>
</sml:RotationSettings>
<sml:TextSettings>
<sml:FixedTextOrientation>FALSE</sml:FixedTextOrientation>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsDrawTextAtLast>TRUE</sml:IsDrawTextAtLast>
<sml:IsTextLayerReverse>TRUE</sml:IsTextLayerReverse>
<sml:MinVisibleTextSize>0.1</sml:MinVisibleTextSize>
<sml:MaxVisibleTextSize>1000</sml:MaxVisibleTextSize>
<sml:IsCompatibleFontHeight>TRUE</sml:IsCompatibleFontHeight>
</sml:TextSettings>
<sml:MapOverlapDisplayedOptions>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:AllowPointOverlap>TRUE</sml:AllowPointOverlap>
<sml:AllowTextAndPointOverlap>TRUE</sml:AllowTextAndPointOverlap>
<sml:AllowPointWithTextDisplay>TRUE</sml:AllowPointWithTextDisplay>
<sml:AllowThemeGraduatedSymbolOverlap>FALSE</sml:AllowThemeGraduatedSymbolOverlap>
<sml:AllowThemeGraphOverlap>FALSE</sml:AllowThemeGraphOverlap>
<sml:OverlappedSpaceX>0</sml:OverlappedSpaceX>
<sml:OverlappedSpaceY>0</sml:OverlappedSpaceY>
<sml:ObliqueRectEnabled>FALSE</sml:ObliqueRectEnabled>
</sml:MapOverlapDisplayedOptions>
<sml:MapLabelAvoidWeightInfos/>
<sml:MapMaxScale>1000000000000.0000000000</sml:MapMaxScale>
<sml:MapMinScale>0.0000000000</sml:MapMinScale>
<sml:ProjectionOnTheFly>TRUE</sml:ProjectionOnTheFly>
<sml:DynamicTransMethod>9603</sml:DynamicTransMethod>
<sml:Parameters>
<sml:TranslateX>0.0000000000000000</sml:TranslateX>
<sml:TranslateY>0.0000000000000000</sml:TranslateY>
<sml:TranslateZ>0.0000000000000000</sml:TranslateZ>
<sml:RotateX>0.0000000000000000</sml:RotateX>
<sml:RotateY>0.0000000000000000</sml:RotateY>
<sml:RotateZ>0.0000000000000000</sml:RotateZ>
<sml:ScaleDifference>0.0000000000000000</sml:ScaleDifference>
<sml:RotationOriginX>0.0000000000000000</sml:RotationOriginX>
<sml:RotationOriginY>0.0000000000000000</sml:RotationOriginY>
<sml:RotationOriginZ>0.0000000000000000</sml:RotationOriginZ>
</sml:Parameters>
<sml:CoordinateReferenceSystem>
<sml:Name>User Define</sml:Name>
<sml:Type>-1</sml:Type>
<sml:EPSGCode>0</sml:EPSGCode>
<sml:SRS/>
<sml:Units>METER</sml:Units>
<sml:DistUnits>METER</sml:DistUnits>
<sml:GeographicCoordinateSystem>
<sml:Name>GCS_WGS_1984</sml:Name>
<sml:Type>4326</sml:Type>
<sml:EPSGCode>4326</sml:EPSGCode>
<sml:Units>DEGREE</sml:Units>
<sml:DistUnits>METER</sml:DistUnits>
<sml:ProjectionHeight>0.0000000000</sml:ProjectionHeight>
<sml:HorizontalGeodeticDatum>
<sml:Name>D_WGS_1984</sml:Name>
<sml:Type>6326</sml:Type>
<sml:Spheroid>
<sml:Name>WGS_1984</sml:Name>
<sml:Type>7030</sml:Type>
<sml:SemiMajorAxis>6378137</sml:SemiMajorAxis>
<sml:InverseFlattening>298.257223563</sml:InverseFlattening>
</sml:Spheroid>
</sml:HorizontalGeodeticDatum>
<sml:PrimeMeridian>
<sml:Name>Greenwich</sml:Name>
<sml:Type>8901</sml:Type>
<sml:PrimeMeridian>0</sml:PrimeMeridian>
</sml:PrimeMeridian>
</sml:GeographicCoordinateSystem>
<sml:MapProjection>
<sml:Name/>
<sml:Type>43000</sml:Type>
</sml:MapProjection>
<sml:Parameters>
<sml:FalseEasting>0</sml:FalseEasting>
<sml:FalseNorthing>0</sml:FalseNorthing>
<sml:CentralMeridian>0</sml:CentralMeridian>
<sml:StandardParallel1>0</sml:StandardParallel1>
<sml:StandardParallel2>0</sml:StandardParallel2>
<sml:ScaleFactor>0</sml:ScaleFactor>
<sml:CentralParallel>0</sml:CentralParallel>
<sml:Azimuth>0</sml:Azimuth>
<sml:FirstPointLongitude>0</sml:FirstPointLongitude>
<sml:SecondPointLongitude>0</sml:SecondPointLongitude>
<sml:RectifiedAngle>0</sml:RectifiedAngle>
</sml:Parameters>
</sml:CoordinateReferenceSystem>
<sml:BackStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:BackStyle>
<sml:Time>
<sml:IsTimeEnable>FALSE</sml:IsTimeEnable>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:TimeWindow>0.0000000000</sml:TimeWindow>
<sml:StartTime>1899-12-30-00:00:00</sml:StartTime>
<sml:EndTime>1899-12-30-00:00:00</sml:EndTime>
<sml:CurrentTick>0</sml:CurrentTick>
<sml:TextStyle>
<sml:FaceName>Times New Roman</sml:FaceName>
<sml:Align>BottomRight</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,130816)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>6</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>400</sml:Weight>
<sml:IsFixedSize>TRUE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:TimeTextPosition>8</sml:TimeTextPosition>
<sml:TimeFormat>YYYY-MM-DD hh:mm:ss</sml:TimeFormat>
<sml:IsShowTime>FALSE</sml:IsShowTime>
</sml:Time>
<sml:DisplayClip>FALSE</sml:DisplayClip>
<sml:CustomViewEntire>FALSE</sml:CustomViewEntire>
<sml:ZoomByScales>TRUE</sml:ZoomByScales>
<sml:ZoomScales>
<sml:ZoomScale>3.38032714320531053009e-09</sml:ZoomScale>
<sml:ZoomScale>6.76065428641062106017e-09</sml:ZoomScale>
<sml:ZoomScale>1.35213085728212421203e-08</sml:ZoomScale>
<sml:ZoomScale>2.70426171456424842407e-08</sml:ZoomScale>
<sml:ZoomScale>5.40852342912849684814e-08</sml:ZoomScale>
<sml:ZoomScale>1.08170468582569936963e-07</sml:ZoomScale>
<sml:ZoomScale>2.16340937165139820986e-07</sml:ZoomScale>
<sml:ZoomScale>4.32681874330279641972e-07</sml:ZoomScale>
<sml:ZoomScale>8.65363748660559283944e-07</sml:ZoomScale>
<sml:ZoomScale>1.73072749732111856789e-06</sml:ZoomScale>
<sml:ZoomScale>3.46145499464223713578e-06</sml:ZoomScale>
<sml:ZoomScale>6.92290998928447427155e-06</sml:ZoomScale>
<sml:ZoomScale>1.38458199785689485431e-05</sml:ZoomScale>
<sml:ZoomScale>2.76916399571378970862e-05</sml:ZoomScale>
<sml:ZoomScale>5.53832799142757941724e-05</sml:ZoomScale>
<sml:ZoomScale>1.10766559828551588345e-04</sml:ZoomScale>
<sml:ZoomScale>2.21533119657103176690e-04</sml:ZoomScale>
<sml:ZoomScale>4.43066239314206461800e-04</sml:ZoomScale>
<sml:ZoomScale>8.86132478628412923599e-04</sml:ZoomScale>
</sml:ZoomScales>
<sml:MaxVisibleVertex>36000000</sml:MaxVisibleVertex>
<sml:IsFillAngle>FALSE</sml:IsFillAngle>
<sml:IsSymbolFillIgnored>FALSE</sml:IsSymbolFillIgnored>
<sml:IsDisableDynamicEffect>FALSE</sml:IsDisableDynamicEffect>
<sml:IsDisableAuotAvoidEffect>FALSE</sml:IsDisableAuotAvoidEffect>
<sml:IsDrawBoundsLocked>FALSE</sml:IsDrawBoundsLocked>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:IsGraphicsAlphaEnabled>TRUE</sml:IsGraphicsAlphaEnabled>
<sml:IsTempCacheFileEnabled>FALSE</sml:IsTempCacheFileEnabled>
<sml:IsFullLabel>TRUE</sml:IsFullLabel>
<sml:ChartSetting>
<sml:ColorMode>DayBright</sml:ColorMode>
<sml:DisplayCategory>STANDARD</sml:DisplayCategory>
<sml:StyleRuleName>S52</sml:StyleRuleName>
<sml:SymbolizedBoundary>FALSE</sml:SymbolizedBoundary>
<sml:SimplifiedMarker>TRUE</sml:SimplifiedMarker>
<sml:MinVisibleScaleEnabled>TRUE</sml:MinVisibleScaleEnabled>
<sml:DisplaySounding>FALSE</sml:DisplaySounding>
<sml:DisplayText>FALSE</sml:DisplayText>
<sml:LocalizationDisplayText>FALSE</sml:LocalizationDisplayText>
<sml:FontName>Arial</sml:FontName>
<sml:FontSize>10.0</sml:FontSize>
<sml:DisplayMetaObject>FALSE</sml:DisplayMetaObject>
<sml:DisplaySafetyContourLabel>TRUE</sml:DisplaySafetyContourLabel>
<sml:DisplayOtherContourLabel>FALSE</sml:DisplayOtherContourLabel>
<sml:TwoShades>FALSE</sml:TwoShades>
<sml:SafetyContour>30.00</sml:SafetyContour>
<sml:ShallowContour>2.00</sml:ShallowContour>
<sml:DeepContour>30.00</sml:DeepContour>
<sml:SafetyDepth>30.00</sml:SafetyDepth>
<sml:DepthUnit>10000</sml:DepthUnit>
<sml:DisplayCellName>FALSE</sml:DisplayCellName>
<sml:LowAccurateEnable>TRUE</sml:LowAccurateEnable>
<sml:TextClipRegionEnabled>FALSE</sml:TextClipRegionEnabled>
<sml:DisplayScaleFactor>0.00000000000000000000e+00</sml:DisplayScaleFactor>
<sml:DisplayBorder>FALSE</sml:DisplayBorder>
<sml:Features/>
<sml:Style>
<sml:MarkerStyle>60000001</sml:MarkerStyle>
<sml:MarkerSize>0</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>0</sml:MarkerWidth>
<sml:MarkerHeight>0</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.6</sml:LineWidth>
<sml:LineColor>RGB(220,70,130856)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>2</sml:FillStyle>
<sml:FillForeColor>RGB(220,70,130856)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ChartSetting>
<sml:Layers>
<sml:MultiEditEnable>FALSE</sml:MultiEditEnable>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>103</sml:DatasetType>
<sml:Caption>地区等级-线</sml:Caption>
<sml:Name>diqudengji_L@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>diqudengji_L</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>TRUE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>1</sml:Type>
<sml:Caption>diqudengji_L@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>dengji_level</sml:FieldExpression>
<sml:SizeExpression/>
<sml:AngleExpression/>
<sml:ConditionExpression/>
<sml:ConditionOffset>10</sml:ConditionOffset>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>二级</sml:Value>
<sml:Caption>二级</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965701</sml:LineStyle>
<sml:LineWidth>2</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>一级</sml:Value>
<sml:Caption>一级</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965670</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>四级</sml:Value>
<sml:Caption>四级</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965674</sml:LineStyle>
<sml:LineWidth>4</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>三级</sml:Value>
<sml:Caption>三级</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965699</sml:LineStyle>
<sml:LineWidth>3</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
</sml:UniqueItems>
<sml:IsDefaultStyleVisible>TRUE</sml:IsDefaultStyleVisible>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>加密桩-点</sml:Caption>
<sml:Name>jiamizhuang_P@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>jiamizhuang_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>FALSE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>1</sml:Type>
<sml:Caption>jiamizhuang_P@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>jiami_type</sml:FieldExpression>
<sml:SizeExpression/>
<sml:AngleExpression/>
<sml:ConditionExpression/>
<sml:ConditionOffset>10</sml:ConditionOffset>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>92000104</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>908602</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
</sml:UniqueItems>
<sml:IsDefaultStyleVisible>TRUE</sml:IsDefaultStyleVisible>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>标志桩-点</sml:Caption>
<sml:Name>biaozhizhuang_P@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>biaozhizhuang_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>TRUE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>1</sml:Type>
<sml:Caption>biaozhizhuang_P@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>biaozhileixng</sml:FieldExpression>
<sml:SizeExpression/>
<sml:AngleExpression/>
<sml:ConditionExpression/>
<sml:ConditionOffset>10</sml:ConditionOffset>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>92000104</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>908602</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
</sml:UniqueItems>
<sml:IsDefaultStyleVisible>TRUE</sml:IsDefaultStyleVisible>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>103</sml:DatasetType>
<sml:Caption>地形地貌-线</sml:Caption>
<sml:Name>topographic_features_L@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>topographic_features_L</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>TRUE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>1</sml:Type>
<sml:Caption>topographic_features_L@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>topographic_type</sml:FieldExpression>
<sml:SizeExpression/>
<sml:AngleExpression/>
<sml:ConditionExpression/>
<sml:ConditionOffset>10</sml:ConditionOffset>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>平原</sml:Value>
<sml:Caption>平原</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965665</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>水网</sml:Value>
<sml:Caption>水网</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965703</sml:LineStyle>
<sml:LineWidth>5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>山区</sml:Value>
<sml:Caption>山区</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965699</sml:LineStyle>
<sml:LineWidth>4</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>丘陵</sml:Value>
<sml:Caption>丘陵</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965672</sml:LineStyle>
<sml:LineWidth>3</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>戈壁</sml:Value>
<sml:Caption>戈壁</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965663</sml:LineStyle>
<sml:LineWidth>2</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
</sml:UniqueItems>
<sml:IsDefaultStyleVisible>TRUE</sml:IsDefaultStyleVisible>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>103</sml:DatasetType>
<sml:Caption>铁路穿越-线</sml:Caption>
<sml:Name>tieluchuanyue_L@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>tieluchuanyue_L</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>TRUE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>1</sml:Type>
<sml:Caption>tieluchuanyue_L@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>chuanyueleixing</sml:FieldExpression>
<sml:SizeExpression/>
<sml:AngleExpression/>
<sml:ConditionExpression/>
<sml:ConditionOffset>10</sml:ConditionOffset>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965667</sml:LineStyle>
<sml:LineWidth>2</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965665</sml:LineStyle>
<sml:LineWidth>2</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
</sml:UniqueItems>
<sml:IsDefaultStyleVisible>TRUE</sml:IsDefaultStyleVisible>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>交叉桩-点</sml:Caption>
<sml:Name>jiaocha_P@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>jiaocha_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>TRUE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>1</sml:Type>
<sml:Caption>jiaocha_P@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>zhuangdian_type</sml:FieldExpression>
<sml:SizeExpression/>
<sml:AngleExpression/>
<sml:ConditionExpression/>
<sml:ConditionOffset>10</sml:ConditionOffset>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>92000104</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>908602</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
</sml:UniqueItems>
<sml:IsDefaultStyleVisible>TRUE</sml:IsDefaultStyleVisible>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>里程桩-点标签</sml:Caption>
<sml:Name>lichengzhuang_P@EntitiesAchivments#1</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>lichengzhuang_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>TRUE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>FALSE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>7</sml:Type>
<sml:Caption>lichengzhuang_P@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression/>
<sml:ExplodeXExpression/>
<sml:ExplodeYExpression/>
<sml:FontTypeExpression/>
<sml:FontColorExpression/>
<sml:FontSizeExpression/>
<sml:FontAngleExpression/>
<sml:LabelOverLengthMode>None</sml:LabelOverLengthMode>
<sml:SplitSeparator/>
<sml:AlongLanguageCustom>ALC_Chinese</sml:AlongLanguageCustom>
<sml:TextLengthLimit>256</sml:TextLengthLimit>
<sml:Options>
<sml:EnableFlow>FALSE</sml:EnableFlow>
<sml:ShowLeaderLines>FALSE</sml:ShowLeaderLines>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:AlongLine>TRUE</sml:AlongLine>
<sml:SoleLabelAlongLine>FALSE</sml:SoleLabelAlongLine>
<sml:LabelAlongLineType>UGAlongLineSignify</sml:LabelAlongLineType>
<sml:LabelAlongLineJoint>FALSE</sml:LabelAlongLineJoint>
<sml:FixedAngle>FALSE</sml:FixedAngle>
<sml:AutoAvoid>TRUE</sml:AutoAvoid>
<sml:OptimizeMutilineAlignment>FALSE</sml:OptimizeMutilineAlignment>
<sml:OnTop>FALSE</sml:OnTop>
<sml:Priority>0</sml:Priority>
<sml:FixedRepeatLength>FALSE</sml:FixedRepeatLength>
<sml:IgnoreLittleObject>TRUE</sml:IgnoreLittleObject>
<sml:SelfOverlap>TRUE</sml:SelfOverlap>
</sml:Options>
<sml:LeaderLineStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>39</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:LeaderLineStyle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>TopLeft</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,130816)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>6</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>FALSE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
<sml:BackType>None</sml:BackType>
<sml:BackStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131020)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:BackStyle>
<sml:BufferX>0.0000000000</sml:BufferX>
<sml:BufferY>0.0000000000</sml:BufferY>
<sml:PosiotnPriority>
<sml:TopLeft>2</sml:TopLeft>
<sml:TopCenter>5</sml:TopCenter>
<sml:TopRight>4</sml:TopRight>
<sml:CenterLeft>6</sml:CenterLeft>
<sml:Center>7</sml:Center>
<sml:CenterRight>8</sml:CenterRight>
<sml:BottomLeft>1</sml:BottomLeft>
<sml:BottomCenter>9</sml:BottomCenter>
<sml:BottomRight>3</sml:BottomRight>
<sml:BaseLeft>10</sml:BaseLeft>
<sml:BaseCenter>11</sml:BaseCenter>
<sml:BaseRight>12</sml:BaseRight>
</sml:PosiotnPriority>
<sml:AutoVoidPosition>TRUE</sml:AutoVoidPosition>
<sml:AvoidMode>AvoidModeFour</sml:AvoidMode>
<sml:MaxObjectNum>0</sml:MaxObjectNum>
<sml:NumFormat/>
<sml:LineDirection>AlongLine</sml:LineDirection>
<sml:RepeatLength>0.000000</sml:RepeatLength>
<sml:TextSpace>1.100000</sml:TextSpace>
<sml:IsAutoAdjustRegionLabel>FALSE</sml:IsAutoAdjustRegionLabel>
<sml:IsVerticalLabel>FALSE</sml:IsVerticalLabel>
<sml:LabelTextSpace>0.000000</sml:LabelTextSpace>
<sml:LeaderLineToBack>FALSE</sml:LeaderLineToBack>
<sml:LineDirectionAngle>60.000000</sml:LineDirectionAngle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>Center</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,131071)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>4</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>TRUE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MaxHeight>0</sml:MaxHeight>
<sml:MinHeight>0</sml:MinHeight>
<sml:MaxWidth>0</sml:MaxWidth>
<sml:MinWidth>0</sml:MinWidth>
<sml:TextDiversificationType>LabelPosition</sml:TextDiversificationType>
<sml:TextDiversificationDelims/>
<sml:LabelType>LabelTable</sml:LabelType>
<sml:LabelPictureStyle>
<sml:Width>0.000000000000000</sml:Width>
<sml:Height>0.000000000000000</sml:Height>
<sml:Angle>0.000000000000000</sml:Angle>
<sml:Type>0</sml:Type>
<sml:FixedSize>TRUE</sml:FixedSize>
<sml:TransparentRate>0</sml:TransparentRate>
</sml:LabelPictureStyle>
<sml:Table>
<sml:Rows>1</sml:Rows>
<sml:Cols>1</sml:Cols>
<sml:Cells>
<sml:Cell>
<sml:Theme>
<sml:Type>7</sml:Type>
<sml:Caption/>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>'桩点：'||lichengzhuang_P.zhuangdian_type||'null'</sml:FieldExpression>
<sml:ExplodeXExpression/>
<sml:ExplodeYExpression/>
<sml:FontTypeExpression/>
<sml:FontColorExpression/>
<sml:FontSizeExpression/>
<sml:FontAngleExpression/>
<sml:LabelOverLengthMode>None</sml:LabelOverLengthMode>
<sml:SplitSeparator/>
<sml:AlongLanguageCustom>ALC_Chinese</sml:AlongLanguageCustom>
<sml:TextLengthLimit>256</sml:TextLengthLimit>
<sml:Options>
<sml:EnableFlow>FALSE</sml:EnableFlow>
<sml:ShowLeaderLines>FALSE</sml:ShowLeaderLines>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:AlongLine>TRUE</sml:AlongLine>
<sml:SoleLabelAlongLine>FALSE</sml:SoleLabelAlongLine>
<sml:LabelAlongLineType>UGAlongLineSignify</sml:LabelAlongLineType>
<sml:LabelAlongLineJoint>FALSE</sml:LabelAlongLineJoint>
<sml:FixedAngle>FALSE</sml:FixedAngle>
<sml:AutoAvoid>TRUE</sml:AutoAvoid>
<sml:OptimizeMutilineAlignment>FALSE</sml:OptimizeMutilineAlignment>
<sml:OnTop>FALSE</sml:OnTop>
<sml:Priority>56828</sml:Priority>
<sml:FixedRepeatLength>FALSE</sml:FixedRepeatLength>
<sml:IgnoreLittleObject>TRUE</sml:IgnoreLittleObject>
<sml:SelfOverlap>TRUE</sml:SelfOverlap>
</sml:Options>
<sml:LeaderLineStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>39</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:LeaderLineStyle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>TopLeft</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,130816)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>6</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>FALSE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
<sml:BackType>None</sml:BackType>
<sml:BackStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131020)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:BackStyle>
<sml:BufferX>0.0000000000</sml:BufferX>
<sml:BufferY>0.0000000000</sml:BufferY>
<sml:PosiotnPriority>
<sml:TopLeft>2</sml:TopLeft>
<sml:TopCenter>5</sml:TopCenter>
<sml:TopRight>4</sml:TopRight>
<sml:CenterLeft>6</sml:CenterLeft>
<sml:Center>7</sml:Center>
<sml:CenterRight>8</sml:CenterRight>
<sml:BottomLeft>1</sml:BottomLeft>
<sml:BottomCenter>9</sml:BottomCenter>
<sml:BottomRight>3</sml:BottomRight>
<sml:BaseLeft>10</sml:BaseLeft>
<sml:BaseCenter>11</sml:BaseCenter>
<sml:BaseRight>12</sml:BaseRight>
</sml:PosiotnPriority>
<sml:AutoVoidPosition>TRUE</sml:AutoVoidPosition>
<sml:AvoidMode>AvoidModeFour</sml:AvoidMode>
<sml:MaxObjectNum>0</sml:MaxObjectNum>
<sml:NumFormat/>
<sml:LineDirection>AlongLine</sml:LineDirection>
<sml:RepeatLength>0.000000</sml:RepeatLength>
<sml:TextSpace>1.100000</sml:TextSpace>
<sml:IsAutoAdjustRegionLabel>FALSE</sml:IsAutoAdjustRegionLabel>
<sml:IsVerticalLabel>FALSE</sml:IsVerticalLabel>
<sml:LabelTextSpace>0.000000</sml:LabelTextSpace>
<sml:LeaderLineToBack>FALSE</sml:LeaderLineToBack>
<sml:LineDirectionAngle>60.000000</sml:LineDirectionAngle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>Center</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,131071)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>4</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>TRUE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MaxHeight>0</sml:MaxHeight>
<sml:MinHeight>0</sml:MinHeight>
<sml:MaxWidth>0</sml:MaxWidth>
<sml:MinWidth>0</sml:MinWidth>
<sml:TextDiversificationType>LabelPosition</sml:TextDiversificationType>
<sml:TextDiversificationDelims/>
<sml:LabelType>LabelText</sml:LabelType>
<sml:LabelPictureStyle>
<sml:Width>0.000000000000000</sml:Width>
<sml:Height>0.000000000000000</sml:Height>
<sml:Angle>0.000000000000000</sml:Angle>
<sml:Type>0</sml:Type>
<sml:FixedSize>TRUE</sml:FixedSize>
<sml:TransparentRate>0</sml:TransparentRate>
</sml:LabelPictureStyle>
<sml:RangeItems>
<sml:RangeItem/>
</sml:RangeItems>
<sml:TextDiversification>FALSE</sml:TextDiversification>
<sml:RangeMethod>6</sml:RangeMethod>
<sml:CustomInterval>0.0000000000</sml:CustomInterval>
<sml:IsTextExpression>FALSE</sml:IsTextExpression>
<sml:IsDissolveAlongLines>FALSE</sml:IsDissolveAlongLines>
<sml:AlongLineWordAngleRange>200</sml:AlongLineWordAngleRange>
<sml:AlongLineResampleSmooth>FALSE</sml:AlongLineResampleSmooth>
<sml:AlongLineResampleTolerance>1.000000000000000</sml:AlongLineResampleTolerance>
<sml:AlongLineSmoothness>2</sml:AlongLineSmoothness>
<sml:AlongLineDrawingMode>0</sml:AlongLineDrawingMode>
<sml:IsRarefyPoints>FALSE</sml:IsRarefyPoints>
<sml:IsOnlyDrawWithinRegionText>FALSE</sml:IsOnlyDrawWithinRegionText>
<sml:IsShouTextInRegionThrend>FALSE</sml:IsShouTextInRegionThrend>
<sml:RarefyPointsRadius>4.000000000000000</sml:RarefyPointsRadius>
<sml:RarefyThreshold>1000</sml:RarefyThreshold>
<sml:AlongLineSelfOverlap>TRUE</sml:AlongLineSelfOverlap>
<sml:IsDisplayByDomain>FALSE</sml:IsDisplayByDomain>
<sml:AlongLineLabelMode>0</sml:AlongLineLabelMode>
<sml:AlongLineAllowCondition>0</sml:AlongLineAllowCondition>
<sml:AlongLineAutoAvoid>FALSE</sml:AlongLineAutoAvoid>
<sml:AlongLineResampleLine>FALSE</sml:AlongLineResampleLine>
</sml:Theme>
</sml:Cell>
</sml:Cells>
</sml:Table>
<sml:RangeItems>
<sml:RangeItem/>
</sml:RangeItems>
<sml:TextDiversification>FALSE</sml:TextDiversification>
<sml:RangeMethod>6</sml:RangeMethod>
<sml:CustomInterval>0.0000000000</sml:CustomInterval>
<sml:IsTextExpression>FALSE</sml:IsTextExpression>
<sml:IsDissolveAlongLines>FALSE</sml:IsDissolveAlongLines>
<sml:AlongLineWordAngleRange>200</sml:AlongLineWordAngleRange>
<sml:AlongLineResampleSmooth>FALSE</sml:AlongLineResampleSmooth>
<sml:AlongLineResampleTolerance>1.000000000000000</sml:AlongLineResampleTolerance>
<sml:AlongLineSmoothness>2</sml:AlongLineSmoothness>
<sml:AlongLineDrawingMode>0</sml:AlongLineDrawingMode>
<sml:IsRarefyPoints>FALSE</sml:IsRarefyPoints>
<sml:IsOnlyDrawWithinRegionText>FALSE</sml:IsOnlyDrawWithinRegionText>
<sml:IsShouTextInRegionThrend>FALSE</sml:IsShouTextInRegionThrend>
<sml:RarefyPointsRadius>4.000000000000000</sml:RarefyPointsRadius>
<sml:RarefyThreshold>1000</sml:RarefyThreshold>
<sml:AlongLineSelfOverlap>TRUE</sml:AlongLineSelfOverlap>
<sml:IsDisplayByDomain>FALSE</sml:IsDisplayByDomain>
<sml:AlongLineLabelMode>0</sml:AlongLineLabelMode>
<sml:AlongLineAllowCondition>0</sml:AlongLineAllowCondition>
<sml:AlongLineAutoAvoid>FALSE</sml:AlongLineAutoAvoid>
<sml:AlongLineResampleLine>FALSE</sml:AlongLineResampleLine>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>里程桩-点</sml:Caption>
<sml:Name>lichengzhuang_P@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>lichengzhuang_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>FALSE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>1</sml:Type>
<sml:Caption>lichengzhuang_P@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>zhuangdian_type</sml:FieldExpression>
<sml:SizeExpression/>
<sml:AngleExpression/>
<sml:ConditionExpression/>
<sml:ConditionOffset>10</sml:ConditionOffset>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>92000104</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:Style>
<sml:MarkerStyle>908602</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:FlagIndex>-1</sml:FlagIndex>
</sml:UniqueItem>
</sml:UniqueItems>
<sml:IsDefaultStyleVisible>TRUE</sml:IsDefaultStyleVisible>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>转角桩-点</sml:Caption>
<sml:Name>zhuangjiao_P@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>zhuangjiao_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>92000088</sml:MarkerStyle>
<sml:MarkerSize>20</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>200</sml:MarkerWidth>
<sml:MarkerHeight>200</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>TRUE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>103</sml:DatasetType>
<sml:Caption>纵管线-线</sml:Caption>
<sml:Name>longitudinal_pipeline_L@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>longitudinal_pipeline_L</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>20</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>200</sml:MarkerWidth>
<sml:MarkerHeight>200</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>FALSE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>纵管线-点</sml:Caption>
<sml:Name>longitudinal_pipeline_P@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>longitudinal_pipeline_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>92000084</sml:MarkerStyle>
<sml:MarkerSize>20</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>200</sml:MarkerWidth>
<sml:MarkerHeight>200</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>FALSE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>103</sml:DatasetType>
<sml:Caption>地面线-线</sml:Caption>
<sml:Name>ground_line_L@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>ground_line_L</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>20</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>200</sml:MarkerWidth>
<sml:MarkerHeight>200</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>TRUE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>地面线-点</sml:Caption>
<sml:Name>ground_line_P@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>ground_line_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>92000084</sml:MarkerStyle>
<sml:MarkerSize>20</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>200</sml:MarkerWidth>
<sml:MarkerHeight>200</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>FALSE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>103</sml:DatasetType>
<sml:Caption>中线-线标签</sml:Caption>
<sml:Name>center_line_L@EntitiesAchivments#1</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>center_line_L</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>FALSE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>FALSE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>7</sml:Type>
<sml:Caption>center_line_L@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression/>
<sml:ExplodeXExpression/>
<sml:ExplodeYExpression/>
<sml:FontTypeExpression/>
<sml:FontColorExpression/>
<sml:FontSizeExpression/>
<sml:FontAngleExpression/>
<sml:LabelOverLengthMode>None</sml:LabelOverLengthMode>
<sml:SplitSeparator/>
<sml:AlongLanguageCustom>ALC_Chinese</sml:AlongLanguageCustom>
<sml:TextLengthLimit>256</sml:TextLengthLimit>
<sml:Options>
<sml:EnableFlow>FALSE</sml:EnableFlow>
<sml:ShowLeaderLines>FALSE</sml:ShowLeaderLines>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:AlongLine>TRUE</sml:AlongLine>
<sml:SoleLabelAlongLine>FALSE</sml:SoleLabelAlongLine>
<sml:LabelAlongLineType>UGAlongLineSignify</sml:LabelAlongLineType>
<sml:LabelAlongLineJoint>FALSE</sml:LabelAlongLineJoint>
<sml:FixedAngle>FALSE</sml:FixedAngle>
<sml:AutoAvoid>TRUE</sml:AutoAvoid>
<sml:OptimizeMutilineAlignment>FALSE</sml:OptimizeMutilineAlignment>
<sml:OnTop>FALSE</sml:OnTop>
<sml:Priority>0</sml:Priority>
<sml:FixedRepeatLength>FALSE</sml:FixedRepeatLength>
<sml:IgnoreLittleObject>TRUE</sml:IgnoreLittleObject>
<sml:SelfOverlap>TRUE</sml:SelfOverlap>
</sml:Options>
<sml:LeaderLineStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>39</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:LeaderLineStyle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>TopLeft</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,130816)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>6</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>FALSE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
<sml:BackType>None</sml:BackType>
<sml:BackStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131020)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:BackStyle>
<sml:BufferX>0.0000000000</sml:BufferX>
<sml:BufferY>0.0000000000</sml:BufferY>
<sml:PosiotnPriority>
<sml:TopLeft>2</sml:TopLeft>
<sml:TopCenter>5</sml:TopCenter>
<sml:TopRight>4</sml:TopRight>
<sml:CenterLeft>6</sml:CenterLeft>
<sml:Center>7</sml:Center>
<sml:CenterRight>8</sml:CenterRight>
<sml:BottomLeft>1</sml:BottomLeft>
<sml:BottomCenter>9</sml:BottomCenter>
<sml:BottomRight>3</sml:BottomRight>
<sml:BaseLeft>10</sml:BaseLeft>
<sml:BaseCenter>11</sml:BaseCenter>
<sml:BaseRight>12</sml:BaseRight>
</sml:PosiotnPriority>
<sml:AutoVoidPosition>TRUE</sml:AutoVoidPosition>
<sml:AvoidMode>AvoidModeFour</sml:AvoidMode>
<sml:MaxObjectNum>0</sml:MaxObjectNum>
<sml:NumFormat/>
<sml:LineDirection>AlongLine</sml:LineDirection>
<sml:RepeatLength>0.000000</sml:RepeatLength>
<sml:TextSpace>1.100000</sml:TextSpace>
<sml:IsAutoAdjustRegionLabel>FALSE</sml:IsAutoAdjustRegionLabel>
<sml:IsVerticalLabel>FALSE</sml:IsVerticalLabel>
<sml:LabelTextSpace>0.000000</sml:LabelTextSpace>
<sml:LeaderLineToBack>FALSE</sml:LeaderLineToBack>
<sml:LineDirectionAngle>60.000000</sml:LineDirectionAngle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>Center</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,131071)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>4</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>TRUE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MaxHeight>0</sml:MaxHeight>
<sml:MinHeight>0</sml:MinHeight>
<sml:MaxWidth>0</sml:MaxWidth>
<sml:MinWidth>0</sml:MinWidth>
<sml:TextDiversificationType>LabelPosition</sml:TextDiversificationType>
<sml:TextDiversificationDelims/>
<sml:LabelType>LabelTable</sml:LabelType>
<sml:LabelPictureStyle>
<sml:Width>0.000000000000000</sml:Width>
<sml:Height>0.000000000000000</sml:Height>
<sml:Angle>0.000000000000000</sml:Angle>
<sml:Type>0</sml:Type>
<sml:FixedSize>TRUE</sml:FixedSize>
<sml:TransparentRate>0</sml:TransparentRate>
</sml:LabelPictureStyle>
<sml:Table>
<sml:Rows>2</sml:Rows>
<sml:Cols>1</sml:Cols>
<sml:Cells>
<sml:Cell>
<sml:Theme>
<sml:Type>7</sml:Type>
<sml:Caption/>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>'主桩号：'||center_line_L.stake_number||'null'</sml:FieldExpression>
<sml:ExplodeXExpression/>
<sml:ExplodeYExpression/>
<sml:FontTypeExpression/>
<sml:FontColorExpression/>
<sml:FontSizeExpression/>
<sml:FontAngleExpression/>
<sml:LabelOverLengthMode>None</sml:LabelOverLengthMode>
<sml:SplitSeparator/>
<sml:AlongLanguageCustom>ALC_Chinese</sml:AlongLanguageCustom>
<sml:TextLengthLimit>256</sml:TextLengthLimit>
<sml:Options>
<sml:EnableFlow>FALSE</sml:EnableFlow>
<sml:ShowLeaderLines>FALSE</sml:ShowLeaderLines>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:AlongLine>TRUE</sml:AlongLine>
<sml:SoleLabelAlongLine>FALSE</sml:SoleLabelAlongLine>
<sml:LabelAlongLineType>UGAlongLineSignify</sml:LabelAlongLineType>
<sml:LabelAlongLineJoint>FALSE</sml:LabelAlongLineJoint>
<sml:FixedAngle>FALSE</sml:FixedAngle>
<sml:AutoAvoid>TRUE</sml:AutoAvoid>
<sml:OptimizeMutilineAlignment>FALSE</sml:OptimizeMutilineAlignment>
<sml:OnTop>FALSE</sml:OnTop>
<sml:Priority>56828</sml:Priority>
<sml:FixedRepeatLength>FALSE</sml:FixedRepeatLength>
<sml:IgnoreLittleObject>TRUE</sml:IgnoreLittleObject>
<sml:SelfOverlap>TRUE</sml:SelfOverlap>
</sml:Options>
<sml:LeaderLineStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>39</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:LeaderLineStyle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>TopLeft</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,130816)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>6</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>FALSE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
<sml:BackType>None</sml:BackType>
<sml:BackStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131020)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:BackStyle>
<sml:BufferX>0.0000000000</sml:BufferX>
<sml:BufferY>0.0000000000</sml:BufferY>
<sml:PosiotnPriority>
<sml:TopLeft>2</sml:TopLeft>
<sml:TopCenter>5</sml:TopCenter>
<sml:TopRight>4</sml:TopRight>
<sml:CenterLeft>6</sml:CenterLeft>
<sml:Center>7</sml:Center>
<sml:CenterRight>8</sml:CenterRight>
<sml:BottomLeft>1</sml:BottomLeft>
<sml:BottomCenter>9</sml:BottomCenter>
<sml:BottomRight>3</sml:BottomRight>
<sml:BaseLeft>10</sml:BaseLeft>
<sml:BaseCenter>11</sml:BaseCenter>
<sml:BaseRight>12</sml:BaseRight>
</sml:PosiotnPriority>
<sml:AutoVoidPosition>TRUE</sml:AutoVoidPosition>
<sml:AvoidMode>AvoidModeFour</sml:AvoidMode>
<sml:MaxObjectNum>0</sml:MaxObjectNum>
<sml:NumFormat/>
<sml:LineDirection>AlongLine</sml:LineDirection>
<sml:RepeatLength>0.000000</sml:RepeatLength>
<sml:TextSpace>1.100000</sml:TextSpace>
<sml:IsAutoAdjustRegionLabel>FALSE</sml:IsAutoAdjustRegionLabel>
<sml:IsVerticalLabel>FALSE</sml:IsVerticalLabel>
<sml:LabelTextSpace>0.000000</sml:LabelTextSpace>
<sml:LeaderLineToBack>FALSE</sml:LeaderLineToBack>
<sml:LineDirectionAngle>60.000000</sml:LineDirectionAngle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>Center</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,131071)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>4</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>TRUE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MaxHeight>0</sml:MaxHeight>
<sml:MinHeight>0</sml:MinHeight>
<sml:MaxWidth>0</sml:MaxWidth>
<sml:MinWidth>0</sml:MinWidth>
<sml:TextDiversificationType>LabelPosition</sml:TextDiversificationType>
<sml:TextDiversificationDelims/>
<sml:LabelType>LabelText</sml:LabelType>
<sml:LabelPictureStyle>
<sml:Width>0.000000000000000</sml:Width>
<sml:Height>0.000000000000000</sml:Height>
<sml:Angle>0.000000000000000</sml:Angle>
<sml:Type>0</sml:Type>
<sml:FixedSize>TRUE</sml:FixedSize>
<sml:TransparentRate>0</sml:TransparentRate>
</sml:LabelPictureStyle>
<sml:RangeItems>
<sml:RangeItem/>
</sml:RangeItems>
<sml:TextDiversification>FALSE</sml:TextDiversification>
<sml:RangeMethod>6</sml:RangeMethod>
<sml:CustomInterval>0.0000000000</sml:CustomInterval>
<sml:IsTextExpression>FALSE</sml:IsTextExpression>
<sml:IsDissolveAlongLines>FALSE</sml:IsDissolveAlongLines>
<sml:AlongLineWordAngleRange>200</sml:AlongLineWordAngleRange>
<sml:AlongLineResampleSmooth>FALSE</sml:AlongLineResampleSmooth>
<sml:AlongLineResampleTolerance>1.000000000000000</sml:AlongLineResampleTolerance>
<sml:AlongLineSmoothness>2</sml:AlongLineSmoothness>
<sml:AlongLineDrawingMode>0</sml:AlongLineDrawingMode>
<sml:IsRarefyPoints>FALSE</sml:IsRarefyPoints>
<sml:IsOnlyDrawWithinRegionText>FALSE</sml:IsOnlyDrawWithinRegionText>
<sml:IsShouTextInRegionThrend>FALSE</sml:IsShouTextInRegionThrend>
<sml:RarefyPointsRadius>4.000000000000000</sml:RarefyPointsRadius>
<sml:RarefyThreshold>1000</sml:RarefyThreshold>
<sml:AlongLineSelfOverlap>TRUE</sml:AlongLineSelfOverlap>
<sml:IsDisplayByDomain>FALSE</sml:IsDisplayByDomain>
<sml:AlongLineLabelMode>0</sml:AlongLineLabelMode>
<sml:AlongLineAllowCondition>0</sml:AlongLineAllowCondition>
<sml:AlongLineAutoAvoid>FALSE</sml:AlongLineAutoAvoid>
<sml:AlongLineResampleLine>FALSE</sml:AlongLineResampleLine>
</sml:Theme>
</sml:Cell>
<sml:Cell>
<sml:Theme>
<sml:Type>7</sml:Type>
<sml:Caption/>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>'加桩号：'||center_line_L.sub_number||'null'</sml:FieldExpression>
<sml:ExplodeXExpression/>
<sml:ExplodeYExpression/>
<sml:FontTypeExpression/>
<sml:FontColorExpression/>
<sml:FontSizeExpression/>
<sml:FontAngleExpression/>
<sml:LabelOverLengthMode>None</sml:LabelOverLengthMode>
<sml:SplitSeparator/>
<sml:AlongLanguageCustom>ALC_Chinese</sml:AlongLanguageCustom>
<sml:TextLengthLimit>256</sml:TextLengthLimit>
<sml:Options>
<sml:EnableFlow>FALSE</sml:EnableFlow>
<sml:ShowLeaderLines>FALSE</sml:ShowLeaderLines>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:AlongLine>TRUE</sml:AlongLine>
<sml:SoleLabelAlongLine>FALSE</sml:SoleLabelAlongLine>
<sml:LabelAlongLineType>UGAlongLineSignify</sml:LabelAlongLineType>
<sml:LabelAlongLineJoint>FALSE</sml:LabelAlongLineJoint>
<sml:FixedAngle>FALSE</sml:FixedAngle>
<sml:AutoAvoid>TRUE</sml:AutoAvoid>
<sml:OptimizeMutilineAlignment>FALSE</sml:OptimizeMutilineAlignment>
<sml:OnTop>FALSE</sml:OnTop>
<sml:Priority>0</sml:Priority>
<sml:FixedRepeatLength>FALSE</sml:FixedRepeatLength>
<sml:IgnoreLittleObject>TRUE</sml:IgnoreLittleObject>
<sml:SelfOverlap>TRUE</sml:SelfOverlap>
</sml:Options>
<sml:LeaderLineStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>39</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:LeaderLineStyle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>TopLeft</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,130816)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>6</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>FALSE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
<sml:BackType>None</sml:BackType>
<sml:BackStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131020)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:BackStyle>
<sml:BufferX>0.0000000000</sml:BufferX>
<sml:BufferY>0.0000000000</sml:BufferY>
<sml:PosiotnPriority>
<sml:TopLeft>2</sml:TopLeft>
<sml:TopCenter>5</sml:TopCenter>
<sml:TopRight>4</sml:TopRight>
<sml:CenterLeft>6</sml:CenterLeft>
<sml:Center>7</sml:Center>
<sml:CenterRight>8</sml:CenterRight>
<sml:BottomLeft>1</sml:BottomLeft>
<sml:BottomCenter>9</sml:BottomCenter>
<sml:BottomRight>3</sml:BottomRight>
<sml:BaseLeft>10</sml:BaseLeft>
<sml:BaseCenter>11</sml:BaseCenter>
<sml:BaseRight>12</sml:BaseRight>
</sml:PosiotnPriority>
<sml:AutoVoidPosition>TRUE</sml:AutoVoidPosition>
<sml:AvoidMode>AvoidModeFour</sml:AvoidMode>
<sml:MaxObjectNum>0</sml:MaxObjectNum>
<sml:NumFormat/>
<sml:LineDirection>AlongLine</sml:LineDirection>
<sml:RepeatLength>0.000000</sml:RepeatLength>
<sml:TextSpace>1.100000</sml:TextSpace>
<sml:IsAutoAdjustRegionLabel>FALSE</sml:IsAutoAdjustRegionLabel>
<sml:IsVerticalLabel>FALSE</sml:IsVerticalLabel>
<sml:LabelTextSpace>0.000000</sml:LabelTextSpace>
<sml:LeaderLineToBack>FALSE</sml:LeaderLineToBack>
<sml:LineDirectionAngle>60.000000</sml:LineDirectionAngle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>Center</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,131071)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>4</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>TRUE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MaxHeight>0</sml:MaxHeight>
<sml:MinHeight>0</sml:MinHeight>
<sml:MaxWidth>0</sml:MaxWidth>
<sml:MinWidth>0</sml:MinWidth>
<sml:TextDiversificationType>LabelPosition</sml:TextDiversificationType>
<sml:TextDiversificationDelims/>
<sml:LabelType>LabelText</sml:LabelType>
<sml:LabelPictureStyle>
<sml:Width>0.000000000000000</sml:Width>
<sml:Height>0.000000000000000</sml:Height>
<sml:Angle>0.000000000000000</sml:Angle>
<sml:Type>0</sml:Type>
<sml:FixedSize>TRUE</sml:FixedSize>
<sml:TransparentRate>0</sml:TransparentRate>
</sml:LabelPictureStyle>
<sml:RangeItems>
<sml:RangeItem/>
</sml:RangeItems>
<sml:TextDiversification>FALSE</sml:TextDiversification>
<sml:RangeMethod>6</sml:RangeMethod>
<sml:CustomInterval>0.0000000000</sml:CustomInterval>
<sml:IsTextExpression>FALSE</sml:IsTextExpression>
<sml:IsDissolveAlongLines>FALSE</sml:IsDissolveAlongLines>
<sml:AlongLineWordAngleRange>200</sml:AlongLineWordAngleRange>
<sml:AlongLineResampleSmooth>FALSE</sml:AlongLineResampleSmooth>
<sml:AlongLineResampleTolerance>1.000000000000000</sml:AlongLineResampleTolerance>
<sml:AlongLineSmoothness>2</sml:AlongLineSmoothness>
<sml:AlongLineDrawingMode>0</sml:AlongLineDrawingMode>
<sml:IsRarefyPoints>FALSE</sml:IsRarefyPoints>
<sml:IsOnlyDrawWithinRegionText>FALSE</sml:IsOnlyDrawWithinRegionText>
<sml:IsShouTextInRegionThrend>FALSE</sml:IsShouTextInRegionThrend>
<sml:RarefyPointsRadius>4.000000000000000</sml:RarefyPointsRadius>
<sml:RarefyThreshold>1000</sml:RarefyThreshold>
<sml:AlongLineSelfOverlap>TRUE</sml:AlongLineSelfOverlap>
<sml:IsDisplayByDomain>FALSE</sml:IsDisplayByDomain>
<sml:AlongLineLabelMode>0</sml:AlongLineLabelMode>
<sml:AlongLineAllowCondition>0</sml:AlongLineAllowCondition>
<sml:AlongLineAutoAvoid>FALSE</sml:AlongLineAutoAvoid>
<sml:AlongLineResampleLine>FALSE</sml:AlongLineResampleLine>
</sml:Theme>
</sml:Cell>
</sml:Cells>
</sml:Table>
<sml:RangeItems>
<sml:RangeItem/>
</sml:RangeItems>
<sml:TextDiversification>FALSE</sml:TextDiversification>
<sml:RangeMethod>6</sml:RangeMethod>
<sml:CustomInterval>0.0000000000</sml:CustomInterval>
<sml:IsTextExpression>FALSE</sml:IsTextExpression>
<sml:IsDissolveAlongLines>FALSE</sml:IsDissolveAlongLines>
<sml:AlongLineWordAngleRange>200</sml:AlongLineWordAngleRange>
<sml:AlongLineResampleSmooth>FALSE</sml:AlongLineResampleSmooth>
<sml:AlongLineResampleTolerance>1.000000000000000</sml:AlongLineResampleTolerance>
<sml:AlongLineSmoothness>2</sml:AlongLineSmoothness>
<sml:AlongLineDrawingMode>0</sml:AlongLineDrawingMode>
<sml:IsRarefyPoints>FALSE</sml:IsRarefyPoints>
<sml:IsOnlyDrawWithinRegionText>FALSE</sml:IsOnlyDrawWithinRegionText>
<sml:IsShouTextInRegionThrend>FALSE</sml:IsShouTextInRegionThrend>
<sml:RarefyPointsRadius>4.000000000000000</sml:RarefyPointsRadius>
<sml:RarefyThreshold>1000</sml:RarefyThreshold>
<sml:AlongLineSelfOverlap>TRUE</sml:AlongLineSelfOverlap>
<sml:IsDisplayByDomain>FALSE</sml:IsDisplayByDomain>
<sml:AlongLineLabelMode>0</sml:AlongLineLabelMode>
<sml:AlongLineAllowCondition>0</sml:AlongLineAllowCondition>
<sml:AlongLineAutoAvoid>FALSE</sml:AlongLineAutoAvoid>
<sml:AlongLineResampleLine>FALSE</sml:AlongLineResampleLine>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>103</sml:DatasetType>
<sml:Caption>中线-线</sml:Caption>
<sml:Name>center_line_L@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>center_line_L</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>20</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>200</sml:MarkerWidth>
<sml:MarkerHeight>200</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>2</sml:LineWidth>
<sml:LineColor>RGB(163,52,130892)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>TRUE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>中线-点标签</sml:Caption>
<sml:Name>center_line_P@EntitiesAchivments#1</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>center_line_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(208,255,131056)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>FALSE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>FALSE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:Theme>
<sml:Type>7</sml:Type>
<sml:Caption>center_line_P@EntitiesAchivments</sml:Caption>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression/>
<sml:ExplodeXExpression/>
<sml:ExplodeYExpression/>
<sml:FontTypeExpression/>
<sml:FontColorExpression/>
<sml:FontSizeExpression/>
<sml:FontAngleExpression/>
<sml:LabelOverLengthMode>None</sml:LabelOverLengthMode>
<sml:SplitSeparator/>
<sml:AlongLanguageCustom>ALC_Chinese</sml:AlongLanguageCustom>
<sml:TextLengthLimit>256</sml:TextLengthLimit>
<sml:Options>
<sml:EnableFlow>FALSE</sml:EnableFlow>
<sml:ShowLeaderLines>FALSE</sml:ShowLeaderLines>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:AlongLine>TRUE</sml:AlongLine>
<sml:SoleLabelAlongLine>FALSE</sml:SoleLabelAlongLine>
<sml:LabelAlongLineType>UGAlongLineSignify</sml:LabelAlongLineType>
<sml:LabelAlongLineJoint>FALSE</sml:LabelAlongLineJoint>
<sml:FixedAngle>FALSE</sml:FixedAngle>
<sml:AutoAvoid>TRUE</sml:AutoAvoid>
<sml:OptimizeMutilineAlignment>FALSE</sml:OptimizeMutilineAlignment>
<sml:OnTop>FALSE</sml:OnTop>
<sml:Priority>0</sml:Priority>
<sml:FixedRepeatLength>FALSE</sml:FixedRepeatLength>
<sml:IgnoreLittleObject>TRUE</sml:IgnoreLittleObject>
<sml:SelfOverlap>TRUE</sml:SelfOverlap>
</sml:Options>
<sml:LeaderLineStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>39</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:LeaderLineStyle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>TopLeft</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,130816)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>6</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>FALSE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
<sml:BackType>None</sml:BackType>
<sml:BackStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131020)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:BackStyle>
<sml:BufferX>0.0000000000</sml:BufferX>
<sml:BufferY>0.0000000000</sml:BufferY>
<sml:PosiotnPriority>
<sml:TopLeft>2</sml:TopLeft>
<sml:TopCenter>5</sml:TopCenter>
<sml:TopRight>4</sml:TopRight>
<sml:CenterLeft>6</sml:CenterLeft>
<sml:Center>7</sml:Center>
<sml:CenterRight>8</sml:CenterRight>
<sml:BottomLeft>1</sml:BottomLeft>
<sml:BottomCenter>9</sml:BottomCenter>
<sml:BottomRight>3</sml:BottomRight>
<sml:BaseLeft>10</sml:BaseLeft>
<sml:BaseCenter>11</sml:BaseCenter>
<sml:BaseRight>12</sml:BaseRight>
</sml:PosiotnPriority>
<sml:AutoVoidPosition>TRUE</sml:AutoVoidPosition>
<sml:AvoidMode>AvoidModeFour</sml:AvoidMode>
<sml:MaxObjectNum>0</sml:MaxObjectNum>
<sml:NumFormat/>
<sml:LineDirection>AlongLine</sml:LineDirection>
<sml:RepeatLength>0.000000</sml:RepeatLength>
<sml:TextSpace>1.100000</sml:TextSpace>
<sml:IsAutoAdjustRegionLabel>FALSE</sml:IsAutoAdjustRegionLabel>
<sml:IsVerticalLabel>FALSE</sml:IsVerticalLabel>
<sml:LabelTextSpace>0.000000</sml:LabelTextSpace>
<sml:LeaderLineToBack>FALSE</sml:LeaderLineToBack>
<sml:LineDirectionAngle>60.000000</sml:LineDirectionAngle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>Center</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,131071)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>4</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>TRUE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MaxHeight>0</sml:MaxHeight>
<sml:MinHeight>0</sml:MinHeight>
<sml:MaxWidth>0</sml:MaxWidth>
<sml:MinWidth>0</sml:MinWidth>
<sml:TextDiversificationType>LabelPosition</sml:TextDiversificationType>
<sml:TextDiversificationDelims/>
<sml:LabelType>LabelTable</sml:LabelType>
<sml:LabelPictureStyle>
<sml:Width>0.000000000000000</sml:Width>
<sml:Height>0.000000000000000</sml:Height>
<sml:Angle>0.000000000000000</sml:Angle>
<sml:Type>0</sml:Type>
<sml:FixedSize>TRUE</sml:FixedSize>
<sml:TransparentRate>0</sml:TransparentRate>
</sml:LabelPictureStyle>
<sml:Table>
<sml:Rows>2</sml:Rows>
<sml:Cols>1</sml:Cols>
<sml:Cells>
<sml:Cell>
<sml:Theme>
<sml:Type>7</sml:Type>
<sml:Caption/>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>'主桩号：'||center_line_P.stake_number||'null'</sml:FieldExpression>
<sml:ExplodeXExpression/>
<sml:ExplodeYExpression/>
<sml:FontTypeExpression/>
<sml:FontColorExpression/>
<sml:FontSizeExpression/>
<sml:FontAngleExpression/>
<sml:LabelOverLengthMode>None</sml:LabelOverLengthMode>
<sml:SplitSeparator/>
<sml:AlongLanguageCustom>ALC_Chinese</sml:AlongLanguageCustom>
<sml:TextLengthLimit>256</sml:TextLengthLimit>
<sml:Options>
<sml:EnableFlow>FALSE</sml:EnableFlow>
<sml:ShowLeaderLines>FALSE</sml:ShowLeaderLines>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:AlongLine>TRUE</sml:AlongLine>
<sml:SoleLabelAlongLine>FALSE</sml:SoleLabelAlongLine>
<sml:LabelAlongLineType>UGAlongLineSignify</sml:LabelAlongLineType>
<sml:LabelAlongLineJoint>FALSE</sml:LabelAlongLineJoint>
<sml:FixedAngle>FALSE</sml:FixedAngle>
<sml:AutoAvoid>TRUE</sml:AutoAvoid>
<sml:OptimizeMutilineAlignment>FALSE</sml:OptimizeMutilineAlignment>
<sml:OnTop>FALSE</sml:OnTop>
<sml:Priority>0</sml:Priority>
<sml:FixedRepeatLength>FALSE</sml:FixedRepeatLength>
<sml:IgnoreLittleObject>TRUE</sml:IgnoreLittleObject>
<sml:SelfOverlap>TRUE</sml:SelfOverlap>
</sml:Options>
<sml:LeaderLineStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>39</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:LeaderLineStyle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>TopLeft</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,130816)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>6</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>FALSE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
<sml:BackType>None</sml:BackType>
<sml:BackStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131020)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:BackStyle>
<sml:BufferX>0.0000000000</sml:BufferX>
<sml:BufferY>0.0000000000</sml:BufferY>
<sml:PosiotnPriority>
<sml:TopLeft>2</sml:TopLeft>
<sml:TopCenter>5</sml:TopCenter>
<sml:TopRight>4</sml:TopRight>
<sml:CenterLeft>6</sml:CenterLeft>
<sml:Center>7</sml:Center>
<sml:CenterRight>8</sml:CenterRight>
<sml:BottomLeft>1</sml:BottomLeft>
<sml:BottomCenter>9</sml:BottomCenter>
<sml:BottomRight>3</sml:BottomRight>
<sml:BaseLeft>10</sml:BaseLeft>
<sml:BaseCenter>11</sml:BaseCenter>
<sml:BaseRight>12</sml:BaseRight>
</sml:PosiotnPriority>
<sml:AutoVoidPosition>TRUE</sml:AutoVoidPosition>
<sml:AvoidMode>AvoidModeFour</sml:AvoidMode>
<sml:MaxObjectNum>0</sml:MaxObjectNum>
<sml:NumFormat/>
<sml:LineDirection>AlongLine</sml:LineDirection>
<sml:RepeatLength>0.000000</sml:RepeatLength>
<sml:TextSpace>1.100000</sml:TextSpace>
<sml:IsAutoAdjustRegionLabel>FALSE</sml:IsAutoAdjustRegionLabel>
<sml:IsVerticalLabel>FALSE</sml:IsVerticalLabel>
<sml:LabelTextSpace>0.000000</sml:LabelTextSpace>
<sml:LeaderLineToBack>FALSE</sml:LeaderLineToBack>
<sml:LineDirectionAngle>60.000000</sml:LineDirectionAngle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>Center</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,131071)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>4</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>TRUE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MaxHeight>0</sml:MaxHeight>
<sml:MinHeight>0</sml:MinHeight>
<sml:MaxWidth>0</sml:MaxWidth>
<sml:MinWidth>0</sml:MinWidth>
<sml:TextDiversificationType>LabelPosition</sml:TextDiversificationType>
<sml:TextDiversificationDelims/>
<sml:LabelType>LabelText</sml:LabelType>
<sml:LabelPictureStyle>
<sml:Width>0.000000000000000</sml:Width>
<sml:Height>0.000000000000000</sml:Height>
<sml:Angle>0.000000000000000</sml:Angle>
<sml:Type>0</sml:Type>
<sml:FixedSize>TRUE</sml:FixedSize>
<sml:TransparentRate>0</sml:TransparentRate>
</sml:LabelPictureStyle>
<sml:RangeItems>
<sml:RangeItem/>
</sml:RangeItems>
<sml:TextDiversification>FALSE</sml:TextDiversification>
<sml:RangeMethod>6</sml:RangeMethod>
<sml:CustomInterval>0.0000000000</sml:CustomInterval>
<sml:IsTextExpression>FALSE</sml:IsTextExpression>
<sml:IsDissolveAlongLines>FALSE</sml:IsDissolveAlongLines>
<sml:AlongLineWordAngleRange>200</sml:AlongLineWordAngleRange>
<sml:AlongLineResampleSmooth>FALSE</sml:AlongLineResampleSmooth>
<sml:AlongLineResampleTolerance>1.000000000000000</sml:AlongLineResampleTolerance>
<sml:AlongLineSmoothness>2</sml:AlongLineSmoothness>
<sml:AlongLineDrawingMode>0</sml:AlongLineDrawingMode>
<sml:IsRarefyPoints>FALSE</sml:IsRarefyPoints>
<sml:IsOnlyDrawWithinRegionText>FALSE</sml:IsOnlyDrawWithinRegionText>
<sml:IsShouTextInRegionThrend>FALSE</sml:IsShouTextInRegionThrend>
<sml:RarefyPointsRadius>4.000000000000000</sml:RarefyPointsRadius>
<sml:RarefyThreshold>1000</sml:RarefyThreshold>
<sml:AlongLineSelfOverlap>TRUE</sml:AlongLineSelfOverlap>
<sml:IsDisplayByDomain>FALSE</sml:IsDisplayByDomain>
<sml:AlongLineLabelMode>0</sml:AlongLineLabelMode>
<sml:AlongLineAllowCondition>0</sml:AlongLineAllowCondition>
<sml:AlongLineAutoAvoid>FALSE</sml:AlongLineAutoAvoid>
<sml:AlongLineResampleLine>FALSE</sml:AlongLineResampleLine>
</sml:Theme>
</sml:Cell>
<sml:Cell>
<sml:Theme>
<sml:Type>7</sml:Type>
<sml:Caption/>
<sml:ThemeWeight>0</sml:ThemeWeight>
<sml:Visible>TRUE</sml:Visible>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
<sml:ExtendExpressions/>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:ThemRepreFieldName/>
<sml:FieldExpression>'加桩号：'||center_line_P.sub_number||'null'</sml:FieldExpression>
<sml:ExplodeXExpression/>
<sml:ExplodeYExpression/>
<sml:FontTypeExpression/>
<sml:FontColorExpression/>
<sml:FontSizeExpression/>
<sml:FontAngleExpression/>
<sml:LabelOverLengthMode>None</sml:LabelOverLengthMode>
<sml:SplitSeparator/>
<sml:AlongLanguageCustom>ALC_Chinese</sml:AlongLanguageCustom>
<sml:TextLengthLimit>256</sml:TextLengthLimit>
<sml:Options>
<sml:EnableFlow>FALSE</sml:EnableFlow>
<sml:ShowLeaderLines>FALSE</sml:ShowLeaderLines>
<sml:FixedOffset>FALSE</sml:FixedOffset>
<sml:AlongLine>TRUE</sml:AlongLine>
<sml:SoleLabelAlongLine>FALSE</sml:SoleLabelAlongLine>
<sml:LabelAlongLineType>UGAlongLineSignify</sml:LabelAlongLineType>
<sml:LabelAlongLineJoint>FALSE</sml:LabelAlongLineJoint>
<sml:FixedAngle>FALSE</sml:FixedAngle>
<sml:AutoAvoid>TRUE</sml:AutoAvoid>
<sml:OptimizeMutilineAlignment>FALSE</sml:OptimizeMutilineAlignment>
<sml:OnTop>FALSE</sml:OnTop>
<sml:Priority>0</sml:Priority>
<sml:FixedRepeatLength>FALSE</sml:FixedRepeatLength>
<sml:IgnoreLittleObject>TRUE</sml:IgnoreLittleObject>
<sml:SelfOverlap>TRUE</sml:SelfOverlap>
</sml:Options>
<sml:LeaderLineStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>39</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:LeaderLineStyle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>TopLeft</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,130816)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>6</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>FALSE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:OffsetXExpression/>
<sml:OffsetYExpression/>
<sml:BackType>None</sml:BackType>
<sml:BackStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131020)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:BackStyle>
<sml:BufferX>0.0000000000</sml:BufferX>
<sml:BufferY>0.0000000000</sml:BufferY>
<sml:PosiotnPriority>
<sml:TopLeft>2</sml:TopLeft>
<sml:TopCenter>5</sml:TopCenter>
<sml:TopRight>4</sml:TopRight>
<sml:CenterLeft>6</sml:CenterLeft>
<sml:Center>7</sml:Center>
<sml:CenterRight>8</sml:CenterRight>
<sml:BottomLeft>1</sml:BottomLeft>
<sml:BottomCenter>9</sml:BottomCenter>
<sml:BottomRight>3</sml:BottomRight>
<sml:BaseLeft>10</sml:BaseLeft>
<sml:BaseCenter>11</sml:BaseCenter>
<sml:BaseRight>12</sml:BaseRight>
</sml:PosiotnPriority>
<sml:AutoVoidPosition>TRUE</sml:AutoVoidPosition>
<sml:AvoidMode>AvoidModeFour</sml:AvoidMode>
<sml:MaxObjectNum>0</sml:MaxObjectNum>
<sml:NumFormat/>
<sml:LineDirection>AlongLine</sml:LineDirection>
<sml:RepeatLength>0.000000</sml:RepeatLength>
<sml:TextSpace>1.100000</sml:TextSpace>
<sml:IsAutoAdjustRegionLabel>FALSE</sml:IsAutoAdjustRegionLabel>
<sml:IsVerticalLabel>FALSE</sml:IsVerticalLabel>
<sml:LabelTextSpace>0.000000</sml:LabelTextSpace>
<sml:LeaderLineToBack>FALSE</sml:LeaderLineToBack>
<sml:LineDirectionAngle>60.000000</sml:LineDirectionAngle>
<sml:TextStyle>
<sml:FaceName>宋体</sml:FaceName>
<sml:Align>Center</sml:Align>
<sml:StringAlign>StringTopLeft</sml:StringAlign>
<sml:ForeColor>RGB(0,0,131071)</sml:ForeColor>
<sml:BackColor>RGB(0,0,130816)</sml:BackColor>
<sml:BackOpaque>FALSE</sml:BackOpaque>
<sml:Height>4</sml:Height>
<sml:Width>0</sml:Width>
<sml:Weight>0</sml:Weight>
<sml:IsFixedSize>TRUE</sml:IsFixedSize>
<sml:FixedSize>0</sml:FixedSize>
<sml:Angle>0</sml:Angle>
<sml:Bold>FALSE</sml:Bold>
<sml:Italic>FALSE</sml:Italic>
<sml:ItalicAngle>0.00</sml:ItalicAngle>
<sml:StrikeOut>FALSE</sml:StrikeOut>
<sml:Underline>FALSE</sml:Underline>
<sml:Shadow>FALSE</sml:Shadow>
<sml:Halo>FALSE</sml:Halo>
<sml:Text3DOpaque>100</sml:Text3DOpaque>
<sml:Text3DScale>1.00</sml:Text3DScale>
<sml:HaloWidth>1</sml:HaloWidth>
<sml:BorderSpacingWidth>4</sml:BorderSpacingWidth>
<sml:ShadowOffsetX>1.000000</sml:ShadowOffsetX>
<sml:ShadowOffsetY>1.000000</sml:ShadowOffsetY>
<sml:ShadowColor>RGB(128,128,130944)</sml:ShadowColor>
<sml:FontInterval>0</sml:FontInterval>
<sml:DownAngle>0.000000</sml:DownAngle>
<sml:WordAngle>0.000000</sml:WordAngle>
<sml:BackBorderStyle>0</sml:BackBorderStyle>
<sml:BackLineWidth>1</sml:BackLineWidth>
<sml:Vertical>FALSE</sml:Vertical>
</sml:TextStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MarkerSymbolStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:MarkerSymbolStyle>
<sml:MaxHeight>0</sml:MaxHeight>
<sml:MinHeight>0</sml:MinHeight>
<sml:MaxWidth>0</sml:MaxWidth>
<sml:MinWidth>0</sml:MinWidth>
<sml:TextDiversificationType>LabelPosition</sml:TextDiversificationType>
<sml:TextDiversificationDelims/>
<sml:LabelType>LabelText</sml:LabelType>
<sml:LabelPictureStyle>
<sml:Width>0.000000000000000</sml:Width>
<sml:Height>0.000000000000000</sml:Height>
<sml:Angle>0.000000000000000</sml:Angle>
<sml:Type>0</sml:Type>
<sml:FixedSize>TRUE</sml:FixedSize>
<sml:TransparentRate>0</sml:TransparentRate>
</sml:LabelPictureStyle>
<sml:RangeItems>
<sml:RangeItem/>
</sml:RangeItems>
<sml:TextDiversification>FALSE</sml:TextDiversification>
<sml:RangeMethod>6</sml:RangeMethod>
<sml:CustomInterval>0.0000000000</sml:CustomInterval>
<sml:IsTextExpression>FALSE</sml:IsTextExpression>
<sml:IsDissolveAlongLines>FALSE</sml:IsDissolveAlongLines>
<sml:AlongLineWordAngleRange>200</sml:AlongLineWordAngleRange>
<sml:AlongLineResampleSmooth>FALSE</sml:AlongLineResampleSmooth>
<sml:AlongLineResampleTolerance>1.000000000000000</sml:AlongLineResampleTolerance>
<sml:AlongLineSmoothness>2</sml:AlongLineSmoothness>
<sml:AlongLineDrawingMode>0</sml:AlongLineDrawingMode>
<sml:IsRarefyPoints>FALSE</sml:IsRarefyPoints>
<sml:IsOnlyDrawWithinRegionText>FALSE</sml:IsOnlyDrawWithinRegionText>
<sml:IsShouTextInRegionThrend>FALSE</sml:IsShouTextInRegionThrend>
<sml:RarefyPointsRadius>4.000000000000000</sml:RarefyPointsRadius>
<sml:RarefyThreshold>1000</sml:RarefyThreshold>
<sml:AlongLineSelfOverlap>TRUE</sml:AlongLineSelfOverlap>
<sml:IsDisplayByDomain>FALSE</sml:IsDisplayByDomain>
<sml:AlongLineLabelMode>0</sml:AlongLineLabelMode>
<sml:AlongLineAllowCondition>0</sml:AlongLineAllowCondition>
<sml:AlongLineAutoAvoid>FALSE</sml:AlongLineAutoAvoid>
<sml:AlongLineResampleLine>FALSE</sml:AlongLineResampleLine>
</sml:Theme>
</sml:Cell>
</sml:Cells>
</sml:Table>
<sml:RangeItems>
<sml:RangeItem/>
</sml:RangeItems>
<sml:TextDiversification>FALSE</sml:TextDiversification>
<sml:RangeMethod>6</sml:RangeMethod>
<sml:CustomInterval>0.0000000000</sml:CustomInterval>
<sml:IsTextExpression>FALSE</sml:IsTextExpression>
<sml:IsDissolveAlongLines>FALSE</sml:IsDissolveAlongLines>
<sml:AlongLineWordAngleRange>200</sml:AlongLineWordAngleRange>
<sml:AlongLineResampleSmooth>FALSE</sml:AlongLineResampleSmooth>
<sml:AlongLineResampleTolerance>1.000000000000000</sml:AlongLineResampleTolerance>
<sml:AlongLineSmoothness>2</sml:AlongLineSmoothness>
<sml:AlongLineDrawingMode>0</sml:AlongLineDrawingMode>
<sml:IsRarefyPoints>FALSE</sml:IsRarefyPoints>
<sml:IsOnlyDrawWithinRegionText>FALSE</sml:IsOnlyDrawWithinRegionText>
<sml:IsShouTextInRegionThrend>FALSE</sml:IsShouTextInRegionThrend>
<sml:RarefyPointsRadius>4.000000000000000</sml:RarefyPointsRadius>
<sml:RarefyThreshold>1000</sml:RarefyThreshold>
<sml:AlongLineSelfOverlap>TRUE</sml:AlongLineSelfOverlap>
<sml:IsDisplayByDomain>FALSE</sml:IsDisplayByDomain>
<sml:AlongLineLabelMode>0</sml:AlongLineLabelMode>
<sml:AlongLineAllowCondition>0</sml:AlongLineAllowCondition>
<sml:AlongLineAutoAvoid>FALSE</sml:AlongLineAutoAvoid>
<sml:AlongLineResampleLine>FALSE</sml:AlongLineResampleLine>
</sml:Theme>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
<sml:Layer>
<sml:Type>1</sml:Type>
<sml:LayerType>1</sml:LayerType>
<sml:LayerSubType>102</sml:LayerSubType>
<sml:DatasetType>101</sml:DatasetType>
<sml:Caption>中线-点</sml:Caption>
<sml:Name>center_line_P@EntitiesAchivments</sml:Name>
<sml:Description/>
<sml:DataSourceAlias>EntitiesAchivments</sml:DataSourceAlias>
<sml:DatasetName>center_line_P</sml:DatasetName>
<sml:LayerWeight>0</sml:LayerWeight>
<sml:FillOrgMode>Absolute</sml:FillOrgMode>
<sml:Style>
<sml:MarkerStyle>92000084</sml:MarkerStyle>
<sml:MarkerSize>20</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>200</sml:MarkerWidth>
<sml:MarkerHeight>200</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>FALSE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:RasterNewTag>FALSE</sml:RasterNewTag>
<sml:RasterOpaqueRate>100</sml:RasterOpaqueRate>
<sml:RasterBrightness>0</sml:RasterBrightness>
<sml:RasterContrast>0</sml:RasterContrast>
<sml:RasterTransColorTransparent>FALSE</sml:RasterTransColorTransparent>
<sml:ColorDictionaryAutoInterpolation>TRUE</sml:ColorDictionaryAutoInterpolation>
<sml:BackgroundTransparent>FALSE</sml:BackgroundTransparent>
<sml:RasterBkColor>RGB(255,255,131071)</sml:RasterBkColor>
<sml:RasterBKTolerance>0</sml:RasterBKTolerance>
<sml:GridNoData>-9999.000000</sml:GridNoData>
<sml:ImageNoData>-9999.0000000000</sml:ImageNoData>
<sml:ImageInterpolationMode>0</sml:ImageInterpolationMode>
<sml:GridNoDataColor>RGB(255,255,131071)</sml:GridNoDataColor>
<sml:BackgroundColor>RGB(255,255,131071)</sml:BackgroundColor>
<sml:ImageBackground>0.0000000000</sml:ImageBackground>
<sml:ImageBKgroundReplace>FALSE</sml:ImageBKgroundReplace>
<sml:ImageNoDataReplace>FALSE</sml:ImageNoDataReplace>
<sml:NoDataTransparent>FALSE</sml:NoDataTransparent>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:VisibleSettings>
<sml:VisibleObjectMinimum>0.1</sml:VisibleObjectMinimum>
<sml:MinVisibleScale>0.00000000000000000000e+00</sml:MinVisibleScale>
<sml:MaxVisibleScale>0.00000000000000000000e+00</sml:MaxVisibleScale>
</sml:VisibleSettings>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:EditableLock>FALSE</sml:EditableLock>
<sml:Snapable>TRUE</sml:Snapable>
<sml:SymbolScalable>FALSE</sml:SymbolScalable>
<sml:LineAdjust>FALSE</sml:LineAdjust>
<sml:IsAntialias>TRUE</sml:IsAntialias>
<sml:AllowTextOverlap>FALSE</sml:AllowTextOverlap>
<sml:IsAllowTextOverlap>TRUE</sml:IsAllowTextOverlap>
<sml:IsSuperAndSubScript>TRUE</sml:IsSuperAndSubScript>
<sml:CrossroadOptimized>FALSE</sml:CrossroadOptimized>
</sml:Options>
<sml:SymbolScaleDefinition>0</sml:SymbolScaleDefinition>
<sml:PologonInterSect>TRUE</sml:PologonInterSect>
<sml:DisplayClip>TRUE</sml:DisplayClip>
<sml:TransPercent>0</sml:TransPercent>
<sml:IsUseRepresentation>FALSE</sml:IsUseRepresentation>
<sml:RepresentationFieldName/>
<sml:RefRepreFieldName/>
<sml:ThemeRepreFieldName/>
<sml:ImageDisplayMode>0</sml:ImageDisplayMode>
<sml:EnableTimeFilter>FALSE</sml:EnableTimeFilter>
<sml:StartTimeField/>
<sml:EndTimeField/>
<sml:StartFilterTime>1899-12-30-00:00:00</sml:StartFilterTime>
<sml:EndFilterTime>1899-12-30-00:00:00</sml:EndFilterTime>
<sml:TimeStep>0.0000000000</sml:TimeStep>
<sml:WebSubLayers/>
<sml:ExternalInfo/>
<sml:Selection>
<sml:UseCustomStyle>FALSE</sml:UseCustomStyle>
<sml:StyleOptions>69508</sml:StyleOptions>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,131071)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>1</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:Selection>
<sml:LayerExtensionType>0</sml:LayerExtensionType>
<sml:LayerExtensionBounds>
<sml:Left>0.0000000000</sml:Left>
<sml:Top>0.0000000000</sml:Top>
<sml:Right>0.0000000000</sml:Right>
<sml:Bottom>0.0000000000</sml:Bottom>
</sml:LayerExtensionBounds>
<sml:LayerExtensionBaseUserXml/>
<sml:LayerExtensionBaseUserInfo/>
<sml:LayerGridFunctionSetting/>
<sml:DeduplicateEnabled>TRUE</sml:DeduplicateEnabled>
<sml:DeduplicateTolerance>0.50</sml:DeduplicateTolerance>
<sml:DeduplicateThreshold>500</sml:DeduplicateThreshold>
<sml:OverlapGeometryEnabled>FALSE</sml:OverlapGeometryEnabled>
<sml:OverlapGeometryThreshold>1.0</sml:OverlapGeometryThreshold>
<sml:OverlapGeometryPixel>1</sml:OverlapGeometryPixel>
<sml:ExtensionPluginName/>
<sml:FullLabelEnabled>TRUE</sml:FullLabelEnabled>
</sml:Layer>
</sml:Layers>
<sml:BGGridSetting>
<sml:BGGridShow>FALSE</sml:BGGridShow>
<sml:BGGridDP>FALSE</sml:BGGridDP>
<sml:BGGridType>2</sml:BGGridType>
<sml:BGGridMax>100</sml:BGGridMax>
<sml:BGRcMargin>
<sml:Left>0</sml:Left>
<sml:Top>0</sml:Top>
<sml:Right>0</sml:Right>
<sml:Bottom>0</sml:Bottom>
</sml:BGRcMargin>
<sml:BGGridSize>
<SIZEX>0</SIZEX>
<SIZEY>0</SIZEY>
</sml:BGGridSize>
<sml:BGGridStyle>&lt;sml:Style&gt;
&lt;sml:MarkerStyle&gt;0&lt;/sml:MarkerStyle&gt;
&lt;sml:MarkerSize&gt;2.4&lt;/sml:MarkerSize&gt;
&lt;sml:MarkerAngle&gt;0&lt;/sml:MarkerAngle&gt;
&lt;sml:MarkerWidth&gt;24&lt;/sml:MarkerWidth&gt;
&lt;sml:MarkerHeight&gt;24&lt;/sml:MarkerHeight&gt;
&lt;sml:MarkerPicturePath/&gt;
&lt;sml:MarkerSVGPath/&gt;
&lt;sml:LineStyle&gt;0&lt;/sml:LineStyle&gt;
&lt;sml:LineWidth&gt;0.1&lt;/sml:LineWidth&gt;
&lt;sml:LineColor&gt;RGB(0,0,130816)&lt;/sml:LineColor&gt;
&lt;sml:LineCapStyle&gt;0&lt;/sml:LineCapStyle&gt;
&lt;sml:LineJoinStyle&gt;0&lt;/sml:LineJoinStyle&gt;
&lt;sml:FillStyle&gt;0&lt;/sml:FillStyle&gt;
&lt;sml:FillForeColor&gt;RGB(189,235,131071)&lt;/sml:FillForeColor&gt;
&lt;sml:FillBackColor&gt;RGB(255,255,131071)&lt;/sml:FillBackColor&gt;
&lt;sml:FillBackOpaque&gt;TRUE&lt;/sml:FillBackOpaque&gt;
&lt;sml:FillOpaqueRate&gt;100&lt;/sml:FillOpaqueRate&gt;
&lt;sml:FillGradientType&gt;None&lt;/sml:FillGradientType&gt;
&lt;sml:FillAngle&gt;0&lt;/sml:FillAngle&gt;
&lt;sml:FillCenterOffsetX&gt;0&lt;/sml:FillCenterOffsetX&gt;
&lt;sml:FillCenterOffsetY&gt;0&lt;/sml:FillCenterOffsetY&gt;
&lt;sml:AcrossLongtitude&gt;FALSE&lt;/sml:AcrossLongtitude&gt;
&lt;/sml:Style&gt;
</sml:BGGridStyle>
<sml:BGGridSpaceStyle>&lt;sml:Style&gt;
&lt;sml:MarkerStyle&gt;0&lt;/sml:MarkerStyle&gt;
&lt;sml:MarkerSize&gt;2.4&lt;/sml:MarkerSize&gt;
&lt;sml:MarkerAngle&gt;0&lt;/sml:MarkerAngle&gt;
&lt;sml:MarkerWidth&gt;24&lt;/sml:MarkerWidth&gt;
&lt;sml:MarkerHeight&gt;24&lt;/sml:MarkerHeight&gt;
&lt;sml:MarkerPicturePath/&gt;
&lt;sml:MarkerSVGPath/&gt;
&lt;sml:LineStyle&gt;2&lt;/sml:LineStyle&gt;
&lt;sml:LineWidth&gt;0.1&lt;/sml:LineWidth&gt;
&lt;sml:LineColor&gt;RGB(0,0,130816)&lt;/sml:LineColor&gt;
&lt;sml:LineCapStyle&gt;0&lt;/sml:LineCapStyle&gt;
&lt;sml:LineJoinStyle&gt;0&lt;/sml:LineJoinStyle&gt;
&lt;sml:FillStyle&gt;0&lt;/sml:FillStyle&gt;
&lt;sml:FillForeColor&gt;RGB(189,235,131071)&lt;/sml:FillForeColor&gt;
&lt;sml:FillBackColor&gt;RGB(255,255,131071)&lt;/sml:FillBackColor&gt;
&lt;sml:FillBackOpaque&gt;TRUE&lt;/sml:FillBackOpaque&gt;
&lt;sml:FillOpaqueRate&gt;100&lt;/sml:FillOpaqueRate&gt;
&lt;sml:FillGradientType&gt;None&lt;/sml:FillGradientType&gt;
&lt;sml:FillAngle&gt;0&lt;/sml:FillAngle&gt;
&lt;sml:FillCenterOffsetX&gt;0&lt;/sml:FillCenterOffsetX&gt;
&lt;sml:FillCenterOffsetY&gt;0&lt;/sml:FillCenterOffsetY&gt;
&lt;sml:AcrossLongtitude&gt;FALSE&lt;/sml:AcrossLongtitude&gt;
&lt;/sml:Style&gt;
</sml:BGGridSpaceStyle>
</sml:BGGridSetting>
<sml:UseMapSources>FALSE</sml:UseMapSources>
<sml:MapBookMarks/>
<sml:StreamLayers/>
<sml:ExternalXML/>
</sml:Map>
</sml:Maps>
<sml:Layouts>
<sml:Layout>
<sml:LayoutName>defaultStamp</sml:LayoutName>
<sml:ShowGrid>TRUE</sml:ShowGrid>
<sml:AutoModifyMap>FALSE</sml:AutoModifyMap>
<sml:GridSnape>FALSE</sml:GridSnape>
<sml:ShowRuler>TRUE</sml:ShowRuler>
<sml:UseRulerLine>TRUE</sml:UseRulerLine>
<sml:RulerLineCont>0</sml:RulerLineCont>
<sml:LayoutPaper>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>81</sml:ZipLength>
<sml:ElementStream>78DAAB61800291290E607A429703032303C3027E0606130E0806B141926005FF8160C18205FF4F00D9C89811865380440AD82890DE59DCA8CA409813884146DCBB77EF3FC3281805A360C00000DE841A5E000000</sml:ElementStream>
</sml:LayoutPaper>
<sml:LayoutElements>
<sml:LayoutElement>
<sml:ID>1</sml:ID>
<sml:ElmentType>GeoRect</sml:ElmentType>
<sml:ElmentTag>底图</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>49</sml:ZipLength>
<sml:ElementStream>78DA9330BEBEDC5560AE43D90A678133165D0E3AFEC7FF7A2F59EBE0A25BE2F8296CBE03C3281805A360D80200B3270DA3000000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(208,255,131056)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>2</sml:ID>
<sml:ElmentType>GeoRect</sml:ElmentType>
<sml:ElmentTag>底板4</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>50</sml:ZipLength>
<sml:ElementStream>78DA7B31F59D6693D52287AE9CB805A776853B243D10BEB8C96299C3DDFAD2FE131FEA1D1846C1281805C31600003612110F0000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(208,255,131056)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>3</sml:ID>
<sml:ElmentType>GeoRect</sml:ElmentType>
<sml:ElmentTag>底板3</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>50</sml:ZipLength>
<sml:ElementStream>78DA3B70E0B7C32FCB450ED37F6E620978D4E2B0A1EBE7A24396CB1CDCF47DA7FCBCD0ECC0300A46C12818B60000953311A50000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(208,255,131056)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>4</sml:ID>
<sml:ElmentType>GeoRect</sml:ElmentType>
<sml:ElmentTag>底板2</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>49</sml:ZipLength>
<sml:ElementStream>78DAFBB62DB5D2CB7A91C3DDF6BBDE5B6E4C74B8D2EEFCD6CE6299830147F5E28019C50E0CA360148C82610B00F2140FB3000000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(208,255,131056)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>5</sml:ID>
<sml:ElmentType>GeoRect</sml:ElmentType>
<sml:ElmentTag>底板1</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>50</sml:ZipLength>
<sml:ElementStream>78DA0BFA7E4DC39467AE8388F2CB75B7DA6738586D54BFE7B874ADC39E170556851F1A1C1846C1281805C316000084820F9F0000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(255,255,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(208,255,131056)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>6</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>56</sml:ZipLength>
<sml:ElementStream>78DA636460606002E263CACF34D72DE971105A9FB7BADD7F8A43C6CEA9295EC14E07D6D91CBD961F3CC58161148C825130EC0000628E0F72</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>7</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>56</sml:ZipLength>
<sml:ElementStream>78DA636460606002E263CACF34D72DE971708C6DFDFBEDF01C878C9D5353BC829D0EDC7D755BE3FEF1390E0CA360148C82610700A9DF11D4</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>8</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>56</sml:ZipLength>
<sml:ElementStream>78DA636460606002E2B9D13B7ED455F53838ED17CE32983ED1E1877551B9ED379703F77CB2A64ACC9EE8C0300A46C12818760000B5B90F86</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>9</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>54</sml:ZipLength>
<sml:ElementStream>78DA636460606002E23F9C674E7BEDEE71B00CEEAB9A70BDCF8121CCDCADE1A5C381FC3CEB8B09F781FC51300A46C1B003004DBE0F6C0000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>10</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>56</sml:ZipLength>
<sml:ElementStream>78DA636460606002E24989562E170A7A1CAEDFFC7B3CAEB4D321C0345A7CFE02FB038C3E3CEF2716773A308C8251300A861D00001EB60EDB</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>11</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>56</sml:ZipLength>
<sml:ElementStream>78DA636460606002E24D27B89E2F2AEB71E07AF88F69EBE6168717760EF71E553B1CE093F93853F3608B03C3281805A360D801003FEA106D</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>12</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>56</sml:ZipLength>
<sml:ElementStream>78DA636460606002E2BC7346DBBF96F538CC623C69F66765BDC3092FC1A35C1A8E0782BC9F3DF2385BEFC0300A46C12818760000616D0FB2</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>13</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>55</sml:ZipLength>
<sml:ElementStream>78DA636460606002E2BC7346DBBF96F538A82D9DA872747AA9034313F3BE530D4E07E6381833246C04F247C1281805C30E000016C00E5500</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>14</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>55</sml:ZipLength>
<sml:ElementStream>78DA636460606002E2658EAB18046B7A1CAA7E14B6FD9995E6C0B1905B21AEC1E9805A500F77C3B134078651300A46C1B00300D9DE0D4200</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>15</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>55</sml:ZipLength>
<sml:ElementStream>78DA636460606002E21F454909CF6B7A1C6AF84D17C5CC34727811E063D7D4E074608E884B8DC35123078651300A46C1B0030022610DD500</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>16</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>50</sml:ZipLength>
<sml:ElementStream>78DA636460606002E2AB7A9B679F4F2F73D8D7736E279BE21407185F679E82D8FF8BA9071846C1281805C30E00008742103F0000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>17</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>48</sml:ZipLength>
<sml:ElementStream>78DA636460606002E2158EAB18046B7A1CF6CC2C12F73E9DE6A0E678FE9247D45A389F61148C825130EC0000A8470DBB</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>18</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>48</sml:ZipLength>
<sml:ElementStream>78DA636460606002E2A85F73188ED7F5389C51AF92C98E367168F86AB9BC3F7C2D9CCF300A46C128187600009D220DB3</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>19</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>49</sml:ZipLength>
<sml:ElementStream>78DA63646060600262B63BC66AB39DA638F0F09CCC5B1056EA00E32F2894765828917A8061148C825130EC00005F800C20000000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>20</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>49</sml:ZipLength>
<sml:ElementStream>78DA6364606060026225A30C7DAEA5731DB6C9E8DCECE82D7580F1A76CD67065574973601805A360140C3B0000610D0B18000000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>21</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>49</sml:ZipLength>
<sml:ElementStream>78DA636460606002E2FF7F2D6AD67F5CE820F33EB16BD7F91207185F417DEF4609C354078651300A46C1B003007EA4113C000000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>22</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>49</sml:ZipLength>
<sml:ElementStream>78DA636460606002E26FAAF77499E7CF757893F3AFF88982A5038CFFC7B46D51BE43F2018651300A46C1B0030092750FBF000000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>23</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>49</sml:ZipLength>
<sml:ElementStream>78DA63646060600262DFBA2C8FA4CF0B1D169C14FA324D40C801C69FA4C59475FB5CF2018651300A46C1B00300B7040EC5000000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>24</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>49</sml:ZipLength>
<sml:ElementStream>78DA636460606002E2A62F17944FA72D751010B73AA97BC3DC01C66FB33C78DDC52BF900C3281805A360D8010053910FB0000000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>25</sml:ID>
<sml:ElmentType>GeoLine</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>49</sml:ZipLength>
<sml:ElementStream>78DA636460606002E245C9DA571BE7AE7058D067645EB9D9D801C66FB33C78DDC52BF900C3281805A360D80100F9730F58000000</sml:ElementStream>
<sml:ElementStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:FixedColorOfSVG>FALSE</sml:FixedColorOfSVG>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,128,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:GaussianBlur>FALSE</sml:GaussianBlur>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:ShadowOffsetX>5</sml:ShadowOffsetX>
<sml:ShadowOffsetY>5</sml:ShadowOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
</sml:ElementStyle>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>26</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>Name</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>106</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C603AA269BFF56E9D5087094B6FEE9B56E3EF60D72765B6A3779143D2C71EA1C4CA660790AAA7EBBA9FEC9D8C2EC3000582401C11F162EB86A713263E5FB2EB594BFFD3D9FB1819D00042E0833D8C6E6868F8CF300A46C128A01B000078E72C360000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>27</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>设计（不可改）</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>95</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60FACFB334E185D64E0E0ED377EAC4044439A4B4766F56944D72D81F611FC6C43AD901A4EAE9BAEE277B27A3CB3040013B10BF58B74FE1C5BA858C0C680021F0C11E46373434FC671805A360140C0000005A2121CE00</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>28</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>校对（不可改）</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>95</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60FACFB334E185D64E0E0ED377EAC4044439CCEBD8E3B1687A8A03FF3F3EB91915131C40AA9EAEEB7EB27732BA0C0314B003F1B3050B159EAEDFC9C8800610021FEC61744343C37F8651300A46C1000000F7F324DD00</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>29</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>审核（不可改）</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>94</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60FACFB334E185D64E0E16D377EAC4044439DCBBE6B1E095608A8344E8970577EF753B80543D5DD7FD64EF64741906286007AB58A8F06CC10E4606348010F8600FA31B1A1AFE338C8251300A060000002F7328170000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>30</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>阶段文本（不可改）</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>95</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60FAC7B334E185D64E0E36D377EAC4044439F45BDDD9FF227EA24389624D995E7A810348D5D375DD4FF64E469761800276207E39639BC2B3755B1919D00042E0833D8C6E6868F8CF300A46C128180000003A02253100</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>31</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>文件号（不可改）</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>97</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60DAE3E6C61373F5DC1C3838671AED3916EA90931C79204C63A2C36ECD4C599B79110E20554FD7753FD93B195D86010A3881F8D9B4F627BBB73DEDDFCEC8800610021FEC61744343C37F8651300A4601DD0100864424D7000000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>32</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>日期文本（不可改）</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>94</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60FAC7B334E185D64E0E36D377EAC4044439CC3D38EFF159D7890E05AB840B0466781D00A97ABAAEFBC9DEC9E8320C500052F16CFAD26773E63332A00184C0077B18DDD0D0F09F61148C825130100000DEC726650000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>33</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>Stage</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>90</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C605ABAC6A9F2AA4C90C395E94919A29C010E2DAA6EE6E78FCD70B03A1139D988A9C001A4EAE9BAEE277B27A3CB3040012B104700011723031A40087CB087D10D0D0DFF1946C1281805030200E4AF1F380000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>34</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>FileNum</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>91</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60FACB89E36D4F77273A349CF7937D1CECEFB0848D7F7EE395450E2D7326DC170D8D7200A97ABAAEFBC9DEC9E8320C50C003C41108C0C5C8800610021FEC61744343C37F8651300A46017D010038E4263200</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>35</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>Date</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>91</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C605ABAC6A9F2AA4C90C395E94919A29C010E7BCE6F7D99B5638643617EEF342B039F0320554FD7753FD93B195D86010A5881380208B81819D00042E0833D8C6E6868F8CF300A46C12818100000C0D423A600</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>36</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>项目号文本（不可改）</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>97</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60DA387D5E89EC141787FD4A8753FB4DC21C22262CCA7B54BCC0E1D9060E9920B9020790AAA7EBBA9FEC9D8C2EC300059C40FC72E1CEE7B3D73DEDDFCEC8800610021FEC61744343C37F8651300A4601DD0100CE79263F000000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>37</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>版次（不可改）</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>94</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60FAF3B334E185D64E0E36D377EAC40444396C8A6BDB77367D8183C481C4699BBBFD0F80543D5DD7FD64EF647419062800A978DED9F16CCD424606348010F8600FA31B1A1AFE338C8251300A06020000CEF6272E0000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>38</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>VersionNum</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>91</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C605ABEC6A9F2AA4C9043C3793FD9C7C1FE0E6197F295176E59ECE0BBB7AAE7DA71DF0320554FD7753FD93B195D86010A5881380208B81819D00042E0833D8C6E6868F8CF300A46C128181000008D5A246600</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>39</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>比例（不可改）</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>94</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C607A02ABC1A2DC6F6E0E36D377EAC40444391CDC66F69FF7DB328717B14FE3ECD6FB1E00A97ABAAEFBC9DEC9E8320C500052F16CFD9427FBBA1919D00042E0833D8C6E6868F8CF300A46C128180800004EDB281F0000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>40</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>ProjectID</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>91</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C603AD97D5788D1A110872BD393324439031C1EE54CC9DC5DB1DC21608E47DCB689F90E20554FD7753FD93B195D86010A7880380201B81819D00042E0833D8C6E6868F8CF300A46C128A02F0000E4E0252200</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>41</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>Scale</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>90</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C605ABEC6A9F2AA4C9043C3793FD9C7C1FE0E59DA3669FE175739185845CD13700C380052F5745DF793BD93D16518A08015882380808B91010D20043ED8C3E8868686FF0CA360148C8201010068B21F160000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>42</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>Proofreader</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>92</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60DA538839AAACD0DB61C2D29BFBA6D5F83BDCAA1168FE79A2C941DBA0F49069DB040790AAA7EBBA9FEC9D8C2EC300056015137A9F2FDFC0C8800610021FEC61744343C37F8651300A46C1400000491B2536</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>43</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>Reviewer</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>92</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60DA538839AAACD0DB61C2D29BFBA6D5F83B043D2958F08DADD921806B5EFD8349DD0E20554FD7753FD93B195D86010AC02A26F43E5FBE8191010D20043ED8C3E8868686FF0CA360148C82810000AF222596</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>44</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>Author</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>92</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60DA538839AAACD0DB61C2D29BFBA6D5F83BA4E5B55D4999DEE4F08B478EC7E9D5240790AAA7EBBA9FEC9D8C2EC300056015137A9F2FDFC0C8800610021FEC61744343C37F8651300A46C1400000F39F2456</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>45</sml:ID>
<sml:ElmentType>GeoText</sml:ElmentType>
<sml:ElmentTag>ProjectName</sml:ElmentTag>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>96</sml:ZipLength>
<sml:ElementStream>78DA63640083FF5C0C6C60BA74228BC285B03887094B6FEE9B56E3EF50CF2CE57C70CE2287058ACF4E2F9A3DD101A4EAE9BAEE277B27A3CB304001071047443CDDBEF4F98A6E4606348010F8600FA31B1A1AFE338C8251300AE80F00B15926F1</sml:ElementStream>
</sml:LayoutElement>
</sml:LayoutElements>
<sml:RulerBkColor>RGB(255,255,131071)</sml:RulerBkColor>
<sml:RulerScaleColor>RGB(42,46,130870)</sml:RulerScaleColor>
<sml:ScaleFontColor>RGB(42,46,130870)</sml:ScaleFontColor>
<sml:LytAttributeNodes>
<sml:LytAttributeNodesNumber>0</sml:LytAttributeNodesNumber>
</sml:LytAttributeNodes>
<sml:ShowPrintPage>FALSE</sml:ShowPrintPage>
<sml:CoordinateRatio>0.5906976744186045</sml:CoordinateRatio>
<sml:DefaultCenter>
<sml:x>1800</sml:x>
<sml:y>850.0000000000002274</sml:y>
</sml:DefaultCenter>
<sml:MinScale>0.00001</sml:MinScale>
<sml:MaxScale>0.001</sml:MaxScale>
<sml:IsAllowTextOverlap>FALSE</sml:IsAllowTextOverlap>
<sml:IsLineSmoothingMode>TRUE</sml:IsLineSmoothingMode>
<sml:IsTextSmoothingMode>FALSE</sml:IsTextSmoothingMode>
<sml:ExternalXML>&lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;CustomInfo&gt;
&lt;MapSeriesSetting Enabled="false"&gt;
  &lt;MapIndex CurrentPage="" IndexLayer="" IndexMap="" PageNameField="" Tag=""/&gt;
  &lt;MapLocalizer Enabled="false" LocateMap="" MarkLayer="" MaskLayer="" Tag=""/&gt;
&lt;/MapSeriesSetting&gt;
&lt;/CustomInfo&gt;
</sml:ExternalXML>
<sml:SnapEnable>TRUE</sml:SnapEnable>
</sml:Layout>
<sml:Layout>
<sml:LayoutName>runmapcad</sml:LayoutName>
<sml:ShowGrid>TRUE</sml:ShowGrid>
<sml:AutoModifyMap>FALSE</sml:AutoModifyMap>
<sml:GridSnape>FALSE</sml:GridSnape>
<sml:ShowRuler>TRUE</sml:ShowRuler>
<sml:UseRulerLine>TRUE</sml:UseRulerLine>
<sml:RulerLineCont>0</sml:RulerLineCont>
<sml:LayoutPaper>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>77</sml:ZipLength>
<sml:ElementStream>78DAAB6180822BF31CC0B4C554070E209521C0C0308B1B82416C902458C17F203870E0C0FF7A201B1933C2700A9048011B65C201D17F820115730231C80810661805A360140C180000AA641A88000000</sml:ElementStream>
</sml:LayoutPaper>
<sml:LayoutElements>
<sml:LayoutElement>
<sml:ID>1</sml:ID>
<sml:ElmentType>GeoDirection</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>1024</sml:StreamLength>
<sml:ZipLength>130</sml:ZipLength>
<sml:ElementStream>78DADBC6C0C0D025F47B7ABFE51A877D599E1EC1BA8B1D98E6FAED9C2ADAE800946238F0B1CCA1E0DFFFFF20362310CF7B7BE6E1A9552B1DA6245CF4FEBA74A94399B9A65FC889750E2F4A0EBA6CDCB200AC07025C0E30306840D91208E1FF1248FC14982010C0D432428560E2400056F71F61064CCD281805A380220000A97032EA0000</sml:ElementStream>
</sml:LayoutElement>
<sml:LayoutElement>
<sml:ID>2</sml:ID>
<sml:ElmentType>GeoMap</sml:ElmentType>
<sml:ElmentTag/>
<sml:StreamLength>2048</sml:StreamLength>
<sml:ZipLength>416</sml:ZipLength>
<sml:ElementStream>78DADBC8C2C070263624EDF1FD790E3FF63E606B329BEAA0F15860AAE9F30D0E1334DEF91A7AAF706040020A2ED9727625E907D83B65974CD6DEE0E0529CBEEAE681650E0742B22DAA96661C5801546307345301486F6182E9FA600F22EB191988068C4876C1EC80D909B30BA65603490F10FC07117B5FFF0701861498009DD43211A196098A59A13423232E1B19099842B233B981D835AF24B32433B5D837B1002C363F7EED09951F37EC18FF43014C310F9426367D401DE0CE447C34B3DE3702FB1EAEC5A1DE0185661081D3ACD0206344F52BC27B6C40FC745DF793BD93A101E3408C1304B0041F2562603E8AA79891944083F51F10DB14E7E65839E7E717A564E62596A406A5A6A516A5E625A706571697A4E6DAF1728115F825E6A6DA05E424E625162920142B4014E9EAEAE6DAE8C39541B5845416A4DA1940C4C16CA8B86B40B0BB737E0A5C0ECE87CA070705EBC3D8A1799925C576BEAE21AE4110C51001A8AC4B667109860A842050953E21CFA1940CA068E5474E0688C00A3E183EBF2BC3B160A3F0D72FFB831C49284246C128180518E03F727DC000009A54F8CE</sml:ElementStream>
</sml:LayoutElement>
</sml:LayoutElements>
<sml:RulerBkColor>RGB(255,255,131071)</sml:RulerBkColor>
<sml:RulerScaleColor>RGB(42,46,130870)</sml:RulerScaleColor>
<sml:ScaleFontColor>RGB(42,46,130870)</sml:ScaleFontColor>
<sml:LytAttributeNodes>
<sml:LytAttributeNodesNumber>0</sml:LytAttributeNodesNumber>
</sml:LytAttributeNodes>
<sml:ShowPrintPage>FALSE</sml:ShowPrintPage>
<sml:CoordinateRatio>0.4490843341880779</sml:CoordinateRatio>
<sml:DefaultCenter>
<sml:x>1969.7883614759225566</sml:x>
<sml:y>1356.2125641025641016</sml:y>
</sml:DefaultCenter>
<sml:MinScale>0.00001</sml:MinScale>
<sml:MaxScale>0.001</sml:MaxScale>
<sml:IsAllowTextOverlap>FALSE</sml:IsAllowTextOverlap>
<sml:IsLineSmoothingMode>TRUE</sml:IsLineSmoothingMode>
<sml:IsTextSmoothingMode>FALSE</sml:IsTextSmoothingMode>
<sml:ExternalXML>&lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;CustomInfo&gt;
&lt;MapSeriesSetting Enabled="false"&gt;
  &lt;MapIndex CurrentPage="" IndexLayer="" IndexMap="" PageNameField="" Tag=""/&gt;
  &lt;MapLocalizer Enabled="false" LocateMap="" MarkLayer="" MaskLayer="" Tag=""/&gt;
&lt;/MapSeriesSetting&gt;
&lt;/CustomInfo&gt;
</sml:ExternalXML>
<sml:SnapEnable>TRUE</sml:SnapEnable>
</sml:Layout>
</sml:Layouts>
<sml:Scenes>
<sml:Scene>
<sml:Version>20090106</sml:Version>
<sml:Name>EntitiesScene</sml:Name>
<sml:SceneType>ELLIPSOID</sml:SceneType>
<sml:DynamicProjection>FALSE</sml:DynamicProjection>
<sml:Bounds>
<sml:Left>0.000000</sml:Left>
<sml:Top>0.000000</sml:Top>
<sml:Right>0.000000</sml:Right>
<sml:Bottom>0.000000</sml:Bottom>
</sml:Bounds>
<sml:BasicSceneSetting>
<sml:EnvironmentParameters>
<sml:EnvironmentLightAmbient>RGBA(0.600000,0.600000,0.600000,1.000000)</sml:EnvironmentLightAmbient>
<sml:EnvironmentLightDiffuse>RGBA(0.800000,0.800000,0.800000,1.000000)</sml:EnvironmentLightDiffuse>
<sml:BackgroundColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:BackgroundColor>
<sml:ImageBrightness>1.000000</sml:ImageBrightness>
<sml:ImageConstrast>1.000000</sml:ImageConstrast>
<sml:ImageHue>0.000000</sml:ImageHue>
<sml:ImageSaturation>1.000000</sml:ImageSaturation>
<sml:ImageGamma>1.000000</sml:ImageGamma>
<sml:TerrainExaggeration>1.000000</sml:TerrainExaggeration>
<sml:BasicTerrainHeight>0.000000</sml:BasicTerrainHeight>
<sml:RenderParameter>
<sml:OverlapDisplayed>TRUE</sml:OverlapDisplayed>
<sml:SceneOverlapDisplayedOptions>
<sml:OverlappedSpaceSize>
<sml:X>0</sml:X>
<sml:Y>0</sml:Y>
</sml:OverlappedSpaceSize>
<sml:IconWithTextDisplay>FALSE</sml:IconWithTextDisplay>
</sml:SceneOverlapDisplayedOptions>
</sml:RenderParameter>
<sml:MultiLevel>0</sml:MultiLevel>
<sml:HighLightMode>TRUE</sml:HighLightMode>
</sml:EnvironmentParameters>
<sml:Cameras>
<sml:Camera>
<sml:Name>name</sml:Name>
<sml:FOV>0.4875881384</sml:FOV>
<sml:Aspect>2.3210831721</sml:Aspect>
<sml:FarDistance>8868384.287933</sml:FarDistance>
<sml:NearDistance>2272.819257</sml:NearDistance>
<sml:camPosition>
<sml:x>8894759.843907</sml:x>
<sml:y>5809169.424212</sml:y>
<sml:z>-2516589.146991</sml:z>
</sml:camPosition>
<sml:Orientation>
<sml:x>-0.067060117823165</sml:x>
<sml:y>0.786703840252226</sml:y>
<sml:z>0.266299259156627</sml:z>
<sml:w>0.552887613265639</sml:w>
</sml:Orientation>
<sml:ProjectionType3D>PT_PERSPECTIVE</sml:ProjectionType3D>
<sml:CollisionDetection>FALSE</sml:CollisionDetection>
<sml:CollisionDistance>0.50</sml:CollisionDistance>
</sml:Camera>
</sml:Cameras>
<sml:Stereo>
<sml:StereoEnable>FALSE</sml:StereoEnable>
<sml:StereoMode>0</sml:StereoMode>
<sml:StereoParallaxMode>0</sml:StereoParallaxMode>
<sml:StereoCameraSeparation>0.050000</sml:StereoCameraSeparation>
<sml:StereoCameraAngle>-0.012000</sml:StereoCameraAngle>
</sml:Stereo>
<sml:MultiViewportMode>MVM_NONE</sml:MultiViewportMode>
<sml:Fog>
<sml:Type>FOG_NONE</sml:Type>
<sml:Color>RGBA(1.0000000000,1.0000000000,1.0000000000,1.0000000000)</sml:Color>
<sml:Start>0.0</sml:Start>
<sml:End>1.0</sml:End>
<sml:Density>1.0</sml:Density>
<sml:FogAltitude>20000.000000</sml:FogAltitude>
</sml:Fog>
<sml:Star3D>
<sml:Visible>TRUE</sml:Visible>
</sml:Star3D>
<sml:Atmosphere>
<sml:Visible>TRUE</sml:Visible>
</sml:Atmosphere>
<sml:Ocean>
<sml:Visible>FALSE</sml:Visible>
</sml:Ocean>
<sml:OceanEffect>
<sml:Visible>FALSE</sml:Visible>
<sml:ReflectEnable>FALSE</sml:ReflectEnable>
<sml:Quality>0</sml:Quality>
<sml:SprayEnable>TRUE</sml:SprayEnable>
<sml:SeaLevel>50.000000</sml:SeaLevel>
<sml:WindSpeed>10.000000</sml:WindSpeed>
<sml:WindDirection>0.000000</sml:WindDirection>
</sml:OceanEffect>
<sml:GlobalImage>
<sml:Visible>TRUE</sml:Visible>
<sml:Transparency>1.00</sml:Transparency>
<sml:ReceiveVolume>FALSE</sml:ReceiveVolume>
</sml:GlobalImage>
<sml:Underground>
<sml:Visible>FALSE</sml:Visible>
<sml:Depth>1000.000000</sml:Depth>
<sml:UndergroundColor>RGBA(0.0000000000,0.0000000000,0.0000000000,1.0000000000)</sml:UndergroundColor>
</sml:Underground>
<sml:Skirt>FALSE</sml:Skirt>
<sml:LatLongGrid>
<sml:Visible>FALSE</sml:Visible>
<sml:VisibleText>FALSE</sml:VisibleText>
</sml:LatLongGrid>
<sml:StatusBar>
<sml:Visible>TRUE</sml:Visible>
</sml:StatusBar>
<sml:Scale>
<sml:Visible>TRUE</sml:Visible>
</sml:Scale>
<sml:ControlPlane>
<sml:Visible>TRUE</sml:Visible>
</sml:ControlPlane>
<sml:Cross>
<sml:Visible>TRUE</sml:Visible>
</sml:Cross>
<sml:Sun>
<sml:Visible>TRUE</sml:Visible>
<sml:SunTimeZoneInfo>-480</sml:SunTimeZoneInfo>
<sml:SunDateTime>2023-03-11 10:31:43</sml:SunDateTime>
</sml:Sun>
</sml:BasicSceneSetting>
<sml:Layers>
<sml:Layer>
<sml:Caption>中线-点</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>center_line_P@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>center_line_P@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>PointZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:bSketchMode>FALSE</sml:bSketchMode>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>0.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>5</sml:MarkerWidth>
<sml:MarkerHeight>5</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
<sml:IsBuildOSGBPipeCache>TRUE</sml:IsBuildOSGBPipeCache>
<sml:LODCount>5</sml:LODCount>
<sml:BestLOD>
<sml:LODDistance_0>200.000000</sml:LODDistance_0>
<sml:LODDistance_1>200.000000</sml:LODDistance_1>
<sml:LODDistance_2>200.000000</sml:LODDistance_2>
<sml:LODDistance_3>200.000000</sml:LODDistance_3>
<sml:LODDistance_4>200.000000</sml:LODDistance_4>
</sml:BestLOD>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>中线-线</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>center_line_L@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>center_line_L@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>LineZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>0.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>5</sml:MarkerWidth>
<sml:MarkerHeight>5</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>地面线-点</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>ground_line_P@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>ground_line_P@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>PointZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:bSketchMode>FALSE</sml:bSketchMode>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>0.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>5</sml:MarkerWidth>
<sml:MarkerHeight>5</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
<sml:IsBuildOSGBPipeCache>TRUE</sml:IsBuildOSGBPipeCache>
<sml:LODCount>5</sml:LODCount>
<sml:BestLOD>
<sml:LODDistance_0>200.000000</sml:LODDistance_0>
<sml:LODDistance_1>200.000000</sml:LODDistance_1>
<sml:LODDistance_2>200.000000</sml:LODDistance_2>
<sml:LODDistance_3>200.000000</sml:LODDistance_3>
<sml:LODDistance_4>200.000000</sml:LODDistance_4>
</sml:BestLOD>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>地面线-线</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>ground_line_L@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>ground_line_L@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>LineZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>0.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>5</sml:MarkerWidth>
<sml:MarkerHeight>5</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>纵管线-点</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>longitudinal_pipeline_P@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>longitudinal_pipeline_P@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>PointZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:bSketchMode>FALSE</sml:bSketchMode>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>0.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>5</sml:MarkerWidth>
<sml:MarkerHeight>5</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
<sml:IsBuildOSGBPipeCache>TRUE</sml:IsBuildOSGBPipeCache>
<sml:LODCount>5</sml:LODCount>
<sml:BestLOD>
<sml:LODDistance_0>200.000000</sml:LODDistance_0>
<sml:LODDistance_1>200.000000</sml:LODDistance_1>
<sml:LODDistance_2>200.000000</sml:LODDistance_2>
<sml:LODDistance_3>200.000000</sml:LODDistance_3>
<sml:LODDistance_4>200.000000</sml:LODDistance_4>
</sml:BestLOD>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>纵管线-线</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>longitudinal_pipeline_L@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>longitudinal_pipeline_L@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>LineZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>0.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>5</sml:MarkerWidth>
<sml:MarkerHeight>5</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>转角桩-点</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>zhuangjiao_P@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>zhuangjiao_P@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>PointZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:bSketchMode>FALSE</sml:bSketchMode>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>0.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>5</sml:MarkerWidth>
<sml:MarkerHeight>5</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
<sml:IsBuildOSGBPipeCache>TRUE</sml:IsBuildOSGBPipeCache>
<sml:LODCount>5</sml:LODCount>
<sml:BestLOD>
<sml:LODDistance_0>200.000000</sml:LODDistance_0>
<sml:LODDistance_1>200.000000</sml:LODDistance_1>
<sml:LODDistance_2>200.000000</sml:LODDistance_2>
<sml:LODDistance_3>200.000000</sml:LODDistance_3>
<sml:LODDistance_4>200.000000</sml:LODDistance_4>
</sml:BestLOD>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>里程桩-点</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>lichengzhuang_P@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>lichengzhuang_P@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>PointZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:bSketchMode>FALSE</sml:bSketchMode>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:Theme3D>
<sml:Type>1</sml:Type>
<sml:Caption/>
<sml:Visible>TRUE</sml:Visible>
<sml:FieldExpression>zhuangdian_type</sml:FieldExpression>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:DefaultModellingStyleEnabled>FALSE</sml:DefaultModellingStyleEnabled>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
</sml:UniqueItems>
</sml:Theme3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
<sml:IsBuildOSGBPipeCache>TRUE</sml:IsBuildOSGBPipeCache>
<sml:LODCount>5</sml:LODCount>
<sml:BestLOD>
<sml:LODDistance_0>200.000000</sml:LODDistance_0>
<sml:LODDistance_1>200.000000</sml:LODDistance_1>
<sml:LODDistance_2>200.000000</sml:LODDistance_2>
<sml:LODDistance_3>200.000000</sml:LODDistance_3>
<sml:LODDistance_4>200.000000</sml:LODDistance_4>
</sml:BestLOD>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>交叉桩-点</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>jiaocha_P@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>jiaocha_P@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>PointZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:bSketchMode>FALSE</sml:bSketchMode>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:Theme3D>
<sml:Type>1</sml:Type>
<sml:Caption/>
<sml:Visible>TRUE</sml:Visible>
<sml:FieldExpression>zhuangdian_type</sml:FieldExpression>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:DefaultModellingStyleEnabled>FALSE</sml:DefaultModellingStyleEnabled>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
</sml:UniqueItems>
</sml:Theme3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
<sml:IsBuildOSGBPipeCache>TRUE</sml:IsBuildOSGBPipeCache>
<sml:LODCount>5</sml:LODCount>
<sml:BestLOD>
<sml:LODDistance_0>200.000000</sml:LODDistance_0>
<sml:LODDistance_1>200.000000</sml:LODDistance_1>
<sml:LODDistance_2>200.000000</sml:LODDistance_2>
<sml:LODDistance_3>200.000000</sml:LODDistance_3>
<sml:LODDistance_4>200.000000</sml:LODDistance_4>
</sml:BestLOD>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>铁路穿越-线</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>tieluchuanyue_L@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>tieluchuanyue_L@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>LineZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:Theme3D>
<sml:Type>1</sml:Type>
<sml:Caption/>
<sml:Visible>TRUE</sml:Visible>
<sml:FieldExpression>chuanyueleixing</sml:FieldExpression>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:DefaultModellingStyleEnabled>FALSE</sml:DefaultModellingStyleEnabled>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965667</sml:LineStyle>
<sml:LineWidth>2</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965665</sml:LineStyle>
<sml:LineWidth>2</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
</sml:UniqueItems>
</sml:Theme3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>地形地貌-线</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>topographic_features_L@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>topographic_features_L@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>LineZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:Theme3D>
<sml:Type>1</sml:Type>
<sml:Caption/>
<sml:Visible>TRUE</sml:Visible>
<sml:FieldExpression>topographic_type</sml:FieldExpression>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:DefaultModellingStyleEnabled>FALSE</sml:DefaultModellingStyleEnabled>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>平原</sml:Value>
<sml:Caption>平原</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965665</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>水网</sml:Value>
<sml:Caption>水网</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965703</sml:LineStyle>
<sml:LineWidth>5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>山区</sml:Value>
<sml:Caption>山区</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965699</sml:LineStyle>
<sml:LineWidth>4</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>丘陵</sml:Value>
<sml:Caption>丘陵</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965672</sml:LineStyle>
<sml:LineWidth>3</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>戈壁</sml:Value>
<sml:Caption>戈壁</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965663</sml:LineStyle>
<sml:LineWidth>2</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
</sml:UniqueItems>
</sml:Theme3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>标志桩-点</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>biaozhizhuang_P@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>biaozhizhuang_P@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>PointZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:bSketchMode>FALSE</sml:bSketchMode>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:Theme3D>
<sml:Type>1</sml:Type>
<sml:Caption/>
<sml:Visible>TRUE</sml:Visible>
<sml:FieldExpression>biaozhileixng</sml:FieldExpression>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:DefaultModellingStyleEnabled>FALSE</sml:DefaultModellingStyleEnabled>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
</sml:UniqueItems>
</sml:Theme3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
<sml:IsBuildOSGBPipeCache>TRUE</sml:IsBuildOSGBPipeCache>
<sml:LODCount>5</sml:LODCount>
<sml:BestLOD>
<sml:LODDistance_0>200.000000</sml:LODDistance_0>
<sml:LODDistance_1>200.000000</sml:LODDistance_1>
<sml:LODDistance_2>200.000000</sml:LODDistance_2>
<sml:LODDistance_3>200.000000</sml:LODDistance_3>
<sml:LODDistance_4>200.000000</sml:LODDistance_4>
</sml:BestLOD>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>加密桩-点</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>jiamizhuang_P@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>jiamizhuang_P@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>PointZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:bSketchMode>FALSE</sml:bSketchMode>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:Theme3D>
<sml:Type>1</sml:Type>
<sml:Caption/>
<sml:Visible>TRUE</sml:Visible>
<sml:FieldExpression>jiami_type</sml:FieldExpression>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:DefaultModellingStyleEnabled>FALSE</sml:DefaultModellingStyleEnabled>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>类型2</sml:Value>
<sml:Caption>类型2</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>类型1</sml:Value>
<sml:Caption>类型1</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
</sml:UniqueItems>
</sml:Theme3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
<sml:IsBuildOSGBPipeCache>TRUE</sml:IsBuildOSGBPipeCache>
<sml:LODCount>5</sml:LODCount>
<sml:BestLOD>
<sml:LODDistance_0>200.000000</sml:LODDistance_0>
<sml:LODDistance_1>200.000000</sml:LODDistance_1>
<sml:LODDistance_2>200.000000</sml:LODDistance_2>
<sml:LODDistance_3>200.000000</sml:LODDistance_3>
<sml:LODDistance_4>200.000000</sml:LODDistance_4>
</sml:BestLOD>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
</sml:Layer>
<sml:Layer>
<sml:Caption>地区等级-线</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>diqudengji_L@EntitiesAchivments</sml:DataSourceAlias>
<sml:LayerName>diqudengji_L@EntitiesAchivments</sml:LayerName>
<sml:LayerType>DatasetLayer</sml:LayerType>
<sml:DatasetType>LineZ</sml:DatasetType>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Selectable>TRUE</sml:Selectable>
<sml:Editable>FALSE</sml:Editable>
<sml:QueryDef>
<sml:QueryType>General</sml:QueryType>
<sml:Options>
<sml:Geometry>TRUE</sml:Geometry>
<sml:Attribute>TRUE</sml:Attribute>
</sml:Options>
<sml:Mode>GeneralQuery</sml:Mode>
<sml:CursorType>OpenDynamic</sml:CursorType>
<sml:CursorLocation>UserServer</sml:CursorLocation>
</sml:QueryDef>
<sml:AlwaysRender>TRUE</sml:AlwaysRender>
<sml:OnlyUseGlobalTile>FALSE</sml:OnlyUseGlobalTile>
<sml:VectorFiltratePixSize>0</sml:VectorFiltratePixSize>
<sml:GridNoData>-9999</sml:GridNoData>
<sml:GridNoDataColor>RGBA(0.000000,0.000000,0.000000,0.000000))</sml:GridNoDataColor>
<sml:ColorTable>
<sml:MaxZValue>0.00000000000000000000</sml:MaxZValue>
<sml:MinZValue>0.00000000000000000000</sml:MinZValue>
<sml:WaterLevel>0.00000000000000000000</sml:WaterLevel>
<sml:bUsingWaterLevel>FALSE</sml:bUsingWaterLevel>
<sml:bUsingColorDictTable>FALSE</sml:bUsingColorDictTable>
</sml:ColorTable>
<sml:SpecialValueTransparent>FALSE</sml:SpecialValueTransparent>
<sml:VisibleAltitudeMin>0.000000</sml:VisibleAltitudeMin>
<sml:VisibleAltitudeMax>0.000000</sml:VisibleAltitudeMax>
<sml:VisibleDistanceMax>47836027.500000</sml:VisibleDistanceMax>
<sml:VisibleDistanceMin>0.000000</sml:VisibleDistanceMin>
<sml:bUseGeoStyle>FALSE</sml:bUseGeoStyle>
<sml:ShadowType>NONE</sml:ShadowType>
</sml:Options>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:Style>
<sml:SelectStyle>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.5</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>25</sml:MarkerWidth>
<sml:MarkerHeight>25</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>1.5</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(178,178,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.700000,0.700000,1.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,1.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:SelectStyle>
<sml:Region2DTo3D>
<sml:BaseHeightField/>
<sml:ExtendHeightField/>
<sml:TopTextureField/>
<sml:SideTextureField/>
<sml:URepeatField/>
<sml:VRepeatField/>
<sml:TopURepeatField/>
<sml:TopVRepeatField/>
<sml:ScaleXField/>
<sml:ScaleYField/>
<sml:FromNodeField/>
<sml:ToNodeField/>
<sml:NodeID/>
</sml:Region2DTo3D>
<sml:Theme3D>
<sml:Type>1</sml:Type>
<sml:Caption/>
<sml:Visible>TRUE</sml:Visible>
<sml:FieldExpression>dengji_level</sml:FieldExpression>
<sml:DefaultStyle>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>0</sml:LineStyle>
<sml:LineWidth>0.1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(189,235,131071)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
</sml:DefaultStyle>
<sml:DefaultModellingStyleEnabled>FALSE</sml:DefaultModellingStyleEnabled>
<sml:UniqueItems>
<sml:UniqueItem>
<sml:Value>二级</sml:Value>
<sml:Caption>二级</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965701</sml:LineStyle>
<sml:LineWidth>2</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>一级</sml:Value>
<sml:Caption>一级</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965670</sml:LineStyle>
<sml:LineWidth>1</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>四级</sml:Value>
<sml:Caption>四级</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965674</sml:LineStyle>
<sml:LineWidth>4</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
<sml:UniqueItem>
<sml:Value>三级</sml:Value>
<sml:Caption>三级</sml:Caption>
<sml:StyleEnable>TRUE</sml:StyleEnable>
<sml:ModellingStyleEnabled>FALSE</sml:ModellingStyleEnabled>
<sml:Style>
<sml:MarkerStyle>0</sml:MarkerStyle>
<sml:MarkerSize>2.4</sml:MarkerSize>
<sml:MarkerAngle>0</sml:MarkerAngle>
<sml:MarkerWidth>24</sml:MarkerWidth>
<sml:MarkerHeight>24</sml:MarkerHeight>
<sml:MarkerPicturePath/>
<sml:MarkerSVGPath/>
<sml:LineStyle>965699</sml:LineStyle>
<sml:LineWidth>3</sml:LineWidth>
<sml:LineColor>RGB(0,0,130816)</sml:LineColor>
<sml:LineCapStyle>0</sml:LineCapStyle>
<sml:LineJoinStyle>0</sml:LineJoinStyle>
<sml:FillStyle>0</sml:FillStyle>
<sml:FillForeColor>RGB(0,0,130816)</sml:FillForeColor>
<sml:FillBackColor>RGB(255,255,131071)</sml:FillBackColor>
<sml:FillBackOpaque>TRUE</sml:FillBackOpaque>
<sml:FillOpaqueRate>100</sml:FillOpaqueRate>
<sml:FillGradientType>None</sml:FillGradientType>
<sml:FillAngle>0</sml:FillAngle>
<sml:FillCenterOffsetX>0</sml:FillCenterOffsetX>
<sml:FillCenterOffsetY>0</sml:FillCenterOffsetY>
<sml:AcrossLongtitude>FALSE</sml:AcrossLongtitude>
<sml:Style3D>
<sml:PointSize>4.0</sml:PointSize>
<sml:ColorPoint>RGBA(1.000000,1.000000,1.000000,1.000000)</sml:ColorPoint>
<sml:LineWidth>1</sml:LineWidth>
<sml:Fill3DMode>FILL_FACEANDLINE</sml:Fill3DMode>
<sml:ColorFill>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:ColorFill>
<sml:LineColor>RGBA(0.000000,0.000000,0.000000,1.000000)</sml:LineColor>
<sml:IconScale>1.0</sml:IconScale>
<sml:TilingU>1.000000</sml:TilingU>
<sml:TilingV>1.000000</sml:TilingV>
<sml:TextureRepeatMode>RepeatTimes</sml:TextureRepeatMode>
<sml:TopTilingU>1.000000</sml:TopTilingU>
<sml:TopTilingV>1.000000</sml:TopTilingV>
<sml:TopTextureRepeatMode>RepeatTimes</sml:TopTextureRepeatMode>
<sml:IconName/>
<sml:SideTextureName/>
<sml:TopTextureName/>
<sml:BottomAltitude>0</sml:BottomAltitude>
<sml:ExtendHeight>0</sml:ExtendHeight>
<sml:AltitudeMode>ClampToGround</sml:AltitudeMode>
<sml:IconAnchor>
<sml:x>0.0000000000</sml:x>
<sml:y>0.0000000000</sml:y>
</sml:IconAnchor>
<sml:FixedPixelSize>TRUE</sml:FixedPixelSize>
<sml:BillboardMode>SCREEN_ALIGNED</sml:BillboardMode>
<sml:AnchorOffsetMode>FixedScale</sml:AnchorOffsetMode>
<sml:IsMarker3D>FALSE</sml:IsMarker3D>
<sml:LineStyle3D>LINE_SIMPLE</sml:LineStyle3D>
<sml:PipeSides>20</sml:PipeSides>
<sml:IsFiletEnable>TRUE</sml:IsFiletEnable>
<sml:IsTessellate>FALSE</sml:IsTessellate>
<sml:ScaleX>1.000000</sml:ScaleX>
<sml:ScaleY>1.000000</sml:ScaleY>
<sml:ScaleZ>1.000000</sml:ScaleZ>
<sml:RotateX>0.000000</sml:RotateX>
<sml:RotateY>0.000000</sml:RotateY>
<sml:RotateZ>0.000000</sml:RotateZ>
</sml:Style3D>
</sml:Style>
</sml:UniqueItem>
</sml:UniqueItems>
</sml:Theme3D>
<sml:DisplayScale>0.00000001352130857282124</sml:DisplayScale>
<sml:LevelWidth>16</sml:LevelWidth>
<sml:UserDefineScale>FALSE</sml:UserDefineScale>
<sml:MultiViewportVisible>65535</sml:MultiViewportVisible>
<sml:CacheFileType>OSGB</sml:CacheFileType>
<sml:IsSCPRelated>FALSE</sml:IsSCPRelated>
<sml:RegionOffsettingSize>0.000000</sml:RegionOffsettingSize>
<sml:RenderCullMode>CCW</sml:RenderCullMode>
<sml:HasLocalCache>TRUE</sml:HasLocalCache>
<sml:IsBuildOSGBCache>FALSE</sml:IsBuildOSGBCache>
</sml:Layer>
</sml:Layers>
<sml:Terrains>
<sml:Terrain>
<sml:Caption>dem_90m</sml:Caption>
<sml:Description/>
<sml:DataSourceAlias>C:/Users/<USER>/AppData/Local/SuperMap/Cache/localhost_8090_iserver_services_3D-local3DCache-dem90mdem/Elevations/<EMAIL>/dem_90m.sct</sml:DataSourceAlias>
<sml:LayerName>dem_90m</sml:LayerName>
<sml:LayerType>TerrainFileLayer</sml:LayerType>
<sml:Password>274941E4B8EB8A60F1C44D8885232ABF</sml:Password>
<sml:Options>
<sml:Visible>TRUE</sml:Visible>
<sml:Modified>FALSE</sml:Modified>
<sml:VisibleDistanceMin>0</sml:VisibleDistanceMin>
<sml:VisibleDistanceMax>0</sml:VisibleDistanceMax>
</sml:Options>
<sml:LODRangeScale>1.000000</sml:LODRangeScale>
</sml:Terrain>
</sml:Terrains>
<sml:GlobalTinTerrain>
<sml:NormalMapEnable>FALSE</sml:NormalMapEnable>
<sml:HypsometricSetting>
<sml:MaxVisibleValue>8735.000000</sml:MaxVisibleValue>
<sml:MinVisibleValue>-277.000000</sml:MinVisibleValue>
<sml:ColorTable>
<sml:ColorKeyValue>
<sml:Key>-277.000000</sml:Key>
<sml:Value>4282622538</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>13.709677</sml:Key>
<sml:Value>4284330340</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>304.419355</sml:Key>
<sml:Value>4285972862</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>595.129032</sml:Key>
<sml:Value>4287550360</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>885.838710</sml:Key>
<sml:Value>4289126321</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>1176.548387</sml:Key>
<sml:Value>4290832327</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>1467.258065</sml:Key>
<sml:Value>4291882461</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>1757.967742</sml:Key>
<sml:Value>4292604913</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>2048.677419</sml:Key>
<sml:Value>4291621870</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>2339.387097</sml:Key>
<sml:Value>4289786093</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>2630.096774</sml:Key>
<sml:Value>4286704109</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>2920.806452</sml:Key>
<sml:Value>4284276711</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>3211.516129</sml:Key>
<sml:Value>4282242270</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>3502.225806</sml:Key>
<sml:Value>4281975511</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>3792.935484</sml:Key>
<sml:Value>4281905361</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>4083.645161</sml:Key>
<sml:Value>4281835468</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>4374.354839</sml:Key>
<sml:Value>4281766342</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>4665.064516</sml:Key>
<sml:Value>4281763263</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>4955.774194</sml:Key>
<sml:Value>4281826746</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>5246.483871</sml:Key>
<sml:Value>4281956278</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>5537.193548</sml:Key>
<sml:Value>4282086315</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>5827.903226</sml:Key>
<sml:Value>4282414240</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>6118.612903</sml:Key>
<sml:Value>4283138457</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>6409.322581</sml:Key>
<sml:Value>4285897120</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>6700.032258</sml:Key>
<sml:Value>4289968304</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>6990.741935</sml:Key>
<sml:Value>4291611843</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>7281.451613</sml:Key>
<sml:Value>4292795860</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>7572.161290</sml:Key>
<sml:Value>4293322206</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>7862.870968</sml:Key>
<sml:Value>4293848552</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>8153.580645</sml:Key>
<sml:Value>4294440689</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>8444.290323</sml:Key>
<sml:Value>4294769657</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>8735.000000</sml:Key>
<sml:Value>4294967295</sml:Value>
</sml:ColorKeyValue>
</sml:ColorTable>
<sml:Opacity>0.000000</sml:Opacity>
<sml:DisplayMode>0</sml:DisplayMode>
<sml:AnalysisMode>0</sml:AnalysisMode>
<sml:LineColor>
<sml:R>1.000000</sml:R>
<sml:G>0.000000</sml:G>
<sml:B>0.000000</sml:B>
<sml:A>1.000000</sml:A>
</sml:LineColor>
<sml:LinesInterval>100.000000</sml:LinesInterval>
</sml:HypsometricSetting>
<sml:SlopeSetting>
<sml:MaxVisibleValue>8735.000000</sml:MaxVisibleValue>
<sml:MinVisibleValue>-277.000000</sml:MinVisibleValue>
<sml:ColorTable>
<sml:ColorKeyValue>
<sml:Key>-277.000000</sml:Key>
<sml:Value>-12344758</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>13.709677</sml:Key>
<sml:Value>-10636956</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>304.419355</sml:Key>
<sml:Value>-8994434</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>595.129032</sml:Key>
<sml:Value>-7416936</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>885.838710</sml:Key>
<sml:Value>-5840975</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>1176.548387</sml:Key>
<sml:Value>-4134969</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>1467.258065</sml:Key>
<sml:Value>-3084835</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>1757.967742</sml:Key>
<sml:Value>-2362383</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>2048.677419</sml:Key>
<sml:Value>-3345426</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>2339.387097</sml:Key>
<sml:Value>-5181203</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>2630.096774</sml:Key>
<sml:Value>-8263187</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>2920.806452</sml:Key>
<sml:Value>-10690585</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>3211.516129</sml:Key>
<sml:Value>-12725026</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>3502.225806</sml:Key>
<sml:Value>-12991785</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>3792.935484</sml:Key>
<sml:Value>-13061935</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>4083.645161</sml:Key>
<sml:Value>-13131828</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>4374.354839</sml:Key>
<sml:Value>-13200954</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>4665.064516</sml:Key>
<sml:Value>-13204033</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>4955.774194</sml:Key>
<sml:Value>-13140550</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>5246.483871</sml:Key>
<sml:Value>-13011018</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>5537.193548</sml:Key>
<sml:Value>-12880981</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>5827.903226</sml:Key>
<sml:Value>-12553056</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>6118.612903</sml:Key>
<sml:Value>-11828839</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>6409.322581</sml:Key>
<sml:Value>-9070176</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>6700.032258</sml:Key>
<sml:Value>-4998992</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>6990.741935</sml:Key>
<sml:Value>-3355453</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>7281.451613</sml:Key>
<sml:Value>-2171436</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>7572.161290</sml:Key>
<sml:Value>-1645090</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>7862.870968</sml:Key>
<sml:Value>-1118744</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>8153.580645</sml:Key>
<sml:Value>-526607</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>8444.290323</sml:Key>
<sml:Value>-197639</sml:Value>
</sml:ColorKeyValue>
<sml:ColorKeyValue>
<sml:Key>8735.000000</sml:Key>
<sml:Value>-1</sml:Value>
</sml:ColorKeyValue>
</sml:ColorTable>
<sml:Opacity>0.000000</sml:Opacity>
<sml:DisplayMode>0</sml:DisplayMode>
</sml:SlopeSetting>
</sml:GlobalTinTerrain>
<sml:ReferenceGeodeticPoint>
<sml:x>108.923611</sml:x>
<sml:y>34.540833</sml:y>
<sml:z>0.000000</sml:z>
</sml:ReferenceGeodeticPoint>
<sml:DynamicSelection>FALSE</sml:DynamicSelection>
<sml:LayerIDUnit>8</sml:LayerIDUnit>
<sml:TransparencyOptimization>FALSE</sml:TransparencyOptimization>
<sml:DepthOptimization>FALSE</sml:DepthOptimization>
<sml:ShadowDarkness>0.000000</sml:ShadowDarkness>
<sml:ClipDataSourceAlias/>
</sml:Scene>
</sml:Scenes>
<sml:DesktopInfo/>
</SuperMapWorkspaceUnicode>
